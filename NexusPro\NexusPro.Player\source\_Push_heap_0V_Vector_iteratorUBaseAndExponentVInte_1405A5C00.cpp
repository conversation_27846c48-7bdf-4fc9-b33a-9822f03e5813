/*
 * Function: ??$_Push_heap_0@V?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@_JU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@0@0PEA_JPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@Z
 * Address: 0x1405A5C00
 */

int std::_Push_heap_0<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>()
{
  __int64 v0; // rax@1
  const struct CryptoPP::Integer *v1; // rax@2
  __int64 v3; // [sp+20h] [bp-A8h]@1
  char v4; // [sp+28h] [bp-A0h]@2
  CryptoPP::Integer *v5; // [sp+78h] [bp-50h]@2
  char v6; // [sp+80h] [bp-48h]@2
  char *v7; // [sp+98h] [bp-30h]@2
  __int64 v8; // [sp+A0h] [bp-28h]@1
  CryptoPP::Integer *v9; // [sp+A8h] [bp-20h]@2
  CryptoPP::Integer *v10; // [sp+B0h] [bp-18h]@2
  __int64 v11; // [sp+B8h] [bp-10h]@2

  v8 = -2i64;
  LODWORD(v0) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator-();
  v3 = v0;
  if ( v0 > 0 )
  {
    v5 = (CryptoPP::Integer *)&v4;
    v7 = &v6;
    LODWORD(v1) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator*();
    v9 = CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(
           v5,
           v1);
    v10 = v9;
    v11 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>((__int64)v7);
    std::_Push_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(
      v11,
      v3,
      0i64,
      v10);
  }
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
}
