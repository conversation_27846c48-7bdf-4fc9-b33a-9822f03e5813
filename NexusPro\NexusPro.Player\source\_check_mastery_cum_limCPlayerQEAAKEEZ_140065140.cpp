/*
 * Function: ?_check_mastery_cum_lim@CPlayer@@QEAAKEE@Z
 * Address: 0x140065140
 */

__int64 __fastcall CPlayer::_check_mastery_cum_lim(CPlayer *this, char byMasteryClass, char byIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  _class_fld *v5; // rax@6
  __int64 result; // rax@6
  int v7; // eax@8
  int v8; // eax@9
  int v9; // eax@11
  _class_fld *v10; // rax@13
  _class_fld *v11; // rax@14
  _class_fld *v12; // rax@15
  _class_fld *v13; // rax@15
  _class_fld *v14; // rax@17
  __int64 v15; // [sp+0h] [bp-A8h]@1
  _base_fld *v16; // [sp+20h] [bp-88h]@4
  _base_fld *v17; // [sp+28h] [bp-80h]@4
  unsigned int v18; // [sp+30h] [bp-78h]@9
  unsigned int v19; // [sp+34h] [bp-74h]@20
  unsigned int v20; // [sp+38h] [bp-70h]@20
  _class_fld *v21; // [sp+40h] [bp-68h]@6
  _class_fld *v22; // [sp+48h] [bp-60h]@13
  int n; // [sp+50h] [bp-58h]@14
  CRecordData *v24; // [sp+58h] [bp-50h]@14
  int v25; // [sp+60h] [bp-48h]@15
  CRecordData *v26; // [sp+68h] [bp-40h]@15
  int v27; // [sp+70h] [bp-38h]@15
  CRecordData *v28; // [sp+78h] [bp-30h]@15
  int v29; // [sp+80h] [bp-28h]@17
  CRecordData *v30; // [sp+88h] [bp-20h]@17
  int v31; // [sp+90h] [bp-18h]@20
  unsigned int v32; // [sp+94h] [bp-14h]@42
  CPlayer *v33; // [sp+B0h] [bp+8h]@1
  char v34; // [sp+B8h] [bp+10h]@1
  char v35; // [sp+C0h] [bp+18h]@1

  v35 = byIndex;
  v34 = byMasteryClass;
  v33 = this;
  v3 = &v15;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v16 = 0i64;
  v17 = 0i64;
  if ( CPlayerDB::GetPtrCurClass(&v33->m_Param)->m_nClass < 4 && CPlayerDB::GetPtrBaseClass(&v33->m_Param)->m_nClass < 4 )
  {
    if ( CPlayerDB::GetRaceCode(&v33->m_Param) < 3 )
    {
      v18 = CPlayerDB::GetMaxLevel(&v33->m_Param);
      v8 = ((int (__fastcall *)(CPlayer *))v33->vfptr->GetLevel)(v33);
      if ( v8 <= (signed int)v18 && ((int (__fastcall *)(_QWORD))v33->vfptr->GetLevel)(v33) > 0 )
      {
        if ( v33->m_Param.m_pClassHistory[0] )
        {
          v22 = CPlayerDB::GetPtrCurClass(&v33->m_Param);
          v10 = CPlayerDB::GetPtrBaseClass(&v33->m_Param);
          if ( v22->m_nClass == v10->m_nClass )
          {
            n = ((int (__fastcall *)(CPlayer *))v33->vfptr->GetLevel)(v33) - 1;
            v24 = (CRecordData *)((char *)&CPlayer::s_tblLimMasteryCumContinue
                                + 704 * CPlayerDB::GetRaceCode(&v33->m_Param));
            v11 = CPlayerDB::GetPtrCurClass(&v33->m_Param);
            v16 = CRecordData::GetRecord(&v24[v11->m_nClass], n);
          }
          else
          {
            v25 = ((int (__fastcall *)(CPlayer *))v33->vfptr->GetLevel)(v33) - 1;
            v26 = (CRecordData *)((char *)&CPlayer::s_tblLimMasteryCum + 704 * CPlayerDB::GetRaceCode(&v33->m_Param));
            v12 = CPlayerDB::GetPtrCurClass(&v33->m_Param);
            v16 = CRecordData::GetRecord(&v26[v12->m_nClass], v25);
            v27 = ((int (__fastcall *)(_QWORD))v33->vfptr->GetLevel)(v33) - 1;
            v28 = (CRecordData *)((char *)&CPlayer::s_tblLimMasteryCum + 704 * CPlayerDB::GetRaceCode(&v33->m_Param));
            v13 = CPlayerDB::GetPtrBaseClass(&v33->m_Param);
            v17 = CRecordData::GetRecord(&v28[v13->m_nClass], v27);
          }
        }
        else
        {
          v29 = ((int (__fastcall *)(_QWORD))v33->vfptr->GetLevel)(v33) - 1;
          v30 = (CRecordData *)((char *)&CPlayer::s_tblLimMasteryCum + 704 * CPlayerDB::GetRaceCode(&v33->m_Param));
          v14 = CPlayerDB::GetPtrCurClass(&v33->m_Param);
          v16 = CRecordData::GetRecord(&v30[v14->m_nClass], v29);
        }
        if ( v16 )
        {
          v19 = 0;
          v20 = 0;
          v31 = (unsigned __int8)v34;
          switch ( v34 )
          {
            case 0:
              v19 = *(_DWORD *)&v16[1].m_strCode[4 * (unsigned __int8)v35];
              if ( v17 )
                v20 = *(_DWORD *)&v17[1].m_strCode[4 * (unsigned __int8)v35];
              break;
            case 1:
              v19 = *(_DWORD *)&v16[1].m_strCode[12];
              if ( v17 )
                v20 = *(_DWORD *)&v17[1].m_strCode[12];
              break;
            case 2:
              v19 = *(_DWORD *)&v16[1].m_strCode[16];
              if ( v17 )
                v20 = *(_DWORD *)&v17[1].m_strCode[16];
              break;
            case 3:
              v19 = *(_DWORD *)&v16[1].m_strCode[4 * (unsigned __int8)v35 + 32];
              if ( v17 )
                v20 = *(_DWORD *)&v17[1].m_strCode[4 * (unsigned __int8)v35 + 32];
              break;
            case 4:
              v19 = *(&v16[2].m_dwIndex + (unsigned __int8)v35);
              if ( v17 )
                v20 = *(&v17[2].m_dwIndex + (unsigned __int8)v35);
              break;
            case 5:
              v19 = *(_DWORD *)&v16[1].m_strCode[4 * (unsigned __int8)v35 + 20];
              if ( v17 )
                v20 = *(_DWORD *)&v17[1].m_strCode[4 * (unsigned __int8)v35 + 20];
              break;
            case 6:
              v19 = *(_DWORD *)&v16[1].m_strCode[8];
              if ( v17 )
                v20 = *(_DWORD *)&v17[1].m_strCode[8];
              break;
            default:
              break;
          }
          if ( (signed int)v19 <= (signed int)v20 )
            v32 = v20;
          else
            v32 = v19;
          result = v32;
        }
        else
        {
          CLogFile::Write(&stru_1799C8E78, "_check_mastery_cum_lim.. pCurFld : NULL");
          result = 0i64;
        }
      }
      else
      {
        v9 = ((int (__fastcall *)(_QWORD))v33->vfptr->GetLevel)(v33);
        CLogFile::Write(&stru_1799C8E78, "_check_mastery_cum_lim.. level : %d, max level : %d", (unsigned int)v9, v18);
        result = 0i64;
      }
    }
    else
    {
      v7 = CPlayerDB::GetRaceCode(&v33->m_Param);
      CLogFile::Write(&stru_1799C8E78, "_check_mastery_cum_lim.. racecode : %d", (unsigned int)v7);
      result = 0i64;
    }
  }
  else
  {
    v21 = CPlayerDB::GetPtrBaseClass(&v33->m_Param);
    v5 = CPlayerDB::GetPtrCurClass(&v33->m_Param);
    CLogFile::Write(
      &stru_1799C8E78,
      "_check_mastery_cum_lim.. cur_class : %d, base_class : %d",
      v5->m_nClass,
      v21->m_nClass);
    result = 0i64;
  }
  return result;
}
