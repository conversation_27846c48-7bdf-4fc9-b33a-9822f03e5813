#pragma once
#ifndef _LOADMONBLKCMAPDATAAEAA_NPEADPEAU_MAP_FLDZ_140181850_H
#define _LOADMONBLKCMAPDATAAEAA_NPEADPEAU_MAP_FLDZ_140181850_H

// Auto-generated header for _LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_140181850.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
CMapData::_LoadMonBlk(CMapData *this, char *pszMapCode, _map_fld *pMapFld)
;

#endif // _LOADMONBLKCMAPDATAAEAA_NPEADPEAU_MAP_FLDZ_140181850_H
