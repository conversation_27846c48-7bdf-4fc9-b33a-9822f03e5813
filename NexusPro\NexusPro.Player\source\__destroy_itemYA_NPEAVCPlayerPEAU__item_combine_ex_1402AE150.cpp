/*
 * Function: ?__destroy_item@@YA_NPEAVCPlayer@@PEAU__item@_combine_ex_item_result_zocl@@PEAU_db_con@_STORAGE_LIST@@PEAU_list@_combine_ex_item_request_clzo@@H@Z
 * Address: 0x1402AE150
 */

char __fastcall __destroy_item(CPlayer *pMaster, _combine_ex_item_result_zocl::__item *pItem, _STORAGE_LIST::_db_con *pSvItem, _combine_ex_item_request_clzo::_list *pMeterial, int nSocketIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v8; // [sp+0h] [bp-38h]@1
  _base_fld *v9; // [sp+20h] [bp-18h]@6
  _combine_ex_item_result_zocl::__item *v10; // [sp+48h] [bp+10h]@1
  _STORAGE_LIST::_db_con *v11; // [sp+50h] [bp+18h]@1
  _combine_ex_item_request_clzo::_list *v12; // [sp+58h] [bp+20h]@1

  v12 = pMeterial;
  v11 = pSvItem;
  v10 = pItem;
  v5 = &v8;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pSvItem )
  {
    v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + pSvItem->m_byTableCode, pSvItem->m_wItemIndex);
    v10->Key.byTableCode = v11->m_byTableCode;
    v10->Key.wItemIndex = v11->m_wItemIndex;
    v10->Key.byRewardIndex = 0;
    v10->dwDur = v12->byAmount;
    v10->dwUpt = v11->m_dwLv;
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
