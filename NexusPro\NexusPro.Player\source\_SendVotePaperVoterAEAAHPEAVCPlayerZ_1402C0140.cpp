/*
 * Function: ?_SendVotePaper@Voter@@AEAAHPEAVCPlayer@@@Z
 * Address: 0x1402C0140
 */

signed __int64 __fastcall Voter::_SendVotePaper(Voter *this, CPlayer *pOne)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@6
  PatriarchElectProcessor *v5; // rax@8
  CandidateMgr *v6; // rax@26
  unsigned __int16 v7; // ax@28
  __int64 v8; // [sp+0h] [bp-68h]@1
  _pt_trans_votepaper_zocl *v9; // [sp+30h] [bp-38h]@28
  char pbyType; // [sp+44h] [bp-24h]@28
  char v11; // [sp+45h] [bp-23h]@28
  unsigned int n; // [sp+54h] [bp-14h]@8
  unsigned int dwAvatorSerial; // [sp+58h] [bp-10h]@26
  int v14; // [sp+5Ch] [bp-Ch]@26
  Voter *v15; // [sp+70h] [bp+8h]@1
  CPlayer *v16; // [sp+78h] [bp+10h]@1

  v16 = pOne;
  v15 = this;
  v2 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( pOne && pOne->m_bOper )
  {
    if ( v15->_kCandidateInfo[CPlayerDB::GetRaceCode(&pOne->m_Param)].byCnt >= 2 )
    {
      if ( v16->m_pUserDB->m_AvatorData.dbAvator.m_bOverlapVote )
      {
        result = 10i64;
      }
      else if ( v16->m_bOper && v16->m_pUserDB->m_AvatorData.dbSupplement.dwAccumPlayTime >= unk_1799CA30C )
      {
        if ( v16->m_bOper && v16->m_pUserDB->m_AvatorData.dbSupplement.VoteEnable )
        {
          if ( v16->m_bOper && v16->m_pUserDB->m_AvatorData.dbSupplement.wScanerCnt >= (signed int)unk_1799CA310 )
          {
            if ( v16->m_bOper && v16->m_pUserDB->m_AvatorData.dbAvator.m_byLevel >= (signed int)unk_1799CA312 )
            {
              if ( v16->m_bOper && v16->m_pUserDB->m_AvatorData.dbAvator.m_byLastClassGrade >= 2 )
              {
                dwAvatorSerial = CPlayerDB::GetCharSerial(&v16->m_Param);
                v14 = CPlayerDB::GetRaceCode(&v16->m_Param);
                v6 = CandidateMgr::Instance();
                if ( CandidateMgr::IsRegistedAvator_2(v6, v14, dwAvatorSerial) )
                {
                  result = 11i64;
                }
                else
                {
                  v9 = &v15->_kCandidateInfo[CPlayerDB::GetRaceCode(&v16->m_Param)];
                  pbyType = 56;
                  v11 = 5;
                  v7 = _pt_trans_votepaper_zocl::size(v9);
                  CNetProcess::LoadSendMsg(unk_1414F2088, v16->m_ObjID.m_wIndex, &pbyType, &v9->byCnt, v7);
                  result = 0i64;
                }
              }
              else
              {
                result = 11i64;
              }
            }
            else
            {
              result = 11i64;
            }
          }
          else
          {
            result = 11i64;
          }
        }
        else
        {
          result = 11i64;
        }
      }
      else
      {
        result = 11i64;
      }
    }
    else
    {
      n = v16->m_id.wIndex;
      v5 = PatriarchElectProcessor::Instance();
      PatriarchElectProcessor::SendMsg_ResultCode(v5, n, 8);
      result = 9i64;
    }
  }
  else
  {
    result = 0i64;
  }
  return result;
}
