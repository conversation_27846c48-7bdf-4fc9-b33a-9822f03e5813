#pragma once
#ifndef _STD_FIND_STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT___1403B26F0_H
#define _STD_FIND_STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT___1403B26F0_H

// Auto-generated header for _std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B26F0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_2(__int64 a1, __int64 a2)
;

#endif // _STD_FIND_STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT___1403B26F0_H
