/*
 * Function: ??$_Unchecked_uninitialized_move@PEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@stdext@@YAPEAU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@PEAU12@00AEAV?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@Z
 * Address: 0x1405A4A20
 */

int __fastcall stdext::_Unchecked_uninitialized_move<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  char v5; // [sp+30h] [bp-18h]@1
  char v6; // [sp+31h] [bp-17h]@1
  __int64 v7; // [sp+50h] [bp+8h]@1
  __int64 v8; // [sp+58h] [bp+10h]@1
  __int64 v9; // [sp+60h] [bp+18h]@1
  __int64 v10; // [sp+68h] [bp+20h]@1

  v10 = a4;
  v9 = a3;
  v8 = a2;
  v7 = a1;
  memset(&v5, 0, sizeof(v5));
  v6 = std::_Move_cat<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *>(&v9);
  return std::_Uninit_move<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer> *,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>,std::_Undefined_move_tag>(
           v7,
           v8,
           v9,
           v10);
}
