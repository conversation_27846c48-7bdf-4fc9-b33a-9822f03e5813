#pragma once
#ifndef _CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMITRIGHTINFO__1__1403A39E0_H
#define _CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMITRIGHTINFO__1__1403A39E0_H

// Auto-generated header for _CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__1__1403A39E0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMITRIGHTINFO__1__1403A39E0_H
