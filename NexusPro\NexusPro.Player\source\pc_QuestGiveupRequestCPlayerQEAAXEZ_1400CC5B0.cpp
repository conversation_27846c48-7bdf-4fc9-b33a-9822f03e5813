/*
 * Function: ?pc_QuestGiveupRequest@CPlayer@@QEAAXE@Z
 * Address: 0x1400CC5B0
 */

void __fastcall CPlayer::pc_QuestGiveupRequest(CPlayer *this, char byQuestDBSlot)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-38h]@1
  char v5; // [sp+20h] [bp-18h]@4
  CPlayer *v6; // [sp+40h] [bp+8h]@1
  char v7; // [sp+48h] [bp+10h]@1

  v7 = byQuestDBSlot;
  v6 = this;
  v2 = &v4;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v5 = byQuestDBSlot;
  if ( CQuestMgr::CanGiveupQuest(&v6->m_QuestMgr, byQuestDBSlot) )
  {
    CQuestMgr::DeleteQuestData(&v6->m_QuestMgr, v7);
    CUserDB::Update_QuestDelete(v6->m_pUserDB, v7);
  }
  else
  {
    v5 = -1;
  }
  CPlayer::SendMsg_QuestGiveUpResult(v6, v5);
}
