#pragma once
#ifndef _STDVECTOR_CMOVEMAPLIMITRIGHT_____PTR64_STDALLOCAT_1403AF0C0_H
#define _STDVECTOR_CMOVEMAPLIMITRIGHT_____PTR64_STDALLOCAT_1403AF0C0_H

// Auto-generated header for _stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF0C0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_1(__int64 a1, __int64 a2)
;

#endif // _STDVECTOR_CMOVEMAPLIMITRIGHT_____PTR64_STDALLOCAT_1403AF0C0_H
