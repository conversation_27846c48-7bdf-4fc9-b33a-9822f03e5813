#pragma once
#ifndef _STDVECTOR_CMOVEMAPLIMITRIGHTINFO_STDALLOCATOR_CMO_1403B1280_H
#define _STDVECTOR_CMOVEMAPLIMITRIGHTINFO_STDALLOCATOR_CMO_1403B1280_H

// Auto-generated header for _stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B1280.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _STDVECTOR_CMOVEMAPLIMITRIGHTINFO_STDALLOCATOR_CMO_1403B1280_H
