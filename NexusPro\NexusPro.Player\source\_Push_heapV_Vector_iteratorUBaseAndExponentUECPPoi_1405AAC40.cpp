/*
 * Function: ??$_Push_heap@V?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@_JU?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@0@_J1U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@Z
 * Address: 0x1405AAC40
 */

void __fastcall std::_Push_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>(__int64 a1, __int64 a2, __int64 a3, CryptoPP::ECPPoint *a4)
{
  __int64 v4; // rax@3
  CryptoPP::ECPPoint *v5; // rax@9
  CryptoPP::ECPPoint *v6; // rax@9
  CryptoPP::ECPPoint *v7; // rax@10
  __int64 i; // [sp+20h] [bp-D8h]@1
  char v9; // [sp+28h] [bp-D0h]@6
  char v10; // [sp+30h] [bp-C8h]@3
  char v11; // [sp+48h] [bp-B0h]@9
  char v12; // [sp+60h] [bp-98h]@9
  char v13; // [sp+78h] [bp-80h]@10
  int v14; // [sp+90h] [bp-68h]@1
  __int64 v15; // [sp+98h] [bp-60h]@1
  __int64 v16; // [sp+A0h] [bp-58h]@3
  __int64 v17; // [sp+A8h] [bp-50h]@3
  int v18; // [sp+B0h] [bp-48h]@4
  __int64 v19; // [sp+B8h] [bp-40h]@9
  __int64 v20; // [sp+C0h] [bp-38h]@9
  CryptoPP::ECPPoint *v21; // [sp+C8h] [bp-30h]@9
  __int64 v22; // [sp+D0h] [bp-28h]@9
  __int64 v23; // [sp+D8h] [bp-20h]@9
  __int64 v24; // [sp+E0h] [bp-18h]@10
  __int64 v25; // [sp+E8h] [bp-10h]@10
  __int64 v26; // [sp+100h] [bp+8h]@1
  __int64 v27; // [sp+108h] [bp+10h]@1
  __int64 v28; // [sp+110h] [bp+18h]@1
  CryptoPP::ECPPoint *v29; // [sp+118h] [bp+20h]@1

  v29 = a4;
  v28 = a3;
  v27 = a2;
  v26 = a1;
  v15 = -2i64;
  v14 = 0;
  for ( i = (a2 - 1) / 2; ; i = (i - 1) / 2 )
  {
    v18 = v28 < v27
       && (v16 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
                   v26,
                   (__int64)&v10,
                   i),
           v17 = v16,
           v14 |= 1u,
           LODWORD(v4) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*(),
           (unsigned __int8)CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator<(v4, v29));
    v9 = v18;
    if ( v14 & 1 )
    {
      v14 &= 0xFFFFFFFE;
      std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    }
    if ( !v9 )
      break;
    v19 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
            v26,
            (__int64)&v12,
            i);
    v20 = v19;
    LODWORD(v5) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
    v21 = v5;
    v22 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
            v26,
            (__int64)&v11,
            v27);
    v23 = v22;
    LODWORD(v6) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
    CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator=(v6, v21);
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
    v27 = i;
  }
  v24 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator+(
          v26,
          (__int64)&v13,
          v27);
  v25 = v24;
  LODWORD(v7) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::operator*();
  CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::operator=(v7, v29);
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>();
  CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>((__int64)v29);
}
