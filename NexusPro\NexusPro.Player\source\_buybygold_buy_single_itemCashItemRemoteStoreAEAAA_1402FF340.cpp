/*
 * Function: ?_buybygold_buy_single_item@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@PEAVCPlayer@@PEAU_request_csi_buy_clzo@@PEAU__item@4@PEAU_param_cashitem_dblog@@PEA_NAEAU_result_csi_buy_zocl@@@Z
 * Address: 0x1402FF340
 */

__int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, _request_csi_buy_clzo::__item *pSrc, _param_cashitem_dblog *pSheet, bool *bCouponUse, _result_csi_buy_zocl *Send)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  __int64 result; // rax@5
  __int64 v10; // [sp+0h] [bp-138h]@1
  _CashShop_fld *v11; // [sp+68h] [bp-D0h]@4
  CS_RCODE v12; // [sp+74h] [bp-C4h]@4
  unsigned int dwPush; // [sp+84h] [bp-B4h]@6
  unsigned int v14; // [sp+A4h] [bp-94h]@6
  bool v15; // [sp+C4h] [bp-74h]@6
  char v16; // [sp+C5h] [bp-73h]@6
  _STORAGE_LIST::_db_con GiveItem; // [sp+E8h] [bp-50h]@8
  CashItemRemoteStore *v18; // [sp+140h] [bp+8h]@1
  CPlayer *pOnea; // [sp+148h] [bp+10h]@1
  _request_csi_buy_clzo *pRecva; // [sp+150h] [bp+18h]@1
  _request_csi_buy_clzo::__item *pSrca; // [sp+158h] [bp+20h]@1

  pSrca = pSrc;
  pRecva = pRecv;
  pOnea = pOne;
  v18 = this;
  v7 = &v10;
  for ( i = 76i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v11 = 0i64;
  v12 = CashItemRemoteStore::_buybygold_buy_single_item_check_item(v18, pOne, pSrc, pSheet, &v11);
  if ( v12 )
  {
    result = (unsigned int)v12;
  }
  else
  {
    dwPush = 0;
    v14 = 0;
    v15 = 0;
    memset(&v16, 0, 2ui64);
    v12 = CashItemRemoteStore::_buybygold_buy_single_item_proc_price(
            v18,
            pOnea,
            pRecva,
            pSrca,
            pSheet,
            v11,
            &v15,
            Send,
            &dwPush,
            &v14);
    if ( v12 )
    {
      result = (unsigned int)v12;
    }
    else
    {
      _STORAGE_LIST::_db_con::_db_con(&GiveItem);
      v12 = CashItemRemoteStore::_buybygold_buy_single_item_give_item(v18, pOnea, pSrca, &GiveItem);
      if ( v12 )
      {
        CPlayer::AddGold(pOnea, dwPush, 1);
        result = (unsigned int)v12;
      }
      else
      {
        CashItemRemoteStore::_buybygold_buy_single_item_additional_process(v18, pOnea, pSrca, pSheet, Send);
        if ( v12 )
        {
          result = (unsigned int)v12;
        }
        else
        {
          CashItemRemoteStore::_buybygold_buy_single_item_proc_complete(
            v18,
            pOnea,
            pSrca,
            pSheet,
            v11,
            &GiveItem,
            Send,
            dwPush,
            v14,
            &v15,
            bCouponUse);
          result = 0i64;
        }
      }
    }
  }
  return result;
}
