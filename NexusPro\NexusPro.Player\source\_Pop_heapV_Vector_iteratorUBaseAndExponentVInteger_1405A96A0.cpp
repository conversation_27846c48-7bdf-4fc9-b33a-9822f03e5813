/*
 * Function: ??$_Pop_heap@V?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@_JU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@0@00U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEA_J@Z
 * Address: 0x1405A96A0
 */

void __fastcall std::_Pop_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(__int64 a1, __int64 a2, __int64 a3, const struct CryptoPP::Integer *a4)
{
  __int64 v4; // rax@1
  __int64 v5; // rax@1
  __int64 v6; // rax@1
  char v7; // [sp+20h] [bp-B8h]@1
  char *v8; // [sp+70h] [bp-68h]@1
  char v9; // [sp+78h] [bp-60h]@1
  char *v10; // [sp+90h] [bp-48h]@1
  __int64 v11; // [sp+98h] [bp-40h]@1
  __int64 v12; // [sp+A0h] [bp-38h]@1
  CryptoPP::Integer *v13; // [sp+A8h] [bp-30h]@1
  const struct CryptoPP::Integer *v14; // [sp+B0h] [bp-28h]@1
  __int64 v15; // [sp+B8h] [bp-20h]@1
  __int64 v16; // [sp+C0h] [bp-18h]@1
  const struct CryptoPP::Integer *v17; // [sp+F8h] [bp+20h]@1

  v17 = a4;
  v11 = -2i64;
  LODWORD(v4) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator*();
  v12 = v4;
  LODWORD(v5) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator*();
  CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::operator=(v5);
  v8 = &v7;
  v10 = &v9;
  v13 = CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(
          (CryptoPP::Integer *)&v7,
          v17);
  v14 = v13;
  LODWORD(v6) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator-();
  v15 = v6;
  v16 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>((__int64)v10);
  std::_Adjust_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(
    v16,
    0i64,
    v15,
    v14);
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::~BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>((__int64)v17);
}
