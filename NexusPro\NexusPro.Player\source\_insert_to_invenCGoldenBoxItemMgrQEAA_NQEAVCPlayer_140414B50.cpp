/*
 * Function: ?_insert_to_inven@CGoldenBoxItemMgr@@QEAA_NQEAVCPlayer@@EG@Z
 * Address: 0x140414B50
 */

char __fastcall CGoldenBoxItemMgr::_insert_to_inven(CGoldenBoxItemMgr *this, CPlayer *const pOne, char byTableCode, unsigned __int16 wItemIndex)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char v6; // al@7
  char *v7; // rax@8
  char result; // al@8
  unsigned int v9; // eax@9
  __int64 v10; // [sp+0h] [bp-A8h]@1
  _base_fld *v11; // [sp+30h] [bp-78h]@4
  _STORAGE_LIST::_db_con v12; // [sp+48h] [bp-60h]@4
  int v13; // [sp+84h] [bp-24h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+88h] [bp-20h]@7
  char *szCharName; // [sp+90h] [bp-18h]@9
  CGoldenBoxItemMgr *v16; // [sp+B0h] [bp+8h]@1
  CPlayer *v17; // [sp+B8h] [bp+10h]@1
  char v18; // [sp+C0h] [bp+18h]@1
  unsigned __int16 v19; // [sp+C8h] [bp+20h]@1

  v19 = wItemIndex;
  v18 = byTableCode;
  v17 = pOne;
  v16 = this;
  v4 = &v10;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)byTableCode, wItemIndex);
  _STORAGE_LIST::_db_con::_db_con(&v12);
  v13 = 0;
  v12.m_byTableCode = v18;
  v12.m_wItemIndex = v19;
  if ( IsOverLapItem((unsigned __int8)v18) )
    v12.m_dwDur = 1i64;
  else
    v12.m_dwDur = GetItemDurPoint((unsigned __int8)v18, v19);
  v6 = GetDefItemUpgSocketNum((unsigned __int8)v18, v19);
  v12.m_dwLv = (unsigned __int8)v6;
  v12.m_dwLv = GetBitAfterSetLimSocket(v6);
  v12.m_wSerial = CPlayerDB::GetNewItemSerial(&v17->m_Param);
  pItem = CPlayer::Emb_AddStorage(v17, 0, (_STORAGE_LIST::_storage_con *)&v12.m_bLoad, 0, 1);
  if ( pItem )
  {
    CPlayer::SendMsg_RewardAddItem(v17, pItem, 10);
    szCharName = CPlayerDB::GetCharNameA(&v17->m_Param);
    v9 = CPlayerDB::GetCharSerial(&v17->m_Param);
    CPlayer::SendMsg_Notify_Get_Golden_Box(v17, 2, v9, szCharName, pItem, 0);
    result = 1;
  }
  else
  {
    v7 = GetItemKorName((unsigned __int8)v18, v19);
    CLogFile::Write(&v16->_kLogger, "Failed _insert_to_inven() >> %s", v7);
    result = 0;
  }
  return result;
}
