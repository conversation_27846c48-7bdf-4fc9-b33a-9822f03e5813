/*
 * Function: ??$_Umove@PEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@IEAAPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@PEAU23@00@Z
 * Address: 0x14059E540
 */

int __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Umove<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *>(__int64 a1, __int64 a2, __int64 a3, __int64 a4)
{
  return stdext::_Unchecked_uninitialized_move<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer> *,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>(
           a2,
           a3,
           a4,
           a1 + 8);
}
