/*
 * Function: ?_<PERSON>len@?$vector@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@KAXXZ
 * Address: 0x140593340
 */

void __noreturn std::vector<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_<PERSON>len()
{
  std::length_error v0; // [sp+20h] [bp-98h]@1
  std::basic_string<char,std::char_traits<char>,std::allocator<char> > _Message; // [sp+68h] [bp-50h]@1
  unsigned __int8 v2; // [sp+98h] [bp-20h]@1
  __int64 v3; // [sp+A0h] [bp-18h]@1

  v3 = -2i64;
  memset(&v2, 0, sizeof(v2));
  std::basic_string<char,std::char_traits<char>,std::allocator<char>>::basic_string<char,std::char_traits<char>,std::allocator<char>>(
    &_Message,
    "vector<T> too long",
    v2);
  std::length_error::length_error(&v0, &_Message);
  CxxThrowException_0((__int64)&v0, (__int64)&TI3_AVlength_error_std__);
}
