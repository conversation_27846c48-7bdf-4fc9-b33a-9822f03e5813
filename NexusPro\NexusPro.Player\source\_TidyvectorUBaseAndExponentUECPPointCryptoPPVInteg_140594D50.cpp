/*
 * Function: ?_Tidy@?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@IEAAXXZ
 * Address: 0x140594D50
 */

__int64 __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Tidy(__int64 a1)
{
  __int64 result; // rax@3
  __int64 v2; // [sp+30h] [bp+8h]@1

  v2 = a1;
  if ( *(_QWORD *)(a1 + 16) )
  {
    std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Destroy(
      a1,
      *(_QWORD *)(a1 + 16),
      *(_QWORD *)(a1 + 24));
    std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::deallocate(
      v2 + 8,
      *(_QWORD *)(v2 + 16),
      (*(_QWORD *)(v2 + 32) - *(_QWORD *)(v2 + 16)) >> 7);
  }
  *(_QWORD *)(v2 + 16) = 0i64;
  *(_QWORD *)(v2 + 24) = 0i64;
  result = v2;
  *(_QWORD *)(v2 + 32) = 0i64;
  return result;
}
