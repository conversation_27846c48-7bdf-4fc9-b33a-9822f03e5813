#pragma once
#ifndef _INSERT_NVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVC_1403B0D30_H
#define _INSERT_NVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVC_1403B0D30_H

// Auto-generated header for _Insert_nvectorVCMoveMapLimitRightInfoVallocatorVC_1403B0D30.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

#endif // _INSERT_NVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVC_1403B0D30_H
