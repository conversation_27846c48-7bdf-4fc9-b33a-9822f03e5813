<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{FD568235-38AF-456B-AA90-DA62EAC2A0B6}</ProjectGuid>
    <RootNamespace>NexusProWorld</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusPro.World</ProjectName>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  
  <ImportGroup Label="Shared">
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <PropertyGroup Label="UserMacros" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Debug\</OutDir>
    <IntDir>$(SolutionDir)obj\Debug\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Release\</OutDir>
    <IntDir>$(SolutionDir)obj\Release\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemGroup>
    <ClCompile Include="source\0allocatorPEAVCMoveMapLimitInfostdQEAAAEBV01Z_1403A2C20.cpp" />
    <ClCompile Include="source\0allocatorPEAVCMoveMapLimitInfostdQEAAXZ_1403A25A0.cpp" />
    <ClCompile Include="source\0allocatorPEAVCMoveMapLimitRightstdQEAAAEBV01Z_1403B05F0.cpp" />
    <ClCompile Include="source\0allocatorPEAVCMoveMapLimitRightstdQEAAXZ_1403AF5D0.cpp" />
    <ClCompile Include="source\0allocatorVCMoveMapLimitRightInfostdQEAAAEBV01Z_1403A2E90.cpp" />
    <ClCompile Include="source\0allocatorVCMoveMapLimitRightInfostdQEAAXZ_1403A2800.cpp" />
    <ClCompile Include="source\0BossSchedule_MapQEAAXZ_14041B720.cpp" />
    <ClCompile Include="source\0CCircleZoneQEAAXZ_14012D660.cpp" />
    <ClCompile Include="source\0CMapDataQEAAXZ_140180050.cpp" />
    <ClCompile Include="source\0CMapDisplayQEAAXZ_14019D560.cpp" />
    <ClCompile Include="source\0CMapExtendQEAAPEAPEAVCSurfaceZ_1401A1500.cpp" />
    <ClCompile Include="source\0CMapExtendQEAAXZ_1401A1410.cpp" />
    <ClCompile Include="source\0CMapOperationQEAAXZ_140195E20.cpp" />
    <ClCompile Include="source\0CMapTabQEAAXZ_14002E480.cpp" />
    <ClCompile Include="source\0CMonsterAggroMgrQEAAXZ_14015DB60.cpp" />
    <ClCompile Include="source\0CMonsterAIQEAAXZ_14014F950.cpp" />
    <ClCompile Include="source\0CMonsterEventRespawnQEAAXZ_1402A5D40.cpp" />
    <ClCompile Include="source\0CMonsterEventSetQEAAXZ_1402A7920.cpp" />
    <ClCompile Include="source\0CMonsterHierarchyQEAAXZ_14014B660.cpp" />
    <ClCompile Include="source\0CMonsterQEAAXZ_1401414E0.cpp" />
    <ClCompile Include="source\0CMoveMapLimitInfoListQEAAXZ_1403A1DC0.cpp" />
    <ClCompile Include="source\0CMoveMapLimitInfoPortalQEAAIHZ_1403A3EE0.cpp" />
    <ClCompile Include="source\0CMoveMapLimitInfoQEAAIHZ_1403A3D00.cpp" />
    <ClCompile Include="source\0CMoveMapLimitManagerQEAAXZ_1403A1D10.cpp" />
    <ClCompile Include="source\0CMoveMapLimitRightInfoListQEAAXZ_1403A1E10.cpp" />
    <ClCompile Include="source\0CMoveMapLimitRightInfoQEAAAEBV0Z_1403AF990.cpp" />
    <ClCompile Include="source\0CMoveMapLimitRightInfoQEAAXZ_1403AE760.cpp" />
    <ClCompile Include="source\0CMoveMapLimitRightPortalQEAAHZ_1403AC7F0.cpp" />
    <ClCompile Include="source\0CMoveMapLimitRightQEAAHZ_1403AE460.cpp" />
    <ClCompile Include="source\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CAC0.cpp" />
    <ClCompile Include="source\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191BB0.cpp" />
    <ClCompile Include="source\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191C10.cpp" />
    <ClCompile Include="source\0CRFMonsterAIMgrQEAAXZ_14014C1E0.cpp" />
    <ClCompile Include="source\0CWorldScheduleQEAAXZ_1403F34F0.cpp" />
    <ClCompile Include="source\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C980.cpp" />
    <ClCompile Include="source\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401910C0.cpp" />
    <ClCompile Include="source\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_140191110.cpp" />
    <ClCompile Include="source\0mapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_140193BE0.cpp" />
    <ClCompile Include="source\0MonsterSetInfoDataQEAAXZ_140161980.cpp" />
    <ClCompile Include="source\0MonsterStateDataQEAAXZ_14014B700.cpp" />
    <ClCompile Include="source\0pairViterator_TreeV_Tmap_traitsVbasic_stringDUcha_140191D10.cpp" />
    <ClCompile Include="source\0ptr2userVCMonsterlua_tinkerQEAAPEAVCMonsterZ_14040B880.cpp" />
    <ClCompile Include="source\0UpairCBVbasic_stringDUchar_traitsDstdVallocatorD2_1401942F0.cpp" />
    <ClCompile Include="source\0UpairCBVbasic_stringDUchar_traitsDstdVallocatorD2_140194310.cpp" />
    <ClCompile Include="source\0vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A2060.cpp" />
    <ClCompile Include="source\0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AE7B0.cpp" />
    <ClCompile Include="source\0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AFB00.cpp" />
    <ClCompile Include="source\0vectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A2120.cpp" />
    <ClCompile Include="source\0_Deque_mapIVallocatorIstdstdIEAAVallocatorI1Z_14065ABD0.cpp" />
    <ClCompile Include="source\0_Deque_map_KVallocator_KstdstdIEAAVallocator_K1Z_140659590.cpp" />
    <ClCompile Include="source\0_event_respawnQEAAXZ_1402A7740.cpp" />
    <ClCompile Include="source\0_mapCGameStatisticsQEAAXZ_140232BE0.cpp" />
    <ClCompile Include="source\0_map_fldQEAAXZ_140198FF0.cpp" />
    <ClCompile Include="source\0_monster_create_setdataQEAAXZ_14014C340.cpp" />
    <ClCompile Include="source\0_monster_set_event_setQEAAXZ_1402A9E80.cpp" />
    <ClCompile Include="source\0_monster_sp_groupQEAAXZ_1401618D0.cpp" />
    <ClCompile Include="source\0_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_tr_140192330.cpp" />
    <ClCompile Include="source\0_notice_move_limit_map_msg_zoclQEAAXZ_1403A7100.cpp" />
    <ClCompile Include="source\0_NPCQuestIndexTempDataQEAAXZ_140073EA0.cpp" />
    <ClCompile Include="source\0_npc_create_setdataQEAAXZ_140199140.cpp" />
    <ClCompile Include="source\0_npc_quest_list_result_zoclQEAAXZ_1400EFD20.cpp" />
    <ClCompile Include="source\0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A7400.cpp" />
    <ClCompile Include="source\0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A9D80.cpp" />
    <ClCompile Include="source\0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AE650.cpp" />
    <ClCompile Include="source\0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AFA30.cpp" />
    <ClCompile Include="source\0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAA_1403B0C60.cpp" />
    <ClCompile Include="source\0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAX_1403B18D0.cpp" />
    <ClCompile Include="source\0_reged_char_result_zoneQEAAXZ_14011F680.cpp" />
    <ClCompile Include="source\0_respawn_monster_act_dh_mission_mgrQEAAXZ_14026ECB0.cpp" />
    <ClCompile Include="source\0_state_event_respawnQEAAXZ_1402A77C0.cpp" />
    <ClCompile Include="source\0_state_monster_set_event_setQEAAXZ_1402A9ED0.cpp" />
    <ClCompile Include="source\0_target_monster_aggro_inform_zoclQEAAXZ_1400F0010.cpp" />
    <ClCompile Include="source\0_target_monster_contsf_allinform_zoclQEAAXZ_140073FF0.cpp" />
    <ClCompile Include="source\0_Tmap_traitsVbasic_stringDUchar_traitsDstdValloca_140194290.cpp" />
    <ClCompile Include="source\0_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDstdV_140193C40.cpp" />
    <ClCompile Include="source\0_Tree_nodV_Tmap_traitsVbasic_stringDUchar_traitsD_1401941B0.cpp" />
    <ClCompile Include="source\0_Tree_ptrV_Tmap_traitsVbasic_stringDUchar_traitsD_140194110.cpp" />
    <ClCompile Include="source\0_Tree_valV_Tmap_traitsVbasic_stringDUchar_traitsD_140193DB0.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7350.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A9C80.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AE5E0.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AF860.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B0BF0.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1800.cpp" />
    <ClCompile Include="source\0_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A8CF0.cpp" />
    <ClCompile Include="source\0_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A8DB0.cpp" />
    <ClCompile Include="source\0_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AF800.cpp" />
    <ClCompile Include="source\0_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AF930.cpp" />
    <ClCompile Include="source\0_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403B0B90.cpp" />
    <ClCompile Include="source\0_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403B15E0.cpp" />
    <ClCompile Include="source\0_Vector_valPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A2530.cpp" />
    <ClCompile Include="source\0_Vector_valPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AF560.cpp" />
    <ClCompile Include="source\0_Vector_valVCMoveMapLimitRightInfoVallocatorVCMov_1403A2790.cpp" />
    <ClCompile Include="source\0__add_monsterQEAAXZ_14027A3F0.cpp" />
    <ClCompile Include="source\0__change_monsterQEAAXZ_14027A4D0.cpp" />
    <ClCompile Include="source\0__monster_groupQEAAXZ_140279FB0.cpp" />
    <ClCompile Include="source\0__respawn_monsterQEAAXZ_14027A450.cpp" />
    <ClCompile Include="source\1BossSchedule_MapQEAAXZ_14041B430.cpp" />
    <ClCompile Include="source\1CCircleZoneUEAAXZ_14012D6F0.cpp" />
    <ClCompile Include="source\1CMapDataUEAAXZ_140180590.cpp" />
    <ClCompile Include="source\1CMapDisplayUEAAXZ_14019D9C0.cpp" />
    <ClCompile Include="source\1CMapExtendQEAAXZ_1401A1600.cpp" />
    <ClCompile Include="source\1CMapOperationUEAAXZ_1401960C0.cpp" />
    <ClCompile Include="source\1CMapTabUEAAXZ_14002E530.cpp" />
    <ClCompile Include="source\1CMonsterAggroMgrQEAAXZ_14015DC90.cpp" />
    <ClCompile Include="source\1CMonsterAIUEAAXZ_14014FA30.cpp" />
    <ClCompile Include="source\1CMonsterEventRespawnUEAAXZ_1402A5DC0.cpp" />
    <ClCompile Include="source\1CMonsterEventSetUEAAXZ_1402A79C0.cpp" />
    <ClCompile Include="source\1CMonsterHierarchyUEAAXZ_140157350.cpp" />
    <ClCompile Include="source\1CMonsterUEAAXZ_140141780.cpp" />
    <ClCompile Include="source\1CMoveMapLimitInfoListQEAAXZ_1403A1FA0.cpp" />
    <ClCompile Include="source\1CMoveMapLimitInfoPortalQEAAXZ_1403A3FD0.cpp" />
    <ClCompile Include="source\1CMoveMapLimitInfoQEAAXZ_1403A3D60.cpp" />
    <ClCompile Include="source\1CMoveMapLimitManagerQEAAXZ_1403A1F10.cpp" />
    <ClCompile Include="source\1CMoveMapLimitRightInfoListQEAAXZ_1403A1E60.cpp" />
    <ClCompile Include="source\1CMoveMapLimitRightInfoQEAAXZ_1403A3990.cpp" />
    <ClCompile Include="source\1CMoveMapLimitRightQEAAXZ_1403AE740.cpp" />
    <ClCompile Include="source\1const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CA80.cpp" />
    <ClCompile Include="source\1CRFMonsterAIMgrQEAAXZ_140203400.cpp" />
    <ClCompile Include="source\1CWorldScheduleQEAAXZ_1403F4630.cpp" />
    <ClCompile Include="source\1iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C8A0.cpp" />
    <ClCompile Include="source\1mapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_1401943D0.cpp" />
    <ClCompile Include="source\1pairViterator_TreeV_Tmap_traitsVbasic_stringDUcha_14018EE50.cpp" />
    <ClCompile Include="source\1ptr2userVCMonsterlua_tinkerUEAAXZ_14040BAC0.cpp" />
    <ClCompile Include="source\1vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A20E0.cpp" />
    <ClCompile Include="source\1vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403A3A20.cpp" />
    <ClCompile Include="source\1vectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A21A0.cpp" />
    <ClCompile Include="source\1_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_tr_140195DD0.cpp" />
    <ClCompile Include="source\1_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A7460.cpp" />
    <ClCompile Include="source\1_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AE5A0.cpp" />
    <ClCompile Include="source\1_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAX_1403AFAC0.cpp" />
    <ClCompile Include="source\1_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDstdV_140194410.cpp" />
    <ClCompile Include="source\1_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A73C0.cpp" />
    <ClCompile Include="source\1_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AE560.cpp" />
    <ClCompile Include="source\1_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403AFA80.cpp" />
    <ClCompile Include="source\1_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A7310.cpp" />
    <ClCompile Include="source\1_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AE520.cpp" />
    <ClCompile Include="source\1_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403AF9F0.cpp" />
    <ClCompile Include="source\1__change_monsterQEAAXZ_140272E60.cpp" />
    <ClCompile Include="source\4CMoveMapLimitRightInfoQEAAAEBV0AEBV0Z_1403AD590.cpp" />
    <ClCompile Include="source\4const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CB30.cpp" />
    <ClCompile Include="source\4iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C9E0.cpp" />
    <ClCompile Include="source\4_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403B2570.cpp" />
    <ClCompile Include="source\4_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B2500.cpp" />
    <ClCompile Include="source\4_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B24A0.cpp" />
    <ClCompile Include="source\8const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018ECA0.cpp" />
    <ClCompile Include="source\8_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A8C80.cpp" />
    <ClCompile Include="source\8_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AF8C0.cpp" />
    <ClCompile Include="source\8_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1860.cpp" />
    <ClCompile Include="source\9const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140195B00.cpp" />
    <ClCompile Include="source\9MonsterStateDataQEBA_NAEBV0Z_14014C3E0.cpp" />
    <ClCompile Include="source\9_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E70.cpp" />
    <ClCompile Include="source\9_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDF0.cpp" />
    <ClCompile Include="source\9_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1640.cpp" />
    <ClCompile Include="source\AddMonsterCDarkHoleChannelQEAAXXZ_140268F40.cpp" />
    <ClCompile Include="source\allocateallocatorPEAVCMoveMapLimitInfostdQEAAPEAPE_1403A2C90.cpp" />
    <ClCompile Include="source\allocateallocatorPEAVCMoveMapLimitRightstdQEAAPEAP_1403B0610.cpp" />
    <ClCompile Include="source\allocateallocatorU_Node_Tree_nodV_Tmap_traitsVbasi_140191E90.cpp" />
    <ClCompile Include="source\allocateallocatorVCMoveMapLimitRightInfostdQEAAPEA_1403A2F00.cpp" />
    <ClCompile Include="source\AmapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_14018C400.cpp" />
    <ClCompile Include="source\assignvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A7980.cpp" />
    <ClCompile Include="source\assignvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403AED40.cpp" />
    <ClCompile Include="source\AutoRecoverCMonsterQEAAXXZ_140147440.cpp" />
    <ClCompile Include="source\AvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A7950.cpp" />
    <ClCompile Include="source\AvectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AEAE0.cpp" />
    <ClCompile Include="source\AvectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A2260.cpp" />
    <ClCompile Include="source\beginvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A77F0.cpp" />
    <ClCompile Include="source\beginvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AE830.cpp" />
    <ClCompile Include="source\beginvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AE8A0.cpp" />
    <ClCompile Include="source\beginvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403B0660.cpp" />
    <ClCompile Include="source\begin_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_14018FF20.cpp" />
    <ClCompile Include="source\CalcScheduleCursorCWorldScheduleQEAAHHHZ_1403F4180.cpp" />
    <ClCompile Include="source\CalcStartNPCQuestCntCQuestMgrSA_NQEAKZ_14028B6E0.cpp" />
    <ClCompile Include="source\capacityvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403AA660.cpp" />
    <ClCompile Include="source\capacityvectorPEAVCMoveMapLimitRightVallocatorPEAV_1403AEE60.cpp" />
    <ClCompile Include="source\capacityvectorVCMoveMapLimitRightInfoVallocatorVCM_1403B16B0.cpp" />
    <ClCompile Include="source\ChangeApparitionCMonsterQEAAX_NKZ_1401434E0.cpp" />
    <ClCompile Include="source\ChangeLayerCMapDisplayQEAA_NGZ_14019F1C0.cpp" />
    <ClCompile Include="source\ChangeMapCMapDisplayQEAA_NPEAVCMapDataZ_14019F130.cpp" />
    <ClCompile Include="source\ChangeMonsterApparitionCDarkHoleChannelQEAAXHZ_140269700.cpp" />
    <ClCompile Include="source\ChangeMonsterCDarkHoleChannelQEAAXXZ_140268570.cpp" />
    <ClCompile Include="source\ChangeSchCursorCWorldScheduleQEAAXPEAU_WorldSchedu_1403F3EE0.cpp" />
    <ClCompile Include="source\ChangeTargetPosDfAIMgrSAXPEAVCMonsterPEAMZ_140150E20.cpp" />
    <ClCompile Include="source\ChatMapRequestCNetworkEXAEAA_NHPEADZ_1401C58C0.cpp" />
    <ClCompile Include="source\CheckAlienationDfAIMgrSAHPEAVCMonsterZ_140151260.cpp" />
    <ClCompile Include="source\CheckAutoRecoverHPCMonsterQEAAXXZ_140143370.cpp" />
    <ClCompile Include="source\CheckCenterPosDummyCMapDataQEAA_NPEAU_dummy_positi_1401855D0.cpp" />
    <ClCompile Include="source\CheckDelayDestroyCMonsterQEAA_NXZ_1401432F0.cpp" />
    <ClCompile Include="source\CheckEmotionBadDfAIMgrSAHPEAVCMonsterPEAVCMonsterA_140151090.cpp" />
    <ClCompile Include="source\CheckEmotionPresentationCMonsterQEAAXXZ_140147FD0.cpp" />
    <ClCompile Include="source\CheckEventSetRespawnCMonsterEventSetQEAAXXZ_1402A8A90.cpp" />
    <ClCompile Include="source\CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152E40.cpp" />
    <ClCompile Include="source\CheckMapPortalLinkCMapOperationAEAAXXZ_140197D40.cpp" />
    <ClCompile Include="source\CheckMonArea_N_ChangeStateDfAIMgrSAHPEAVCMonsterAI_140153350.cpp" />
    <ClCompile Include="source\CheckMonsterRotateCMonsterQEAAXXZ_140147B80.cpp" />
    <ClCompile Include="source\CheckMonsterStateDataCMonsterQEAA_NXZ_1401435C0.cpp" />
    <ClCompile Include="source\CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQuestI_140287ED0.cpp" />
    <ClCompile Include="source\CheckRespawnEventCMonsterEventRespawnQEAAXXZ_1402A6FE0.cpp" />
    <ClCompile Include="source\CheckRespawnMonsterCDarkHoleChannelQEAAXXZ_14026A0D0.cpp" />
    <ClCompile Include="source\CheckRespawnProcessCMonsterQEAA_NXZ_140143070.cpp" />
    <ClCompile Include="source\CheckSchCWorldScheduleQEAAXXZ_1403F3AA0.cpp" />
    <ClCompile Include="source\CheckSPFDelayTimeDfAIMgrSAHPEAVCMonsterAIHKZ_140151850.cpp" />
    <ClCompile Include="source\CheckSPFDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152150.cpp" />
    <ClCompile Include="source\check_dummyAutominePersonalMgrQEAA_NPEAVCMapDataEP_1402DEBA0.cpp" />
    <ClCompile Include="source\ChildKindCountCMonsterHierarchyQEAAEXZ_14014C320.cpp" />
    <ClCompile Include="source\class_addVCMonsterlua_tinkerYAXPEAUlua_StatePEBDZ_140407BA0.cpp" />
    <ClCompile Include="source\class_defVCMonsterP81EAAPEAVCLuaSignalReActorXZlua_140407D20.cpp" />
    <ClCompile Include="source\CleanUpCMoveMapLimitInfoListAEAAXXZ_1403A6290.cpp" />
    <ClCompile Include="source\CleanUpCMoveMapLimitRightInfoAEAAXXZ_1403AD710.cpp" />
    <ClCompile Include="source\CleanUpCMoveMapLimitRightUEAAXXZ_1403AE4A0.cpp" />
    <ClCompile Include="source\ClearBossSchedule_MapQEAAXXZ_14041B4D0.cpp" />
    <ClCompile Include="source\ClearEmotionPresentationCMonsterQEAAXXZ_140147F80.cpp" />
    <ClCompile Include="source\clearvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A79E0.cpp" />
    <ClCompile Include="source\clearvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AEC20.cpp" />
    <ClCompile Include="source\clear_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_1401958F0.cpp" />
    <ClCompile Include="source\Command_ChildMonDestroyCMonsterQEAAXKZ_140143550.cpp" />
    <ClCompile Include="source\constructallocatorPEAU_Node_Tree_nodV_Tmap_traitsV_140194230.cpp" />
    <ClCompile Include="source\constructallocatorPEAVCMoveMapLimitRightstdQEAAXPE_1403B38B0.cpp" />
    <ClCompile Include="source\constructallocatorVCMoveMapLimitRightInfostdQEAAXP_1403B3190.cpp" />
    <ClCompile Include="source\ConvertLocalCMapDataQEAA_NPEAU_dummy_positionZ_140185100.cpp" />
    <ClCompile Include="source\ConvertToMapCMapExtendQEAAXPEAVCSizeZ_1401A1DA0.cpp" />
    <ClCompile Include="source\CreateAICMonsterQEAAHHZ_1401423D0.cpp" />
    <ClCompile Include="source\CreateCCircleZoneQEAA_NPEAVCMapDataEZ_14012DA60.cpp" />
    <ClCompile Include="source\CreateCGravityStoneRegenerQEAA_NPEAVCMapDataZ_14012E950.cpp" />
    <ClCompile Include="source\CreateCMerchantQEAA_NPEAU_npc_create_setdataZ_140139140.cpp" />
    <ClCompile Include="source\CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_140141C50.cpp" />
    <ClCompile Include="source\CreateCMoveMapLimitInfoSAPEAV1IHZ_1403A3DB0.cpp" />
    <ClCompile Include="source\CreateCMoveMapLimitRightSAPEAV1HZ_1403AC5E0.cpp" />
    <ClCompile Include="source\CreateFileMappingA_0_140676E22.cpp" />
    <ClCompile Include="source\CreateMonsterCDarkHoleChannelQEAAXXZ_1402682C0.cpp" />
    <ClCompile Include="source\CreateObjectCMapTabSAPEAVCObjectXZ_14002E350.cpp" />
    <ClCompile Include="source\CreateObjSurfaceCMapDisplayAEAAJXZ_14019F690.cpp" />
    <ClCompile Include="source\CreatePaletteFromBitmapCDisplayQEAAJPEAPEAUIDirect_1404346D0.cpp" />
    <ClCompile Include="source\CreateRepMonsterYAPEAVCMonsterPEAVCMapDataGPEAMPEA_140148D90.cpp" />
    <ClCompile Include="source\CreateRespawnMonsterYAPEAVCMonsterPEAVCMapDataGHPE_140148B90.cpp" />
    <ClCompile Include="source\CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurfac_140433B00.cpp" />
    <ClCompile Include="source\D3DUtil_GetCubeMapViewMatrixYAAUD3DXMATRIXKZ_14052B5D0.cpp" />
    <ClCompile Include="source\DataCheckCWorldScheduleQEAA_NXZ_1403F3FF0.cpp" />
    <ClCompile Include="source\Dconst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191210.cpp" />
    <ClCompile Include="source\deallocateallocatorPEAVCMoveMapLimitInfostdQEAAXPE_1403A2C40.cpp" />
    <ClCompile Include="source\deallocateallocatorPEAVCMoveMapLimitRightstdQEAAXP_1403A3BB0.cpp" />
    <ClCompile Include="source\deallocateallocatorU_Node_Tree_nodV_Tmap_traitsVba_140191A00.cpp" />
    <ClCompile Include="source\deallocateallocatorVCMoveMapLimitRightInfostdQEAAX_1403A2EB0.cpp" />
    <ClCompile Include="source\defP6APEAVCMonsterPEAD0MMMZlua_tinkerYAXPEAUlua_St_140407ED0.cpp" />
    <ClCompile Include="source\DestoryCRFMonsterAIMgrSAXXZ_140203300.cpp" />
    <ClCompile Include="source\destroyallocatorPEAU_Node_Tree_nodV_Tmap_traitsVba_1401940C0.cpp" />
    <ClCompile Include="source\destroyallocatorPEAVCMoveMapLimitRightstdQEAAXPEAP_1403B3910.cpp" />
    <ClCompile Include="source\destroyallocatorU_Node_Tree_nodV_Tmap_traitsVbasic_140195C50.cpp" />
    <ClCompile Include="source\destroyallocatorVCMoveMapLimitRightInfostdQEAAXPEA_1403A3880.cpp" />
    <ClCompile Include="source\DestroyCCircleZoneQEAAXXZ_14012DB70.cpp" />
    <ClCompile Include="source\DestroyCMonsterQEAA_NEPEAVCGameObjectZ_1401424F0.cpp" />
    <ClCompile Include="source\DestroyCMoveMapLimitManagerSAXXZ_1403A16B0.cpp" />
    <ClCompile Include="source\destroyerVCMonsterlua_tinkerYAHPEAUlua_StateZ_140408950.cpp" />
    <ClCompile Include="source\Diterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018EC60.cpp" />
    <ClCompile Include="source\DoDataExchangeCMapTabMEAAXPEAVCDataExchangeZ_14002E5C0.cpp" />
    <ClCompile Include="source\DrawBitmapCSurfaceQEAAJPEADKKZ_140435410.cpp" />
    <ClCompile Include="source\DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140434FE0.cpp" />
    <ClCompile Include="source\DrawCollisionLineCMapDisplayAEAAXXZ_1401A02A0.cpp" />
    <ClCompile Include="source\DrawDisplayCMapDisplayQEAAXXZ_14019F260.cpp" />
    <ClCompile Include="source\DrawDummyCMapDisplayAEAAXXZ_1401A0010.cpp" />
    <ClCompile Include="source\DrawLightMapGroupYAXPEAVCVertexBufferPEAU_BSP_MAT__1404F1590.cpp" />
    <ClCompile Include="source\DrawMapCMapDisplayAEAAXXZ_14019F450.cpp" />
    <ClCompile Include="source\DrawMapEntitiesRenderCBspQEAAXXZ_1404FBBB0.cpp" />
    <ClCompile Include="source\DrawObjectCMapDisplayAEAAXXZ_14019F4E0.cpp" />
    <ClCompile Include="source\DrawRectCMapExtendQEAAXXZ_1401A1C40.cpp" />
    <ClCompile Include="source\DrawSelectMonsterLookAtPosCMapDisplayAEAAJPEAVCMon_14019FA70.cpp" />
    <ClCompile Include="source\DrawTextACMapDisplayAEAAXXZ_1401A0340.cpp" />
    <ClCompile Include="source\D_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E20.cpp" />
    <ClCompile Include="source\D_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDA0.cpp" />
    <ClCompile Include="source\D_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B31F0.cpp" />
    <ClCompile Include="source\Econst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191C70.cpp" />
    <ClCompile Include="source\Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_140191170.cpp" />
    <ClCompile Include="source\Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401959B0.cpp" />
    <ClCompile Include="source\emptyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AEA70.cpp" />
    <ClCompile Include="source\EndScreenPointCMapExtendQEAAHPEAVCSizeZ_1401A1940.cpp" />
    <ClCompile Include="source\endvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_1403A7860.cpp" />
    <ClCompile Include="source\endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_1403AE910.cpp" />
    <ClCompile Include="source\endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_1403AE980.cpp" />
    <ClCompile Include="source\endvectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_1403B06D0.cpp" />
    <ClCompile Include="source\end_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14018D6A0.cpp" />
    <ClCompile Include="source\EnterMapCMapDataQEAAXPEAVCGameObjectKZ_140184D30.cpp" />
    <ClCompile Include="source\EnterWorldRequestCNetworkEXAEAA_NHPEAU_MSG_HEADERP_1401D0D30.cpp" />
    <ClCompile Include="source\EnterWorldResultCNetworkEXAEAA_NKPEADZ_1401C06D0.cpp" />
    <ClCompile Include="source\erasevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A8310.cpp" />
    <ClCompile Include="source\erasevectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AF210.cpp" />
    <ClCompile Include="source\erasevectorVCMoveMapLimitRightInfoVallocatorVCMove_1403B0820.cpp" />
    <ClCompile Include="source\erase_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194660.cpp" />
    <ClCompile Include="source\erase_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194B20.cpp" />
    <ClCompile Include="source\ExitMapCMapDataQEAAXPEAVCGameObjectKZ_140184EC0.cpp" />
    <ClCompile Include="source\ExitWorldRequestCNetworkEXAEAA_NHPEADZ_1401C9D20.cpp" />
    <ClCompile Include="source\E_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E40.cpp" />
    <ClCompile Include="source\E_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDC0.cpp" />
    <ClCompile Include="source\E_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B3230.cpp" />
    <ClCompile Include="source\Fconst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191CC0.cpp" />
    <ClCompile Include="source\fillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCMov_1403AAFE0.cpp" />
    <ClCompile Include="source\fillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVCMo_1403B1FD0.cpp" />
    <ClCompile Include="source\fillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveMap_1403B22D0.cpp" />
    <ClCompile Include="source\FindEmptyNPCYAPEAVCMerchantPEAV1HZ_140139CD0.cpp" />
    <ClCompile Include="source\findV_Vector_iteratorPEAVCMoveMapLimitRightValloca_1403B1920.cpp" />
    <ClCompile Include="source\Fiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401911C0.cpp" />
    <ClCompile Include="source\FrameMoveMapEntitiesCBspQEAAXXZ_1404FB7D0.cpp" />
    <ClCompile Include="source\GeEmotionImpStdTimeCMonsterQEAAMXZ_1401558D0.cpp" />
    <ClCompile Include="source\GetAggroResetTimeCMonsterQEAAKXZ_1401617D0.cpp" />
    <ClCompile Include="source\GetAggroShortTimeCMonsterQEAAKXZ_140161790.cpp" />
    <ClCompile Include="source\GetAngleCMonsterHelperSAMQEAM0Z_1401597A0.cpp" />
    <ClCompile Include="source\GetBonusInAreaAggroCMonsterQEAAMXZ_140161890.cpp" />
    <ClCompile Include="source\GetBspInfoCMapDataQEAAPEAU_bsp_infoXZ_1401843C0.cpp" />
    <ClCompile Include="source\GetChildCMonsterHierarchyQEAAPEAVCMonsterHHZ_140157DA0.cpp" />
    <ClCompile Include="source\GetChildCountCMonsterHierarchyQEAAKHZ_140161530.cpp" />
    <ClCompile Include="source\GetCMoveMapLimitInfoListAEAAPEAVCMoveMapLimitInfoH_1403A6020.cpp" />
    <ClCompile Include="source\GetCMoveMapLimitRightInfoListQEAAPEAVCMoveMapLimit_1403A1FE0.cpp" />
    <ClCompile Include="source\GetColorCCircleZoneQEAAEXZ_140034B20.cpp" />
    <ClCompile Include="source\GetCommandMapCCmdTargetMEBAPEBUAFX_OLECMDMAPXZ_0_1404DBBC8.cpp" />
    <ClCompile Include="source\GetCritical_Exception_RateCMonsterUEAAHXZ_14014BB70.cpp" />
    <ClCompile Include="source\GetDefFacingCMonsterUEAAMHZ_14014BA70.cpp" />
    <ClCompile Include="source\GetDefGapCMonsterUEAAMHZ_14014BA20.cpp" />
    <ClCompile Include="source\GetDirectionCMonsterHelperSAXAEAY02M00MZ_1401598E0.cpp" />
    <ClCompile Include="source\GetDispatchMapCCmdTargetMEBAPEBUAFX_DISPMAPXZ_0_1404DBBCE.cpp" />
    <ClCompile Include="source\GetDummyPostionCMapDataQEAAPEAU_dummy_positionPEAD_140186440.cpp" />
    <ClCompile Include="source\GetEmotionStateCMonsterQEAAEXZ_140143810.cpp" />
    <ClCompile Include="source\GetEmptyEventSetCMonsterEventSetQEAAPEAU_event_set_1402A8FA0.cpp" />
    <ClCompile Include="source\GetEvenSetLootingCMonsterEventSetQEAAPEAU_event_se_1402A90B0.cpp" />
    <ClCompile Include="source\GetEventSinkMapCCmdTargetMEBAPEBUAFX_EVENTSINKMAPX_1404DBBE0.cpp" />
    <ClCompile Include="source\GetExtendSizeCMapExtendQEAAPEAVCSizeXZ_14002D0B0.cpp" />
    <ClCompile Include="source\GetFireTolCMonsterUEAAHXZ_140145620.cpp" />
    <ClCompile Include="source\GetHelpMeCaseCMonsterQEAAHXZ_1401554F0.cpp" />
    <ClCompile Include="source\GetHPCMonsterUEAAHXZ_1401461E0.cpp" />
    <ClCompile Include="source\GetInterfaceMapCCmdTargetMEBAPEBUAFX_INTERFACEMAPX_1404DBF52.cpp" />
    <ClCompile Include="source\GetInterfaceMapCWndMEBAPEBUAFX_INTERFACEMAPXZ_0_1404DBBDA.cpp" />
    <ClCompile Include="source\GetInxCMoveMapLimitInfoQEAAIXZ_1403A74A0.cpp" />
    <ClCompile Include="source\GetLightMapColorYAKQEAMHZ_140502530.cpp" />
    <ClCompile Include="source\GetLightMapSurfaceYAPEAXHZ_1405025F0.cpp" />
    <ClCompile Include="source\GetLightMapTexSizeYAKXZ_140500900.cpp" />
    <ClCompile Include="source\GetLightMapUVFromPointCBspAEAAXQEAMH0Z_1404E3370.cpp" />
    <ClCompile Include="source\GetLinkPortalCMapDataQEAAPEAU_portal_dummyPEADZ_1401846E0.cpp" />
    <ClCompile Include="source\GetLocalFromWorldCExtDummyQEAAHPEAY02MKQEAMZ_1404DF180.cpp" />
    <ClCompile Include="source\GetLostMonsterTargetDistanceMonsterSetInfoDataQEAA_140155670.cpp" />
    <ClCompile Include="source\GetMapCMapOperationQEAAHPEAVCMapDataZ_1401979B0.cpp" />
    <ClCompile Include="source\GetMapCMapOperationQEAAPEAVCMapDataHZ_140197970.cpp" />
    <ClCompile Include="source\GetMapCMapOperationQEAAPEAVCMapDataPEADZ_140197A30.cpp" />
    <ClCompile Include="source\GetMapCodeCMapDataQEAAEXZ_1400C2CD0.cpp" />
    <ClCompile Include="source\GetMapCurDirectCTransportShipQEAAPEAVCMapDataXZ_140264DD0.cpp" />
    <ClCompile Include="source\GetMapDataCGuildRoomInfoQEAAPEAVCMapDataXZ_1402EB250.cpp" />
    <ClCompile Include="source\GetMapPosCGuildRoomInfoQEAA_NPEAMZ_1402E6690.cpp" />
    <ClCompile Include="source\GetMaxDMGSFContCountCMonsterQEAAHXZ_140147050.cpp" />
    <ClCompile Include="source\GetMaxHPCMonsterUEAAHXZ_1401462A0.cpp" />
    <ClCompile Include="source\GetMaxToleranceProbMaxMonsterSetInfoDataQEAAMHZ_14014BDF0.cpp" />
    <ClCompile Include="source\GetMipMapSkipSizeYAHPEAU_DDSURFACEDESC2KKKZ_1404FFE70.cpp" />
    <ClCompile Include="source\GetMob_AsistTypeCMonsterQEAAHXZ_140161590.cpp" />
    <ClCompile Include="source\GetMob_SubRaceCMonsterQEAAHXZ_140161570.cpp" />
    <ClCompile Include="source\GetMonStateInfoCMonsterQEAAGXZ_140143720.cpp" />
    <ClCompile Include="source\GetMonsterDropRateMonsterSetInfoDataQEAAKHZ_14015D510.cpp" />
    <ClCompile Include="source\GetMonsterForcePowerRateMonsterSetInfoDataQEAAMXZ_140161640.cpp" />
    <ClCompile Include="source\GetMonsterGradeCMonsterQEAAHXZ_14014BFD0.cpp" />
    <ClCompile Include="source\GetMonsterNumInCurMissionAreaCDarkHoleChannelQEAAH_14026B170.cpp" />
    <ClCompile Include="source\GetMonsterSetCMonsterEventSetQEAAPEAU_monster_set__1402A9030.cpp" />
    <ClCompile Include="source\GetMoveSpeedCMonsterQEAAMXZ_140142D80.cpp" />
    <ClCompile Include="source\GetMoveTypeCMonsterQEAAEXZ_1401437B0.cpp" />
    <ClCompile Include="source\GetMyDMGSFContCountCMonsterQEAAHXZ_1401470B0.cpp" />
    <ClCompile Include="source\GetNewMonSerialCMonsterSAKXZ_14014BFF0.cpp" />
    <ClCompile Include="source\GetObjNameCMonsterUEAAPEADXZ_140142700.cpp" />
    <ClCompile Include="source\GetObjRaceCMonsterUEAAHXZ_14014BB60.cpp" />
    <ClCompile Include="source\GetOffensiveTypeCMonsterQEAAHXZ_1401554D0.cpp" />
    <ClCompile Include="source\GetParentCMonsterHierarchyQEAAPEAVCMonsterXZ_14014C300.cpp" />
    <ClCompile Include="source\GetPathFinderCMonsterAIQEAAPEAVCPathMgrXZ_1401555B0.cpp" />
    <ClCompile Include="source\GetPortalCMapDataQEAAPEAU_portal_dummyHZ_1401846A0.cpp" />
    <ClCompile Include="source\GetPortalCMapDataQEAAPEAU_portal_dummyPEADZ_140184550.cpp" />
    <ClCompile Include="source\GetPortalInxCCircleZoneQEAAHXZ_140034B00.cpp" />
    <ClCompile Include="source\GetPortalInxCMapDataQEAAHPEADZ_140184600.cpp" />
    <ClCompile Include="source\GetPosStartMapCMapOperationQEAAPEAVCMapDataE_NPEAM_140197B90.cpp" />
    <ClCompile Include="source\GetRaceTownCMapDataQEAAEPEAMEZ_1401862D0.cpp" />
    <ClCompile Include="source\GetRandPosInDummyCMapDataQEAA_NPEAU_dummy_position_1401857A0.cpp" />
    <ClCompile Include="source\GetRandPosInRangeCMapDataQEAA_NPEAMH0Z_140185B10.cpp" />
    <ClCompile Include="source\GetRandPosVirtualDumCMapDataQEAA_NPEAMH0Z_140185C70.cpp" />
    <ClCompile Include="source\GetRandPosVirtualDumExcludeStdRangeCMapDataQEAA_NP_140185EE0.cpp" />
    <ClCompile Include="source\GetRectInRadiusCMapDataQEAAXPEAU_pnt_rectHHZ_1401843E0.cpp" />
    <ClCompile Include="source\GetResDummySectorCMapDataQEAAHHPEAMZ_140184950.cpp" />
    <ClCompile Include="source\GetRuntimeClassCMapTabUEBAPEAUCRuntimeClassXZ_14002E460.cpp" />
    <ClCompile Include="source\GetSecInfoCMapDataQEAAPEAU_sec_infoXZ_1401843A0.cpp" />
    <ClCompile Include="source\GetSectorIndexCMapDataQEAAHPEAMZ_140184790.cpp" />
    <ClCompile Include="source\GetSectorListObjCMapDataQEAAPEAVCObjectListGKZ_140184890.cpp" />
    <ClCompile Include="source\GetSectorListTowerCMapDataQEAAPEAVCObjectListGKZ_140184910.cpp" />
    <ClCompile Include="source\GetSectorNumByLayerIndexCMapDataQEAAHGZ_1401866D0.cpp" />
    <ClCompile Include="source\GetSettlementMapDataCMapOperationQEAAPEAVCMapDataH_1402D7960.cpp" />
    <ClCompile Include="source\GetSignalReActorCMonsterQEAAPEAVCLuaSignalReActorX_140406790.cpp" />
    <ClCompile Include="source\GetSoilTolCMonsterUEAAHXZ_140145820.cpp" />
    <ClCompile Include="source\GetStartMapCMapOperationQEAAPEAVCMapDataEZ_140197AE0.cpp" />
    <ClCompile Include="source\GetStateChunkMonsterStateDataQEBAGXZ_14014C450.cpp" />
    <ClCompile Include="source\GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateTBL_14014C040.cpp" />
    <ClCompile Include="source\GetThisClassCMapTabSAPEAUCRuntimeClassXZ_14002E440.cpp" />
    <ClCompile Include="source\GetTypeCMoveMapLimitInfoQEAAHXZ_1403A6F50.cpp" />
    <ClCompile Include="source\GetTypeCMoveMapLimitRightQEBAHXZ_1403AE6B0.cpp" />
    <ClCompile Include="source\GetViewAngleCapCMonsterQEAA_NHAEAHZ_140146C20.cpp" />
    <ClCompile Include="source\GetVisualAngleCMonsterQEAAMXZ_14014CAA0.cpp" />
    <ClCompile Include="source\GetVisualFieldCMonsterQEAAMXZ_14014BF80.cpp" />
    <ClCompile Include="source\GetWaterTolCMonsterUEAAHXZ_140145720.cpp" />
    <ClCompile Include="source\GetWidthCMonsterUEAAMXZ_140146610.cpp" />
    <ClCompile Include="source\GetWindTolCMonsterUEAAHXZ_140145920.cpp" />
    <ClCompile Include="source\GetWorldFromLocalCExtDummyQEAAHPEAY02MKQEAMZ_1404DF130.cpp" />
    <ClCompile Include="source\GetYAngleByteCMonsterQEAAEXZ_14014CB50.cpp" />
    <ClCompile Include="source\GetYAngleCMonsterQEAAMXZ_1401438D0.cpp" />
    <ClCompile Include="source\gm_MapChangeCMainThreadQEAAXPEAVCMapDataZ_1401F79D0.cpp" />
    <ClCompile Include="source\gm_UpdateMapCMainThreadQEAAXXZ_1401F7E60.cpp" />
    <ClCompile Include="source\GoalCCircleZoneQEAAEPEAVCMapDataPEAMZ_14012DBE0.cpp" />
    <ClCompile Include="source\G_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B15B0.cpp" />
    <ClCompile Include="source\G_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B0B40.cpp" />
    <ClCompile Include="source\HearMapSoundCBspQEAAXXZ_1404FD620.cpp" />
    <ClCompile Include="source\HierarcyHelpCastCMonsterHelperSAXPEAVCMonsterZ_14015A480.cpp" />
    <ClCompile Include="source\H_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B09F0.cpp" />
    <ClCompile Include="source\InsertNpcQuestHistoryCQuestMgrQEAAEPEADENZ_14028B320.cpp" />
    <ClCompile Include="source\insertvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A91B0.cpp" />
    <ClCompile Include="source\insertvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AEEE0.cpp" />
    <ClCompile Include="source\insertvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403B0740.cpp" />
    <ClCompile Include="source\insert_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018D710.cpp" />
    <ClCompile Include="source\insert_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FFC0.cpp" />
    <ClCompile Include="source\InstanceCMoveMapLimitManagerSAPEAV1XZ_1403A15F0.cpp" />
    <ClCompile Include="source\InstanceCRFMonsterAIMgrSAPEAV1XZ_14014C100.cpp" />
    <ClCompile Include="source\invokelua2objectPEAVCMonsterlua_tinkerSAPEAVCMonst_14040B1C0.cpp" />
    <ClCompile Include="source\invokeobject2luaPEAVCMonsterlua_tinkerSAXPEAUlua_S_14040B350.cpp" />
    <ClCompile Include="source\invokePEAVCLuaSignalReActormem_functorVCMonsterXXX_140409390.cpp" />
    <ClCompile Include="source\invokePEAVCMonsterfunctorPEADPEADMMMlua_tinkerSAHP_140409470.cpp" />
    <ClCompile Include="source\invokeptr2luaVCMonsterlua_tinkerSAXPEAUlua_StatePE_14040B670.cpp" />
    <ClCompile Include="source\invokeuser2typeP6APEAVCMonsterPEAD0MMMZlua_tinkerS_14040A830.cpp" />
    <ClCompile Include="source\invokeuser2typeP8CMonsterEAAPEAVCLuaSignalReActorX_14040A770.cpp" />
    <ClCompile Include="source\invokevoid2ptrA6APEAVCMonsterPEAD0MMMZlua_tinkerSA_14040AEE0.cpp" />
    <ClCompile Include="source\invokevoid2ptrVCMonsterlua_tinkerSAPEAVCMonsterPEA_14040B790.cpp" />
    <ClCompile Include="source\invokevoid2typeP6APEAVCMonsterPEAD0MMMZlua_tinkerS_14040ACB0.cpp" />
    <ClCompile Include="source\invokevoid2typeP8CMonsterEAAPEAVCLuaSignalReActorX_14040AC30.cpp" />
    <ClCompile Include="source\invokevoid2typePEAVCMonsterlua_tinkerSAPEAVCMonste_14040B410.cpp" />
    <ClCompile Include="source\invokevoid2valP8CMonsterEAAPEAVCLuaSignalReActorXZ_14040AEB0.cpp" />
    <ClCompile Include="source\IsBossMonsterCMonsterQEAA_NXZ_14007D4E0.cpp" />
    <ClCompile Include="source\IsCompleteNpcQuestCQuestMgrQEAA_NPEADHZ_14028A850.cpp" />
    <ClCompile Include="source\IsEqualLimitCMoveMapLimitInfoQEAA_NHHKZ_1403A3E70.cpp" />
    <ClCompile Include="source\IsExistStdMapIDCMapOperationQEAA_NHZ_140120AB0.cpp" />
    <ClCompile Include="source\IsHaveRightCMoveMapLimitRightInfoQEAA_NHZ_1403ACBA0.cpp" />
    <ClCompile Include="source\IsHaveRightCMoveMapLimitRightPortalUEAA_NXZ_1403AC6A0.cpp" />
    <ClCompile Include="source\IsHaveRightCMoveMapLimitRightUEAA_NXZ_1403AE4F0.cpp" />
    <ClCompile Include="source\IsINIFileChangedCMonsterEventSetQEAA_NPEBDU_FILETI_1402A9150.cpp" />
    <ClCompile Include="source\IsInRegionCMapOperationQEAA_NPEADPEAVCGameObjectZ_140197C50.cpp" />
    <ClCompile Include="source\IsInSectorCMonsterHelperSAHQEAM00MMPEAMZ_140158160.cpp" />
    <ClCompile Include="source\IsMapInCMapDataQEAA_NPEAMZ_140184B40.cpp" />
    <ClCompile Include="source\IsMovableCMonsterQEAA_NXZ_140142E20.cpp" />
    <ClCompile Include="source\IsNearPositionCCircleZoneAEAA_NPEBMZ_14012DE20.cpp" />
    <ClCompile Include="source\IsPossibleRepeatNpcQuestCQuestMgrQEAA_NPEADHZ_14028A900.cpp" />
    <ClCompile Include="source\IsProcLinkNpcQuestCQuestMgrQEAA_NPEADHZ_14028AA30.cpp" />
    <ClCompile Include="source\IsProcNpcQuestCQuestMgrQEAA_NPEADZ_14028AB00.cpp" />
    <ClCompile Include="source\IsRoateMonsterCMonsterQEAA_NXZ_1401555D0.cpp" />
    <ClCompile Include="source\IsRotateBlockMonsterSetInfoDataQEAA_NPEAU_mon_bloc_14015D110.cpp" />
    <ClCompile Include="source\IsSame_target_monster_contsf_allinform_zoclSA_NAEA_1400F01A0.cpp" />
    <ClCompile Include="source\j_0allocatorPEAVCMoveMapLimitInfostdQEAAAEBV01Z_1400131B5.cpp" />
    <ClCompile Include="source\j_0allocatorPEAVCMoveMapLimitInfostdQEAAXZ_1400106D1.cpp" />
    <ClCompile Include="source\j_0allocatorPEAVCMoveMapLimitRightstdQEAAAEBV01Z_14000B320.cpp" />
    <ClCompile Include="source\j_0allocatorPEAVCMoveMapLimitRightstdQEAAXZ_140012C42.cpp" />
    <ClCompile Include="source\j_0allocatorVCMoveMapLimitRightInfostdQEAAAEBV01Z_14000F187.cpp" />
    <ClCompile Include="source\j_0allocatorVCMoveMapLimitRightInfostdQEAAXZ_140006D89.cpp" />
    <ClCompile Include="source\j_0BossSchedule_MapQEAAXZ_140011770.cpp" />
    <ClCompile Include="source\j_0CCircleZoneQEAAXZ_140009C3C.cpp" />
    <ClCompile Include="source\j_0CMapDataQEAAXZ_14000DF1C.cpp" />
    <ClCompile Include="source\j_0CMapDisplayQEAAXZ_140013E08.cpp" />
    <ClCompile Include="source\j_0CMapExtendQEAAPEAPEAVCSurfaceZ_140004011.cpp" />
    <ClCompile Include="source\j_0CMapExtendQEAAXZ_14000D55D.cpp" />
    <ClCompile Include="source\j_0CMapOperationQEAAXZ_14000E390.cpp" />
    <ClCompile Include="source\j_0CMapTabQEAAXZ_14000C991.cpp" />
    <ClCompile Include="source\j_0CMonsterAggroMgrQEAAXZ_14000FBD2.cpp" />
    <ClCompile Include="source\j_0CMonsterAIQEAAXZ_140007CD4.cpp" />
    <ClCompile Include="source\j_0CMonsterEventRespawnQEAAXZ_140002CED.cpp" />
    <ClCompile Include="source\j_0CMonsterEventSetQEAAXZ_140007CCF.cpp" />
    <ClCompile Include="source\j_0CMonsterHierarchyQEAAXZ_140007577.cpp" />
    <ClCompile Include="source\j_0CMonsterQEAAXZ_140011DA6.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitInfoListQEAAXZ_1400082B0.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitInfoPortalQEAAIHZ_14000BDF2.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitInfoQEAAIHZ_14000210D.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitManagerQEAAXZ_1400114EB.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitRightInfoListQEAAXZ_14000E12E.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitRightInfoQEAAAEBV0Z_14000EDEA.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitRightInfoQEAAXZ_140013C5A.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitRightPortalQEAAHZ_140002F4A.cpp" />
    <ClCompile Include="source\j_0CMoveMapLimitRightQEAAHZ_140003959.cpp" />
    <ClCompile Include="source\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140005BFF.cpp" />
    <ClCompile Include="source\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_1400078D8.cpp" />
    <ClCompile Include="source\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_14000A52E.cpp" />
    <ClCompile Include="source\j_0CRFMonsterAIMgrQEAAXZ_14000A5FB.cpp" />
    <ClCompile Include="source\j_0CWorldScheduleQEAAXZ_14000B0F0.cpp" />
    <ClCompile Include="source\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000375B.cpp" />
    <ClCompile Include="source\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000BD34.cpp" />
    <ClCompile Include="source\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140013F48.cpp" />
    <ClCompile Include="source\j_0mapVbasic_stringDUchar_traitsDstdVallocatorD2st_140013FED.cpp" />
    <ClCompile Include="source\j_0MonsterSetInfoDataQEAAXZ_14000B389.cpp" />
    <ClCompile Include="source\j_0MonsterStateDataQEAAXZ_14000BB54.cpp" />
    <ClCompile Include="source\j_0pairViterator_TreeV_Tmap_traitsVbasic_stringDUc_14000E962.cpp" />
    <ClCompile Include="source\j_0ptr2userVCMonsterlua_tinkerQEAAPEAVCMonsterZ_1400136F6.cpp" />
    <ClCompile Include="source\j_0UpairCBVbasic_stringDUchar_traitsDstdVallocator_14000473C.cpp" />
    <ClCompile Include="source\j_0UpairCBVbasic_stringDUchar_traitsDstdVallocator_14000911F.cpp" />
    <ClCompile Include="source\j_0vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_14000D1BB.cpp" />
    <ClCompile Include="source\j_0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140007DF1.cpp" />
    <ClCompile Include="source\j_0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140011180.cpp" />
    <ClCompile Include="source\j_0vectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_1400040CA.cpp" />
    <ClCompile Include="source\j_0_event_respawnQEAAXZ_14000211C.cpp" />
    <ClCompile Include="source\j_0_mapCGameStatisticsQEAAXZ_140012274.cpp" />
    <ClCompile Include="source\j_0_map_fldQEAAXZ_14000EE26.cpp" />
    <ClCompile Include="source\j_0_monster_create_setdataQEAAXZ_14001019A.cpp" />
    <ClCompile Include="source\j_0_monster_set_event_setQEAAXZ_14000F89E.cpp" />
    <ClCompile Include="source\j_0_monster_sp_groupQEAAXZ_14000353A.cpp" />
    <ClCompile Include="source\j_0_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar__140010172.cpp" />
    <ClCompile Include="source\j_0_notice_move_limit_map_msg_zoclQEAAXZ_14000B037.cpp" />
    <ClCompile Include="source\j_0_NPCQuestIndexTempDataQEAAXZ_140013719.cpp" />
    <ClCompile Include="source\j_0_npc_create_setdataQEAAXZ_140003922.cpp" />
    <ClCompile Include="source\j_0_npc_quest_list_result_zoclQEAAXZ_1400139D5.cpp" />
    <ClCompile Include="source\j_0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_14000218A.cpp" />
    <ClCompile Include="source\j_0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_1400035E9.cpp" />
    <ClCompile Include="source\j_0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_140005B14.cpp" />
    <ClCompile Include="source\j_0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_1400130ED.cpp" />
    <ClCompile Include="source\j_0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_140005E52.cpp" />
    <ClCompile Include="source\j_0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_1400109F6.cpp" />
    <ClCompile Include="source\j_0_reged_char_result_zoneQEAAXZ_14000DF9E.cpp" />
    <ClCompile Include="source\j_0_respawn_monster_act_dh_mission_mgrQEAAXZ_14000722F.cpp" />
    <ClCompile Include="source\j_0_state_event_respawnQEAAXZ_1400087D3.cpp" />
    <ClCompile Include="source\j_0_state_monster_set_event_setQEAAXZ_140013AF2.cpp" />
    <ClCompile Include="source\j_0_target_monster_aggro_inform_zoclQEAAXZ_140013FAC.cpp" />
    <ClCompile Include="source\j_0_target_monster_contsf_allinform_zoclQEAAXZ_14000FFBA.cpp" />
    <ClCompile Include="source\j_0_Tmap_traitsVbasic_stringDUchar_traitsDstdVallo_14000A80D.cpp" />
    <ClCompile Include="source\j_0_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14000AA97.cpp" />
    <ClCompile Include="source\j_0_Tree_nodV_Tmap_traitsVbasic_stringDUchar_trait_14000DD73.cpp" />
    <ClCompile Include="source\j_0_Tree_ptrV_Tmap_traitsVbasic_stringDUchar_trait_140013F93.cpp" />
    <ClCompile Include="source\j_0_Tree_valV_Tmap_traitsVbasic_stringDUchar_trait_14000FC3B.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000A5E2.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000FA01.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000CF59.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000E0F7.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140009BF1.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140013449.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_140006D6B.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_1400092A5.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorPEAVCMoveMapLimitRightVallocato_1400035A8.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorPEAVCMoveMapLimitRightVallocato_14000A2C7.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorVCMoveMapLimitRightInfoVallocat_140003C33.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorVCMoveMapLimitRightInfoVallocat_14000992B.cpp" />
    <ClCompile Include="source\j_0_Vector_valPEAVCMoveMapLimitInfoVallocatorPEAVC_14000162C.cpp" />
    <ClCompile Include="source\j_0_Vector_valPEAVCMoveMapLimitRightVallocatorPEAV_140001DED.cpp" />
    <ClCompile Include="source\j_0_Vector_valVCMoveMapLimitRightInfoVallocatorVCM_14000DA26.cpp" />
    <ClCompile Include="source\j_0__add_monsterQEAAXZ_14000DAEE.cpp" />
    <ClCompile Include="source\j_0__change_monsterQEAAXZ_1400027BB.cpp" />
    <ClCompile Include="source\j_0__monster_groupQEAAXZ_14000BC26.cpp" />
    <ClCompile Include="source\j_0__respawn_monsterQEAAXZ_14000A097.cpp" />
    <ClCompile Include="source\j_1BossSchedule_MapQEAAXZ_140001FB4.cpp" />
    <ClCompile Include="source\j_1CCircleZoneUEAAXZ_1400012B2.cpp" />
    <ClCompile Include="source\j_1CMapDataUEAAXZ_1400102E9.cpp" />
    <ClCompile Include="source\j_1CMapDisplayUEAAXZ_14000C892.cpp" />
    <ClCompile Include="source\j_1CMapExtendQEAAXZ_140012C83.cpp" />
    <ClCompile Include="source\j_1CMapOperationUEAAXZ_14000CB21.cpp" />
    <ClCompile Include="source\j_1CMapTabUEAAXZ_140006762.cpp" />
    <ClCompile Include="source\j_1CMonsterAggroMgrQEAAXZ_140008D1E.cpp" />
    <ClCompile Include="source\j_1CMonsterAIUEAAXZ_14000A254.cpp" />
    <ClCompile Include="source\j_1CMonsterEventRespawnUEAAXZ_14000C239.cpp" />
    <ClCompile Include="source\j_1CMonsterEventSetUEAAXZ_1400063A7.cpp" />
    <ClCompile Include="source\j_1CMonsterHierarchyUEAAXZ_1400020F4.cpp" />
    <ClCompile Include="source\j_1CMonsterUEAAXZ_140012B9D.cpp" />
    <ClCompile Include="source\j_1CMoveMapLimitInfoListQEAAXZ_140001D9D.cpp" />
    <ClCompile Include="source\j_1CMoveMapLimitInfoPortalQEAAXZ_14000D88C.cpp" />
    <ClCompile Include="source\j_1CMoveMapLimitInfoQEAAXZ_140002CE3.cpp" />
    <ClCompile Include="source\j_1CMoveMapLimitManagerQEAAXZ_140012DBE.cpp" />
    <ClCompile Include="source\j_1CMoveMapLimitRightInfoListQEAAXZ_14000F36C.cpp" />
    <ClCompile Include="source\j_1CMoveMapLimitRightInfoQEAAXZ_14000AA88.cpp" />
    <ClCompile Include="source\j_1CMoveMapLimitRightQEAAXZ_14001325A.cpp" />
    <ClCompile Include="source\j_1const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140010064.cpp" />
    <ClCompile Include="source\j_1CRFMonsterAIMgrQEAAXZ_14000495D.cpp" />
    <ClCompile Include="source\j_1CWorldScheduleQEAAXZ_14000240F.cpp" />
    <ClCompile Include="source\j_1iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_1400127F1.cpp" />
    <ClCompile Include="source\j_1mapVbasic_stringDUchar_traitsDstdVallocatorD2st_140010451.cpp" />
    <ClCompile Include="source\j_1pairViterator_TreeV_Tmap_traitsVbasic_stringDUc_14000B500.cpp" />
    <ClCompile Include="source\j_1ptr2userVCMonsterlua_tinkerUEAAXZ_140008FCB.cpp" />
    <ClCompile Include="source\j_1vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_14000FE8E.cpp" />
    <ClCompile Include="source\j_1vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_14000C478.cpp" />
    <ClCompile Include="source\j_1vectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_14000987C.cpp" />
    <ClCompile Include="source\j_1_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar__14000F727.cpp" />
    <ClCompile Include="source\j_1_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_140012675.cpp" />
    <ClCompile Include="source\j_1_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_140003B2A.cpp" />
    <ClCompile Include="source\j_1_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_140011F4F.cpp" />
    <ClCompile Include="source\j_1_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14000B569.cpp" />
    <ClCompile Include="source\j_1_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000EC05.cpp" />
    <ClCompile Include="source\j_1_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000B2D5.cpp" />
    <ClCompile Include="source\j_1_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140011C2F.cpp" />
    <ClCompile Include="source\j_1_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_14000DAB7.cpp" />
    <ClCompile Include="source\j_1_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140003954.cpp" />
    <ClCompile Include="source\j_1_Vector_iteratorVCMoveMapLimitRightInfoVallocat_14000B271.cpp" />
    <ClCompile Include="source\j_1__change_monsterQEAAXZ_140003DB9.cpp" />
    <ClCompile Include="source\j_4CMoveMapLimitRightInfoQEAAAEBV0AEBV0Z_140006C3F.cpp" />
    <ClCompile Include="source\j_4const_iterator_TreeV_Tmap_traitsVbasic_stringDU_14001050F.cpp" />
    <ClCompile Include="source\j_4iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000AC7C.cpp" />
    <ClCompile Include="source\j_4_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_14000D4A4.cpp" />
    <ClCompile Include="source\j_4_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000159B.cpp" />
    <ClCompile Include="source\j_4_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140007ABD.cpp" />
    <ClCompile Include="source\j_8const_iterator_TreeV_Tmap_traitsVbasic_stringDU_1400088FA.cpp" />
    <ClCompile Include="source\j_8_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000EB88.cpp" />
    <ClCompile Include="source\j_8_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000E908.cpp" />
    <ClCompile Include="source\j_8_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140004FD4.cpp" />
    <ClCompile Include="source\j_9const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140006DA2.cpp" />
    <ClCompile Include="source\j_9MonsterStateDataQEBA_NAEBV0Z_14000E9B2.cpp" />
    <ClCompile Include="source\j_9_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000A867.cpp" />
    <ClCompile Include="source\j_9_Vector_const_iteratorPEAVCMoveMapLimitRightVal_140004A84.cpp" />
    <ClCompile Include="source\j_9_Vector_const_iteratorVCMoveMapLimitRightInfoVa_14000A7D6.cpp" />
    <ClCompile Include="source\j_AddMonsterCDarkHoleChannelQEAAXXZ_14000AC04.cpp" />
    <ClCompile Include="source\j_allocateallocatorPEAVCMoveMapLimitInfostdQEAAPEA_14000984F.cpp" />
    <ClCompile Include="source\j_allocateallocatorPEAVCMoveMapLimitRightstdQEAAPE_1400086DE.cpp" />
    <ClCompile Include="source\j_allocateallocatorU_Node_Tree_nodV_Tmap_traitsVba_140006F28.cpp" />
    <ClCompile Include="source\j_allocateallocatorVCMoveMapLimitRightInfostdQEAAP_14000CF6D.cpp" />
    <ClCompile Include="source\j_AmapVbasic_stringDUchar_traitsDstdVallocatorD2st_14000B7F3.cpp" />
    <ClCompile Include="source\j_assignvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_14000E5F2.cpp" />
    <ClCompile Include="source\j_assignvectorVCMoveMapLimitRightInfoVallocatorVCM_1400059B6.cpp" />
    <ClCompile Include="source\j_AutoRecoverCMonsterQEAAXXZ_140008017.cpp" />
    <ClCompile Include="source\j_AvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_1400098E0.cpp" />
    <ClCompile Include="source\j_AvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140014029.cpp" />
    <ClCompile Include="source\j_AvectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_14000C0EA.cpp" />
    <ClCompile Include="source\j_beginvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140003F85.cpp" />
    <ClCompile Include="source\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000CEF0.cpp" />
    <ClCompile Include="source\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D706.cpp" />
    <ClCompile Include="source\j_beginvectorVCMoveMapLimitRightInfoVallocatorVCMo_1400132D7.cpp" />
    <ClCompile Include="source\j_begin_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000965B.cpp" />
    <ClCompile Include="source\j_CalcScheduleCursorCWorldScheduleQEAAHHHZ_14000BF9B.cpp" />
    <ClCompile Include="source\j_CalcStartNPCQuestCntCQuestMgrSA_NQEAKZ_1400033E1.cpp" />
    <ClCompile Include="source\j_capacityvectorPEAVCMoveMapLimitInfoVallocatorPEA_140006839.cpp" />
    <ClCompile Include="source\j_capacityvectorPEAVCMoveMapLimitRightVallocatorPE_14001099C.cpp" />
    <ClCompile Include="source\j_capacityvectorVCMoveMapLimitRightInfoVallocatorV_140001F50.cpp" />
    <ClCompile Include="source\j_ChangeApparitionCMonsterQEAAX_NKZ_1400084A4.cpp" />
    <ClCompile Include="source\j_ChangeLayerCMapDisplayQEAA_NGZ_140007AA9.cpp" />
    <ClCompile Include="source\j_ChangeMapCMapDisplayQEAA_NPEAVCMapDataZ_14000C2AC.cpp" />
    <ClCompile Include="source\j_ChangeMonsterApparitionCDarkHoleChannelQEAAXHZ_140007B94.cpp" />
    <ClCompile Include="source\j_ChangeMonsterCDarkHoleChannelQEAAXXZ_14000EDC7.cpp" />
    <ClCompile Include="source\j_ChangeSchCursorCWorldScheduleQEAAXPEAU_WorldSche_1400100F5.cpp" />
    <ClCompile Include="source\j_ChangeTargetPosDfAIMgrSAXPEAVCMonsterPEAMZ_1400069DD.cpp" />
    <ClCompile Include="source\j_ChatMapRequestCNetworkEXAEAA_NHPEADZ_140008AB7.cpp" />
    <ClCompile Include="source\j_CheckAlienationDfAIMgrSAHPEAVCMonsterZ_14001119E.cpp" />
    <ClCompile Include="source\j_CheckAutoRecoverHPCMonsterQEAAXXZ_14000F862.cpp" />
    <ClCompile Include="source\j_CheckCenterPosDummyCMapDataQEAA_NPEAU_dummy_posi_1400097A5.cpp" />
    <ClCompile Include="source\j_CheckDelayDestroyCMonsterQEAA_NXZ_14000A989.cpp" />
    <ClCompile Include="source\j_CheckEmotionBadDfAIMgrSAHPEAVCMonsterPEAVCMonste_14000D675.cpp" />
    <ClCompile Include="source\j_CheckEmotionPresentationCMonsterQEAAXXZ_140006E9C.cpp" />
    <ClCompile Include="source\j_CheckEventSetRespawnCMonsterEventSetQEAAXXZ_14000BB6D.cpp" />
    <ClCompile Include="source\j_CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_14000D562.cpp" />
    <ClCompile Include="source\j_CheckMapPortalLinkCMapOperationAEAAXXZ_140005885.cpp" />
    <ClCompile Include="source\j_CheckMonArea_N_ChangeStateDfAIMgrSAHPEAVCMonster_140002B5D.cpp" />
    <ClCompile Include="source\j_CheckMonsterRotateCMonsterQEAAXXZ_140010A8C.cpp" />
    <ClCompile Include="source\j_CheckMonsterStateDataCMonsterQEAA_NXZ_1400048FE.cpp" />
    <ClCompile Include="source\j_CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQues_1400013FC.cpp" />
    <ClCompile Include="source\j_CheckRespawnEventCMonsterEventRespawnQEAAXXZ_1400100A5.cpp" />
    <ClCompile Include="source\j_CheckRespawnMonsterCDarkHoleChannelQEAAXXZ_14000902F.cpp" />
    <ClCompile Include="source\j_CheckRespawnProcessCMonsterQEAA_NXZ_140013A25.cpp" />
    <ClCompile Include="source\j_CheckSchCWorldScheduleQEAAXXZ_140006E65.cpp" />
    <ClCompile Include="source\j_CheckSPFDelayTimeDfAIMgrSAHPEAVCMonsterAIHKZ_14000F7EF.cpp" />
    <ClCompile Include="source\j_CheckSPFDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_1400044EE.cpp" />
    <ClCompile Include="source\j_check_dummyAutominePersonalMgrQEAA_NPEAVCMapData_140012DF0.cpp" />
    <ClCompile Include="source\j_ChildKindCountCMonsterHierarchyQEAAEXZ_140012382.cpp" />
    <ClCompile Include="source\j_class_addVCMonsterlua_tinkerYAXPEAUlua_StatePEBD_140002D8D.cpp" />
    <ClCompile Include="source\j_class_defVCMonsterP81EAAPEAVCLuaSignalReActorXZl_140002AC2.cpp" />
    <ClCompile Include="source\j_CleanUpCMoveMapLimitInfoListAEAAXXZ_140012422.cpp" />
    <ClCompile Include="source\j_CleanUpCMoveMapLimitRightInfoAEAAXXZ_14000A213.cpp" />
    <ClCompile Include="source\j_CleanUpCMoveMapLimitRightUEAAXXZ_140009BCE.cpp" />
    <ClCompile Include="source\j_ClearBossSchedule_MapQEAAXXZ_1400034AE.cpp" />
    <ClCompile Include="source\j_ClearEmotionPresentationCMonsterQEAAXXZ_140013B47.cpp" />
    <ClCompile Include="source\j_clearvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_1400102D0.cpp" />
    <ClCompile Include="source\j_clearvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013F8E.cpp" />
    <ClCompile Include="source\j_clear_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000D657.cpp" />
    <ClCompile Include="source\j_Command_ChildMonDestroyCMonsterQEAAXKZ_1400098F4.cpp" />
    <ClCompile Include="source\j_constructallocatorPEAU_Node_Tree_nodV_Tmap_trait_140007EF5.cpp" />
    <ClCompile Include="source\j_constructallocatorPEAVCMoveMapLimitRightstdQEAAX_14000DD3C.cpp" />
    <ClCompile Include="source\j_constructallocatorVCMoveMapLimitRightInfostdQEAA_14000FE43.cpp" />
    <ClCompile Include="source\j_ConvertLocalCMapDataQEAA_NPEAU_dummy_positionZ_1400056B9.cpp" />
    <ClCompile Include="source\j_ConvertToMapCMapExtendQEAAXPEAVCSizeZ_1400035AD.cpp" />
    <ClCompile Include="source\j_CreateAICMonsterQEAAHHZ_14000B104.cpp" />
    <ClCompile Include="source\j_CreateCCircleZoneQEAA_NPEAVCMapDataEZ_140002D42.cpp" />
    <ClCompile Include="source\j_CreateCGravityStoneRegenerQEAA_NPEAVCMapDataZ_14000AB0A.cpp" />
    <ClCompile Include="source\j_CreateCMerchantQEAA_NPEAU_npc_create_setdataZ_140004750.cpp" />
    <ClCompile Include="source\j_CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_1400073D3.cpp" />
    <ClCompile Include="source\j_CreateCMoveMapLimitInfoSAPEAV1IHZ_14001258A.cpp" />
    <ClCompile Include="source\j_CreateCMoveMapLimitRightSAPEAV1HZ_140011CB1.cpp" />
    <ClCompile Include="source\j_CreateMonsterCDarkHoleChannelQEAAXXZ_1400068A2.cpp" />
    <ClCompile Include="source\j_CreateObjectCMapTabSAPEAVCObjectXZ_140002A54.cpp" />
    <ClCompile Include="source\j_CreateObjSurfaceCMapDisplayAEAAJXZ_140007590.cpp" />
    <ClCompile Include="source\j_CreatePaletteFromBitmapCDisplayQEAAJPEAPEAUIDire_140008DD2.cpp" />
    <ClCompile Include="source\j_CreateRepMonsterYAPEAVCMonsterPEAVCMapDataGPEAMP_140009EDF.cpp" />
    <ClCompile Include="source\j_CreateRespawnMonsterYAPEAVCMonsterPEAVCMapDataGH_14000BFDC.cpp" />
    <ClCompile Include="source\j_CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurf_1400108B6.cpp" />
    <ClCompile Include="source\j_DataCheckCWorldScheduleQEAA_NXZ_140010AF0.cpp" />
    <ClCompile Include="source\j_Dconst_iterator_TreeV_Tmap_traitsVbasic_stringDU_140001361.cpp" />
    <ClCompile Include="source\j_deallocateallocatorPEAVCMoveMapLimitInfostdQEAAX_140001D52.cpp" />
    <ClCompile Include="source\j_deallocateallocatorPEAVCMoveMapLimitRightstdQEAA_14001327D.cpp" />
    <ClCompile Include="source\j_deallocateallocatorU_Node_Tree_nodV_Tmap_traitsV_14001329B.cpp" />
    <ClCompile Include="source\j_deallocateallocatorVCMoveMapLimitRightInfostdQEA_140003391.cpp" />
    <ClCompile Include="source\j_defP6APEAVCMonsterPEAD0MMMZlua_tinkerYAXPEAUlua__14000A8E4.cpp" />
    <ClCompile Include="source\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.cpp" />
    <ClCompile Include="source\j_destroyallocatorPEAU_Node_Tree_nodV_Tmap_traitsV_140007234.cpp" />
    <ClCompile Include="source\j_destroyallocatorPEAVCMoveMapLimitRightstdQEAAXPE_140010226.cpp" />
    <ClCompile Include="source\j_destroyallocatorU_Node_Tree_nodV_Tmap_traitsVbas_140013AAC.cpp" />
    <ClCompile Include="source\j_destroyallocatorVCMoveMapLimitRightInfostdQEAAXP_14000D300.cpp" />
    <ClCompile Include="source\j_DestroyCCircleZoneQEAAXXZ_140012373.cpp" />
    <ClCompile Include="source\j_DestroyCMonsterQEAA_NEPEAVCGameObjectZ_14000AA29.cpp" />
    <ClCompile Include="source\j_DestroyCMoveMapLimitManagerSAXXZ_140012715.cpp" />
    <ClCompile Include="source\j_destroyerVCMonsterlua_tinkerYAHPEAUlua_StateZ_1400137A5.cpp" />
    <ClCompile Include="source\j_Diterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140004F7F.cpp" />
    <ClCompile Include="source\j_DoDataExchangeCMapTabMEAAXPEAVCDataExchangeZ_140005452.cpp" />
    <ClCompile Include="source\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.cpp" />
    <ClCompile Include="source\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.cpp" />
    <ClCompile Include="source\j_DrawCollisionLineCMapDisplayAEAAXXZ_140001889.cpp" />
    <ClCompile Include="source\j_DrawDisplayCMapDisplayQEAAXXZ_14000380F.cpp" />
    <ClCompile Include="source\j_DrawDummyCMapDisplayAEAAXXZ_140010721.cpp" />
    <ClCompile Include="source\j_DrawMapCMapDisplayAEAAXXZ_14000C77F.cpp" />
    <ClCompile Include="source\j_DrawObjectCMapDisplayAEAAXXZ_1400114C3.cpp" />
    <ClCompile Include="source\j_DrawRectCMapExtendQEAAXXZ_140011752.cpp" />
    <ClCompile Include="source\j_DrawSelectMonsterLookAtPosCMapDisplayAEAAJPEAVCM_1400070E0.cpp" />
    <ClCompile Include="source\j_DrawTextACMapDisplayAEAAXXZ_140002A5E.cpp" />
    <ClCompile Include="source\j_D_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000F5AB.cpp" />
    <ClCompile Include="source\j_D_Vector_const_iteratorPEAVCMoveMapLimitRightVal_140001BE5.cpp" />
    <ClCompile Include="source\j_D_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140004908.cpp" />
    <ClCompile Include="source\j_Econst_iterator_TreeV_Tmap_traitsVbasic_stringDU_140004525.cpp" />
    <ClCompile Include="source\j_Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000457A.cpp" />
    <ClCompile Include="source\j_Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140012062.cpp" />
    <ClCompile Include="source\j_emptyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000AC63.cpp" />
    <ClCompile Include="source\j_EndScreenPointCMapExtendQEAAHPEAVCSizeZ_14000B212.cpp" />
    <ClCompile Include="source\j_endvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_140002E41.cpp" />
    <ClCompile Include="source\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_140001D8E.cpp" />
    <ClCompile Include="source\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_14000B8ED.cpp" />
    <ClCompile Include="source\j_endvectorVCMoveMapLimitRightInfoVallocatorVCMove_14000EC91.cpp" />
    <ClCompile Include="source\j_end_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_1400054BB.cpp" />
    <ClCompile Include="source\j_EnterMapCMapDataQEAAXPEAVCGameObjectKZ_14000EDF9.cpp" />
    <ClCompile Include="source\j_EnterWorldRequestCNetworkEXAEAA_NHPEAU_MSG_HEADE_1400053DA.cpp" />
    <ClCompile Include="source\j_EnterWorldResultCNetworkEXAEAA_NKPEADZ_1400105AA.cpp" />
    <ClCompile Include="source\j_erasevectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_14000B7B7.cpp" />
    <ClCompile Include="source\j_erasevectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D391.cpp" />
    <ClCompile Include="source\j_erasevectorVCMoveMapLimitRightInfoVallocatorVCMo_140008A9E.cpp" />
    <ClCompile Include="source\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140003968.cpp" />
    <ClCompile Include="source\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_1400118AB.cpp" />
    <ClCompile Include="source\j_ExitMapCMapDataQEAAXPEAVCGameObjectKZ_1400021E9.cpp" />
    <ClCompile Include="source\j_ExitWorldRequestCNetworkEXAEAA_NHPEADZ_140001005.cpp" />
    <ClCompile Include="source\j_E_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000BBCC.cpp" />
    <ClCompile Include="source\j_E_Vector_const_iteratorPEAVCMoveMapLimitRightVal_1400045E8.cpp" />
    <ClCompile Include="source\j_E_Vector_iteratorPEAVCMoveMapLimitRightVallocato_1400038FA.cpp" />
    <ClCompile Include="source\j_Fconst_iterator_TreeV_Tmap_traitsVbasic_stringDU_14001361F.cpp" />
    <ClCompile Include="source\j_fillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCM_140002153.cpp" />
    <ClCompile Include="source\j_fillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVC_14001335E.cpp" />
    <ClCompile Include="source\j_fillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveM_14001310B.cpp" />
    <ClCompile Include="source\j_FindEmptyNPCYAPEAVCMerchantPEAV1HZ_140010E60.cpp" />
    <ClCompile Include="source\j_findV_Vector_iteratorPEAVCMoveMapLimitRightVallo_1400103B1.cpp" />
    <ClCompile Include="source\j_Fiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140001569.cpp" />
    <ClCompile Include="source\j_GeEmotionImpStdTimeCMonsterQEAAMXZ_14000388C.cpp" />
    <ClCompile Include="source\j_GetAggroResetTimeCMonsterQEAAKXZ_1400116D5.cpp" />
    <ClCompile Include="source\j_GetAggroShortTimeCMonsterQEAAKXZ_140003B11.cpp" />
    <ClCompile Include="source\j_GetAngleCMonsterHelperSAMQEAM0Z_140007D24.cpp" />
    <ClCompile Include="source\j_GetBonusInAreaAggroCMonsterQEAAMXZ_1400050FB.cpp" />
    <ClCompile Include="source\j_GetBspInfoCMapDataQEAAPEAU_bsp_infoXZ_14000885F.cpp" />
    <ClCompile Include="source\j_GetChildCMonsterHierarchyQEAAPEAVCMonsterHHZ_140004A02.cpp" />
    <ClCompile Include="source\j_GetChildCountCMonsterHierarchyQEAAKHZ_1400077F2.cpp" />
    <ClCompile Include="source\j_GetCMoveMapLimitInfoListAEAAPEAVCMoveMapLimitInf_140007626.cpp" />
    <ClCompile Include="source\j_GetCMoveMapLimitRightInfoListQEAAPEAVCMoveMapLim_1400059F2.cpp" />
    <ClCompile Include="source\j_GetColorCCircleZoneQEAAEXZ_140011B58.cpp" />
    <ClCompile Include="source\j_GetCritical_Exception_RateCMonsterUEAAHXZ_14000D6B6.cpp" />
    <ClCompile Include="source\j_GetDefFacingCMonsterUEAAMHZ_1400048CC.cpp" />
    <ClCompile Include="source\j_GetDefGapCMonsterUEAAMHZ_14000C950.cpp" />
    <ClCompile Include="source\j_GetDirectionCMonsterHelperSAXAEAY02M00MZ_14000CCA2.cpp" />
    <ClCompile Include="source\j_GetDummyPostionCMapDataQEAAPEAU_dummy_positionPE_14000ABD2.cpp" />
    <ClCompile Include="source\j_GetEmotionStateCMonsterQEAAEXZ_140006730.cpp" />
    <ClCompile Include="source\j_GetEmptyEventSetCMonsterEventSetQEAAPEAU_event_s_140012B70.cpp" />
    <ClCompile Include="source\j_GetEvenSetLootingCMonsterEventSetQEAAPEAU_event__1400126DE.cpp" />
    <ClCompile Include="source\j_GetExtendSizeCMapExtendQEAAPEAVCSizeXZ_14000270C.cpp" />
    <ClCompile Include="source\j_GetFireTolCMonsterUEAAHXZ_14000A14B.cpp" />
    <ClCompile Include="source\j_GetHelpMeCaseCMonsterQEAAHXZ_1400024A0.cpp" />
    <ClCompile Include="source\j_GetHPCMonsterUEAAHXZ_1400032B0.cpp" />
    <ClCompile Include="source\j_GetInxCMoveMapLimitInfoQEAAIXZ_140013291.cpp" />
    <ClCompile Include="source\j_GetLinkPortalCMapDataQEAAPEAU_portal_dummyPEADZ_14000E624.cpp" />
    <ClCompile Include="source\j_GetLostMonsterTargetDistanceMonsterSetInfoDataQE_140006460.cpp" />
    <ClCompile Include="source\j_GetMapCMapOperationQEAAHPEAVCMapDataZ_14000CFEF.cpp" />
    <ClCompile Include="source\j_GetMapCMapOperationQEAAPEAVCMapDataHZ_14000E4D0.cpp" />
    <ClCompile Include="source\j_GetMapCMapOperationQEAAPEAVCMapDataPEADZ_14000A042.cpp" />
    <ClCompile Include="source\j_GetMapCodeCMapDataQEAAEXZ_14000CB2B.cpp" />
    <ClCompile Include="source\j_GetMapCurDirectCTransportShipQEAAPEAVCMapDataXZ_140013A89.cpp" />
    <ClCompile Include="source\j_GetMapDataCGuildRoomInfoQEAAPEAVCMapDataXZ_14000786F.cpp" />
    <ClCompile Include="source\j_GetMapPosCGuildRoomInfoQEAA_NPEAMZ_1400035F3.cpp" />
    <ClCompile Include="source\j_GetMaxDMGSFContCountCMonsterQEAAHXZ_140003D32.cpp" />
    <ClCompile Include="source\j_GetMaxHPCMonsterUEAAHXZ_1400044C1.cpp" />
    <ClCompile Include="source\j_GetMaxToleranceProbMaxMonsterSetInfoDataQEAAMHZ_1400082C9.cpp" />
    <ClCompile Include="source\j_GetMob_AsistTypeCMonsterQEAAHXZ_140012CBF.cpp" />
    <ClCompile Include="source\j_GetMob_SubRaceCMonsterQEAAHXZ_140011C52.cpp" />
    <ClCompile Include="source\j_GetMonStateInfoCMonsterQEAAGXZ_14000E7E1.cpp" />
    <ClCompile Include="source\j_GetMonsterDropRateMonsterSetInfoDataQEAAKHZ_14000E543.cpp" />
    <ClCompile Include="source\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.cpp" />
    <ClCompile Include="source\j_GetMonsterGradeCMonsterQEAAHXZ_140006811.cpp" />
    <ClCompile Include="source\j_GetMonsterNumInCurMissionAreaCDarkHoleChannelQEA_14000EE9E.cpp" />
    <ClCompile Include="source\j_GetMonsterSetCMonsterEventSetQEAAPEAU_monster_se_140013403.cpp" />
    <ClCompile Include="source\j_GetMoveSpeedCMonsterQEAAMXZ_14000DF62.cpp" />
    <ClCompile Include="source\j_GetMoveTypeCMonsterQEAAEXZ_1400076DF.cpp" />
    <ClCompile Include="source\j_GetMyDMGSFContCountCMonsterQEAAHXZ_14000D918.cpp" />
    <ClCompile Include="source\j_GetNewMonSerialCMonsterSAKXZ_140012E45.cpp" />
    <ClCompile Include="source\j_GetObjNameCMonsterUEAAPEADXZ_14000DC3D.cpp" />
    <ClCompile Include="source\j_GetObjRaceCMonsterUEAAHXZ_140005317.cpp" />
    <ClCompile Include="source\j_GetOffensiveTypeCMonsterQEAAHXZ_14000F448.cpp" />
    <ClCompile Include="source\j_GetParentCMonsterHierarchyQEAAPEAVCMonsterXZ_140003828.cpp" />
    <ClCompile Include="source\j_GetPathFinderCMonsterAIQEAAPEAVCPathMgrXZ_140007AF9.cpp" />
    <ClCompile Include="source\j_GetPortalCMapDataQEAAPEAU_portal_dummyHZ_140009188.cpp" />
    <ClCompile Include="source\j_GetPortalCMapDataQEAAPEAU_portal_dummyPEADZ_1400110B8.cpp" />
    <ClCompile Include="source\j_GetPortalInxCCircleZoneQEAAHXZ_140010A1E.cpp" />
    <ClCompile Include="source\j_GetPortalInxCMapDataQEAAHPEADZ_14000B825.cpp" />
    <ClCompile Include="source\j_GetPosStartMapCMapOperationQEAAPEAVCMapDataE_NPE_1400137A0.cpp" />
    <ClCompile Include="source\j_GetRaceTownCMapDataQEAAEPEAMEZ_140006C2B.cpp" />
    <ClCompile Include="source\j_GetRandPosInDummyCMapDataQEAA_NPEAU_dummy_positi_14000279D.cpp" />
    <ClCompile Include="source\j_GetRandPosInRangeCMapDataQEAA_NPEAMH0Z_14000A1B4.cpp" />
    <ClCompile Include="source\j_GetRandPosVirtualDumCMapDataQEAA_NPEAMH0Z_140007B30.cpp" />
    <ClCompile Include="source\j_GetRandPosVirtualDumExcludeStdRangeCMapDataQEAA__140004CD7.cpp" />
    <ClCompile Include="source\j_GetRectInRadiusCMapDataQEAAXPEAU_pnt_rectHHZ_140005F97.cpp" />
    <ClCompile Include="source\j_GetResDummySectorCMapDataQEAAHHPEAMZ_140009282.cpp" />
    <ClCompile Include="source\j_GetRuntimeClassCMapTabUEBAPEAUCRuntimeClassXZ_1400039E5.cpp" />
    <ClCompile Include="source\j_GetSecInfoCMapDataQEAAPEAU_sec_infoXZ_14000D0B7.cpp" />
    <ClCompile Include="source\j_GetSectorIndexCMapDataQEAAHPEAMZ_14000920F.cpp" />
    <ClCompile Include="source\j_GetSectorListObjCMapDataQEAAPEAVCObjectListGKZ_140008C92.cpp" />
    <ClCompile Include="source\j_GetSectorListTowerCMapDataQEAAPEAVCObjectListGKZ_14000AB46.cpp" />
    <ClCompile Include="source\j_GetSectorNumByLayerIndexCMapDataQEAAHGZ_1400102B2.cpp" />
    <ClCompile Include="source\j_GetSettlementMapDataCMapOperationQEAAPEAVCMapDat_140011405.cpp" />
    <ClCompile Include="source\j_GetSignalReActorCMonsterQEAAPEAVCLuaSignalReActo_14000489F.cpp" />
    <ClCompile Include="source\j_GetSoilTolCMonsterUEAAHXZ_140007E7D.cpp" />
    <ClCompile Include="source\j_GetStartMapCMapOperationQEAAPEAVCMapDataEZ_140009958.cpp" />
    <ClCompile Include="source\j_GetStateChunkMonsterStateDataQEBAGXZ_14000CD74.cpp" />
    <ClCompile Include="source\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.cpp" />
    <ClCompile Include="source\j_GetThisClassCMapTabSAPEAUCRuntimeClassXZ_14000A5D3.cpp" />
    <ClCompile Include="source\j_GetTypeCMoveMapLimitInfoQEAAHXZ_1400016C2.cpp" />
    <ClCompile Include="source\j_GetTypeCMoveMapLimitRightQEBAHXZ_1400137F0.cpp" />
    <ClCompile Include="source\j_GetViewAngleCapCMonsterQEAA_NHAEAHZ_140010280.cpp" />
    <ClCompile Include="source\j_GetVisualAngleCMonsterQEAAMXZ_1400101CC.cpp" />
    <ClCompile Include="source\j_GetVisualFieldCMonsterQEAAMXZ_14000227F.cpp" />
    <ClCompile Include="source\j_GetWaterTolCMonsterUEAAHXZ_14000AD3A.cpp" />
    <ClCompile Include="source\j_GetWidthCMonsterUEAAMXZ_140007FB8.cpp" />
    <ClCompile Include="source\j_GetWindTolCMonsterUEAAHXZ_14000673F.cpp" />
    <ClCompile Include="source\j_GetYAngleByteCMonsterQEAAEXZ_140011284.cpp" />
    <ClCompile Include="source\j_GetYAngleCMonsterQEAAMXZ_14000371A.cpp" />
    <ClCompile Include="source\j_gm_MapChangeCMainThreadQEAAXPEAVCMapDataZ_140008F6C.cpp" />
    <ClCompile Include="source\j_gm_UpdateMapCMainThreadQEAAXXZ_1400050D3.cpp" />
    <ClCompile Include="source\j_GoalCCircleZoneQEAAEPEAVCMapDataPEAMZ_1400043EA.cpp" />
    <ClCompile Include="source\j_G_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000DCAB.cpp" />
    <ClCompile Include="source\j_G_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140003E7C.cpp" />
    <ClCompile Include="source\j_HierarcyHelpCastCMonsterHelperSAXPEAVCMonsterZ_140007176.cpp" />
    <ClCompile Include="source\j_H_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140012751.cpp" />
    <ClCompile Include="source\j_InsertNpcQuestHistoryCQuestMgrQEAAEPEADENZ_1400048E5.cpp" />
    <ClCompile Include="source\j_insertvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400024BE.cpp" />
    <ClCompile Include="source\j_insertvectorPEAVCMoveMapLimitRightVallocatorPEAV_140006E60.cpp" />
    <ClCompile Include="source\j_insertvectorVCMoveMapLimitRightInfoVallocatorVCM_14000DDFF.cpp" />
    <ClCompile Include="source\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400086ED.cpp" />
    <ClCompile Include="source\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140011527.cpp" />
    <ClCompile Include="source\j_InstanceCMoveMapLimitManagerSAPEAV1XZ_14001131F.cpp" />
    <ClCompile Include="source\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.cpp" />
    <ClCompile Include="source\j_invokelua2objectPEAVCMonsterlua_tinkerSAPEAVCMon_140012A80.cpp" />
    <ClCompile Include="source\j_invokeobject2luaPEAVCMonsterlua_tinkerSAXPEAUlua_140003A3F.cpp" />
    <ClCompile Include="source\j_invokePEAVCLuaSignalReActormem_functorVCMonsterX_14000309E.cpp" />
    <ClCompile Include="source\j_invokePEAVCMonsterfunctorPEADPEADMMMlua_tinkerSA_14000BB2C.cpp" />
    <ClCompile Include="source\j_invokeptr2luaVCMonsterlua_tinkerSAXPEAUlua_State_14000558D.cpp" />
    <ClCompile Include="source\j_invokeuser2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14001023F.cpp" />
    <ClCompile Include="source\j_invokeuser2typeP8CMonsterEAAPEAVCLuaSignalReActo_140009A7A.cpp" />
    <ClCompile Include="source\j_invokevoid2ptrA6APEAVCMonsterPEAD0MMMZlua_tinker_1400024AA.cpp" />
    <ClCompile Include="source\j_invokevoid2ptrVCMonsterlua_tinkerSAPEAVCMonsterP_14000FDAD.cpp" />
    <ClCompile Include="source\j_invokevoid2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14000DDAF.cpp" />
    <ClCompile Include="source\j_invokevoid2typeP8CMonsterEAAPEAVCLuaSignalReActo_14000A04C.cpp" />
    <ClCompile Include="source\j_invokevoid2typePEAVCMonsterlua_tinkerSAPEAVCMons_140009070.cpp" />
    <ClCompile Include="source\j_invokevoid2valP8CMonsterEAAPEAVCLuaSignalReActor_140013377.cpp" />
    <ClCompile Include="source\j_IsBossMonsterCMonsterQEAA_NXZ_140008E1D.cpp" />
    <ClCompile Include="source\j_IsCompleteNpcQuestCQuestMgrQEAA_NPEADHZ_14000D50D.cpp" />
    <ClCompile Include="source\j_IsEqualLimitCMoveMapLimitInfoQEAA_NHHKZ_1400030D5.cpp" />
    <ClCompile Include="source\j_IsExistStdMapIDCMapOperationQEAA_NHZ_140008A44.cpp" />
    <ClCompile Include="source\j_IsHaveRightCMoveMapLimitRightInfoQEAA_NHZ_1400097A0.cpp" />
    <ClCompile Include="source\j_IsHaveRightCMoveMapLimitRightPortalUEAA_NXZ_1400106B8.cpp" />
    <ClCompile Include="source\j_IsHaveRightCMoveMapLimitRightUEAA_NXZ_140009219.cpp" />
    <ClCompile Include="source\j_IsINIFileChangedCMonsterEventSetQEAA_NPEBDU_FILE_140008DAF.cpp" />
    <ClCompile Include="source\j_IsInRegionCMapOperationQEAA_NPEADPEAVCGameObject_140013548.cpp" />
    <ClCompile Include="source\j_IsInSectorCMonsterHelperSAHQEAM00MMPEAMZ_14000535D.cpp" />
    <ClCompile Include="source\j_IsMapInCMapDataQEAA_NPEAMZ_14000DF3F.cpp" />
    <ClCompile Include="source\j_IsMovableCMonsterQEAA_NXZ_14000E511.cpp" />
    <ClCompile Include="source\j_IsNearPositionCCircleZoneAEAA_NPEBMZ_14000A3FD.cpp" />
    <ClCompile Include="source\j_IsPossibleRepeatNpcQuestCQuestMgrQEAA_NPEADHZ_1400081E3.cpp" />
    <ClCompile Include="source\j_IsProcLinkNpcQuestCQuestMgrQEAA_NPEADHZ_14000194C.cpp" />
    <ClCompile Include="source\j_IsProcNpcQuestCQuestMgrQEAA_NPEADZ_14001364C.cpp" />
    <ClCompile Include="source\j_IsRoateMonsterCMonsterQEAA_NXZ_140010D2F.cpp" />
    <ClCompile Include="source\j_IsRotateBlockMonsterSetInfoDataQEAA_NPEAU_mon_bl_140007B0D.cpp" />
    <ClCompile Include="source\j_IsSame_target_monster_contsf_allinform_zoclSA_NA_14000A501.cpp" />
    <ClCompile Include="source\j_LinkEventRespawnCMonsterQEAAXPEAU_event_respawnZ_14000C85B.cpp" />
    <ClCompile Include="source\j_LinkEventSetCMonsterQEAAXPEAU_event_setZ_14000970F.cpp" />
    <ClCompile Include="source\j_LoadAllBossSchedule_MapQEAA_NXZ_140002725.cpp" />
    <ClCompile Include="source\j_LoadDummyCMapDataQEAA_NPEADPEAU_dummy_positionZ_14000C7B6.cpp" />
    <ClCompile Include="source\j_LoadEventSetCMonsterEventSetQEAA_NPEADZ_140010091.cpp" />
    <ClCompile Include="source\j_LoadEventSetLootingCMonsterEventSetQEAA_NXZ_1400088E1.cpp" />
    <ClCompile Include="source\j_LoadINICMoveMapLimitInfoPortalAEAA_NXZ_140006514.cpp" />
    <ClCompile Include="source\j_LoadMapsCMapOperationAEAA_NXZ_1400110EA.cpp" />
    <ClCompile Include="source\j_LoadMonsterSetInfoDataQEAAHPEBDZ_140006AB4.cpp" />
    <ClCompile Include="source\j_LoadRegionCMapOperationAEAA_NXZ_14000FB2D.cpp" />
    <ClCompile Include="source\j_LoadWorldInfoINICMainThreadAEAAHXZ_14000BB7C.cpp" />
    <ClCompile Include="source\j_LoopCMonsterUEAAXXZ_140006249.cpp" />
    <ClCompile Include="source\j_LoopCMoveMapLimitInfoListQEAAXXZ_14000F59C.cpp" />
    <ClCompile Include="source\j_LoopCMoveMapLimitInfoPortalUEAAXXZ_140009BD3.cpp" />
    <ClCompile Include="source\j_LoopCMoveMapLimitInfoUEAAXXZ_1400098AE.cpp" />
    <ClCompile Include="source\j_LoopCMoveMapLimitManagerQEAAXXZ_140005475.cpp" />
    <ClCompile Include="source\j_LoopCWorldScheduleQEAAXXZ_140013A84.cpp" />
    <ClCompile Include="source\j_lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_14000B23F.cpp" />
    <ClCompile Include="source\j_lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAU_14000E35E.cpp" />
    <ClCompile Include="source\j_max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KX_1400129C2.cpp" />
    <ClCompile Include="source\j_max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_K_14000A894.cpp" />
    <ClCompile Include="source\j_max_sizeallocatorVCMoveMapLimitRightInfostdQEBA__140003788.cpp" />
    <ClCompile Include="source\j_max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEA_14000A26D.cpp" />
    <ClCompile Include="source\j_max_sizevectorPEAVCMoveMapLimitRightVallocatorPE_14000991C.cpp" />
    <ClCompile Include="source\j_max_sizevectorVCMoveMapLimitRightInfoVallocatorV_14000FFC4.cpp" />
    <ClCompile Include="source\j_max_size_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140009B33.cpp" />
    <ClCompile Include="source\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.cpp" />
    <ClCompile Include="source\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.cpp" />
    <ClCompile Include="source\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.cpp" />
    <ClCompile Include="source\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.cpp" />
    <ClCompile Include="source\j_MoveLimitMapZoneRequestCMoveMapLimitManagerQEAA__140003BC0.cpp" />
    <ClCompile Include="source\j_MoveScreenPointCMapExtendQEAAXPEAVCPointZ_14001005A.cpp" />
    <ClCompile Include="source\j_MoveToOwnStoneMapRequestCNetworkEXAEAA_NHPEADZ_140008C8D.cpp" />
    <ClCompile Include="source\j_nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_140007130.cpp" />
    <ClCompile Include="source\j_NPCDialogRequestCNetworkEXAEAA_NHPEADZ_1400047D2.cpp" />
    <ClCompile Include="source\j_NPCQuestListRequestCNetworkEXAEAA_NHPEADZ_14000907F.cpp" />
    <ClCompile Include="source\j_NPCQuestRequestCNetworkEXAEAA_NHPEADZ_1400128AF.cpp" />
    <ClCompile Include="source\j_NPCWatchingRequestCNetworkEXAEAA_NHPEADZ_140004CE1.cpp" />
    <ClCompile Include="source\j_OffDisplayCMapDisplayQEAA_NXZ_140001221.cpp" />
    <ClCompile Include="source\j_OnButtonMapchangeCMapTabIEAAXXZ_140010028.cpp" />
    <ClCompile Include="source\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.cpp" />
    <ClCompile Include="source\j_OnChildMonsterCreateCMonsterHierarchyQEAAXPEAU_m_140002A09.cpp" />
    <ClCompile Include="source\j_OnChildMonsterDestroyCMonsterHierarchyQEAAXXZ_14000B9FB.cpp" />
    <ClCompile Include="source\j_OnChildRegenLoopCMonsterHierarchyQEAAXXZ_140013D95.cpp" />
    <ClCompile Include="source\j_OnDisplayCMapDisplayQEAA_NPEAVCMapDataGZ_140011A81.cpp" />
    <ClCompile Include="source\j_OnLoopCMapDataQEAAXXZ_14000EC87.cpp" />
    <ClCompile Include="source\j_OnLoopCMapOperationQEAAXXZ_14000A2E5.cpp" />
    <ClCompile Include="source\j_OnSetActiveCMapTabUEAAHXZ_140004507.cpp" />
    <ClCompile Include="source\j_OpenMapCMapDataQEAA_NPEADPEAU_map_fld_NZ_14000B25D.cpp" />
    <ClCompile Include="source\j_OpenWorldFailureResultCNetworkEXAEAA_NKPEADZ_140001235.cpp" />
    <ClCompile Include="source\j_OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_140005B78.cpp" />
    <ClCompile Include="source\j_OutOfSecCMonsterUEAAXXZ_14000CF2C.cpp" />
    <ClCompile Include="source\j_PassOneStepCWorldScheduleQEAAXXZ_14000C3B5.cpp" />
    <ClCompile Include="source\j_pc_AlterWorldServiceCMainThreadQEAAX_NZ_14000D15C.cpp" />
    <ClCompile Include="source\j_pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_14000649C.cpp" />
    <ClCompile Include="source\j_pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1400082E7.cpp" />
    <ClCompile Include="source\j_pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1400132C8.cpp" />
    <ClCompile Include="source\j_PopChildMonAllCMonsterHierarchyQEAAXXZ_14000B956.cpp" />
    <ClCompile Include="source\j_PopChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_14000A9A2.cpp" />
    <ClCompile Include="source\j_ProcessCMonsterAggroMgrQEAAXXZ_14000D5FD.cpp" />
    <ClCompile Include="source\j_ProcForceMoveHQCMoveMapLimitInfoPortalAEAAEHPEAD_140005781.cpp" />
    <ClCompile Include="source\j_ProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHPE_14000FE1B.cpp" />
    <ClCompile Include="source\j_ProcUseMoveScrollCMoveMapLimitInfoPortalAEAAEHPE_14000E5D4.cpp" />
    <ClCompile Include="source\j_PushChildMonCMonsterHierarchyQEAAHHPEAVCMonsterZ_140010E5B.cpp" />
    <ClCompile Include="source\j_pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCM_140004705.cpp" />
    <ClCompile Include="source\j_push_backvectorPEAVCMoveMapLimitRightVallocatorP_140009917.cpp" />
    <ClCompile Include="source\j_push_functorPEAVCLuaSignalReActorVCMonsterlua_ti_1400031B6.cpp" />
    <ClCompile Include="source\j_push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAX_14000DA1C.cpp" />
    <ClCompile Include="source\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.cpp" />
    <ClCompile Include="source\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.cpp" />
    <ClCompile Include="source\j_readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua__140010726.cpp" />
    <ClCompile Include="source\j_RegistCMoveMapLimitRightInfoQEAA_NHZ_140004EB7.cpp" />
    <ClCompile Include="source\j_ReleaseDisplayCMapDisplayQEAAJXZ_140002586.cpp" />
    <ClCompile Include="source\j_RequestCMoveMapLimitInfoListQEAAEHHHKHPEADPEAVCM_14000D9C2.cpp" />
    <ClCompile Include="source\j_RequestCMoveMapLimitInfoPortalUEAAEHHPEADPEAVCMo_14000760D.cpp" />
    <ClCompile Include="source\j_RequestCMoveMapLimitManagerQEAAEHHHKHPEADZ_140003184.cpp" />
    <ClCompile Include="source\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.cpp" />
    <ClCompile Include="source\j_ResetAggroCMonsterAggroMgrQEAAXXZ_140004ECB.cpp" />
    <ClCompile Include="source\j_RespawnMonsterCMapOperationAEAAXXZ_140012B5C.cpp" />
    <ClCompile Include="source\j_SaveAllBossSchedule_MapQEAA_NXZ_14000A08D.cpp" />
    <ClCompile Include="source\j_ScrollMapDownCMapExtendQEAAXHHZ_140006D39.cpp" />
    <ClCompile Include="source\j_ScrollMapLeftCMapExtendQEAAXHZ_140012797.cpp" />
    <ClCompile Include="source\j_ScrollMapRightCMapExtendQEAAXHHZ_14000F57E.cpp" />
    <ClCompile Include="source\j_ScrollMapUpCMapExtendQEAAXHZ_1400067FD.cpp" />
    <ClCompile Include="source\j_SearchChildMonCMonsterHierarchyQEAAHPEAVCMonster_1400104E7.cpp" />
    <ClCompile Include="source\j_SearchEmptyMonsterYAPEAVCMonster_NZ_140011F86.cpp" />
    <ClCompile Include="source\j_SearchNearMonsterByDistanceCMonsterHelperSAPEAVC_140011BDA.cpp" />
    <ClCompile Include="source\j_SearchNearMonsterCMonsterHelperSAKPEAVCMonsterPE_14000D125.cpp" />
    <ClCompile Include="source\j_SearchPathACPathMgrQEAAHPEAVCMonsterQEAMHZ_140003D5A.cpp" />
    <ClCompile Include="source\j_SearchPatrollPathDfAIMgrSAXPEAVCMonsterAIPEAVCMo_140003FE9.cpp" />
    <ClCompile Include="source\j_SearchPatrolMovePosCMonsterHelperSAHPEAVCMonster_140007883.cpp" />
    <ClCompile Include="source\j_SelectObjectCMapDisplayQEAAPEAVCGameObjectPEAVCP_140006217.cpp" />
    <ClCompile Include="source\j_SetBlock_mon_blockQEAA_NPEAU_mon_block_fldPEAVCM_14000731F.cpp" />
    <ClCompile Include="source\j_SetCurBspMapCGameObjectQEAA_NPEAVCMapDataZ_14000F1EB.cpp" />
    <ClCompile Include="source\j_SetDefPartCMonsterQEAAXPEAU_monster_fldZ_140004269.cpp" />
    <ClCompile Include="source\j_SetDummyPointCDummyDrawQEAAXPEAVCMapDataPEAMHPEA_1400077DE.cpp" />
    <ClCompile Include="source\j_SetDummyRangeCDummyDrawQEAAXPEAVCMapDataPEAM111H_140011324.cpp" />
    <ClCompile Include="source\j_SetEmotionStateCMonsterQEAAXEZ_14000709A.cpp" />
    <ClCompile Include="source\j_SetEventRespawnCMonsterEventRespawnQEAA_NXZ_1400071D5.cpp" />
    <ClCompile Include="source\j_SetFlagCMoveMapLimitRightInfoQEAAXHH_NZ_14000E052.cpp" />
    <ClCompile Include="source\j_SetFlagCMoveMapLimitRightPortalUEAAXH_NZ_140004241.cpp" />
    <ClCompile Include="source\j_SetFlagCMoveMapLimitRightUEAAXH_NZ_14000FD71.cpp" />
    <ClCompile Include="source\j_SetGroupMapPointRequestCNetworkEXAEAA_NHPEADZ_140003201.cpp" />
    <ClCompile Include="source\j_SetHPCMonsterUEAA_NH_NZ_1400129DB.cpp" />
    <ClCompile Include="source\j_SetMoveTypeCMonsterQEAAXEZ_1400119DC.cpp" />
    <ClCompile Include="source\j_SetMyDataCMonsterAIUEAAHPEAVUsStateTBLPEAXZ_140012EE0.cpp" />
    <ClCompile Include="source\j_SetParentCMonsterHierarchyQEAAHPEAVCMonsterZ_140009B6F.cpp" />
    <ClCompile Include="source\j_SetRoomMapInfoCGuildRoomInfoQEAAXPEAVCMapDataGEE_14000410B.cpp" />
    <ClCompile Include="source\j_SetStunCMonsterUEAAX_NZ_140008E7C.cpp" />
    <ClCompile Include="source\j_set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__140006654.cpp" />
    <ClCompile Include="source\j_ShortRankDelayCMonsterAggroMgrQEAAXKZ_14000B2FD.cpp" />
    <ClCompile Include="source\j_ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectD_14000AB8C.cpp" />
    <ClCompile Include="source\j_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1400080A3.cpp" />
    <ClCompile Include="source\j_sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCM_140004D81.cpp" />
    <ClCompile Include="source\j_sizevectorVCMoveMapLimitRightInfoVallocatorVCMov_14000D3D7.cpp" />
    <ClCompile Include="source\j_size_add_char_result_zoneQEAAHXZ_1400047E1.cpp" />
    <ClCompile Include="source\j_size_del_char_result_zoneQEAAHXZ_14000C180.cpp" />
    <ClCompile Include="source\j_size_enter_world_request_wracQEAAHXZ_14000B924.cpp" />
    <ClCompile Include="source\j_size_enter_world_result_zoneQEAAHXZ_14000B06E.cpp" />
    <ClCompile Include="source\j_size_moveout_user_result_zoneQEAAHXZ_140006D34.cpp" />
    <ClCompile Include="source\j_size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400015A5.cpp" />
    <ClCompile Include="source\j_size_move_to_own_stonemap_result_zoclQEAAHXZ_140007C7F.cpp" />
    <ClCompile Include="source\j_size_open_world_request_wracQEAAHXZ_140009F61.cpp" />
    <ClCompile Include="source\j_size_reged_char_result_zoneQEAAHXZ_140003044.cpp" />
    <ClCompile Include="source\j_size_sel_char_result_zoneQEAAHXZ_140012468.cpp" />
    <ClCompile Include="source\j_size_server_notify_inform_zoneQEAAHXZ_140005187.cpp" />
    <ClCompile Include="source\j_size_start_world_request_wracQEAAHXZ_14000BF55.cpp" />
    <ClCompile Include="source\j_size_stop_world_request_wracQEAAHXZ_140005D3F.cpp" />
    <ClCompile Include="source\j_size_target_monster_contsf_allinform_zoclQEAAHXZ_14000B2D0.cpp" />
    <ClCompile Include="source\j_size_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14001127A.cpp" />
    <ClCompile Include="source\j_size_world_account_ping_wracQEAAHXZ_14000276B.cpp" />
    <ClCompile Include="source\j_StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0_140008896.cpp" />
    <ClCompile Include="source\j_StartScreenPointCMapExtendQEAAXPEAVCPointPEAVCMa_1400055EC.cpp" />
    <ClCompile Include="source\j_StopEventSetCMonsterEventSetQEAA_NPEAD0Z_140012E54.cpp" />
    <ClCompile Include="source\j_StopRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_140009377.cpp" />
    <ClCompile Include="source\j_SubProcForceMoveHQCMoveMapLimitInfoPortalAEAAXXZ_140006555.cpp" />
    <ClCompile Include="source\j_SubProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAE_140009994.cpp" />
    <ClCompile Include="source\j_SubProcNotifyForceMoveHQCMoveMapLimitInfoPortalA_140009683.cpp" />
    <ClCompile Include="source\j_TakeCGravityStoneRegenerQEAAEPEAVCMapDataPEAMZ_140007BDA.cpp" />
    <ClCompile Include="source\j_TransPortCMonsterHelperSAXPEAVCMonsterQEAMZ_14000E589.cpp" />
    <ClCompile Include="source\j_type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePE_140004C2D.cpp" />
    <ClCompile Include="source\j_unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1st_14000D84B.cpp" />
    <ClCompile Include="source\j_unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1s_14000F281.cpp" />
    <ClCompile Include="source\j_unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1std_140001276.cpp" />
    <ClCompile Include="source\j_unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1s_140007C61.cpp" />
    <ClCompile Include="source\j_unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1_140004D5E.cpp" />
    <ClCompile Include="source\j_unregist_from_mapAutominePersonalQEAA_NEZ_14000267B.cpp" />
    <ClCompile Include="source\j_UpdateLookAtPosCMonsterQEAAXQEAMZ_140008F94.cpp" />
    <ClCompile Include="source\j_UpdateLookAtPosCMonsterQEAAXXZ_140010514.cpp" />
    <ClCompile Include="source\j_UpdateSecterListCMapDataQEAA_NPEAVCGameObjectKKZ_1400051EB.cpp" />
    <ClCompile Include="source\j_UpdateSFContCMonsterUEAAXXZ_1400114AA.cpp" />
    <ClCompile Include="source\j_UpdateTabCMapTabQEAAXXZ_14000A051.cpp" />
    <ClCompile Include="source\j_upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6AP_14000A439.cpp" />
    <ClCompile Include="source\j_upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua__14000F0FB.cpp" />
    <ClCompile Include="source\j_wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_14000B4BA.cpp" />
    <ClCompile Include="source\j_wa_ExitWorldYAXPEAU_CLIDZ_14000FFAB.cpp" />
    <ClCompile Include="source\j_WorldExitInformCNetworkEXAEAA_NKPEADZ_14000CDD8.cpp" />
    <ClCompile Include="source\j_WorldMsgInformCNetworkEXAEAA_NKPEADZ_1400138DB.cpp" />
    <ClCompile Include="source\j_WorldServiceInformCNetworkEXAEAA_NKPEADZ_14000FC36.cpp" />
    <ClCompile Include="source\j_Y_Vector_const_iteratorPEAVCMoveMapLimitRightVal_1400092C3.cpp" />
    <ClCompile Include="source\j_Y_Vector_iteratorPEAVCMoveMapLimitRightVallocato_14000622B.cpp" />
    <ClCompile Include="source\j_ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_14000D19D.cpp" />
    <ClCompile Include="source\j__AllocatePEAVCMoveMapLimitInfostdYAPEAPEAVCMoveM_14000F452.cpp" />
    <ClCompile Include="source\j__AllocatePEAVCMoveMapLimitRightstdYAPEAPEAVCMove_1400131F6.cpp" />
    <ClCompile Include="source\j__AllocateU_Node_Tree_nodV_Tmap_traitsVbasic_stri_14000FF60.cpp" />
    <ClCompile Include="source\j__AllocateVCMoveMapLimitRightInfostdYAPEAVCMoveMa_14000F9BB.cpp" />
    <ClCompile Include="source\j__Assign_nvectorPEAVCMoveMapLimitInfoVallocatorPE_1400091F1.cpp" />
    <ClCompile Include="source\j__Assign_nvectorVCMoveMapLimitRightInfoVallocator_14000D90E.cpp" />
    <ClCompile Include="source\j__BossBirthWriteLogCMonsterQEAAXXZ_14000B582.cpp" />
    <ClCompile Include="source\j__BossDieWriteLog_EndCMonsterQEAAXXZ_140012710.cpp" />
    <ClCompile Include="source\j__BossDieWriteLog_StartCMonsterQEAAXEPEAVCGameObj_140004D40.cpp" />
    <ClCompile Include="source\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140001E65.cpp" />
    <ClCompile Include="source\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140004FA7.cpp" />
    <ClCompile Include="source\j__BuyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_140012251.cpp" />
    <ClCompile Include="source\j__BuyvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_14000AD8F.cpp" />
    <ClCompile Include="source\j__BuyvectorVCMoveMapLimitRightInfoVallocatorVCMov_14000A38A.cpp" />
    <ClCompile Include="source\j__CheckDestMonsterLimitLvYA_NHHEZ_140011400.cpp" />
    <ClCompile Include="source\j__Color_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140002B85.cpp" />
    <ClCompile Include="source\j__ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic__14000A7B3.cpp" />
    <ClCompile Include="source\j__ConstructPEAVCMoveMapLimitRightPEAV1stdYAXPEAPE_14000378D.cpp" />
    <ClCompile Include="source\j__ConstructVCMoveMapLimitRightInfoV1stdYAXPEAVCMo_1400022D9.cpp" />
    <ClCompile Include="source\j__Copy_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140004BD8.cpp" />
    <ClCompile Include="source\j__Copy_backward_optPEAPEAVCMoveMapLimitRightPEAPE_140010A5A.cpp" />
    <ClCompile Include="source\j__Copy_backward_optPEAVCMoveMapLimitRightInfoPEAV_140010E3D.cpp" />
    <ClCompile Include="source\j__Copy_optPEAPEAVCMoveMapLimitInfoPEAPEAV1Urandom_140003F5D.cpp" />
    <ClCompile Include="source\j__Copy_optPEAPEAVCMoveMapLimitRightPEAPEAV1Urando_14000B1FE.cpp" />
    <ClCompile Include="source\j__Copy_optPEAVCMoveMapLimitRightInfoPEAV1Urandom__14000F128.cpp" />
    <ClCompile Include="source\j__CreateMonYAPEAVCMonsterPEAD0MMMZ_1400074BE.cpp" />
    <ClCompile Include="source\j__Decconst_iterator_TreeV_Tmap_traitsVbasic_strin_140012B48.cpp" />
    <ClCompile Include="source\j__DestroyPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140011798.cpp" />
    <ClCompile Include="source\j__DestroyPEAVCMoveMapLimitRightstdYAXPEAPEAVCMove_140006AAF.cpp" />
    <ClCompile Include="source\j__DestroySDMCMonsterSAXXZ_14000CCA7.cpp" />
    <ClCompile Include="source\j__DestroyU_Node_Tree_nodV_Tmap_traitsVbasic_strin_14000BD57.cpp" />
    <ClCompile Include="source\j__DestroyVCMoveMapLimitRightInfostdYAXPEAVCMoveMa_140001451.cpp" />
    <ClCompile Include="source\j__DestroyvectorPEAVCMoveMapLimitInfoVallocatorPEA_1400083FA.cpp" />
    <ClCompile Include="source\j__DestroyvectorPEAVCMoveMapLimitRightVallocatorPE_14000D94A.cpp" />
    <ClCompile Include="source\j__DestroyvectorVCMoveMapLimitRightInfoVallocatorV_140012C65.cpp" />
    <ClCompile Include="source\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_140004F20.cpp" />
    <ClCompile Include="source\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_14000F69B.cpp" />
    <ClCompile Include="source\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_140008323.cpp" />
    <ClCompile Include="source\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_1400090E3.cpp" />
    <ClCompile Include="source\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_140005E98.cpp" />
    <ClCompile Include="source\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_14000C0DB.cpp" />
    <ClCompile Include="source\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.cpp" />
    <ClCompile Include="source\j__ECCircleZoneUEAAPEAXIZ_1400060EB.cpp" />
    <ClCompile Include="source\j__ECMapDataUEAAPEAXIZ_140011BFD.cpp" />
    <ClCompile Include="source\j__ECMapDisplayUEAAPEAXIZ_0_140007923.cpp" />
    <ClCompile Include="source\j__ECMapDisplayUEAAPEAXIZ_140003332.cpp" />
    <ClCompile Include="source\j__ECMapTabUEAAPEAXIZ_0_1400076B7.cpp" />
    <ClCompile Include="source\j__ECMapTabUEAAPEAXIZ_140001B8B.cpp" />
    <ClCompile Include="source\j__ECMonsterEventSetUEAAPEAXIZ_0_14000DA53.cpp" />
    <ClCompile Include="source\j__ECMonsterEventSetUEAAPEAXIZ_14000D7C9.cpp" />
    <ClCompile Include="source\j__ECMonsterUEAAPEAXIZ_14000546B.cpp" />
    <ClCompile Include="source\j__Erase_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140005D94.cpp" />
    <ClCompile Include="source\j__FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVC_14000FA6F.cpp" />
    <ClCompile Include="source\j__FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1400126CF.cpp" />
    <ClCompile Include="source\j__FillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMove_14000FB8C.cpp" />
    <ClCompile Include="source\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAP_140001EF6.cpp" />
    <ClCompile Include="source\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1Urandom_ac_1400054C5.cpp" />
    <ClCompile Include="source\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1stdYAXPEA_140013C91.cpp" />
    <ClCompile Include="source\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1Urandom_a_140012F85.cpp" />
    <ClCompile Include="source\j__FindV_Vector_iteratorPEAVCMoveMapLimitRightVall_14000AE43.cpp" />
    <ClCompile Include="source\j__GBossSchedule_MapQEAAPEAXIZ_140002BFD.cpp" />
    <ClCompile Include="source\j__GCCircleZoneUEAAPEAXIZ_140009CEB.cpp" />
    <ClCompile Include="source\j__GCMapDataUEAAPEAXIZ_1400069B5.cpp" />
    <ClCompile Include="source\j__GCMapOperationUEAAPEAXIZ_0_14001042E.cpp" />
    <ClCompile Include="source\j__GCMapOperationUEAAPEAXIZ_14000A984.cpp" />
    <ClCompile Include="source\j__GCMonsterAIUEAAPEAXIZ_0_140013697.cpp" />
    <ClCompile Include="source\j__GCMonsterAIUEAAPEAXIZ_14000E4CB.cpp" />
    <ClCompile Include="source\j__GCMonsterEventRespawnUEAAPEAXIZ_0_14000C635.cpp" />
    <ClCompile Include="source\j__GCMonsterEventRespawnUEAAPEAXIZ_14000216C.cpp" />
    <ClCompile Include="source\j__GCMonsterHierarchyUEAAPEAXIZ_0_14000E633.cpp" />
    <ClCompile Include="source\j__GCMonsterHierarchyUEAAPEAXIZ_14000B5E1.cpp" />
    <ClCompile Include="source\j__GCMonsterUEAAPEAXIZ_1400109BF.cpp" />
    <ClCompile Include="source\j__GCMoveMapLimitInfoQEAAPEAXIZ_14000E200.cpp" />
    <ClCompile Include="source\j__GCMoveMapLimitManagerQEAAPEAXIZ_1400042B9.cpp" />
    <ClCompile Include="source\j__GCMoveMapLimitRightInfoQEAAPEAXIZ_140012FC6.cpp" />
    <ClCompile Include="source\j__GCMoveMapLimitRightQEAAPEAXIZ_1400129E5.cpp" />
    <ClCompile Include="source\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.cpp" />
    <ClCompile Include="source\j__GetBaseClassCMapTabKAPEAUCRuntimeClassXZ_140006366.cpp" />
    <ClCompile Include="source\j__GetBlinkNodeCMonsterAggroMgrIEAAPEAUCAggroNodeX_14000BFC3.cpp" />
    <ClCompile Include="source\j__GetMonsterContTimeYAGEEZ_14000AF9C.cpp" />
    <ClCompile Include="source\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_0_14000E74B.cpp" />
    <ClCompile Include="source\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_140005C36.cpp" />
    <ClCompile Include="source\j__G_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_1400011AE.cpp" />
    <ClCompile Include="source\j__G__change_monsterQEAAPEAXIZ_140002572.cpp" />
    <ClCompile Include="source\j__Incconst_iterator_TreeV_Tmap_traitsVbasic_strin_14000FA0B.cpp" />
    <ClCompile Include="source\j__Insert_nvectorPEAVCMoveMapLimitInfoVallocatorPE_140012D5A.cpp" />
    <ClCompile Include="source\j__Insert_nvectorPEAVCMoveMapLimitRightVallocatorP_140001749.cpp" />
    <ClCompile Include="source\j__Insert_nvectorVCMoveMapLimitRightInfoVallocator_14000B460.cpp" />
    <ClCompile Include="source\j__Insert_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000ECAA.cpp" />
    <ClCompile Include="source\j__Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400042E6.cpp" />
    <ClCompile Include="source\j__Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_a_14001348A.cpp" />
    <ClCompile Include="source\j__Iter_catPEAPEAVCMoveMapLimitRightstdYAAUrandom__14000A146.cpp" />
    <ClCompile Include="source\j__Iter_randomPEAPEAVCMoveMapLimitInfoPEAPEAV1stdY_14000CC9D.cpp" />
    <ClCompile Include="source\j__Iter_randomPEAPEAVCMoveMapLimitRightPEAPEAV1std_14000E4B2.cpp" />
    <ClCompile Include="source\j__Iter_randomPEAVCMoveMapLimitRightInfoPEAV1stdYA_140008E72.cpp" />
    <ClCompile Include="source\j__Key_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140011793.cpp" />
    <ClCompile Include="source\j__Kfn_Tmap_traitsVbasic_stringDUchar_traitsDstdVa_14000D7D8.cpp" />
    <ClCompile Include="source\j__Lbound_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1400019DD.cpp" />
    <ClCompile Include="source\j__Left_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000543E.cpp" />
    <ClCompile Include="source\j__Lmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000DEC7.cpp" />
    <ClCompile Include="source\j__LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_14000C162.cpp" />
    <ClCompile Include="source\j__LoadPortalCMapDataAEAA_NPEADZ_14000ADCB.cpp" />
    <ClCompile Include="source\j__LoadQuestCMapDataAEAA_NPEADZ_140009AB6.cpp" />
    <ClCompile Include="source\j__LoadResourceCMapDataAEAA_NPEADZ_140001500.cpp" />
    <ClCompile Include="source\j__LoadSafeCMapDataAEAA_NPEADZ_140001762.cpp" />
    <ClCompile Include="source\j__LoadStartCMapDataAEAA_NPEADZ_1400059C0.cpp" />
    <ClCompile Include="source\j__LoadStoreDummyCMapDataAEAA_NPEADZ_140003ACB.cpp" />
    <ClCompile Include="source\j__Lrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_14000BB27.cpp" />
    <ClCompile Include="source\j__Max_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140009DC2.cpp" />
    <ClCompile Include="source\j__Min_TreeV_Tmap_traitsVbasic_stringDUchar_traits_1400133A9.cpp" />
    <ClCompile Include="source\j__Move_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140002347.cpp" />
    <ClCompile Include="source\j__Move_backward_optPEAPEAVCMoveMapLimitRightPEAPE_14000B2B2.cpp" />
    <ClCompile Include="source\j__Move_backward_optPEAVCMoveMapLimitRightInfoPEAV_140004570.cpp" />
    <ClCompile Include="source\j__Move_catPEAPEAVCMoveMapLimitInfostdYAAU_Undefin_14000BC71.cpp" />
    <ClCompile Include="source\j__Move_catPEAPEAVCMoveMapLimitRightstdYAAU_Undefi_140008D19.cpp" />
    <ClCompile Include="source\j__Move_catPEAVCMoveMapLimitRightInfostdYAAU_Undef_14000DADA.cpp" />
    <ClCompile Include="source\j__Mynodeconst_iterator_TreeV_Tmap_traitsVbasic_st_140004E62.cpp" />
    <ClCompile Include="source\j__Myval_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400099C6.cpp" />
    <ClCompile Include="source\j__Parent_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000E4DA.cpp" />
    <ClCompile Include="source\j__Ptr_catPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAAU__140011D1A.cpp" />
    <ClCompile Include="source\j__Ptr_catPEAPEAVCMoveMapLimitRightPEAPEAV1stdYAAU_14000409D.cpp" />
    <ClCompile Include="source\j__Ptr_catPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_N_140005272.cpp" />
    <ClCompile Include="source\j__Ptr_catV_Vector_const_iteratorPEAVCMoveMapLimit_140010640.cpp" />
    <ClCompile Include="source\j__Right_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000B753.cpp" />
    <ClCompile Include="source\j__Rmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140003E18.cpp" />
    <ClCompile Include="source\j__Root_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000949E.cpp" />
    <ClCompile Include="source\j__Rrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140013926.cpp" />
    <ClCompile Include="source\j__ShortRankCMonsterAggroMgrIEAAXXZ_140004381.cpp" />
    <ClCompile Include="source\j__TidyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140010DB6.cpp" />
    <ClCompile Include="source\j__TidyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013971.cpp" />
    <ClCompile Include="source\j__TidyvectorVCMoveMapLimitRightInfoVallocatorVCMo_140013F25.cpp" />
    <ClCompile Include="source\j__Tidy_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140007BAD.cpp" />
    <ClCompile Include="source\j__UcopyV_Vector_const_iteratorPEAVCMoveMapLimitRi_14001024E.cpp" />
    <ClCompile Include="source\j__UfillvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400104D8.cpp" />
    <ClCompile Include="source\j__UfillvectorPEAVCMoveMapLimitRightVallocatorPEAV_14000CF5E.cpp" />
    <ClCompile Include="source\j__UfillvectorVCMoveMapLimitRightInfoVallocatorVCM_140013ACA.cpp" />
    <ClCompile Include="source\j__UmovePEAPEAVCMoveMapLimitInfovectorPEAVCMoveMap_14000C469.cpp" />
    <ClCompile Include="source\j__UmovePEAPEAVCMoveMapLimitRightvectorPEAVCMoveMa_14000E98A.cpp" />
    <ClCompile Include="source\j__UmovePEAVCMoveMapLimitRightInfovectorVCMoveMapL_1400126D4.cpp" />
    <ClCompile Include="source\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitInfo_140012AA8.cpp" />
    <ClCompile Include="source\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitRigh_14000807B.cpp" />
    <ClCompile Include="source\j__Unchecked_move_backwardPEAVCMoveMapLimitRightIn_14000C856.cpp" />
    <ClCompile Include="source\j__XlenvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140008D91.cpp" />
    <ClCompile Include="source\j__XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000B005.cpp" />
    <ClCompile Include="source\j__XlenvectorVCMoveMapLimitRightInfoVallocatorVCMo_140008DFA.cpp" />
    <ClCompile Include="source\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.cpp" />
    <ClCompile Include="source\LinkEventRespawnCMonsterQEAAXPEAU_event_respawnZ_1402A78D0.cpp" />
    <ClCompile Include="source\LinkEventSetCMonsterQEAAXPEAU_event_setZ_1402AA080.cpp" />
    <ClCompile Include="source\LoadAllBossSchedule_MapQEAA_NXZ_14041A070.cpp" />
    <ClCompile Include="source\LoadDummyCMapDataQEAA_NPEADPEAU_dummy_positionZ_140184250.cpp" />
    <ClCompile Include="source\LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.cpp" />
    <ClCompile Include="source\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.cpp" />
    <ClCompile Include="source\LoadEventSetLootingCMonsterEventSetQEAA_NXZ_1402A91E0.cpp" />
    <ClCompile Include="source\LoadINICMoveMapLimitInfoPortalAEAA_NXZ_1403A56F0.cpp" />
    <ClCompile Include="source\LoadLightMapYAXPEADZ_1405023A0.cpp" />
    <ClCompile Include="source\LoadMapsCMapOperationAEAA_NXZ_140196750.cpp" />
    <ClCompile Include="source\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.cpp" />
    <ClCompile Include="source\LoadR3TLightMapYAPEAPEAU_LIGHTMAPPEAUR3TextureW4_D_140500910.cpp" />
    <ClCompile Include="source\LoadRegionCMapOperationAEAA_NXZ_140196C40.cpp" />
    <ClCompile Include="source\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.cpp" />
    <ClCompile Include="source\LoopCMonsterUEAAXXZ_140147C90.cpp" />
    <ClCompile Include="source\LoopCMoveMapLimitInfoListQEAAXXZ_1403A54D0.cpp" />
    <ClCompile Include="source\LoopCMoveMapLimitInfoPortalUEAAXXZ_1403A43D0.cpp" />
    <ClCompile Include="source\LoopCMoveMapLimitInfoUEAAXXZ_1403A6F40.cpp" />
    <ClCompile Include="source\LoopCMoveMapLimitManagerQEAAXXZ_1403A1B40.cpp" />
    <ClCompile Include="source\LoopCWorldScheduleQEAAXXZ_1403F3A30.cpp" />
    <ClCompile Include="source\lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_t_14018EB80.cpp" />
    <ClCompile Include="source\lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlu_14040AFF0.cpp" />
    <ClCompile Include="source\MakeMipMapYAXGGPEAG0Z_1405005B0.cpp" />
    <ClCompile Include="source\MakeMipMapYAXGGPEAGPEAEZ_140500770.cpp" />
    <ClCompile Include="source\MapChannelEqualityComparisonFilterCryptoPPAEBAIAEB_140654950.cpp" />
    <ClCompile Include="source\MapViewOfFile_0_140676E1C.cpp" />
    <ClCompile Include="source\max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KXZ_1403A31C0.cpp" />
    <ClCompile Include="source\max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_KXZ_1403B0CC0.cpp" />
    <ClCompile Include="source\max_sizeallocatorVCMoveMapLimitRightInfostdQEBA_KX_1403A3230.cpp" />
    <ClCompile Include="source\max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403A2A70.cpp" />
    <ClCompile Include="source\max_sizevectorPEAVCMoveMapLimitRightVallocatorPEAV_1403AFD20.cpp" />
    <ClCompile Include="source\max_sizevectorVCMoveMapLimitRightInfoVallocatorVCM_1403A2CE0.cpp" />
    <ClCompile Include="source\max_size_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1401913D0.cpp" />
    <ClCompile Include="source\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.cpp" />
    <ClCompile Include="source\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.cpp" />
    <ClCompile Include="source\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.cpp" />
    <ClCompile Include="source\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.cpp" />
    <ClCompile Include="source\MoveLimitMapZoneRequestCMoveMapLimitManagerQEAA_NH_1403A1A60.cpp" />
    <ClCompile Include="source\MoveScreenPointCMapExtendQEAAXPEAVCPointZ_1401A1730.cpp" />
    <ClCompile Include="source\MoveToOwnStoneMapRequestCNetworkEXAEAA_NHPEADZ_1401CF500.cpp" />
    <ClCompile Include="source\nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_1404082A0.cpp" />
    <ClCompile Include="source\NPCDialogRequestCNetworkEXAEAA_NHPEADZ_1401CF580.cpp" />
    <ClCompile Include="source\NPCQuestListRequestCNetworkEXAEAA_NHPEADZ_1401CEF70.cpp" />
    <ClCompile Include="source\NPCQuestRequestCNetworkEXAEAA_NHPEADZ_1401CEE40.cpp" />
    <ClCompile Include="source\NPCWatchingRequestCNetworkEXAEAA_NHPEADZ_1401CF690.cpp" />
    <ClCompile Include="source\OffDisplayCMapDisplayQEAA_NXZ_14019EF70.cpp" />
    <ClCompile Include="source\OnButtonMapchangeCMapTabIEAAXXZ_14002EC30.cpp" />
    <ClCompile Include="source\OnButtonMonsterCGameServerViewQEAAXXZ_14002B190.cpp" />
    <ClCompile Include="source\OnChildMonsterCreateCMonsterHierarchyQEAAXPEAU_mon_140157450.cpp" />
    <ClCompile Include="source\OnChildMonsterDestroyCMonsterHierarchyQEAAXXZ_140157870.cpp" />
    <ClCompile Include="source\OnChildRegenLoopCMonsterHierarchyQEAAXXZ_140157590.cpp" />
    <ClCompile Include="source\OnDisplayCMapDisplayQEAA_NPEAVCMapDataGZ_14019EEB0.cpp" />
    <ClCompile Include="source\OnLoopCMapDataQEAAXXZ_140181510.cpp" />
    <ClCompile Include="source\OnLoopCMapOperationQEAAXXZ_140196F30.cpp" />
    <ClCompile Include="source\OnSetActiveCMapTabUEAAHXZ_14002E680.cpp" />
    <ClCompile Include="source\OpenFileMappingA_0_140676E28.cpp" />
    <ClCompile Include="source\OpenMapCMapDataQEAA_NPEADPEAU_map_fld_NZ_140180D80.cpp" />
    <ClCompile Include="source\OpenWorldFailureResultCNetworkEXAEAA_NKPEADZ_1401C0420.cpp" />
    <ClCompile Include="source\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.cpp" />
    <ClCompile Include="source\OutOfSecCMonsterUEAAXXZ_14014B990.cpp" />
    <ClCompile Include="source\PassOneStepCWorldScheduleQEAAXXZ_1403F3E30.cpp" />
    <ClCompile Include="source\pc_AlterWorldServiceCMainThreadQEAAX_NZ_1401F61B0.cpp" />
    <ClCompile Include="source\pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_1401F5B30.cpp" />
    <ClCompile Include="source\pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1401F56F0.cpp" />
    <ClCompile Include="source\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.cpp" />
    <ClCompile Include="source\PopChildMonAllCMonsterHierarchyQEAAXXZ_140157BE0.cpp" />
    <ClCompile Include="source\PopChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_140157AA0.cpp" />
    <ClCompile Include="source\ProcessCMonsterAggroMgrQEAAXXZ_14015E120.cpp" />
    <ClCompile Include="source\ProcForceMoveHQCMoveMapLimitInfoPortalAEAAEHPEADPE_1403A44B0.cpp" />
    <ClCompile Include="source\ProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHPEAD_1403A47B0.cpp" />
    <ClCompile Include="source\ProcUseMoveScrollCMoveMapLimitInfoPortalAEAAEHPEAD_1403A4520.cpp" />
    <ClCompile Include="source\PushChildMonCMonsterHierarchyQEAAHHPEAVCMonsterZ_140157990.cpp" />
    <ClCompile Include="source\pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCMon_14040A3F0.cpp" />
    <ClCompile Include="source\push_backvectorPEAVCMoveMapLimitRightVallocatorPEA_1403AEB10.cpp" />
    <ClCompile Include="source\push_functorPEAVCLuaSignalReActorVCMonsterlua_tink_1404089C0.cpp" />
    <ClCompile Include="source\push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAXPE_140408A80.cpp" />
    <ClCompile Include="source\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.cpp" />
    <ClCompile Include="source\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.cpp" />
    <ClCompile Include="source\readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua_St_14040A210.cpp" />
    <ClCompile Include="source\RegistCMoveMapLimitRightInfoQEAA_NHZ_1403AC960.cpp" />
    <ClCompile Include="source\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.cpp" />
    <ClCompile Include="source\ReleaseLightMapYAXXZ_140502480.cpp" />
    <ClCompile Include="source\RequestCMoveMapLimitInfoListQEAAEHHHKHPEADPEAVCMov_1403A5F80.cpp" />
    <ClCompile Include="source\RequestCMoveMapLimitInfoPortalUEAAEHHPEADPEAVCMove_1403A4200.cpp" />
    <ClCompile Include="source\RequestCMoveMapLimitManagerQEAAEHHHKHPEADZ_1403A19D0.cpp" />
    <ClCompile Include="source\RequestElanMapUserForceMoveHQCMoveMapLimitManagerQ_140284700.cpp" />
    <ClCompile Include="source\ResetAggroCMonsterAggroMgrQEAAXXZ_14015E900.cpp" />
    <ClCompile Include="source\RespawnMonsterCMapOperationAEAAXXZ_140197190.cpp" />
    <ClCompile Include="source\SaveAllBossSchedule_MapQEAA_NXZ_140419FB0.cpp" />
    <ClCompile Include="source\ScrollMapDownCMapExtendQEAAXHHZ_1401A2000.cpp" />
    <ClCompile Include="source\ScrollMapLeftCMapExtendQEAAXHZ_1401A2120.cpp" />
    <ClCompile Include="source\ScrollMapRightCMapExtendQEAAXHHZ_1401A2220.cpp" />
    <ClCompile Include="source\ScrollMapUpCMapExtendQEAAXHZ_1401A1EF0.cpp" />
    <ClCompile Include="source\SearchChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_140157D00.cpp" />
    <ClCompile Include="source\SearchEmptyMonsterYAPEAVCMonster_NZ_140148F20.cpp" />
    <ClCompile Include="source\SearchNearMonsterByDistanceCMonsterHelperSAPEAVCMo_140159540.cpp" />
    <ClCompile Include="source\SearchNearMonsterCMonsterHelperSAKPEAVCMonsterPEAU_140158DE0.cpp" />
    <ClCompile Include="source\SearchPathACPathMgrQEAAHPEAVCMonsterQEAMHZ_140155C90.cpp" />
    <ClCompile Include="source\SearchPatrollPathDfAIMgrSAXPEAVCMonsterAIPEAVCMons_1401532F0.cpp" />
    <ClCompile Include="source\SearchPatrolMovePosCMonsterHelperSAHPEAVCMonsterAE_140159F20.cpp" />
    <ClCompile Include="source\SelectObjectCMapDisplayQEAAPEAVCGameObjectPEAVCPoi_14019F340.cpp" />
    <ClCompile Include="source\SetBlock_mon_blockQEAA_NPEAU_mon_block_fldPEAVCMap_140189E60.cpp" />
    <ClCompile Include="source\SetCurBspMapCGameObjectQEAA_NPEAVCMapDataZ_14017AF30.cpp" />
    <ClCompile Include="source\SetDefPartCMonsterQEAAXPEAU_monster_fldZ_140142B40.cpp" />
    <ClCompile Include="source\SetDummyPointCDummyDrawQEAAXPEAVCMapDataPEAMHPEAVC_14019C560.cpp" />
    <ClCompile Include="source\SetDummyRangeCDummyDrawQEAAXPEAVCMapDataPEAM111HPE_14019C7B0.cpp" />
    <ClCompile Include="source\SetEmotionStateCMonsterQEAAXEZ_1401437D0.cpp" />
    <ClCompile Include="source\SetEventRespawnCMonsterEventRespawnQEAA_NXZ_1402A5DE0.cpp" />
    <ClCompile Include="source\SetFlagCMoveMapLimitRightInfoQEAAXHH_NZ_1403ACC50.cpp" />
    <ClCompile Include="source\SetFlagCMoveMapLimitRightPortalUEAAXH_NZ_1403AC790.cpp" />
    <ClCompile Include="source\SetFlagCMoveMapLimitRightUEAAXH_NZ_1403AE500.cpp" />
    <ClCompile Include="source\SetGroupMapPointRequestCNetworkEXAEAA_NHPEADZ_1401D8360.cpp" />
    <ClCompile Include="source\SetHPCMonsterUEAA_NH_NZ_140146200.cpp" />
    <ClCompile Include="source\SetLightMapYAXJZ_1404EDF30.cpp" />
    <ClCompile Include="source\SetMapMode_0_140676F48.cpp" />
    <ClCompile Include="source\SetMoveTypeCMonsterQEAAXEZ_140143770.cpp" />
    <ClCompile Include="source\SetMyDataCMonsterAIUEAAHPEAVUsStateTBLPEAXZ_14014FB70.cpp" />
    <ClCompile Include="source\SetParentCMonsterHierarchyQEAAHPEAVCMonsterZ_140157960.cpp" />
    <ClCompile Include="source\SetRoomMapInfoCGuildRoomInfoQEAAXPEAVCMapDataGEEZ_1402E5A80.cpp" />
    <ClCompile Include="source\SetStunCMonsterUEAAX_NZ_140146130.cpp" />
    <ClCompile Include="source\SetWorldViewMatrixVSYAXQEAY03MZ_140515870.cpp" />
    <ClCompile Include="source\set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.cpp" />
    <ClCompile Include="source\ShortRankDelayCMonsterAggroMgrQEAAXKZ_14015E0C0.cpp" />
    <ClCompile Include="source\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.cpp" />
    <ClCompile Include="source\sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMove_1403A78D0.cpp" />
    <ClCompile Include="source\sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCMov_1403AE9F0.cpp" />
    <ClCompile Include="source\sizevectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A21E0.cpp" />
    <ClCompile Include="source\size_add_char_result_zoneQEAAHXZ_14011F870.cpp" />
    <ClCompile Include="source\size_del_char_result_zoneQEAAHXZ_14011F880.cpp" />
    <ClCompile Include="source\size_enter_world_request_wracQEAAHXZ_14011F240.cpp" />
    <ClCompile Include="source\size_enter_world_result_zoneQEAAHXZ_14011F250.cpp" />
    <ClCompile Include="source\size_moveout_user_result_zoneQEAAHXZ_14011FBD0.cpp" />
    <ClCompile Include="source\size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400F03D0.cpp" />
    <ClCompile Include="source\size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.cpp" />
    <ClCompile Include="source\size_open_world_request_wracQEAAHXZ_1402080C0.cpp" />
    <ClCompile Include="source\size_reged_char_result_zoneQEAAHXZ_14011F6F0.cpp" />
    <ClCompile Include="source\size_sel_char_result_zoneQEAAHXZ_14011F8D0.cpp" />
    <ClCompile Include="source\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.cpp" />
    <ClCompile Include="source\size_start_world_request_wracQEAAHXZ_1402080D0.cpp" />
    <ClCompile Include="source\size_stop_world_request_wracQEAAHXZ_1402080E0.cpp" />
    <ClCompile Include="source\size_target_monster_contsf_allinform_zoclQEAAHXZ_1400F0140.cpp" />
    <ClCompile Include="source\size_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_14018FFA0.cpp" />
    <ClCompile Include="source\size_world_account_ping_wracQEAAHXZ_1402080F0.cpp" />
    <ClCompile Include="source\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.cpp" />
    <ClCompile Include="source\StartScreenPointCMapExtendQEAAXPEAVCPointPEAVCMapD_1401A1670.cpp" />
    <ClCompile Include="source\StopEventSetCMonsterEventSetQEAA_NPEAD0Z_1402A8870.cpp" />
    <ClCompile Include="source\StopRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6E50.cpp" />
    <ClCompile Include="source\SubProcForceMoveHQCMoveMapLimitInfoPortalAEAAXXZ_1403A4A50.cpp" />
    <ClCompile Include="source\SubProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHP_1403A4D10.cpp" />
    <ClCompile Include="source\SubProcNotifyForceMoveHQCMoveMapLimitInfoPortalAEA_1403A4880.cpp" />
    <ClCompile Include="source\TakeCGravityStoneRegenerQEAAEPEAVCMapDataPEAMZ_14012EB20.cpp" />
    <ClCompile Include="source\TransPortCMonsterHelperSAXPEAVCMonsterQEAMZ_14015A310.cpp" />
    <ClCompile Include="source\type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAV_14040B0E0.cpp" />
    <ClCompile Include="source\unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1stde_1403AAB40.cpp" />
    <ClCompile Include="source\unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1std_1403B1C00.cpp" />
    <ClCompile Include="source\unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1stdex_1403B21A0.cpp" />
    <ClCompile Include="source\unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1std_1403ABFD0.cpp" />
    <ClCompile Include="source\unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1st_1403B3280.cpp" />
    <ClCompile Include="source\UnLightMappingTex1YAXXZ_1404EFB90.cpp" />
    <ClCompile Include="source\UnmapViewOfFile_0_140676E16.cpp" />
    <ClCompile Include="source\unregist_from_mapAutominePersonalQEAA_NEZ_1402DB110.cpp" />
    <ClCompile Include="source\UpdateLookAtPosCMonsterQEAAXQEAMZ_140148220.cpp" />
    <ClCompile Include="source\UpdateLookAtPosCMonsterQEAAXXZ_140148090.cpp" />
    <ClCompile Include="source\UpdateSecterListCMapDataQEAA_NPEAVCGameObjectKKZ_140184BE0.cpp" />
    <ClCompile Include="source\UpdateSFContCMonsterUEAAXXZ_140147170.cpp" />
    <ClCompile Include="source\UpdateTabCMapTabQEAAXXZ_14002E6E0.cpp" />
    <ClCompile Include="source\upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6APEA_14040A3A0.cpp" />
    <ClCompile Include="source\upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua_ti_14040A260.cpp" />
    <ClCompile Include="source\wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.cpp" />
    <ClCompile Include="source\wa_ExitWorldYAXPEAU_CLIDZ_140046190.cpp" />
    <ClCompile Include="source\WorldExitInformCNetworkEXAEAA_NKPEADZ_1401C09A0.cpp" />
    <ClCompile Include="source\WorldMsgInformCNetworkEXAEAA_NKPEADZ_1401C09F0.cpp" />
    <ClCompile Include="source\WorldServiceInformCNetworkEXAEAA_NKPEADZ_1401C0940.cpp" />
    <ClCompile Include="source\Y_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B17C0.cpp" />
    <ClCompile Include="source\Y_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B1550.cpp" />
    <ClCompile Include="source\ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_1401C3D90.cpp" />
    <ClCompile Include="source\_AllocatePEAVCMoveMapLimitInfostdYAPEAPEAVCMoveMap_1403A3390.cpp" />
    <ClCompile Include="source\_AllocatePEAVCMoveMapLimitRightstdYAPEAPEAVCMoveMa_1403B20F0.cpp" />
    <ClCompile Include="source\_AllocateU_Node_Tree_nodV_Tmap_traitsVbasic_string_140192B40.cpp" />
    <ClCompile Include="source\_AllocateVCMoveMapLimitRightInfostdYAPEAVCMoveMapL_1403A34C0.cpp" />
    <ClCompile Include="source\_Assign_nvectorPEAVCMoveMapLimitInfoVallocatorPEAV_1403A84E0.cpp" />
    <ClCompile Include="source\_Assign_nvectorVCMoveMapLimitRightInfoVallocatorVC_1403AF5E0.cpp" />
    <ClCompile Include="source\_BossBirthWriteLogCMonsterQEAAXXZ_140143910.cpp" />
    <ClCompile Include="source\_BossDieWriteLog_EndCMonsterQEAAXXZ_1401440C0.cpp" />
    <ClCompile Include="source\_BossDieWriteLog_StartCMonsterQEAAXEPEAVCGameObjec_1401439D0.cpp" />
    <ClCompile Include="source\_BossSchedule_Map_BossSchedule_Map__1_dtor0_14041B480.cpp" />
    <ClCompile Include="source\_Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191820.cpp" />
    <ClCompile Include="source\_Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140193E70.cpp" />
    <ClCompile Include="source\_BuyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMove_1403A2350.cpp" />
    <ClCompile Include="source\_BuyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMov_1403AF3E0.cpp" />
    <ClCompile Include="source\_BuyvectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A25B0.cpp" />
    <ClCompile Include="source\_CDisplayCreateSurfaceFromBitmap__1_dtor0_140433DE0.cpp" />
    <ClCompile Include="source\_CDisplayShowBitmap__1_dtor0_140434370.cpp" />
    <ClCompile Include="source\_CheckDestMonsterLimitLvYA_NHHEZ_140099540.cpp" />
    <ClCompile Include="source\_CMainThreadLoadWorldInfoINI__1_dtor0_1401E70F0.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor0_1401802F0.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor10_1401804D0.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor11_140180500.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor12_140180530.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor13_140180560.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor1_140180320.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor2_140180350.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor3_140180380.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor4_1401803B0.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor5_1401803E0.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor6_140180410.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor7_140180440.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor8_140180470.cpp" />
    <ClCompile Include="source\_CMapDataCMapData__1_dtor9_1401804A0.cpp" />
    <ClCompile Include="source\_CMapDataOpenMap__1_dtor0_140181480.cpp" />
    <ClCompile Include="source\_CMapDataOpenMap__1_dtor1_1401814B0.cpp" />
    <ClCompile Include="source\_CMapDataOpenMap__1_dtor2_1401814E0.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor0_140180A60.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor10_140180C40.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor11_140180C70.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor12_140180CA0.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor13_140180CD0.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor14_140180D00.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor1_140180A90.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor2_140180AC0.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor3_140180AF0.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor4_140180B20.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor5_140180B50.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor6_140180B80.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor7_140180BB0.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor8_140180BE0.cpp" />
    <ClCompile Include="source\_CMapData_CMapData__1_dtor9_140180C10.cpp" />
    <ClCompile Include="source\_CMapData_LoadMonBlk__1_dtor0_140182550.cpp" />
    <ClCompile Include="source\_CMapData_LoadMonBlk__1_dtor1_140182580.cpp" />
    <ClCompile Include="source\_CMapData_LoadPortal__1_dtor0_1401829E0.cpp" />
    <ClCompile Include="source\_CMapData_LoadQuest__1_dtor0_140183E00.cpp" />
    <ClCompile Include="source\_CMapData_LoadResource__1_dtor0_140183B30.cpp" />
    <ClCompile Include="source\_CMapData_LoadSafe__1_dtor0_140184220.cpp" />
    <ClCompile Include="source\_CMapData_LoadStart__1_dtor0_1401832B0.cpp" />
    <ClCompile Include="source\_CMapData_LoadStoreDummy__1_dtor0_140182EB0.cpp" />
    <ClCompile Include="source\_CMapDisplayCMapDisplay__1_dtor0_14019D8B0.cpp" />
    <ClCompile Include="source\_CMapDisplayCMapDisplay__1_dtor1_14019D8E0.cpp" />
    <ClCompile Include="source\_CMapDisplayCMapDisplay__1_dtor2_14019D920.cpp" />
    <ClCompile Include="source\_CMapDisplayCMapDisplay__1_dtor3_14019D950.cpp" />
    <ClCompile Include="source\_CMapDisplayCMapDisplay__1_dtor4_14019D980.cpp" />
    <ClCompile Include="source\_CMapDisplay_CMapDisplay__1_dtor0_14019DB80.cpp" />
    <ClCompile Include="source\_CMapDisplay_CMapDisplay__1_dtor1_14019DBB0.cpp" />
    <ClCompile Include="source\_CMapDisplay_CMapDisplay__1_dtor2_14019DBF0.cpp" />
    <ClCompile Include="source\_CMapDisplay_CMapDisplay__1_dtor3_14019DC20.cpp" />
    <ClCompile Include="source\_CMapDisplay_CMapDisplay__1_dtor4_14019DC50.cpp" />
    <ClCompile Include="source\_CMapOperationCMapOperation__1_dtor0_140195FD0.cpp" />
    <ClCompile Include="source\_CMapOperationCMapOperation__1_dtor1_140196000.cpp" />
    <ClCompile Include="source\_CMapOperationCMapOperation__1_dtor2_140196030.cpp" />
    <ClCompile Include="source\_CMapOperationCMapOperation__1_dtor3_140196060.cpp" />
    <ClCompile Include="source\_CMapOperationCMapOperation__1_dtor4_140196090.cpp" />
    <ClCompile Include="source\_CMapOperationLoadRegion__1_dtor0_140196F00.cpp" />
    <ClCompile Include="source\_CMapOperation_CMapOperation__1_dtor0_140196210.cpp" />
    <ClCompile Include="source\_CMapOperation_CMapOperation__1_dtor1_140196240.cpp" />
    <ClCompile Include="source\_CMapOperation_CMapOperation__1_dtor2_140196270.cpp" />
    <ClCompile Include="source\_CMapOperation_CMapOperation__1_dtor3_1401962A0.cpp" />
    <ClCompile Include="source\_CMapOperation_CMapOperation__1_dtor4_1401962D0.cpp" />
    <ClCompile Include="source\_CMapTabCMapTab__1_dtor0_14002E500.cpp" />
    <ClCompile Include="source\_CMapTabCreateObject__1_dtor0_14002E3D0.cpp" />
    <ClCompile Include="source\_CMapTabUpdateTab__1_dtor0_14002EC00.cpp" />
    <ClCompile Include="source\_CMapTab_CMapTab__1_dtor0_14002E590.cpp" />
    <ClCompile Include="source\_CMonsterAICMonsterAI__1_dtor0_14014FA00.cpp" />
    <ClCompile Include="source\_CMonsterAI_CMonsterAI__1_dtor0_14014FA90.cpp" />
    <ClCompile Include="source\_CMonsterCMonster__1_dtor0_140141660.cpp" />
    <ClCompile Include="source\_CMonsterCMonster__1_dtor1_140141690.cpp" />
    <ClCompile Include="source\_CMonsterCMonster__1_dtor2_1401416C0.cpp" />
    <ClCompile Include="source\_CMonsterCMonster__1_dtor3_1401416F0.cpp" />
    <ClCompile Include="source\_CMonsterCMonster__1_dtor4_140141720.cpp" />
    <ClCompile Include="source\_CMonsterCMonster__1_dtor5_140141750.cpp" />
    <ClCompile Include="source\_CMonsterCreateAI__1_dtor0_1401424C0.cpp" />
    <ClCompile Include="source\_CMonster_CMonster__1_dtor0_140141850.cpp" />
    <ClCompile Include="source\_CMonster_CMonster__1_dtor1_140141880.cpp" />
    <ClCompile Include="source\_CMonster_CMonster__1_dtor2_1401418B0.cpp" />
    <ClCompile Include="source\_CMonster_CMonster__1_dtor3_1401418E0.cpp" />
    <ClCompile Include="source\_CMonster_CMonster__1_dtor4_140141910.cpp" />
    <ClCompile Include="source\_CMonster_CMonster__1_dtor5_140141940.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoCreate__1_dtor0_1403A3E40.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListCleanUp__1_dtor0_1403A6430.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListCleanUp__1_dtor1_1403A6460.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListCleanUp__1_dtor2_1403A6490.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListCleanUp__1_dtor3_1403A64C0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListGet__1_dtor0_1403A61D0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListGet__1_dtor1_1403A6200.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListGet__1_dtor2_1403A6230.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListGet__1_dtor3_1403A6260.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLoad__1_dtor0_1403A5A60.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLoad__1_dtor1_1403A5A90.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLoad__1_dtor2_1403A5AC0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLoad__1_dtor3_1403A5AF0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLogOut__1_dtor0_1403A5EC0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLogOut__1_dtor1_1403A5EF0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLogOut__1_dtor2_1403A5F20.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLogOut__1_dtor3_1403A5F50.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLoop__1_dtor0_1403A5630.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLoop__1_dtor1_1403A5660.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLoop__1_dtor2_1403A5690.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoListLoop__1_dtor3_1403A56C0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoPortalCMoveMapLimitInfoPortal__1_1403A3FA0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor0_1403A46F0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor1_1403A4720.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor2_1403A4750.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor3_1403A4780.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoPortal_CMoveMapLimitInfoPortal___1403A40C0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitInfoPortal_CMoveMapLimitInfoPortal___1403A40F0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitManagerCMoveMapLimitManager__1_dtor0_1403A1D70.cpp" />
    <ClCompile Include="source\_CMoveMapLimitManagerInstance__1_dtor0_1403A1680.cpp" />
    <ClCompile Include="source\_CMoveMapLimitManager_CMoveMapLimitManager__1_dtor_1403A1F60.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightCreate__1_dtor0_1403AC670.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoCreateComplete__1_dtor0_1403AD2B0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoCreateComplete__1_dtor1_1403AD2E0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoCreateComplete__1_dtor2_1403AD310.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoCreateComplete__1_dtor3_1403AD340.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLoad__1_dtor0_1403ACE70.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLoad__1_dtor1_1403ACEA0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLoad__1_dtor2_1403ACED0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLoad__1_dtor3_1403ACF00.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLogOut__1_dtor0_1403AD4D0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLogOut__1_dtor1_1403AD500.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLogOut__1_dtor2_1403AD530.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoLogOut__1_dtor3_1403AD560.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfooperator___1_dtor0_1403AD6B0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfooperator___1_dtor1_1403AD6E0.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoRegist__1_dtor0_1403ACB10.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoRegist__1_dtor2_1403ACB40.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfoRegist__1_dtor3_1403ACB70.cpp" />
    <ClCompile Include="source\_CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__1__1403A39E0.cpp" />
    <ClCompile Include="source\_Color_TreeV_Tmap_traitsVbasic_stringDUchar_traits_1401913B0.cpp" />
    <ClCompile Include="source\_ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140194330.cpp" />
    <ClCompile Include="source\_ConstructPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1403B3BA0.cpp" />
    <ClCompile Include="source\_ConstructVCMoveMapLimitRightInfoV1stdYAXPEAVCMove_1403B37C0.cpp" />
    <ClCompile Include="source\_Copy_backward_optPEAPEAVCMoveMapLimitInfoPEAPEAV1_1403ABF00.cpp" />
    <ClCompile Include="source\_Copy_backward_optPEAPEAVCMoveMapLimitRightPEAPEAV_1403B35E0.cpp" />
    <ClCompile Include="source\_Copy_backward_optPEAVCMoveMapLimitRightInfoPEAV1U_1403B3720.cpp" />
    <ClCompile Include="source\_Copy_optPEAPEAVCMoveMapLimitInfoPEAPEAV1Urandom_a_1403AB540.cpp" />
    <ClCompile Include="source\_Copy_optPEAPEAVCMoveMapLimitRightPEAPEAV1Urandom__1403B27E0.cpp" />
    <ClCompile Include="source\_Copy_optPEAVCMoveMapLimitRightInfoPEAV1Urandom_ac_1403B2D90.cpp" />
    <ClCompile Include="source\_CreateMonYAPEAVCMonsterPEAD0MMMZ_140406260.cpp" />
    <ClCompile Include="source\_CRFMonsterAIMgrInstance__1_dtor0_14014C190.cpp" />
    <ClCompile Include="source\_CWorldScheduleCWorldSchedule__1_dtor0_1403F3560.cpp" />
    <ClCompile Include="source\_CWorldSchedule_CWorldSchedule__1_dtor0_1403F4690.cpp" />
    <ClCompile Include="source\_Decconst_iterator_TreeV_Tmap_traitsVbasic_stringD_140191F90.cpp" />
    <ClCompile Include="source\_DestroyPEAU_Node_Tree_nodV_Tmap_traitsVbasic_stri_1401942E0.cpp" />
    <ClCompile Include="source\_DestroyPEAVCMoveMapLimitRightstdYAXPEAPEAVCMoveMa_1403B3C40.cpp" />
    <ClCompile Include="source\_DestroySDMCMonsterSAXXZ_140149460.cpp" />
    <ClCompile Include="source\_DestroyU_Node_Tree_nodV_Tmap_traitsVbasic_stringD_140195D10.cpp" />
    <ClCompile Include="source\_DestroyVCMoveMapLimitRightInfostdYAXPEAVCMoveMapL_1403A38D0.cpp" />
    <ClCompile Include="source\_DestroyvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403A2AC0.cpp" />
    <ClCompile Include="source\_DestroyvectorPEAVCMoveMapLimitRightVallocatorPEAV_1403A3B40.cpp" />
    <ClCompile Include="source\_DestroyvectorVCMoveMapLimitRightInfoVallocatorVCM_1403A2D30.cpp" />
    <ClCompile Include="source\_Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEAVC_1403A3310.cpp" />
    <ClCompile Include="source\_Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEAVC_1403A3700.cpp" />
    <ClCompile Include="source\_Destroy_rangePEAVCMoveMapLimitRightVallocatorPEAV_1403A3C00.cpp" />
    <ClCompile Include="source\_Destroy_rangePEAVCMoveMapLimitRightVallocatorPEAV_1403A3CE0.cpp" />
    <ClCompile Include="source\_Destroy_rangeVCMoveMapLimitRightInfoVallocatorVCM_1403A3440.cpp" />
    <ClCompile Include="source\_Destroy_rangeVCMoveMapLimitRightInfoVallocatorVCM_1403A3780.cpp" />
    <ClCompile Include="source\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__CMonsters_logTrace_1406E8580.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__CMonsters_logTrace_1406E85C0.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__g_MapDisplay___1406E8740.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__g_MapOper___1406E8700.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__g_MonsterEventResp_1406E8EA0.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__g_strLMapMap___1406E86C0.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__g_strMapMap___1406E8680.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__g_WorldSch___1406E9680.cpp" />
    <ClCompile Include="source\_ECCircleZoneUEAAPEAXIZ_1403F0010.cpp" />
    <ClCompile Include="source\_ECMapDataUEAAPEAXIZ_140199070.cpp" />
    <ClCompile Include="source\_ECMapDisplayUEAAPEAXIZ_1401A1240.cpp" />
    <ClCompile Include="source\_ECMapTabUEAAPEAXIZ_14002EFB0.cpp" />
    <ClCompile Include="source\_ECMonsterEventSetUEAAPEAXIZ_1402AA010.cpp" />
    <ClCompile Include="source\_ECMonsterUEAAPEAXIZ_140204E60.cpp" />
    <ClCompile Include="source\_Erase_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140195B70.cpp" />
    <ClCompile Include="source\_FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCMo_1403AB9A0.cpp" />
    <ClCompile Include="source\_FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVCM_1403B2BD0.cpp" />
    <ClCompile Include="source\_FillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveMa_1403B2EE0.cpp" />
    <ClCompile Include="source\_Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAPEA_1403AC570.cpp" />
    <ClCompile Include="source\_Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1Urandom_acce_1403AC310.cpp" />
    <ClCompile Include="source\_Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1stdYAXPEAPE_1403B3C50.cpp" />
    <ClCompile Include="source\_Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1Urandom_acc_1403B39C0.cpp" />
    <ClCompile Include="source\_FindV_Vector_iteratorPEAVCMoveMapLimitRightValloc_1403B25D0.cpp" />
    <ClCompile Include="source\_GBossSchedule_MapQEAAPEAXIZ_14041B3C0.cpp" />
    <ClCompile Include="source\_GCCircleZoneUEAAPEAXIZ_14012E3C0.cpp" />
    <ClCompile Include="source\_GCMapDataUEAAPEAXIZ_1401880E0.cpp" />
    <ClCompile Include="source\_GCMapOperationUEAAPEAXIZ_140198890.cpp" />
    <ClCompile Include="source\_GCMonsterAIUEAAPEAXIZ_14014FCA0.cpp" />
    <ClCompile Include="source\_GCMonsterEventRespawnUEAAPEAXIZ_1402A7860.cpp" />
    <ClCompile Include="source\_GCMonsterHierarchyUEAAPEAXIZ_14014B690.cpp" />
    <ClCompile Include="source\_GCMonsterUEAAPEAXIZ_14014BB90.cpp" />
    <ClCompile Include="source\_GCMoveMapLimitInfoQEAAPEAXIZ_1403A74C0.cpp" />
    <ClCompile Include="source\_GCMoveMapLimitManagerQEAAPEAXIZ_1403A1EA0.cpp" />
    <ClCompile Include="source\_GCMoveMapLimitRightInfoQEAAPEAXIZ_1403A3920.cpp" />
    <ClCompile Include="source\_GCMoveMapLimitRightQEAAPEAXIZ_1403AE6D0.cpp" />
    <ClCompile Include="source\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.cpp" />
    <ClCompile Include="source\_GetBaseClassCMapTabKAPEAUCRuntimeClassXZ_14002E410.cpp" />
    <ClCompile Include="source\_GetBlinkNodeCMonsterAggroMgrIEAAPEAUCAggroNodeXZ_14015E2E0.cpp" />
    <ClCompile Include="source\_GetMonsterContTimeYAGEEZ_140147560.cpp" />
    <ClCompile Include="source\_Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_14040B9D0.cpp" />
    <ClCompile Include="source\_GrowmapdequeIVallocatorIstdstdIEAAX_KZ_140657610.cpp" />
    <ClCompile Include="source\_Growmapdeque_KVallocator_KstdstdIEAAX_KZ_140656D90.cpp" />
    <ClCompile Include="source\_G_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_t_140195D60.cpp" />
    <ClCompile Include="source\_G__change_monsterQEAAPEAXIZ_140272DF0.cpp" />
    <ClCompile Include="source\_Incconst_iterator_TreeV_Tmap_traitsVbasic_stringD_140192190.cpp" />
    <ClCompile Include="source\_Insert_nvectorPEAVCMoveMapLimitInfoVallocatorPEAV_1403A9E40.cpp" />
    <ClCompile Include="source\_Insert_nvectorPEAVCMoveMapLimitRightVallocatorPEA_1403AFD70.cpp" />
    <ClCompile Include="source\_Insert_nvectorVCMoveMapLimitRightInfoVallocatorVC_1403B0D30.cpp" />
    <ClCompile Include="source\_Insert_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190690.cpp" />
    <ClCompile Include="source\_Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEC0.cpp" />
    <ClCompile Include="source\_Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_acc_1403AC2B0.cpp" />
    <ClCompile Include="source\_Iter_catPEAPEAVCMoveMapLimitRightstdYAAUrandom_ac_1403B3960.cpp" />
    <ClCompile Include="source\_Iter_randomPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAA_1403AB4E0.cpp" />
    <ClCompile Include="source\_Iter_randomPEAPEAVCMoveMapLimitRightPEAPEAV1stdYA_1403B2780.cpp" />
    <ClCompile Include="source\_Iter_randomPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_1403B2D30.cpp" />
    <ClCompile Include="source\_Key_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_14018D650.cpp" />
    <ClCompile Include="source\_Kfn_Tmap_traitsVbasic_stringDUchar_traitsDstdVall_140190EB0.cpp" />
    <ClCompile Include="source\_Lbound_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190D70.cpp" />
    <ClCompile Include="source\_Left_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140190680.cpp" />
    <ClCompile Include="source\_Lmost_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140191420.cpp" />
    <ClCompile Include="source\_LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_140181850.cpp" />
    <ClCompile Include="source\_LoadPortalCMapDataAEAA_NPEADZ_1401825B0.cpp" />
    <ClCompile Include="source\_LoadQuestCMapDataAEAA_NPEADZ_140183B60.cpp" />
    <ClCompile Include="source\_LoadResourceCMapDataAEAA_NPEADZ_1401835B0.cpp" />
    <ClCompile Include="source\_LoadSafeCMapDataAEAA_NPEADZ_140183F80.cpp" />
    <ClCompile Include="source\_LoadStartCMapDataAEAA_NPEADZ_140182EE0.cpp" />
    <ClCompile Include="source\_LoadStoreDummyCMapDataAEAA_NPEADZ_140182A10.cpp" />
    <ClCompile Include="source\_Lrotate_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191470.cpp" />
    <ClCompile Include="source\_lua_tinkerptr2lua_CMonster_invoke__1_dtor0_14040B720.cpp" />
    <ClCompile Include="source\_Max_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_140192110.cpp" />
    <ClCompile Include="source\_mc_AddMonster__1_dtor0_1402759A0.cpp" />
    <ClCompile Include="source\_mc_ChangeMonster__1_dtor0_140276380.cpp" />
    <ClCompile Include="source\_mc_RespawnMonster__1_dtor0_140275E30.cpp" />
    <ClCompile Include="source\_Min_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_1401922B0.cpp" />
    <ClCompile Include="source\_Move_backward_optPEAPEAVCMoveMapLimitInfoPEAPEAV1_1403ABA50.cpp" />
    <ClCompile Include="source\_Move_backward_optPEAPEAVCMoveMapLimitRightPEAPEAV_1403B2C80.cpp" />
    <ClCompile Include="source\_Move_backward_optPEAVCMoveMapLimitRightInfoPEAV1U_1403B2FC0.cpp" />
    <ClCompile Include="source\_Move_catPEAPEAVCMoveMapLimitInfostdYAAU_Undefined_1403AB9F0.cpp" />
    <ClCompile Include="source\_Move_catPEAPEAVCMoveMapLimitRightstdYAAU_Undefine_1403B2C20.cpp" />
    <ClCompile Include="source\_Move_catPEAVCMoveMapLimitRightInfostdYAAU_Undefin_1403B2F60.cpp" />
    <ClCompile Include="source\_Mynodeconst_iterator_TreeV_Tmap_traitsVbasic_stri_14018ED10.cpp" />
    <ClCompile Include="source\_Myval_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FF00.cpp" />
    <ClCompile Include="source\_Parent_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190D50.cpp" />
    <ClCompile Include="source\_Ptr_catPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAAU_Sc_1403A36A0.cpp" />
    <ClCompile Include="source\_Ptr_catPEAPEAVCMoveMapLimitRightPEAPEAV1stdYAAU_S_1403A3C80.cpp" />
    <ClCompile Include="source\_Ptr_catPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_Non_1403A3720.cpp" />
    <ClCompile Include="source\_Ptr_catV_Vector_const_iteratorPEAVCMoveMapLimitRi_1403B3320.cpp" />
    <ClCompile Include="source\_qc_monsterGroup__1_dtor0_140273E80.cpp" />
    <ClCompile Include="source\_Right_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEE0.cpp" />
    <ClCompile Include="source\_Rmost_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140190E60.cpp" />
    <ClCompile Include="source\_Root_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140191620.cpp" />
    <ClCompile Include="source\_Rrotate_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191670.cpp" />
    <ClCompile Include="source\_ShortRankCMonsterAggroMgrIEAAXXZ_14015E370.cpp" />
    <ClCompile Include="source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1A70.cpp" />
    <ClCompile Include="source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1AA0.cpp" />
    <ClCompile Include="source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1AD0.cpp" />
    <ClCompile Include="source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1B00.cpp" />
    <ClCompile Include="source\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1B30.cpp" />
    <ClCompile Include="source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C680.cpp" />
    <ClCompile Include="source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C6B0.cpp" />
    <ClCompile Include="source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C6F0.cpp" />
    <ClCompile Include="source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C720.cpp" />
    <ClCompile Include="source\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C750.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A2BC0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A7AA0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A83F0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8420.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8450.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8620.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A9240.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA350.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA380.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA3E0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403A2E30.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403AF740.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403AF770.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B07D0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0900.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0930.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0960.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B1280.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B12B0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B12E0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B1340.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AECE0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF090.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF0C0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF100.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF130.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF2F0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF320.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF350.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AFC40.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AFC70.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0280.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B02B0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0310.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0590.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1E70.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1EA0.cpp" />
    <ClCompile Include="source\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1ED0.cpp" />
    <ClCompile Include="source\_std_Construct_CMoveMapLimitRightInfo_CMoveMapLimi_1403B3850.cpp" />
    <ClCompile Include="source\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B2690.cpp" />
    <ClCompile Include="source\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B26C0.cpp" />
    <ClCompile Include="source\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B26F0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E560.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E590.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E5C0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E600.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E630.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E660.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E6A0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E6D0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E700.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E740.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190400.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190430.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190470.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401904A0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401904D0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190500.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190BD0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140191900.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140191930.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140193FB0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401945D0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194910.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194940.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194970.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401949B0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401949F0.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195430.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195460.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195490.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195A50.cpp" />
    <ClCompile Include="source\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195A80.cpp" />
    <ClCompile Include="source\_std_Vector_iterator_CMoveMapLimitRight_____ptr64__1403B0A90.cpp" />
    <ClCompile Include="source\_std_Vector_iterator_CMoveMapLimitRight_____ptr64__1403B0AC0.cpp" />
    <ClCompile Include="source\_TidyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A2450.cpp" />
    <ClCompile Include="source\_TidyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403A3A60.cpp" />
    <ClCompile Include="source\_TidyvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403A26B0.cpp" />
    <ClCompile Include="source\_Tidy_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194450.cpp" />
    <ClCompile Include="source\_UcopyV_Vector_const_iteratorPEAVCMoveMapLimitRigh_1403B1D70.cpp" />
    <ClCompile Include="source\_UfillvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403AA6E0.cpp" />
    <ClCompile Include="source\_UfillvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AF4E0.cpp" />
    <ClCompile Include="source\_UfillvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403B1730.cpp" />
    <ClCompile Include="source\_UmovePEAPEAVCMoveMapLimitInfovectorPEAVCMoveMapLi_1403AAF70.cpp" />
    <ClCompile Include="source\_UmovePEAPEAVCMoveMapLimitRightvectorPEAVCMoveMapL_1403B1F60.cpp" />
    <ClCompile Include="source\_UmovePEAVCMoveMapLimitRightInfovectorVCMoveMapLim_1403B2260.cpp" />
    <ClCompile Include="source\_Unchecked_move_backwardPEAPEAVCMoveMapLimitInfoPE_1403AB040.cpp" />
    <ClCompile Include="source\_Unchecked_move_backwardPEAPEAVCMoveMapLimitRightP_1403B2030.cpp" />
    <ClCompile Include="source\_Unchecked_move_backwardPEAVCMoveMapLimitRightInfo_1403B2330.cpp" />
    <ClCompile Include="source\_XlenvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A2B30.cpp" />
    <ClCompile Include="source\_XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403B0500.cpp" />
    <ClCompile Include="source\_XlenvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403A2DA0.cpp" />
  </ItemGroup>
  
  <ItemGroup>
    <ClInclude Include="headers\0allocatorPEAVCMoveMapLimitInfostdQEAAAEBV01Z_1403A2C20.h" />
    <ClInclude Include="headers\0allocatorPEAVCMoveMapLimitInfostdQEAAXZ_1403A25A0.h" />
    <ClInclude Include="headers\0allocatorPEAVCMoveMapLimitRightstdQEAAAEBV01Z_1403B05F0.h" />
    <ClInclude Include="headers\0allocatorPEAVCMoveMapLimitRightstdQEAAXZ_1403AF5D0.h" />
    <ClInclude Include="headers\0allocatorVCMoveMapLimitRightInfostdQEAAAEBV01Z_1403A2E90.h" />
    <ClInclude Include="headers\0allocatorVCMoveMapLimitRightInfostdQEAAXZ_1403A2800.h" />
    <ClInclude Include="headers\0BossSchedule_MapQEAAXZ_14041B720.h" />
    <ClInclude Include="headers\0CCircleZoneQEAAXZ_14012D660.h" />
    <ClInclude Include="headers\0CMapDataQEAAXZ_140180050.h" />
    <ClInclude Include="headers\0CMapDisplayQEAAXZ_14019D560.h" />
    <ClInclude Include="headers\0CMapExtendQEAAPEAPEAVCSurfaceZ_1401A1500.h" />
    <ClInclude Include="headers\0CMapExtendQEAAXZ_1401A1410.h" />
    <ClInclude Include="headers\0CMapOperationQEAAXZ_140195E20.h" />
    <ClInclude Include="headers\0CMapTabQEAAXZ_14002E480.h" />
    <ClInclude Include="headers\0CMonsterAggroMgrQEAAXZ_14015DB60.h" />
    <ClInclude Include="headers\0CMonsterAIQEAAXZ_14014F950.h" />
    <ClInclude Include="headers\0CMonsterEventRespawnQEAAXZ_1402A5D40.h" />
    <ClInclude Include="headers\0CMonsterEventSetQEAAXZ_1402A7920.h" />
    <ClInclude Include="headers\0CMonsterHierarchyQEAAXZ_14014B660.h" />
    <ClInclude Include="headers\0CMonsterQEAAXZ_1401414E0.h" />
    <ClInclude Include="headers\0CMoveMapLimitInfoListQEAAXZ_1403A1DC0.h" />
    <ClInclude Include="headers\0CMoveMapLimitInfoPortalQEAAIHZ_1403A3EE0.h" />
    <ClInclude Include="headers\0CMoveMapLimitInfoQEAAIHZ_1403A3D00.h" />
    <ClInclude Include="headers\0CMoveMapLimitManagerQEAAXZ_1403A1D10.h" />
    <ClInclude Include="headers\0CMoveMapLimitRightInfoListQEAAXZ_1403A1E10.h" />
    <ClInclude Include="headers\0CMoveMapLimitRightInfoQEAAAEBV0Z_1403AF990.h" />
    <ClInclude Include="headers\0CMoveMapLimitRightInfoQEAAXZ_1403AE760.h" />
    <ClInclude Include="headers\0CMoveMapLimitRightPortalQEAAHZ_1403AC7F0.h" />
    <ClInclude Include="headers\0CMoveMapLimitRightQEAAHZ_1403AE460.h" />
    <ClInclude Include="headers\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CAC0.h" />
    <ClInclude Include="headers\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191BB0.h" />
    <ClInclude Include="headers\0const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191C10.h" />
    <ClInclude Include="headers\0CRFMonsterAIMgrQEAAXZ_14014C1E0.h" />
    <ClInclude Include="headers\0CWorldScheduleQEAAXZ_1403F34F0.h" />
    <ClInclude Include="headers\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C980.h" />
    <ClInclude Include="headers\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401910C0.h" />
    <ClInclude Include="headers\0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_140191110.h" />
    <ClInclude Include="headers\0mapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_140193BE0.h" />
    <ClInclude Include="headers\0MonsterSetInfoDataQEAAXZ_140161980.h" />
    <ClInclude Include="headers\0MonsterStateDataQEAAXZ_14014B700.h" />
    <ClInclude Include="headers\0pairViterator_TreeV_Tmap_traitsVbasic_stringDUcha_140191D10.h" />
    <ClInclude Include="headers\0ptr2userVCMonsterlua_tinkerQEAAPEAVCMonsterZ_14040B880.h" />
    <ClInclude Include="headers\0UpairCBVbasic_stringDUchar_traitsDstdVallocatorD2_1401942F0.h" />
    <ClInclude Include="headers\0UpairCBVbasic_stringDUchar_traitsDstdVallocatorD2_140194310.h" />
    <ClInclude Include="headers\0vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A2060.h" />
    <ClInclude Include="headers\0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AE7B0.h" />
    <ClInclude Include="headers\0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AFB00.h" />
    <ClInclude Include="headers\0vectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A2120.h" />
    <ClInclude Include="headers\0_Deque_mapIVallocatorIstdstdIEAAVallocatorI1Z_14065ABD0.h" />
    <ClInclude Include="headers\0_Deque_map_KVallocator_KstdstdIEAAVallocator_K1Z_140659590.h" />
    <ClInclude Include="headers\0_event_respawnQEAAXZ_1402A7740.h" />
    <ClInclude Include="headers\0_mapCGameStatisticsQEAAXZ_140232BE0.h" />
    <ClInclude Include="headers\0_map_fldQEAAXZ_140198FF0.h" />
    <ClInclude Include="headers\0_monster_create_setdataQEAAXZ_14014C340.h" />
    <ClInclude Include="headers\0_monster_set_event_setQEAAXZ_1402A9E80.h" />
    <ClInclude Include="headers\0_monster_sp_groupQEAAXZ_1401618D0.h" />
    <ClInclude Include="headers\0_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_tr_140192330.h" />
    <ClInclude Include="headers\0_notice_move_limit_map_msg_zoclQEAAXZ_1403A7100.h" />
    <ClInclude Include="headers\0_NPCQuestIndexTempDataQEAAXZ_140073EA0.h" />
    <ClInclude Include="headers\0_npc_create_setdataQEAAXZ_140199140.h" />
    <ClInclude Include="headers\0_npc_quest_list_result_zoclQEAAXZ_1400EFD20.h" />
    <ClInclude Include="headers\0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A7400.h" />
    <ClInclude Include="headers\0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A9D80.h" />
    <ClInclude Include="headers\0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AE650.h" />
    <ClInclude Include="headers\0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AFA30.h" />
    <ClInclude Include="headers\0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAA_1403B0C60.h" />
    <ClInclude Include="headers\0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAX_1403B18D0.h" />
    <ClInclude Include="headers\0_reged_char_result_zoneQEAAXZ_14011F680.h" />
    <ClInclude Include="headers\0_respawn_monster_act_dh_mission_mgrQEAAXZ_14026ECB0.h" />
    <ClInclude Include="headers\0_state_event_respawnQEAAXZ_1402A77C0.h" />
    <ClInclude Include="headers\0_state_monster_set_event_setQEAAXZ_1402A9ED0.h" />
    <ClInclude Include="headers\0_target_monster_aggro_inform_zoclQEAAXZ_1400F0010.h" />
    <ClInclude Include="headers\0_target_monster_contsf_allinform_zoclQEAAXZ_140073FF0.h" />
    <ClInclude Include="headers\0_Tmap_traitsVbasic_stringDUchar_traitsDstdValloca_140194290.h" />
    <ClInclude Include="headers\0_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDstdV_140193C40.h" />
    <ClInclude Include="headers\0_Tree_nodV_Tmap_traitsVbasic_stringDUchar_traitsD_1401941B0.h" />
    <ClInclude Include="headers\0_Tree_ptrV_Tmap_traitsVbasic_stringDUchar_traitsD_140194110.h" />
    <ClInclude Include="headers\0_Tree_valV_Tmap_traitsVbasic_stringDUchar_traitsD_140193DB0.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7350.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A9C80.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AE5E0.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AF860.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B0BF0.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1800.h" />
    <ClInclude Include="headers\0_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A8CF0.h" />
    <ClInclude Include="headers\0_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A8DB0.h" />
    <ClInclude Include="headers\0_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AF800.h" />
    <ClInclude Include="headers\0_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AF930.h" />
    <ClInclude Include="headers\0_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403B0B90.h" />
    <ClInclude Include="headers\0_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403B15E0.h" />
    <ClInclude Include="headers\0_Vector_valPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A2530.h" />
    <ClInclude Include="headers\0_Vector_valPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AF560.h" />
    <ClInclude Include="headers\0_Vector_valVCMoveMapLimitRightInfoVallocatorVCMov_1403A2790.h" />
    <ClInclude Include="headers\0__add_monsterQEAAXZ_14027A3F0.h" />
    <ClInclude Include="headers\0__change_monsterQEAAXZ_14027A4D0.h" />
    <ClInclude Include="headers\0__monster_groupQEAAXZ_140279FB0.h" />
    <ClInclude Include="headers\0__respawn_monsterQEAAXZ_14027A450.h" />
    <ClInclude Include="headers\1BossSchedule_MapQEAAXZ_14041B430.h" />
    <ClInclude Include="headers\1CCircleZoneUEAAXZ_14012D6F0.h" />
    <ClInclude Include="headers\1CMapDataUEAAXZ_140180590.h" />
    <ClInclude Include="headers\1CMapDisplayUEAAXZ_14019D9C0.h" />
    <ClInclude Include="headers\1CMapExtendQEAAXZ_1401A1600.h" />
    <ClInclude Include="headers\1CMapOperationUEAAXZ_1401960C0.h" />
    <ClInclude Include="headers\1CMapTabUEAAXZ_14002E530.h" />
    <ClInclude Include="headers\1CMonsterAggroMgrQEAAXZ_14015DC90.h" />
    <ClInclude Include="headers\1CMonsterAIUEAAXZ_14014FA30.h" />
    <ClInclude Include="headers\1CMonsterEventRespawnUEAAXZ_1402A5DC0.h" />
    <ClInclude Include="headers\1CMonsterEventSetUEAAXZ_1402A79C0.h" />
    <ClInclude Include="headers\1CMonsterHierarchyUEAAXZ_140157350.h" />
    <ClInclude Include="headers\1CMonsterUEAAXZ_140141780.h" />
    <ClInclude Include="headers\1CMoveMapLimitInfoListQEAAXZ_1403A1FA0.h" />
    <ClInclude Include="headers\1CMoveMapLimitInfoPortalQEAAXZ_1403A3FD0.h" />
    <ClInclude Include="headers\1CMoveMapLimitInfoQEAAXZ_1403A3D60.h" />
    <ClInclude Include="headers\1CMoveMapLimitManagerQEAAXZ_1403A1F10.h" />
    <ClInclude Include="headers\1CMoveMapLimitRightInfoListQEAAXZ_1403A1E60.h" />
    <ClInclude Include="headers\1CMoveMapLimitRightInfoQEAAXZ_1403A3990.h" />
    <ClInclude Include="headers\1CMoveMapLimitRightQEAAXZ_1403AE740.h" />
    <ClInclude Include="headers\1const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CA80.h" />
    <ClInclude Include="headers\1CRFMonsterAIMgrQEAAXZ_140203400.h" />
    <ClInclude Include="headers\1CWorldScheduleQEAAXZ_1403F4630.h" />
    <ClInclude Include="headers\1iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C8A0.h" />
    <ClInclude Include="headers\1mapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_1401943D0.h" />
    <ClInclude Include="headers\1pairViterator_TreeV_Tmap_traitsVbasic_stringDUcha_14018EE50.h" />
    <ClInclude Include="headers\1ptr2userVCMonsterlua_tinkerUEAAXZ_14040BAC0.h" />
    <ClInclude Include="headers\1vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A20E0.h" />
    <ClInclude Include="headers\1vectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403A3A20.h" />
    <ClInclude Include="headers\1vectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A21A0.h" />
    <ClInclude Include="headers\1_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_tr_140195DD0.h" />
    <ClInclude Include="headers\1_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1stdQ_1403A7460.h" />
    <ClInclude Include="headers\1_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403AE5A0.h" />
    <ClInclude Include="headers\1_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEAAX_1403AFAC0.h" />
    <ClInclude Include="headers\1_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDstdV_140194410.h" />
    <ClInclude Include="headers\1_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A73C0.h" />
    <ClInclude Include="headers\1_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AE560.h" />
    <ClInclude Include="headers\1_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403AFA80.h" />
    <ClInclude Include="headers\1_Vector_iteratorPEAVCMoveMapLimitInfoVallocatorPE_1403A7310.h" />
    <ClInclude Include="headers\1_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403AE520.h" />
    <ClInclude Include="headers\1_Vector_iteratorVCMoveMapLimitRightInfoVallocator_1403AF9F0.h" />
    <ClInclude Include="headers\1__change_monsterQEAAXZ_140272E60.h" />
    <ClInclude Include="headers\4CMoveMapLimitRightInfoQEAAAEBV0AEBV0Z_1403AD590.h" />
    <ClInclude Include="headers\4const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018CB30.h" />
    <ClInclude Include="headers\4iterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018C9E0.h" />
    <ClInclude Include="headers\4_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1std_1403B2570.h" />
    <ClInclude Include="headers\4_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B2500.h" />
    <ClInclude Include="headers\4_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B24A0.h" />
    <ClInclude Include="headers\8const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_14018ECA0.h" />
    <ClInclude Include="headers\8_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A8C80.h" />
    <ClInclude Include="headers\8_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AF8C0.h" />
    <ClInclude Include="headers\8_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1860.h" />
    <ClInclude Include="headers\9const_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140195B00.h" />
    <ClInclude Include="headers\9MonsterStateDataQEBA_NAEBV0Z_14014C3E0.h" />
    <ClInclude Include="headers\9_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E70.h" />
    <ClInclude Include="headers\9_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDF0.h" />
    <ClInclude Include="headers\9_Vector_const_iteratorVCMoveMapLimitRightInfoVall_1403B1640.h" />
    <ClInclude Include="headers\AddMonsterCDarkHoleChannelQEAAXXZ_140268F40.h" />
    <ClInclude Include="headers\allocateallocatorPEAVCMoveMapLimitInfostdQEAAPEAPE_1403A2C90.h" />
    <ClInclude Include="headers\allocateallocatorPEAVCMoveMapLimitRightstdQEAAPEAP_1403B0610.h" />
    <ClInclude Include="headers\allocateallocatorU_Node_Tree_nodV_Tmap_traitsVbasi_140191E90.h" />
    <ClInclude Include="headers\allocateallocatorVCMoveMapLimitRightInfostdQEAAPEA_1403A2F00.h" />
    <ClInclude Include="headers\AmapVbasic_stringDUchar_traitsDstdVallocatorD2stdU_14018C400.h" />
    <ClInclude Include="headers\assignvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A7980.h" />
    <ClInclude Include="headers\assignvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403AED40.h" />
    <ClInclude Include="headers\AutoRecoverCMonsterQEAAXXZ_140147440.h" />
    <ClInclude Include="headers\AvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveMap_1403A7950.h" />
    <ClInclude Include="headers\AvectorPEAVCMoveMapLimitRightVallocatorPEAVCMoveMa_1403AEAE0.h" />
    <ClInclude Include="headers\AvectorVCMoveMapLimitRightInfoVallocatorVCMoveMapL_1403A2260.h" />
    <ClInclude Include="headers\beginvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A77F0.h" />
    <ClInclude Include="headers\beginvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AE830.h" />
    <ClInclude Include="headers\beginvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AE8A0.h" />
    <ClInclude Include="headers\beginvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403B0660.h" />
    <ClInclude Include="headers\begin_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_14018FF20.h" />
    <ClInclude Include="headers\CalcScheduleCursorCWorldScheduleQEAAHHHZ_1403F4180.h" />
    <ClInclude Include="headers\CalcStartNPCQuestCntCQuestMgrSA_NQEAKZ_14028B6E0.h" />
    <ClInclude Include="headers\capacityvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403AA660.h" />
    <ClInclude Include="headers\capacityvectorPEAVCMoveMapLimitRightVallocatorPEAV_1403AEE60.h" />
    <ClInclude Include="headers\capacityvectorVCMoveMapLimitRightInfoVallocatorVCM_1403B16B0.h" />
    <ClInclude Include="headers\ChangeApparitionCMonsterQEAAX_NKZ_1401434E0.h" />
    <ClInclude Include="headers\ChangeLayerCMapDisplayQEAA_NGZ_14019F1C0.h" />
    <ClInclude Include="headers\ChangeMapCMapDisplayQEAA_NPEAVCMapDataZ_14019F130.h" />
    <ClInclude Include="headers\ChangeMonsterApparitionCDarkHoleChannelQEAAXHZ_140269700.h" />
    <ClInclude Include="headers\ChangeMonsterCDarkHoleChannelQEAAXXZ_140268570.h" />
    <ClInclude Include="headers\ChangeSchCursorCWorldScheduleQEAAXPEAU_WorldSchedu_1403F3EE0.h" />
    <ClInclude Include="headers\ChangeTargetPosDfAIMgrSAXPEAVCMonsterPEAMZ_140150E20.h" />
    <ClInclude Include="headers\ChatMapRequestCNetworkEXAEAA_NHPEADZ_1401C58C0.h" />
    <ClInclude Include="headers\CheckAlienationDfAIMgrSAHPEAVCMonsterZ_140151260.h" />
    <ClInclude Include="headers\CheckAutoRecoverHPCMonsterQEAAXXZ_140143370.h" />
    <ClInclude Include="headers\CheckCenterPosDummyCMapDataQEAA_NPEAU_dummy_positi_1401855D0.h" />
    <ClInclude Include="headers\CheckDelayDestroyCMonsterQEAA_NXZ_1401432F0.h" />
    <ClInclude Include="headers\CheckEmotionBadDfAIMgrSAHPEAVCMonsterPEAVCMonsterA_140151090.h" />
    <ClInclude Include="headers\CheckEmotionPresentationCMonsterQEAAXXZ_140147FD0.h" />
    <ClInclude Include="headers\CheckEventSetRespawnCMonsterEventSetQEAAXXZ_1402A8A90.h" />
    <ClInclude Include="headers\CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152E40.h" />
    <ClInclude Include="headers\CheckMapPortalLinkCMapOperationAEAAXXZ_140197D40.h" />
    <ClInclude Include="headers\CheckMonArea_N_ChangeStateDfAIMgrSAHPEAVCMonsterAI_140153350.h" />
    <ClInclude Include="headers\CheckMonsterRotateCMonsterQEAAXXZ_140147B80.h" />
    <ClInclude Include="headers\CheckMonsterStateDataCMonsterQEAA_NXZ_1401435C0.h" />
    <ClInclude Include="headers\CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQuestI_140287ED0.h" />
    <ClInclude Include="headers\CheckRespawnEventCMonsterEventRespawnQEAAXXZ_1402A6FE0.h" />
    <ClInclude Include="headers\CheckRespawnMonsterCDarkHoleChannelQEAAXXZ_14026A0D0.h" />
    <ClInclude Include="headers\CheckRespawnProcessCMonsterQEAA_NXZ_140143070.h" />
    <ClInclude Include="headers\CheckSchCWorldScheduleQEAAXXZ_1403F3AA0.h" />
    <ClInclude Include="headers\CheckSPFDelayTimeDfAIMgrSAHPEAVCMonsterAIHKZ_140151850.h" />
    <ClInclude Include="headers\CheckSPFDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_140152150.h" />
    <ClInclude Include="headers\check_dummyAutominePersonalMgrQEAA_NPEAVCMapDataEP_1402DEBA0.h" />
    <ClInclude Include="headers\ChildKindCountCMonsterHierarchyQEAAEXZ_14014C320.h" />
    <ClInclude Include="headers\class_addVCMonsterlua_tinkerYAXPEAUlua_StatePEBDZ_140407BA0.h" />
    <ClInclude Include="headers\class_defVCMonsterP81EAAPEAVCLuaSignalReActorXZlua_140407D20.h" />
    <ClInclude Include="headers\CleanUpCMoveMapLimitInfoListAEAAXXZ_1403A6290.h" />
    <ClInclude Include="headers\CleanUpCMoveMapLimitRightInfoAEAAXXZ_1403AD710.h" />
    <ClInclude Include="headers\CleanUpCMoveMapLimitRightUEAAXXZ_1403AE4A0.h" />
    <ClInclude Include="headers\ClearBossSchedule_MapQEAAXXZ_14041B4D0.h" />
    <ClInclude Include="headers\ClearEmotionPresentationCMonsterQEAAXXZ_140147F80.h" />
    <ClInclude Include="headers\clearvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A79E0.h" />
    <ClInclude Include="headers\clearvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AEC20.h" />
    <ClInclude Include="headers\clear_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_1401958F0.h" />
    <ClInclude Include="headers\Command_ChildMonDestroyCMonsterQEAAXKZ_140143550.h" />
    <ClInclude Include="headers\constructallocatorPEAU_Node_Tree_nodV_Tmap_traitsV_140194230.h" />
    <ClInclude Include="headers\constructallocatorPEAVCMoveMapLimitRightstdQEAAXPE_1403B38B0.h" />
    <ClInclude Include="headers\constructallocatorVCMoveMapLimitRightInfostdQEAAXP_1403B3190.h" />
    <ClInclude Include="headers\ConvertLocalCMapDataQEAA_NPEAU_dummy_positionZ_140185100.h" />
    <ClInclude Include="headers\ConvertToMapCMapExtendQEAAXPEAVCSizeZ_1401A1DA0.h" />
    <ClInclude Include="headers\CreateAICMonsterQEAAHHZ_1401423D0.h" />
    <ClInclude Include="headers\CreateCCircleZoneQEAA_NPEAVCMapDataEZ_14012DA60.h" />
    <ClInclude Include="headers\CreateCGravityStoneRegenerQEAA_NPEAVCMapDataZ_14012E950.h" />
    <ClInclude Include="headers\CreateCMerchantQEAA_NPEAU_npc_create_setdataZ_140139140.h" />
    <ClInclude Include="headers\CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_140141C50.h" />
    <ClInclude Include="headers\CreateCMoveMapLimitInfoSAPEAV1IHZ_1403A3DB0.h" />
    <ClInclude Include="headers\CreateCMoveMapLimitRightSAPEAV1HZ_1403AC5E0.h" />
    <ClInclude Include="headers\CreateFileMappingA_0_140676E22.h" />
    <ClInclude Include="headers\CreateMonsterCDarkHoleChannelQEAAXXZ_1402682C0.h" />
    <ClInclude Include="headers\CreateObjectCMapTabSAPEAVCObjectXZ_14002E350.h" />
    <ClInclude Include="headers\CreateObjSurfaceCMapDisplayAEAAJXZ_14019F690.h" />
    <ClInclude Include="headers\CreatePaletteFromBitmapCDisplayQEAAJPEAPEAUIDirect_1404346D0.h" />
    <ClInclude Include="headers\CreateRepMonsterYAPEAVCMonsterPEAVCMapDataGPEAMPEA_140148D90.h" />
    <ClInclude Include="headers\CreateRespawnMonsterYAPEAVCMonsterPEAVCMapDataGHPE_140148B90.h" />
    <ClInclude Include="headers\CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurfac_140433B00.h" />
    <ClInclude Include="headers\D3DUtil_GetCubeMapViewMatrixYAAUD3DXMATRIXKZ_14052B5D0.h" />
    <ClInclude Include="headers\DataCheckCWorldScheduleQEAA_NXZ_1403F3FF0.h" />
    <ClInclude Include="headers\Dconst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191210.h" />
    <ClInclude Include="headers\deallocateallocatorPEAVCMoveMapLimitInfostdQEAAXPE_1403A2C40.h" />
    <ClInclude Include="headers\deallocateallocatorPEAVCMoveMapLimitRightstdQEAAXP_1403A3BB0.h" />
    <ClInclude Include="headers\deallocateallocatorU_Node_Tree_nodV_Tmap_traitsVba_140191A00.h" />
    <ClInclude Include="headers\deallocateallocatorVCMoveMapLimitRightInfostdQEAAX_1403A2EB0.h" />
    <ClInclude Include="headers\defP6APEAVCMonsterPEAD0MMMZlua_tinkerYAXPEAUlua_St_140407ED0.h" />
    <ClInclude Include="headers\DestoryCRFMonsterAIMgrSAXXZ_140203300.h" />
    <ClInclude Include="headers\destroyallocatorPEAU_Node_Tree_nodV_Tmap_traitsVba_1401940C0.h" />
    <ClInclude Include="headers\destroyallocatorPEAVCMoveMapLimitRightstdQEAAXPEAP_1403B3910.h" />
    <ClInclude Include="headers\destroyallocatorU_Node_Tree_nodV_Tmap_traitsVbasic_140195C50.h" />
    <ClInclude Include="headers\destroyallocatorVCMoveMapLimitRightInfostdQEAAXPEA_1403A3880.h" />
    <ClInclude Include="headers\DestroyCCircleZoneQEAAXXZ_14012DB70.h" />
    <ClInclude Include="headers\DestroyCMonsterQEAA_NEPEAVCGameObjectZ_1401424F0.h" />
    <ClInclude Include="headers\DestroyCMoveMapLimitManagerSAXXZ_1403A16B0.h" />
    <ClInclude Include="headers\destroyerVCMonsterlua_tinkerYAHPEAUlua_StateZ_140408950.h" />
    <ClInclude Include="headers\Diterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14018EC60.h" />
    <ClInclude Include="headers\DoDataExchangeCMapTabMEAAXPEAVCDataExchangeZ_14002E5C0.h" />
    <ClInclude Include="headers\DrawBitmapCSurfaceQEAAJPEADKKZ_140435410.h" />
    <ClInclude Include="headers\DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140434FE0.h" />
    <ClInclude Include="headers\DrawCollisionLineCMapDisplayAEAAXXZ_1401A02A0.h" />
    <ClInclude Include="headers\DrawDisplayCMapDisplayQEAAXXZ_14019F260.h" />
    <ClInclude Include="headers\DrawDummyCMapDisplayAEAAXXZ_1401A0010.h" />
    <ClInclude Include="headers\DrawLightMapGroupYAXPEAVCVertexBufferPEAU_BSP_MAT__1404F1590.h" />
    <ClInclude Include="headers\DrawMapCMapDisplayAEAAXXZ_14019F450.h" />
    <ClInclude Include="headers\DrawMapEntitiesRenderCBspQEAAXXZ_1404FBBB0.h" />
    <ClInclude Include="headers\DrawObjectCMapDisplayAEAAXXZ_14019F4E0.h" />
    <ClInclude Include="headers\DrawRectCMapExtendQEAAXXZ_1401A1C40.h" />
    <ClInclude Include="headers\DrawSelectMonsterLookAtPosCMapDisplayAEAAJPEAVCMon_14019FA70.h" />
    <ClInclude Include="headers\DrawTextACMapDisplayAEAAXXZ_1401A0340.h" />
    <ClInclude Include="headers\D_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E20.h" />
    <ClInclude Include="headers\D_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDA0.h" />
    <ClInclude Include="headers\D_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B31F0.h" />
    <ClInclude Include="headers\Econst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191C70.h" />
    <ClInclude Include="headers\Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_140191170.h" />
    <ClInclude Include="headers\Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401959B0.h" />
    <ClInclude Include="headers\emptyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AEA70.h" />
    <ClInclude Include="headers\EndScreenPointCMapExtendQEAAHPEAVCSizeZ_1401A1940.h" />
    <ClInclude Include="headers\endvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_1403A7860.h" />
    <ClInclude Include="headers\endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_1403AE910.h" />
    <ClInclude Include="headers\endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_1403AE980.h" />
    <ClInclude Include="headers\endvectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_1403B06D0.h" />
    <ClInclude Include="headers\end_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14018D6A0.h" />
    <ClInclude Include="headers\EnterMapCMapDataQEAAXPEAVCGameObjectKZ_140184D30.h" />
    <ClInclude Include="headers\EnterWorldRequestCNetworkEXAEAA_NHPEAU_MSG_HEADERP_1401D0D30.h" />
    <ClInclude Include="headers\EnterWorldResultCNetworkEXAEAA_NKPEADZ_1401C06D0.h" />
    <ClInclude Include="headers\erasevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A8310.h" />
    <ClInclude Include="headers\erasevectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403AF210.h" />
    <ClInclude Include="headers\erasevectorVCMoveMapLimitRightInfoVallocatorVCMove_1403B0820.h" />
    <ClInclude Include="headers\erase_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194660.h" />
    <ClInclude Include="headers\erase_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194B20.h" />
    <ClInclude Include="headers\ExitMapCMapDataQEAAXPEAVCGameObjectKZ_140184EC0.h" />
    <ClInclude Include="headers\ExitWorldRequestCNetworkEXAEAA_NHPEADZ_1401C9D20.h" />
    <ClInclude Include="headers\E_Vector_const_iteratorPEAVCMoveMapLimitInfoValloc_1403A7E40.h" />
    <ClInclude Include="headers\E_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403AEDC0.h" />
    <ClInclude Include="headers\E_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B3230.h" />
    <ClInclude Include="headers\Fconst_iterator_TreeV_Tmap_traitsVbasic_stringDUch_140191CC0.h" />
    <ClInclude Include="headers\fillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCMov_1403AAFE0.h" />
    <ClInclude Include="headers\fillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVCMo_1403B1FD0.h" />
    <ClInclude Include="headers\fillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveMap_1403B22D0.h" />
    <ClInclude Include="headers\FindEmptyNPCYAPEAVCMerchantPEAV1HZ_140139CD0.h" />
    <ClInclude Include="headers\findV_Vector_iteratorPEAVCMoveMapLimitRightValloca_1403B1920.h" />
    <ClInclude Include="headers\Fiterator_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1401911C0.h" />
    <ClInclude Include="headers\FrameMoveMapEntitiesCBspQEAAXXZ_1404FB7D0.h" />
    <ClInclude Include="headers\GeEmotionImpStdTimeCMonsterQEAAMXZ_1401558D0.h" />
    <ClInclude Include="headers\GetAggroResetTimeCMonsterQEAAKXZ_1401617D0.h" />
    <ClInclude Include="headers\GetAggroShortTimeCMonsterQEAAKXZ_140161790.h" />
    <ClInclude Include="headers\GetAngleCMonsterHelperSAMQEAM0Z_1401597A0.h" />
    <ClInclude Include="headers\GetBonusInAreaAggroCMonsterQEAAMXZ_140161890.h" />
    <ClInclude Include="headers\GetBspInfoCMapDataQEAAPEAU_bsp_infoXZ_1401843C0.h" />
    <ClInclude Include="headers\GetChildCMonsterHierarchyQEAAPEAVCMonsterHHZ_140157DA0.h" />
    <ClInclude Include="headers\GetChildCountCMonsterHierarchyQEAAKHZ_140161530.h" />
    <ClInclude Include="headers\GetCMoveMapLimitInfoListAEAAPEAVCMoveMapLimitInfoH_1403A6020.h" />
    <ClInclude Include="headers\GetCMoveMapLimitRightInfoListQEAAPEAVCMoveMapLimit_1403A1FE0.h" />
    <ClInclude Include="headers\GetColorCCircleZoneQEAAEXZ_140034B20.h" />
    <ClInclude Include="headers\GetCommandMapCCmdTargetMEBAPEBUAFX_OLECMDMAPXZ_0_1404DBBC8.h" />
    <ClInclude Include="headers\GetCritical_Exception_RateCMonsterUEAAHXZ_14014BB70.h" />
    <ClInclude Include="headers\GetDefFacingCMonsterUEAAMHZ_14014BA70.h" />
    <ClInclude Include="headers\GetDefGapCMonsterUEAAMHZ_14014BA20.h" />
    <ClInclude Include="headers\GetDirectionCMonsterHelperSAXAEAY02M00MZ_1401598E0.h" />
    <ClInclude Include="headers\GetDispatchMapCCmdTargetMEBAPEBUAFX_DISPMAPXZ_0_1404DBBCE.h" />
    <ClInclude Include="headers\GetDummyPostionCMapDataQEAAPEAU_dummy_positionPEAD_140186440.h" />
    <ClInclude Include="headers\GetEmotionStateCMonsterQEAAEXZ_140143810.h" />
    <ClInclude Include="headers\GetEmptyEventSetCMonsterEventSetQEAAPEAU_event_set_1402A8FA0.h" />
    <ClInclude Include="headers\GetEvenSetLootingCMonsterEventSetQEAAPEAU_event_se_1402A90B0.h" />
    <ClInclude Include="headers\GetEventSinkMapCCmdTargetMEBAPEBUAFX_EVENTSINKMAPX_1404DBBE0.h" />
    <ClInclude Include="headers\GetExtendSizeCMapExtendQEAAPEAVCSizeXZ_14002D0B0.h" />
    <ClInclude Include="headers\GetFireTolCMonsterUEAAHXZ_140145620.h" />
    <ClInclude Include="headers\GetHelpMeCaseCMonsterQEAAHXZ_1401554F0.h" />
    <ClInclude Include="headers\GetHPCMonsterUEAAHXZ_1401461E0.h" />
    <ClInclude Include="headers\GetInterfaceMapCCmdTargetMEBAPEBUAFX_INTERFACEMAPX_1404DBF52.h" />
    <ClInclude Include="headers\GetInterfaceMapCWndMEBAPEBUAFX_INTERFACEMAPXZ_0_1404DBBDA.h" />
    <ClInclude Include="headers\GetInxCMoveMapLimitInfoQEAAIXZ_1403A74A0.h" />
    <ClInclude Include="headers\GetLightMapColorYAKQEAMHZ_140502530.h" />
    <ClInclude Include="headers\GetLightMapSurfaceYAPEAXHZ_1405025F0.h" />
    <ClInclude Include="headers\GetLightMapTexSizeYAKXZ_140500900.h" />
    <ClInclude Include="headers\GetLightMapUVFromPointCBspAEAAXQEAMH0Z_1404E3370.h" />
    <ClInclude Include="headers\GetLinkPortalCMapDataQEAAPEAU_portal_dummyPEADZ_1401846E0.h" />
    <ClInclude Include="headers\GetLocalFromWorldCExtDummyQEAAHPEAY02MKQEAMZ_1404DF180.h" />
    <ClInclude Include="headers\GetLostMonsterTargetDistanceMonsterSetInfoDataQEAA_140155670.h" />
    <ClInclude Include="headers\GetMapCMapOperationQEAAHPEAVCMapDataZ_1401979B0.h" />
    <ClInclude Include="headers\GetMapCMapOperationQEAAPEAVCMapDataHZ_140197970.h" />
    <ClInclude Include="headers\GetMapCMapOperationQEAAPEAVCMapDataPEADZ_140197A30.h" />
    <ClInclude Include="headers\GetMapCodeCMapDataQEAAEXZ_1400C2CD0.h" />
    <ClInclude Include="headers\GetMapCurDirectCTransportShipQEAAPEAVCMapDataXZ_140264DD0.h" />
    <ClInclude Include="headers\GetMapDataCGuildRoomInfoQEAAPEAVCMapDataXZ_1402EB250.h" />
    <ClInclude Include="headers\GetMapPosCGuildRoomInfoQEAA_NPEAMZ_1402E6690.h" />
    <ClInclude Include="headers\GetMaxDMGSFContCountCMonsterQEAAHXZ_140147050.h" />
    <ClInclude Include="headers\GetMaxHPCMonsterUEAAHXZ_1401462A0.h" />
    <ClInclude Include="headers\GetMaxToleranceProbMaxMonsterSetInfoDataQEAAMHZ_14014BDF0.h" />
    <ClInclude Include="headers\GetMipMapSkipSizeYAHPEAU_DDSURFACEDESC2KKKZ_1404FFE70.h" />
    <ClInclude Include="headers\GetMob_AsistTypeCMonsterQEAAHXZ_140161590.h" />
    <ClInclude Include="headers\GetMob_SubRaceCMonsterQEAAHXZ_140161570.h" />
    <ClInclude Include="headers\GetMonStateInfoCMonsterQEAAGXZ_140143720.h" />
    <ClInclude Include="headers\GetMonsterDropRateMonsterSetInfoDataQEAAKHZ_14015D510.h" />
    <ClInclude Include="headers\GetMonsterForcePowerRateMonsterSetInfoDataQEAAMXZ_140161640.h" />
    <ClInclude Include="headers\GetMonsterGradeCMonsterQEAAHXZ_14014BFD0.h" />
    <ClInclude Include="headers\GetMonsterNumInCurMissionAreaCDarkHoleChannelQEAAH_14026B170.h" />
    <ClInclude Include="headers\GetMonsterSetCMonsterEventSetQEAAPEAU_monster_set__1402A9030.h" />
    <ClInclude Include="headers\GetMoveSpeedCMonsterQEAAMXZ_140142D80.h" />
    <ClInclude Include="headers\GetMoveTypeCMonsterQEAAEXZ_1401437B0.h" />
    <ClInclude Include="headers\GetMyDMGSFContCountCMonsterQEAAHXZ_1401470B0.h" />
    <ClInclude Include="headers\GetNewMonSerialCMonsterSAKXZ_14014BFF0.h" />
    <ClInclude Include="headers\GetObjNameCMonsterUEAAPEADXZ_140142700.h" />
    <ClInclude Include="headers\GetObjRaceCMonsterUEAAHXZ_14014BB60.h" />
    <ClInclude Include="headers\GetOffensiveTypeCMonsterQEAAHXZ_1401554D0.h" />
    <ClInclude Include="headers\GetParentCMonsterHierarchyQEAAPEAVCMonsterXZ_14014C300.h" />
    <ClInclude Include="headers\GetPathFinderCMonsterAIQEAAPEAVCPathMgrXZ_1401555B0.h" />
    <ClInclude Include="headers\GetPortalCMapDataQEAAPEAU_portal_dummyHZ_1401846A0.h" />
    <ClInclude Include="headers\GetPortalCMapDataQEAAPEAU_portal_dummyPEADZ_140184550.h" />
    <ClInclude Include="headers\GetPortalInxCCircleZoneQEAAHXZ_140034B00.h" />
    <ClInclude Include="headers\GetPortalInxCMapDataQEAAHPEADZ_140184600.h" />
    <ClInclude Include="headers\GetPosStartMapCMapOperationQEAAPEAVCMapDataE_NPEAM_140197B90.h" />
    <ClInclude Include="headers\GetRaceTownCMapDataQEAAEPEAMEZ_1401862D0.h" />
    <ClInclude Include="headers\GetRandPosInDummyCMapDataQEAA_NPEAU_dummy_position_1401857A0.h" />
    <ClInclude Include="headers\GetRandPosInRangeCMapDataQEAA_NPEAMH0Z_140185B10.h" />
    <ClInclude Include="headers\GetRandPosVirtualDumCMapDataQEAA_NPEAMH0Z_140185C70.h" />
    <ClInclude Include="headers\GetRandPosVirtualDumExcludeStdRangeCMapDataQEAA_NP_140185EE0.h" />
    <ClInclude Include="headers\GetRectInRadiusCMapDataQEAAXPEAU_pnt_rectHHZ_1401843E0.h" />
    <ClInclude Include="headers\GetResDummySectorCMapDataQEAAHHPEAMZ_140184950.h" />
    <ClInclude Include="headers\GetRuntimeClassCMapTabUEBAPEAUCRuntimeClassXZ_14002E460.h" />
    <ClInclude Include="headers\GetSecInfoCMapDataQEAAPEAU_sec_infoXZ_1401843A0.h" />
    <ClInclude Include="headers\GetSectorIndexCMapDataQEAAHPEAMZ_140184790.h" />
    <ClInclude Include="headers\GetSectorListObjCMapDataQEAAPEAVCObjectListGKZ_140184890.h" />
    <ClInclude Include="headers\GetSectorListTowerCMapDataQEAAPEAVCObjectListGKZ_140184910.h" />
    <ClInclude Include="headers\GetSectorNumByLayerIndexCMapDataQEAAHGZ_1401866D0.h" />
    <ClInclude Include="headers\GetSettlementMapDataCMapOperationQEAAPEAVCMapDataH_1402D7960.h" />
    <ClInclude Include="headers\GetSignalReActorCMonsterQEAAPEAVCLuaSignalReActorX_140406790.h" />
    <ClInclude Include="headers\GetSoilTolCMonsterUEAAHXZ_140145820.h" />
    <ClInclude Include="headers\GetStartMapCMapOperationQEAAPEAVCMapDataEZ_140197AE0.h" />
    <ClInclude Include="headers\GetStateChunkMonsterStateDataQEBAGXZ_14014C450.h" />
    <ClInclude Include="headers\GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateTBL_14014C040.h" />
    <ClInclude Include="headers\GetThisClassCMapTabSAPEAUCRuntimeClassXZ_14002E440.h" />
    <ClInclude Include="headers\GetTypeCMoveMapLimitInfoQEAAHXZ_1403A6F50.h" />
    <ClInclude Include="headers\GetTypeCMoveMapLimitRightQEBAHXZ_1403AE6B0.h" />
    <ClInclude Include="headers\GetViewAngleCapCMonsterQEAA_NHAEAHZ_140146C20.h" />
    <ClInclude Include="headers\GetVisualAngleCMonsterQEAAMXZ_14014CAA0.h" />
    <ClInclude Include="headers\GetVisualFieldCMonsterQEAAMXZ_14014BF80.h" />
    <ClInclude Include="headers\GetWaterTolCMonsterUEAAHXZ_140145720.h" />
    <ClInclude Include="headers\GetWidthCMonsterUEAAMXZ_140146610.h" />
    <ClInclude Include="headers\GetWindTolCMonsterUEAAHXZ_140145920.h" />
    <ClInclude Include="headers\GetWorldFromLocalCExtDummyQEAAHPEAY02MKQEAMZ_1404DF130.h" />
    <ClInclude Include="headers\GetYAngleByteCMonsterQEAAEXZ_14014CB50.h" />
    <ClInclude Include="headers\GetYAngleCMonsterQEAAMXZ_1401438D0.h" />
    <ClInclude Include="headers\gm_MapChangeCMainThreadQEAAXPEAVCMapDataZ_1401F79D0.h" />
    <ClInclude Include="headers\gm_UpdateMapCMainThreadQEAAXXZ_1401F7E60.h" />
    <ClInclude Include="headers\GoalCCircleZoneQEAAEPEAVCMapDataPEAMZ_14012DBE0.h" />
    <ClInclude Include="headers\G_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B15B0.h" />
    <ClInclude Include="headers\G_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B0B40.h" />
    <ClInclude Include="headers\HearMapSoundCBspQEAAXXZ_1404FD620.h" />
    <ClInclude Include="headers\HierarcyHelpCastCMonsterHelperSAXPEAVCMonsterZ_14015A480.h" />
    <ClInclude Include="headers\H_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B09F0.h" />
    <ClInclude Include="headers\InsertNpcQuestHistoryCQuestMgrQEAAEPEADENZ_14028B320.h" />
    <ClInclude Include="headers\insertvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403A91B0.h" />
    <ClInclude Include="headers\insertvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AEEE0.h" />
    <ClInclude Include="headers\insertvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403B0740.h" />
    <ClInclude Include="headers\insert_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018D710.h" />
    <ClInclude Include="headers\insert_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FFC0.h" />
    <ClInclude Include="headers\InstanceCMoveMapLimitManagerSAPEAV1XZ_1403A15F0.h" />
    <ClInclude Include="headers\InstanceCRFMonsterAIMgrSAPEAV1XZ_14014C100.h" />
    <ClInclude Include="headers\invokelua2objectPEAVCMonsterlua_tinkerSAPEAVCMonst_14040B1C0.h" />
    <ClInclude Include="headers\invokeobject2luaPEAVCMonsterlua_tinkerSAXPEAUlua_S_14040B350.h" />
    <ClInclude Include="headers\invokePEAVCLuaSignalReActormem_functorVCMonsterXXX_140409390.h" />
    <ClInclude Include="headers\invokePEAVCMonsterfunctorPEADPEADMMMlua_tinkerSAHP_140409470.h" />
    <ClInclude Include="headers\invokeptr2luaVCMonsterlua_tinkerSAXPEAUlua_StatePE_14040B670.h" />
    <ClInclude Include="headers\invokeuser2typeP6APEAVCMonsterPEAD0MMMZlua_tinkerS_14040A830.h" />
    <ClInclude Include="headers\invokeuser2typeP8CMonsterEAAPEAVCLuaSignalReActorX_14040A770.h" />
    <ClInclude Include="headers\invokevoid2ptrA6APEAVCMonsterPEAD0MMMZlua_tinkerSA_14040AEE0.h" />
    <ClInclude Include="headers\invokevoid2ptrVCMonsterlua_tinkerSAPEAVCMonsterPEA_14040B790.h" />
    <ClInclude Include="headers\invokevoid2typeP6APEAVCMonsterPEAD0MMMZlua_tinkerS_14040ACB0.h" />
    <ClInclude Include="headers\invokevoid2typeP8CMonsterEAAPEAVCLuaSignalReActorX_14040AC30.h" />
    <ClInclude Include="headers\invokevoid2typePEAVCMonsterlua_tinkerSAPEAVCMonste_14040B410.h" />
    <ClInclude Include="headers\invokevoid2valP8CMonsterEAAPEAVCLuaSignalReActorXZ_14040AEB0.h" />
    <ClInclude Include="headers\IsBossMonsterCMonsterQEAA_NXZ_14007D4E0.h" />
    <ClInclude Include="headers\IsCompleteNpcQuestCQuestMgrQEAA_NPEADHZ_14028A850.h" />
    <ClInclude Include="headers\IsEqualLimitCMoveMapLimitInfoQEAA_NHHKZ_1403A3E70.h" />
    <ClInclude Include="headers\IsExistStdMapIDCMapOperationQEAA_NHZ_140120AB0.h" />
    <ClInclude Include="headers\IsHaveRightCMoveMapLimitRightInfoQEAA_NHZ_1403ACBA0.h" />
    <ClInclude Include="headers\IsHaveRightCMoveMapLimitRightPortalUEAA_NXZ_1403AC6A0.h" />
    <ClInclude Include="headers\IsHaveRightCMoveMapLimitRightUEAA_NXZ_1403AE4F0.h" />
    <ClInclude Include="headers\IsINIFileChangedCMonsterEventSetQEAA_NPEBDU_FILETI_1402A9150.h" />
    <ClInclude Include="headers\IsInRegionCMapOperationQEAA_NPEADPEAVCGameObjectZ_140197C50.h" />
    <ClInclude Include="headers\IsInSectorCMonsterHelperSAHQEAM00MMPEAMZ_140158160.h" />
    <ClInclude Include="headers\IsMapInCMapDataQEAA_NPEAMZ_140184B40.h" />
    <ClInclude Include="headers\IsMovableCMonsterQEAA_NXZ_140142E20.h" />
    <ClInclude Include="headers\IsNearPositionCCircleZoneAEAA_NPEBMZ_14012DE20.h" />
    <ClInclude Include="headers\IsPossibleRepeatNpcQuestCQuestMgrQEAA_NPEADHZ_14028A900.h" />
    <ClInclude Include="headers\IsProcLinkNpcQuestCQuestMgrQEAA_NPEADHZ_14028AA30.h" />
    <ClInclude Include="headers\IsProcNpcQuestCQuestMgrQEAA_NPEADZ_14028AB00.h" />
    <ClInclude Include="headers\IsRoateMonsterCMonsterQEAA_NXZ_1401555D0.h" />
    <ClInclude Include="headers\IsRotateBlockMonsterSetInfoDataQEAA_NPEAU_mon_bloc_14015D110.h" />
    <ClInclude Include="headers\IsSame_target_monster_contsf_allinform_zoclSA_NAEA_1400F01A0.h" />
    <ClInclude Include="headers\j_0allocatorPEAVCMoveMapLimitInfostdQEAAAEBV01Z_1400131B5.h" />
    <ClInclude Include="headers\j_0allocatorPEAVCMoveMapLimitInfostdQEAAXZ_1400106D1.h" />
    <ClInclude Include="headers\j_0allocatorPEAVCMoveMapLimitRightstdQEAAAEBV01Z_14000B320.h" />
    <ClInclude Include="headers\j_0allocatorPEAVCMoveMapLimitRightstdQEAAXZ_140012C42.h" />
    <ClInclude Include="headers\j_0allocatorVCMoveMapLimitRightInfostdQEAAAEBV01Z_14000F187.h" />
    <ClInclude Include="headers\j_0allocatorVCMoveMapLimitRightInfostdQEAAXZ_140006D89.h" />
    <ClInclude Include="headers\j_0BossSchedule_MapQEAAXZ_140011770.h" />
    <ClInclude Include="headers\j_0CCircleZoneQEAAXZ_140009C3C.h" />
    <ClInclude Include="headers\j_0CMapDataQEAAXZ_14000DF1C.h" />
    <ClInclude Include="headers\j_0CMapDisplayQEAAXZ_140013E08.h" />
    <ClInclude Include="headers\j_0CMapExtendQEAAPEAPEAVCSurfaceZ_140004011.h" />
    <ClInclude Include="headers\j_0CMapExtendQEAAXZ_14000D55D.h" />
    <ClInclude Include="headers\j_0CMapOperationQEAAXZ_14000E390.h" />
    <ClInclude Include="headers\j_0CMapTabQEAAXZ_14000C991.h" />
    <ClInclude Include="headers\j_0CMonsterAggroMgrQEAAXZ_14000FBD2.h" />
    <ClInclude Include="headers\j_0CMonsterAIQEAAXZ_140007CD4.h" />
    <ClInclude Include="headers\j_0CMonsterEventRespawnQEAAXZ_140002CED.h" />
    <ClInclude Include="headers\j_0CMonsterEventSetQEAAXZ_140007CCF.h" />
    <ClInclude Include="headers\j_0CMonsterHierarchyQEAAXZ_140007577.h" />
    <ClInclude Include="headers\j_0CMonsterQEAAXZ_140011DA6.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitInfoListQEAAXZ_1400082B0.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitInfoPortalQEAAIHZ_14000BDF2.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitInfoQEAAIHZ_14000210D.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitManagerQEAAXZ_1400114EB.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitRightInfoListQEAAXZ_14000E12E.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitRightInfoQEAAAEBV0Z_14000EDEA.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitRightInfoQEAAXZ_140013C5A.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitRightPortalQEAAHZ_140002F4A.h" />
    <ClInclude Include="headers\j_0CMoveMapLimitRightQEAAHZ_140003959.h" />
    <ClInclude Include="headers\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140005BFF.h" />
    <ClInclude Include="headers\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_1400078D8.h" />
    <ClInclude Include="headers\j_0const_iterator_TreeV_Tmap_traitsVbasic_stringDU_14000A52E.h" />
    <ClInclude Include="headers\j_0CRFMonsterAIMgrQEAAXZ_14000A5FB.h" />
    <ClInclude Include="headers\j_0CWorldScheduleQEAAXZ_14000B0F0.h" />
    <ClInclude Include="headers\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000375B.h" />
    <ClInclude Include="headers\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000BD34.h" />
    <ClInclude Include="headers\j_0iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140013F48.h" />
    <ClInclude Include="headers\j_0mapVbasic_stringDUchar_traitsDstdVallocatorD2st_140013FED.h" />
    <ClInclude Include="headers\j_0MonsterSetInfoDataQEAAXZ_14000B389.h" />
    <ClInclude Include="headers\j_0MonsterStateDataQEAAXZ_14000BB54.h" />
    <ClInclude Include="headers\j_0pairViterator_TreeV_Tmap_traitsVbasic_stringDUc_14000E962.h" />
    <ClInclude Include="headers\j_0ptr2userVCMonsterlua_tinkerQEAAPEAVCMonsterZ_1400136F6.h" />
    <ClInclude Include="headers\j_0UpairCBVbasic_stringDUchar_traitsDstdVallocator_14000473C.h" />
    <ClInclude Include="headers\j_0UpairCBVbasic_stringDUchar_traitsDstdVallocator_14000911F.h" />
    <ClInclude Include="headers\j_0vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_14000D1BB.h" />
    <ClInclude Include="headers\j_0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140007DF1.h" />
    <ClInclude Include="headers\j_0vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140011180.h" />
    <ClInclude Include="headers\j_0vectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_1400040CA.h" />
    <ClInclude Include="headers\j_0_event_respawnQEAAXZ_14000211C.h" />
    <ClInclude Include="headers\j_0_mapCGameStatisticsQEAAXZ_140012274.h" />
    <ClInclude Include="headers\j_0_map_fldQEAAXZ_14000EE26.h" />
    <ClInclude Include="headers\j_0_monster_create_setdataQEAAXZ_14001019A.h" />
    <ClInclude Include="headers\j_0_monster_set_event_setQEAAXZ_14000F89E.h" />
    <ClInclude Include="headers\j_0_monster_sp_groupQEAAXZ_14000353A.h" />
    <ClInclude Include="headers\j_0_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar__140010172.h" />
    <ClInclude Include="headers\j_0_notice_move_limit_map_msg_zoclQEAAXZ_14000B037.h" />
    <ClInclude Include="headers\j_0_NPCQuestIndexTempDataQEAAXZ_140013719.h" />
    <ClInclude Include="headers\j_0_npc_create_setdataQEAAXZ_140003922.h" />
    <ClInclude Include="headers\j_0_npc_quest_list_result_zoclQEAAXZ_1400139D5.h" />
    <ClInclude Include="headers\j_0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_14000218A.h" />
    <ClInclude Include="headers\j_0_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_1400035E9.h" />
    <ClInclude Include="headers\j_0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_140005B14.h" />
    <ClInclude Include="headers\j_0_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_1400130ED.h" />
    <ClInclude Include="headers\j_0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_140005E52.h" />
    <ClInclude Include="headers\j_0_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_1400109F6.h" />
    <ClInclude Include="headers\j_0_reged_char_result_zoneQEAAXZ_14000DF9E.h" />
    <ClInclude Include="headers\j_0_respawn_monster_act_dh_mission_mgrQEAAXZ_14000722F.h" />
    <ClInclude Include="headers\j_0_state_event_respawnQEAAXZ_1400087D3.h" />
    <ClInclude Include="headers\j_0_state_monster_set_event_setQEAAXZ_140013AF2.h" />
    <ClInclude Include="headers\j_0_target_monster_aggro_inform_zoclQEAAXZ_140013FAC.h" />
    <ClInclude Include="headers\j_0_target_monster_contsf_allinform_zoclQEAAXZ_14000FFBA.h" />
    <ClInclude Include="headers\j_0_Tmap_traitsVbasic_stringDUchar_traitsDstdVallo_14000A80D.h" />
    <ClInclude Include="headers\j_0_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14000AA97.h" />
    <ClInclude Include="headers\j_0_Tree_nodV_Tmap_traitsVbasic_stringDUchar_trait_14000DD73.h" />
    <ClInclude Include="headers\j_0_Tree_ptrV_Tmap_traitsVbasic_stringDUchar_trait_140013F93.h" />
    <ClInclude Include="headers\j_0_Tree_valV_Tmap_traitsVbasic_stringDUchar_trait_14000FC3B.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000A5E2.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000FA01.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000CF59.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000E0F7.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140009BF1.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140013449.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_140006D6B.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_1400092A5.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorPEAVCMoveMapLimitRightVallocato_1400035A8.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorPEAVCMoveMapLimitRightVallocato_14000A2C7.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorVCMoveMapLimitRightInfoVallocat_140003C33.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorVCMoveMapLimitRightInfoVallocat_14000992B.h" />
    <ClInclude Include="headers\j_0_Vector_valPEAVCMoveMapLimitInfoVallocatorPEAVC_14000162C.h" />
    <ClInclude Include="headers\j_0_Vector_valPEAVCMoveMapLimitRightVallocatorPEAV_140001DED.h" />
    <ClInclude Include="headers\j_0_Vector_valVCMoveMapLimitRightInfoVallocatorVCM_14000DA26.h" />
    <ClInclude Include="headers\j_0__add_monsterQEAAXZ_14000DAEE.h" />
    <ClInclude Include="headers\j_0__change_monsterQEAAXZ_1400027BB.h" />
    <ClInclude Include="headers\j_0__monster_groupQEAAXZ_14000BC26.h" />
    <ClInclude Include="headers\j_0__respawn_monsterQEAAXZ_14000A097.h" />
    <ClInclude Include="headers\j_1BossSchedule_MapQEAAXZ_140001FB4.h" />
    <ClInclude Include="headers\j_1CCircleZoneUEAAXZ_1400012B2.h" />
    <ClInclude Include="headers\j_1CMapDataUEAAXZ_1400102E9.h" />
    <ClInclude Include="headers\j_1CMapDisplayUEAAXZ_14000C892.h" />
    <ClInclude Include="headers\j_1CMapExtendQEAAXZ_140012C83.h" />
    <ClInclude Include="headers\j_1CMapOperationUEAAXZ_14000CB21.h" />
    <ClInclude Include="headers\j_1CMapTabUEAAXZ_140006762.h" />
    <ClInclude Include="headers\j_1CMonsterAggroMgrQEAAXZ_140008D1E.h" />
    <ClInclude Include="headers\j_1CMonsterAIUEAAXZ_14000A254.h" />
    <ClInclude Include="headers\j_1CMonsterEventRespawnUEAAXZ_14000C239.h" />
    <ClInclude Include="headers\j_1CMonsterEventSetUEAAXZ_1400063A7.h" />
    <ClInclude Include="headers\j_1CMonsterHierarchyUEAAXZ_1400020F4.h" />
    <ClInclude Include="headers\j_1CMonsterUEAAXZ_140012B9D.h" />
    <ClInclude Include="headers\j_1CMoveMapLimitInfoListQEAAXZ_140001D9D.h" />
    <ClInclude Include="headers\j_1CMoveMapLimitInfoPortalQEAAXZ_14000D88C.h" />
    <ClInclude Include="headers\j_1CMoveMapLimitInfoQEAAXZ_140002CE3.h" />
    <ClInclude Include="headers\j_1CMoveMapLimitManagerQEAAXZ_140012DBE.h" />
    <ClInclude Include="headers\j_1CMoveMapLimitRightInfoListQEAAXZ_14000F36C.h" />
    <ClInclude Include="headers\j_1CMoveMapLimitRightInfoQEAAXZ_14000AA88.h" />
    <ClInclude Include="headers\j_1CMoveMapLimitRightQEAAXZ_14001325A.h" />
    <ClInclude Include="headers\j_1const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140010064.h" />
    <ClInclude Include="headers\j_1CRFMonsterAIMgrQEAAXZ_14000495D.h" />
    <ClInclude Include="headers\j_1CWorldScheduleQEAAXZ_14000240F.h" />
    <ClInclude Include="headers\j_1iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_1400127F1.h" />
    <ClInclude Include="headers\j_1mapVbasic_stringDUchar_traitsDstdVallocatorD2st_140010451.h" />
    <ClInclude Include="headers\j_1pairViterator_TreeV_Tmap_traitsVbasic_stringDUc_14000B500.h" />
    <ClInclude Include="headers\j_1ptr2userVCMonsterlua_tinkerUEAAXZ_140008FCB.h" />
    <ClInclude Include="headers\j_1vectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_14000FE8E.h" />
    <ClInclude Include="headers\j_1vectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_14000C478.h" />
    <ClInclude Include="headers\j_1vectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_14000987C.h" />
    <ClInclude Include="headers\j_1_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar__14000F727.h" />
    <ClInclude Include="headers\j_1_RanitPEAVCMoveMapLimitInfo_JPEBQEAV1AEBQEAV1st_140012675.h" />
    <ClInclude Include="headers\j_1_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_140003B2A.h" />
    <ClInclude Include="headers\j_1_RanitVCMoveMapLimitRightInfo_JPEBV1AEBV1stdQEA_140011F4F.h" />
    <ClInclude Include="headers\j_1_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDst_14000B569.h" />
    <ClInclude Include="headers\j_1_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000EC05.h" />
    <ClInclude Include="headers\j_1_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000B2D5.h" />
    <ClInclude Include="headers\j_1_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140011C2F.h" />
    <ClInclude Include="headers\j_1_Vector_iteratorPEAVCMoveMapLimitInfoVallocator_14000DAB7.h" />
    <ClInclude Include="headers\j_1_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140003954.h" />
    <ClInclude Include="headers\j_1_Vector_iteratorVCMoveMapLimitRightInfoVallocat_14000B271.h" />
    <ClInclude Include="headers\j_1__change_monsterQEAAXZ_140003DB9.h" />
    <ClInclude Include="headers\j_4CMoveMapLimitRightInfoQEAAAEBV0AEBV0Z_140006C3F.h" />
    <ClInclude Include="headers\j_4const_iterator_TreeV_Tmap_traitsVbasic_stringDU_14001050F.h" />
    <ClInclude Include="headers\j_4iterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000AC7C.h" />
    <ClInclude Include="headers\j_4_RanitPEAVCMoveMapLimitRight_JPEBQEAV1AEBQEAV1s_14000D4A4.h" />
    <ClInclude Include="headers\j_4_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000159B.h" />
    <ClInclude Include="headers\j_4_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140007ABD.h" />
    <ClInclude Include="headers\j_8const_iterator_TreeV_Tmap_traitsVbasic_stringDU_1400088FA.h" />
    <ClInclude Include="headers\j_8_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000EB88.h" />
    <ClInclude Include="headers\j_8_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000E908.h" />
    <ClInclude Include="headers\j_8_Vector_const_iteratorVCMoveMapLimitRightInfoVa_140004FD4.h" />
    <ClInclude Include="headers\j_9const_iterator_TreeV_Tmap_traitsVbasic_stringDU_140006DA2.h" />
    <ClInclude Include="headers\j_9MonsterStateDataQEBA_NAEBV0Z_14000E9B2.h" />
    <ClInclude Include="headers\j_9_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000A867.h" />
    <ClInclude Include="headers\j_9_Vector_const_iteratorPEAVCMoveMapLimitRightVal_140004A84.h" />
    <ClInclude Include="headers\j_9_Vector_const_iteratorVCMoveMapLimitRightInfoVa_14000A7D6.h" />
    <ClInclude Include="headers\j_AddMonsterCDarkHoleChannelQEAAXXZ_14000AC04.h" />
    <ClInclude Include="headers\j_allocateallocatorPEAVCMoveMapLimitInfostdQEAAPEA_14000984F.h" />
    <ClInclude Include="headers\j_allocateallocatorPEAVCMoveMapLimitRightstdQEAAPE_1400086DE.h" />
    <ClInclude Include="headers\j_allocateallocatorU_Node_Tree_nodV_Tmap_traitsVba_140006F28.h" />
    <ClInclude Include="headers\j_allocateallocatorVCMoveMapLimitRightInfostdQEAAP_14000CF6D.h" />
    <ClInclude Include="headers\j_AmapVbasic_stringDUchar_traitsDstdVallocatorD2st_14000B7F3.h" />
    <ClInclude Include="headers\j_assignvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_14000E5F2.h" />
    <ClInclude Include="headers\j_assignvectorVCMoveMapLimitRightInfoVallocatorVCM_1400059B6.h" />
    <ClInclude Include="headers\j_AutoRecoverCMonsterQEAAXXZ_140008017.h" />
    <ClInclude Include="headers\j_AvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMoveM_1400098E0.h" />
    <ClInclude Include="headers\j_AvectorPEAVCMoveMapLimitRightVallocatorPEAVCMove_140014029.h" />
    <ClInclude Include="headers\j_AvectorVCMoveMapLimitRightInfoVallocatorVCMoveMa_14000C0EA.h" />
    <ClInclude Include="headers\j_beginvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140003F85.h" />
    <ClInclude Include="headers\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000CEF0.h" />
    <ClInclude Include="headers\j_beginvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D706.h" />
    <ClInclude Include="headers\j_beginvectorVCMoveMapLimitRightInfoVallocatorVCMo_1400132D7.h" />
    <ClInclude Include="headers\j_begin_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000965B.h" />
    <ClInclude Include="headers\j_CalcScheduleCursorCWorldScheduleQEAAHHHZ_14000BF9B.h" />
    <ClInclude Include="headers\j_CalcStartNPCQuestCntCQuestMgrSA_NQEAKZ_1400033E1.h" />
    <ClInclude Include="headers\j_capacityvectorPEAVCMoveMapLimitInfoVallocatorPEA_140006839.h" />
    <ClInclude Include="headers\j_capacityvectorPEAVCMoveMapLimitRightVallocatorPE_14001099C.h" />
    <ClInclude Include="headers\j_capacityvectorVCMoveMapLimitRightInfoVallocatorV_140001F50.h" />
    <ClInclude Include="headers\j_ChangeApparitionCMonsterQEAAX_NKZ_1400084A4.h" />
    <ClInclude Include="headers\j_ChangeLayerCMapDisplayQEAA_NGZ_140007AA9.h" />
    <ClInclude Include="headers\j_ChangeMapCMapDisplayQEAA_NPEAVCMapDataZ_14000C2AC.h" />
    <ClInclude Include="headers\j_ChangeMonsterApparitionCDarkHoleChannelQEAAXHZ_140007B94.h" />
    <ClInclude Include="headers\j_ChangeMonsterCDarkHoleChannelQEAAXXZ_14000EDC7.h" />
    <ClInclude Include="headers\j_ChangeSchCursorCWorldScheduleQEAAXPEAU_WorldSche_1400100F5.h" />
    <ClInclude Include="headers\j_ChangeTargetPosDfAIMgrSAXPEAVCMonsterPEAMZ_1400069DD.h" />
    <ClInclude Include="headers\j_ChatMapRequestCNetworkEXAEAA_NHPEADZ_140008AB7.h" />
    <ClInclude Include="headers\j_CheckAlienationDfAIMgrSAHPEAVCMonsterZ_14001119E.h" />
    <ClInclude Include="headers\j_CheckAutoRecoverHPCMonsterQEAAXXZ_14000F862.h" />
    <ClInclude Include="headers\j_CheckCenterPosDummyCMapDataQEAA_NPEAU_dummy_posi_1400097A5.h" />
    <ClInclude Include="headers\j_CheckDelayDestroyCMonsterQEAA_NXZ_14000A989.h" />
    <ClInclude Include="headers\j_CheckEmotionBadDfAIMgrSAHPEAVCMonsterPEAVCMonste_14000D675.h" />
    <ClInclude Include="headers\j_CheckEmotionPresentationCMonsterQEAAXXZ_140006E9C.h" />
    <ClInclude Include="headers\j_CheckEventSetRespawnCMonsterEventSetQEAAXXZ_14000BB6D.h" />
    <ClInclude Include="headers\j_CheckGenDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_14000D562.h" />
    <ClInclude Include="headers\j_CheckMapPortalLinkCMapOperationAEAAXXZ_140005885.h" />
    <ClInclude Include="headers\j_CheckMonArea_N_ChangeStateDfAIMgrSAHPEAVCMonster_140002B5D.h" />
    <ClInclude Include="headers\j_CheckMonsterRotateCMonsterQEAAXXZ_140010A8C.h" />
    <ClInclude Include="headers\j_CheckMonsterStateDataCMonsterQEAA_NXZ_1400048FE.h" />
    <ClInclude Include="headers\j_CheckNPCQuestListCQuestMgrQEAAXPEADEPEAU_NPCQues_1400013FC.h" />
    <ClInclude Include="headers\j_CheckRespawnEventCMonsterEventRespawnQEAAXXZ_1400100A5.h" />
    <ClInclude Include="headers\j_CheckRespawnMonsterCDarkHoleChannelQEAAXXZ_14000902F.h" />
    <ClInclude Include="headers\j_CheckRespawnProcessCMonsterQEAA_NXZ_140013A25.h" />
    <ClInclude Include="headers\j_CheckSchCWorldScheduleQEAAXXZ_140006E65.h" />
    <ClInclude Include="headers\j_CheckSPFDelayTimeDfAIMgrSAHPEAVCMonsterAIHKZ_14000F7EF.h" />
    <ClInclude Include="headers\j_CheckSPFDfAIMgrSAHPEAVCMonsterAIPEAVCMonsterZ_1400044EE.h" />
    <ClInclude Include="headers\j_check_dummyAutominePersonalMgrQEAA_NPEAVCMapData_140012DF0.h" />
    <ClInclude Include="headers\j_ChildKindCountCMonsterHierarchyQEAAEXZ_140012382.h" />
    <ClInclude Include="headers\j_class_addVCMonsterlua_tinkerYAXPEAUlua_StatePEBD_140002D8D.h" />
    <ClInclude Include="headers\j_class_defVCMonsterP81EAAPEAVCLuaSignalReActorXZl_140002AC2.h" />
    <ClInclude Include="headers\j_CleanUpCMoveMapLimitInfoListAEAAXXZ_140012422.h" />
    <ClInclude Include="headers\j_CleanUpCMoveMapLimitRightInfoAEAAXXZ_14000A213.h" />
    <ClInclude Include="headers\j_CleanUpCMoveMapLimitRightUEAAXXZ_140009BCE.h" />
    <ClInclude Include="headers\j_ClearBossSchedule_MapQEAAXXZ_1400034AE.h" />
    <ClInclude Include="headers\j_ClearEmotionPresentationCMonsterQEAAXXZ_140013B47.h" />
    <ClInclude Include="headers\j_clearvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_1400102D0.h" />
    <ClInclude Include="headers\j_clearvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013F8E.h" />
    <ClInclude Include="headers\j_clear_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000D657.h" />
    <ClInclude Include="headers\j_Command_ChildMonDestroyCMonsterQEAAXKZ_1400098F4.h" />
    <ClInclude Include="headers\j_constructallocatorPEAU_Node_Tree_nodV_Tmap_trait_140007EF5.h" />
    <ClInclude Include="headers\j_constructallocatorPEAVCMoveMapLimitRightstdQEAAX_14000DD3C.h" />
    <ClInclude Include="headers\j_constructallocatorVCMoveMapLimitRightInfostdQEAA_14000FE43.h" />
    <ClInclude Include="headers\j_ConvertLocalCMapDataQEAA_NPEAU_dummy_positionZ_1400056B9.h" />
    <ClInclude Include="headers\j_ConvertToMapCMapExtendQEAAXPEAVCSizeZ_1400035AD.h" />
    <ClInclude Include="headers\j_CreateAICMonsterQEAAHHZ_14000B104.h" />
    <ClInclude Include="headers\j_CreateCCircleZoneQEAA_NPEAVCMapDataEZ_140002D42.h" />
    <ClInclude Include="headers\j_CreateCGravityStoneRegenerQEAA_NPEAVCMapDataZ_14000AB0A.h" />
    <ClInclude Include="headers\j_CreateCMerchantQEAA_NPEAU_npc_create_setdataZ_140004750.h" />
    <ClInclude Include="headers\j_CreateCMonsterQEAA_NPEAU_monster_create_setdataZ_1400073D3.h" />
    <ClInclude Include="headers\j_CreateCMoveMapLimitInfoSAPEAV1IHZ_14001258A.h" />
    <ClInclude Include="headers\j_CreateCMoveMapLimitRightSAPEAV1HZ_140011CB1.h" />
    <ClInclude Include="headers\j_CreateMonsterCDarkHoleChannelQEAAXXZ_1400068A2.h" />
    <ClInclude Include="headers\j_CreateObjectCMapTabSAPEAVCObjectXZ_140002A54.h" />
    <ClInclude Include="headers\j_CreateObjSurfaceCMapDisplayAEAAJXZ_140007590.h" />
    <ClInclude Include="headers\j_CreatePaletteFromBitmapCDisplayQEAAJPEAPEAUIDire_140008DD2.h" />
    <ClInclude Include="headers\j_CreateRepMonsterYAPEAVCMonsterPEAVCMapDataGPEAMP_140009EDF.h" />
    <ClInclude Include="headers\j_CreateRespawnMonsterYAPEAVCMonsterPEAVCMapDataGH_14000BFDC.h" />
    <ClInclude Include="headers\j_CreateSurfaceFromBitmapCDisplayQEAAJPEAPEAVCSurf_1400108B6.h" />
    <ClInclude Include="headers\j_DataCheckCWorldScheduleQEAA_NXZ_140010AF0.h" />
    <ClInclude Include="headers\j_Dconst_iterator_TreeV_Tmap_traitsVbasic_stringDU_140001361.h" />
    <ClInclude Include="headers\j_deallocateallocatorPEAVCMoveMapLimitInfostdQEAAX_140001D52.h" />
    <ClInclude Include="headers\j_deallocateallocatorPEAVCMoveMapLimitRightstdQEAA_14001327D.h" />
    <ClInclude Include="headers\j_deallocateallocatorU_Node_Tree_nodV_Tmap_traitsV_14001329B.h" />
    <ClInclude Include="headers\j_deallocateallocatorVCMoveMapLimitRightInfostdQEA_140003391.h" />
    <ClInclude Include="headers\j_defP6APEAVCMonsterPEAD0MMMZlua_tinkerYAXPEAUlua__14000A8E4.h" />
    <ClInclude Include="headers\j_DestoryCRFMonsterAIMgrSAXXZ_14000CA8B.h" />
    <ClInclude Include="headers\j_destroyallocatorPEAU_Node_Tree_nodV_Tmap_traitsV_140007234.h" />
    <ClInclude Include="headers\j_destroyallocatorPEAVCMoveMapLimitRightstdQEAAXPE_140010226.h" />
    <ClInclude Include="headers\j_destroyallocatorU_Node_Tree_nodV_Tmap_traitsVbas_140013AAC.h" />
    <ClInclude Include="headers\j_destroyallocatorVCMoveMapLimitRightInfostdQEAAXP_14000D300.h" />
    <ClInclude Include="headers\j_DestroyCCircleZoneQEAAXXZ_140012373.h" />
    <ClInclude Include="headers\j_DestroyCMonsterQEAA_NEPEAVCGameObjectZ_14000AA29.h" />
    <ClInclude Include="headers\j_DestroyCMoveMapLimitManagerSAXXZ_140012715.h" />
    <ClInclude Include="headers\j_destroyerVCMonsterlua_tinkerYAHPEAUlua_StateZ_1400137A5.h" />
    <ClInclude Include="headers\j_Diterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140004F7F.h" />
    <ClInclude Include="headers\j_DoDataExchangeCMapTabMEAAXPEAVCDataExchangeZ_140005452.h" />
    <ClInclude Include="headers\j_DrawBitmapCSurfaceQEAAJPEADKKZ_14000327E.h" />
    <ClInclude Include="headers\j_DrawBitmapCSurfaceQEAAJPEAUHBITMAP__KKKKZ_140002F0E.h" />
    <ClInclude Include="headers\j_DrawCollisionLineCMapDisplayAEAAXXZ_140001889.h" />
    <ClInclude Include="headers\j_DrawDisplayCMapDisplayQEAAXXZ_14000380F.h" />
    <ClInclude Include="headers\j_DrawDummyCMapDisplayAEAAXXZ_140010721.h" />
    <ClInclude Include="headers\j_DrawMapCMapDisplayAEAAXXZ_14000C77F.h" />
    <ClInclude Include="headers\j_DrawObjectCMapDisplayAEAAXXZ_1400114C3.h" />
    <ClInclude Include="headers\j_DrawRectCMapExtendQEAAXXZ_140011752.h" />
    <ClInclude Include="headers\j_DrawSelectMonsterLookAtPosCMapDisplayAEAAJPEAVCM_1400070E0.h" />
    <ClInclude Include="headers\j_DrawTextACMapDisplayAEAAXXZ_140002A5E.h" />
    <ClInclude Include="headers\j_D_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000F5AB.h" />
    <ClInclude Include="headers\j_D_Vector_const_iteratorPEAVCMoveMapLimitRightVal_140001BE5.h" />
    <ClInclude Include="headers\j_D_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140004908.h" />
    <ClInclude Include="headers\j_Econst_iterator_TreeV_Tmap_traitsVbasic_stringDU_140004525.h" />
    <ClInclude Include="headers\j_Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_14000457A.h" />
    <ClInclude Include="headers\j_Eiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140012062.h" />
    <ClInclude Include="headers\j_emptyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000AC63.h" />
    <ClInclude Include="headers\j_EndScreenPointCMapExtendQEAAHPEAVCSizeZ_14000B212.h" />
    <ClInclude Include="headers\j_endvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_140002E41.h" />
    <ClInclude Include="headers\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_140001D8E.h" />
    <ClInclude Include="headers\j_endvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_14000B8ED.h" />
    <ClInclude Include="headers\j_endvectorVCMoveMapLimitRightInfoVallocatorVCMove_14000EC91.h" />
    <ClInclude Include="headers\j_end_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_1400054BB.h" />
    <ClInclude Include="headers\j_EnterMapCMapDataQEAAXPEAVCGameObjectKZ_14000EDF9.h" />
    <ClInclude Include="headers\j_EnterWorldRequestCNetworkEXAEAA_NHPEAU_MSG_HEADE_1400053DA.h" />
    <ClInclude Include="headers\j_EnterWorldResultCNetworkEXAEAA_NKPEADZ_1400105AA.h" />
    <ClInclude Include="headers\j_erasevectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_14000B7B7.h" />
    <ClInclude Include="headers\j_erasevectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000D391.h" />
    <ClInclude Include="headers\j_erasevectorVCMoveMapLimitRightInfoVallocatorVCMo_140008A9E.h" />
    <ClInclude Include="headers\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140003968.h" />
    <ClInclude Include="headers\j_erase_TreeV_Tmap_traitsVbasic_stringDUchar_trait_1400118AB.h" />
    <ClInclude Include="headers\j_ExitMapCMapDataQEAAXPEAVCGameObjectKZ_1400021E9.h" />
    <ClInclude Include="headers\j_ExitWorldRequestCNetworkEXAEAA_NHPEADZ_140001005.h" />
    <ClInclude Include="headers\j_E_Vector_const_iteratorPEAVCMoveMapLimitInfoVall_14000BBCC.h" />
    <ClInclude Include="headers\j_E_Vector_const_iteratorPEAVCMoveMapLimitRightVal_1400045E8.h" />
    <ClInclude Include="headers\j_E_Vector_iteratorPEAVCMoveMapLimitRightVallocato_1400038FA.h" />
    <ClInclude Include="headers\j_Fconst_iterator_TreeV_Tmap_traitsVbasic_stringDU_14001361F.h" />
    <ClInclude Include="headers\j_fillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCM_140002153.h" />
    <ClInclude Include="headers\j_fillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVC_14001335E.h" />
    <ClInclude Include="headers\j_fillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveM_14001310B.h" />
    <ClInclude Include="headers\j_FindEmptyNPCYAPEAVCMerchantPEAV1HZ_140010E60.h" />
    <ClInclude Include="headers\j_findV_Vector_iteratorPEAVCMoveMapLimitRightVallo_1400103B1.h" />
    <ClInclude Include="headers\j_Fiterator_TreeV_Tmap_traitsVbasic_stringDUchar_t_140001569.h" />
    <ClInclude Include="headers\j_GeEmotionImpStdTimeCMonsterQEAAMXZ_14000388C.h" />
    <ClInclude Include="headers\j_GetAggroResetTimeCMonsterQEAAKXZ_1400116D5.h" />
    <ClInclude Include="headers\j_GetAggroShortTimeCMonsterQEAAKXZ_140003B11.h" />
    <ClInclude Include="headers\j_GetAngleCMonsterHelperSAMQEAM0Z_140007D24.h" />
    <ClInclude Include="headers\j_GetBonusInAreaAggroCMonsterQEAAMXZ_1400050FB.h" />
    <ClInclude Include="headers\j_GetBspInfoCMapDataQEAAPEAU_bsp_infoXZ_14000885F.h" />
    <ClInclude Include="headers\j_GetChildCMonsterHierarchyQEAAPEAVCMonsterHHZ_140004A02.h" />
    <ClInclude Include="headers\j_GetChildCountCMonsterHierarchyQEAAKHZ_1400077F2.h" />
    <ClInclude Include="headers\j_GetCMoveMapLimitInfoListAEAAPEAVCMoveMapLimitInf_140007626.h" />
    <ClInclude Include="headers\j_GetCMoveMapLimitRightInfoListQEAAPEAVCMoveMapLim_1400059F2.h" />
    <ClInclude Include="headers\j_GetColorCCircleZoneQEAAEXZ_140011B58.h" />
    <ClInclude Include="headers\j_GetCritical_Exception_RateCMonsterUEAAHXZ_14000D6B6.h" />
    <ClInclude Include="headers\j_GetDefFacingCMonsterUEAAMHZ_1400048CC.h" />
    <ClInclude Include="headers\j_GetDefGapCMonsterUEAAMHZ_14000C950.h" />
    <ClInclude Include="headers\j_GetDirectionCMonsterHelperSAXAEAY02M00MZ_14000CCA2.h" />
    <ClInclude Include="headers\j_GetDummyPostionCMapDataQEAAPEAU_dummy_positionPE_14000ABD2.h" />
    <ClInclude Include="headers\j_GetEmotionStateCMonsterQEAAEXZ_140006730.h" />
    <ClInclude Include="headers\j_GetEmptyEventSetCMonsterEventSetQEAAPEAU_event_s_140012B70.h" />
    <ClInclude Include="headers\j_GetEvenSetLootingCMonsterEventSetQEAAPEAU_event__1400126DE.h" />
    <ClInclude Include="headers\j_GetExtendSizeCMapExtendQEAAPEAVCSizeXZ_14000270C.h" />
    <ClInclude Include="headers\j_GetFireTolCMonsterUEAAHXZ_14000A14B.h" />
    <ClInclude Include="headers\j_GetHelpMeCaseCMonsterQEAAHXZ_1400024A0.h" />
    <ClInclude Include="headers\j_GetHPCMonsterUEAAHXZ_1400032B0.h" />
    <ClInclude Include="headers\j_GetInxCMoveMapLimitInfoQEAAIXZ_140013291.h" />
    <ClInclude Include="headers\j_GetLinkPortalCMapDataQEAAPEAU_portal_dummyPEADZ_14000E624.h" />
    <ClInclude Include="headers\j_GetLostMonsterTargetDistanceMonsterSetInfoDataQE_140006460.h" />
    <ClInclude Include="headers\j_GetMapCMapOperationQEAAHPEAVCMapDataZ_14000CFEF.h" />
    <ClInclude Include="headers\j_GetMapCMapOperationQEAAPEAVCMapDataHZ_14000E4D0.h" />
    <ClInclude Include="headers\j_GetMapCMapOperationQEAAPEAVCMapDataPEADZ_14000A042.h" />
    <ClInclude Include="headers\j_GetMapCodeCMapDataQEAAEXZ_14000CB2B.h" />
    <ClInclude Include="headers\j_GetMapCurDirectCTransportShipQEAAPEAVCMapDataXZ_140013A89.h" />
    <ClInclude Include="headers\j_GetMapDataCGuildRoomInfoQEAAPEAVCMapDataXZ_14000786F.h" />
    <ClInclude Include="headers\j_GetMapPosCGuildRoomInfoQEAA_NPEAMZ_1400035F3.h" />
    <ClInclude Include="headers\j_GetMaxDMGSFContCountCMonsterQEAAHXZ_140003D32.h" />
    <ClInclude Include="headers\j_GetMaxHPCMonsterUEAAHXZ_1400044C1.h" />
    <ClInclude Include="headers\j_GetMaxToleranceProbMaxMonsterSetInfoDataQEAAMHZ_1400082C9.h" />
    <ClInclude Include="headers\j_GetMob_AsistTypeCMonsterQEAAHXZ_140012CBF.h" />
    <ClInclude Include="headers\j_GetMob_SubRaceCMonsterQEAAHXZ_140011C52.h" />
    <ClInclude Include="headers\j_GetMonStateInfoCMonsterQEAAGXZ_14000E7E1.h" />
    <ClInclude Include="headers\j_GetMonsterDropRateMonsterSetInfoDataQEAAKHZ_14000E543.h" />
    <ClInclude Include="headers\j_GetMonsterForcePowerRateMonsterSetInfoDataQEAAMX_14000DFF8.h" />
    <ClInclude Include="headers\j_GetMonsterGradeCMonsterQEAAHXZ_140006811.h" />
    <ClInclude Include="headers\j_GetMonsterNumInCurMissionAreaCDarkHoleChannelQEA_14000EE9E.h" />
    <ClInclude Include="headers\j_GetMonsterSetCMonsterEventSetQEAAPEAU_monster_se_140013403.h" />
    <ClInclude Include="headers\j_GetMoveSpeedCMonsterQEAAMXZ_14000DF62.h" />
    <ClInclude Include="headers\j_GetMoveTypeCMonsterQEAAEXZ_1400076DF.h" />
    <ClInclude Include="headers\j_GetMyDMGSFContCountCMonsterQEAAHXZ_14000D918.h" />
    <ClInclude Include="headers\j_GetNewMonSerialCMonsterSAKXZ_140012E45.h" />
    <ClInclude Include="headers\j_GetObjNameCMonsterUEAAPEADXZ_14000DC3D.h" />
    <ClInclude Include="headers\j_GetObjRaceCMonsterUEAAHXZ_140005317.h" />
    <ClInclude Include="headers\j_GetOffensiveTypeCMonsterQEAAHXZ_14000F448.h" />
    <ClInclude Include="headers\j_GetParentCMonsterHierarchyQEAAPEAVCMonsterXZ_140003828.h" />
    <ClInclude Include="headers\j_GetPathFinderCMonsterAIQEAAPEAVCPathMgrXZ_140007AF9.h" />
    <ClInclude Include="headers\j_GetPortalCMapDataQEAAPEAU_portal_dummyHZ_140009188.h" />
    <ClInclude Include="headers\j_GetPortalCMapDataQEAAPEAU_portal_dummyPEADZ_1400110B8.h" />
    <ClInclude Include="headers\j_GetPortalInxCCircleZoneQEAAHXZ_140010A1E.h" />
    <ClInclude Include="headers\j_GetPortalInxCMapDataQEAAHPEADZ_14000B825.h" />
    <ClInclude Include="headers\j_GetPosStartMapCMapOperationQEAAPEAVCMapDataE_NPE_1400137A0.h" />
    <ClInclude Include="headers\j_GetRaceTownCMapDataQEAAEPEAMEZ_140006C2B.h" />
    <ClInclude Include="headers\j_GetRandPosInDummyCMapDataQEAA_NPEAU_dummy_positi_14000279D.h" />
    <ClInclude Include="headers\j_GetRandPosInRangeCMapDataQEAA_NPEAMH0Z_14000A1B4.h" />
    <ClInclude Include="headers\j_GetRandPosVirtualDumCMapDataQEAA_NPEAMH0Z_140007B30.h" />
    <ClInclude Include="headers\j_GetRandPosVirtualDumExcludeStdRangeCMapDataQEAA__140004CD7.h" />
    <ClInclude Include="headers\j_GetRectInRadiusCMapDataQEAAXPEAU_pnt_rectHHZ_140005F97.h" />
    <ClInclude Include="headers\j_GetResDummySectorCMapDataQEAAHHPEAMZ_140009282.h" />
    <ClInclude Include="headers\j_GetRuntimeClassCMapTabUEBAPEAUCRuntimeClassXZ_1400039E5.h" />
    <ClInclude Include="headers\j_GetSecInfoCMapDataQEAAPEAU_sec_infoXZ_14000D0B7.h" />
    <ClInclude Include="headers\j_GetSectorIndexCMapDataQEAAHPEAMZ_14000920F.h" />
    <ClInclude Include="headers\j_GetSectorListObjCMapDataQEAAPEAVCObjectListGKZ_140008C92.h" />
    <ClInclude Include="headers\j_GetSectorListTowerCMapDataQEAAPEAVCObjectListGKZ_14000AB46.h" />
    <ClInclude Include="headers\j_GetSectorNumByLayerIndexCMapDataQEAAHGZ_1400102B2.h" />
    <ClInclude Include="headers\j_GetSettlementMapDataCMapOperationQEAAPEAVCMapDat_140011405.h" />
    <ClInclude Include="headers\j_GetSignalReActorCMonsterQEAAPEAVCLuaSignalReActo_14000489F.h" />
    <ClInclude Include="headers\j_GetSoilTolCMonsterUEAAHXZ_140007E7D.h" />
    <ClInclude Include="headers\j_GetStartMapCMapOperationQEAAPEAVCMapDataEZ_140009958.h" />
    <ClInclude Include="headers\j_GetStateChunkMonsterStateDataQEBAGXZ_14000CD74.h" />
    <ClInclude Include="headers\j_GetStateTBLCRFMonsterAIMgrQEAAAVUsPointVUsStateT_140010118.h" />
    <ClInclude Include="headers\j_GetThisClassCMapTabSAPEAUCRuntimeClassXZ_14000A5D3.h" />
    <ClInclude Include="headers\j_GetTypeCMoveMapLimitInfoQEAAHXZ_1400016C2.h" />
    <ClInclude Include="headers\j_GetTypeCMoveMapLimitRightQEBAHXZ_1400137F0.h" />
    <ClInclude Include="headers\j_GetViewAngleCapCMonsterQEAA_NHAEAHZ_140010280.h" />
    <ClInclude Include="headers\j_GetVisualAngleCMonsterQEAAMXZ_1400101CC.h" />
    <ClInclude Include="headers\j_GetVisualFieldCMonsterQEAAMXZ_14000227F.h" />
    <ClInclude Include="headers\j_GetWaterTolCMonsterUEAAHXZ_14000AD3A.h" />
    <ClInclude Include="headers\j_GetWidthCMonsterUEAAMXZ_140007FB8.h" />
    <ClInclude Include="headers\j_GetWindTolCMonsterUEAAHXZ_14000673F.h" />
    <ClInclude Include="headers\j_GetYAngleByteCMonsterQEAAEXZ_140011284.h" />
    <ClInclude Include="headers\j_GetYAngleCMonsterQEAAMXZ_14000371A.h" />
    <ClInclude Include="headers\j_gm_MapChangeCMainThreadQEAAXPEAVCMapDataZ_140008F6C.h" />
    <ClInclude Include="headers\j_gm_UpdateMapCMainThreadQEAAXXZ_1400050D3.h" />
    <ClInclude Include="headers\j_GoalCCircleZoneQEAAEPEAVCMapDataPEAMZ_1400043EA.h" />
    <ClInclude Include="headers\j_G_Vector_const_iteratorPEAVCMoveMapLimitRightVal_14000DCAB.h" />
    <ClInclude Include="headers\j_G_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140003E7C.h" />
    <ClInclude Include="headers\j_HierarcyHelpCastCMonsterHelperSAXPEAVCMonsterZ_140007176.h" />
    <ClInclude Include="headers\j_H_Vector_iteratorPEAVCMoveMapLimitRightVallocato_140012751.h" />
    <ClInclude Include="headers\j_InsertNpcQuestHistoryCQuestMgrQEAAEPEADENZ_1400048E5.h" />
    <ClInclude Include="headers\j_insertvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400024BE.h" />
    <ClInclude Include="headers\j_insertvectorPEAVCMoveMapLimitRightVallocatorPEAV_140006E60.h" />
    <ClInclude Include="headers\j_insertvectorVCMoveMapLimitRightInfoVallocatorVCM_14000DDFF.h" />
    <ClInclude Include="headers\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400086ED.h" />
    <ClInclude Include="headers\j_insert_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140011527.h" />
    <ClInclude Include="headers\j_InstanceCMoveMapLimitManagerSAPEAV1XZ_14001131F.h" />
    <ClInclude Include="headers\j_InstanceCRFMonsterAIMgrSAPEAV1XZ_1400121CA.h" />
    <ClInclude Include="headers\j_invokelua2objectPEAVCMonsterlua_tinkerSAPEAVCMon_140012A80.h" />
    <ClInclude Include="headers\j_invokeobject2luaPEAVCMonsterlua_tinkerSAXPEAUlua_140003A3F.h" />
    <ClInclude Include="headers\j_invokePEAVCLuaSignalReActormem_functorVCMonsterX_14000309E.h" />
    <ClInclude Include="headers\j_invokePEAVCMonsterfunctorPEADPEADMMMlua_tinkerSA_14000BB2C.h" />
    <ClInclude Include="headers\j_invokeptr2luaVCMonsterlua_tinkerSAXPEAUlua_State_14000558D.h" />
    <ClInclude Include="headers\j_invokeuser2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14001023F.h" />
    <ClInclude Include="headers\j_invokeuser2typeP8CMonsterEAAPEAVCLuaSignalReActo_140009A7A.h" />
    <ClInclude Include="headers\j_invokevoid2ptrA6APEAVCMonsterPEAD0MMMZlua_tinker_1400024AA.h" />
    <ClInclude Include="headers\j_invokevoid2ptrVCMonsterlua_tinkerSAPEAVCMonsterP_14000FDAD.h" />
    <ClInclude Include="headers\j_invokevoid2typeP6APEAVCMonsterPEAD0MMMZlua_tinke_14000DDAF.h" />
    <ClInclude Include="headers\j_invokevoid2typeP8CMonsterEAAPEAVCLuaSignalReActo_14000A04C.h" />
    <ClInclude Include="headers\j_invokevoid2typePEAVCMonsterlua_tinkerSAPEAVCMons_140009070.h" />
    <ClInclude Include="headers\j_invokevoid2valP8CMonsterEAAPEAVCLuaSignalReActor_140013377.h" />
    <ClInclude Include="headers\j_IsBossMonsterCMonsterQEAA_NXZ_140008E1D.h" />
    <ClInclude Include="headers\j_IsCompleteNpcQuestCQuestMgrQEAA_NPEADHZ_14000D50D.h" />
    <ClInclude Include="headers\j_IsEqualLimitCMoveMapLimitInfoQEAA_NHHKZ_1400030D5.h" />
    <ClInclude Include="headers\j_IsExistStdMapIDCMapOperationQEAA_NHZ_140008A44.h" />
    <ClInclude Include="headers\j_IsHaveRightCMoveMapLimitRightInfoQEAA_NHZ_1400097A0.h" />
    <ClInclude Include="headers\j_IsHaveRightCMoveMapLimitRightPortalUEAA_NXZ_1400106B8.h" />
    <ClInclude Include="headers\j_IsHaveRightCMoveMapLimitRightUEAA_NXZ_140009219.h" />
    <ClInclude Include="headers\j_IsINIFileChangedCMonsterEventSetQEAA_NPEBDU_FILE_140008DAF.h" />
    <ClInclude Include="headers\j_IsInRegionCMapOperationQEAA_NPEADPEAVCGameObject_140013548.h" />
    <ClInclude Include="headers\j_IsInSectorCMonsterHelperSAHQEAM00MMPEAMZ_14000535D.h" />
    <ClInclude Include="headers\j_IsMapInCMapDataQEAA_NPEAMZ_14000DF3F.h" />
    <ClInclude Include="headers\j_IsMovableCMonsterQEAA_NXZ_14000E511.h" />
    <ClInclude Include="headers\j_IsNearPositionCCircleZoneAEAA_NPEBMZ_14000A3FD.h" />
    <ClInclude Include="headers\j_IsPossibleRepeatNpcQuestCQuestMgrQEAA_NPEADHZ_1400081E3.h" />
    <ClInclude Include="headers\j_IsProcLinkNpcQuestCQuestMgrQEAA_NPEADHZ_14000194C.h" />
    <ClInclude Include="headers\j_IsProcNpcQuestCQuestMgrQEAA_NPEADZ_14001364C.h" />
    <ClInclude Include="headers\j_IsRoateMonsterCMonsterQEAA_NXZ_140010D2F.h" />
    <ClInclude Include="headers\j_IsRotateBlockMonsterSetInfoDataQEAA_NPEAU_mon_bl_140007B0D.h" />
    <ClInclude Include="headers\j_IsSame_target_monster_contsf_allinform_zoclSA_NA_14000A501.h" />
    <ClInclude Include="headers\j_LinkEventRespawnCMonsterQEAAXPEAU_event_respawnZ_14000C85B.h" />
    <ClInclude Include="headers\j_LinkEventSetCMonsterQEAAXPEAU_event_setZ_14000970F.h" />
    <ClInclude Include="headers\j_LoadAllBossSchedule_MapQEAA_NXZ_140002725.h" />
    <ClInclude Include="headers\j_LoadDummyCMapDataQEAA_NPEADPEAU_dummy_positionZ_14000C7B6.h" />
    <ClInclude Include="headers\j_LoadEventSetCMonsterEventSetQEAA_NPEADZ_140010091.h" />
    <ClInclude Include="headers\j_LoadEventSetLootingCMonsterEventSetQEAA_NXZ_1400088E1.h" />
    <ClInclude Include="headers\j_LoadINICMoveMapLimitInfoPortalAEAA_NXZ_140006514.h" />
    <ClInclude Include="headers\j_LoadMapsCMapOperationAEAA_NXZ_1400110EA.h" />
    <ClInclude Include="headers\j_LoadMonsterSetInfoDataQEAAHPEBDZ_140006AB4.h" />
    <ClInclude Include="headers\j_LoadRegionCMapOperationAEAA_NXZ_14000FB2D.h" />
    <ClInclude Include="headers\j_LoadWorldInfoINICMainThreadAEAAHXZ_14000BB7C.h" />
    <ClInclude Include="headers\j_LoopCMonsterUEAAXXZ_140006249.h" />
    <ClInclude Include="headers\j_LoopCMoveMapLimitInfoListQEAAXXZ_14000F59C.h" />
    <ClInclude Include="headers\j_LoopCMoveMapLimitInfoPortalUEAAXXZ_140009BD3.h" />
    <ClInclude Include="headers\j_LoopCMoveMapLimitInfoUEAAXXZ_1400098AE.h" />
    <ClInclude Include="headers\j_LoopCMoveMapLimitManagerQEAAXXZ_140005475.h" />
    <ClInclude Include="headers\j_LoopCWorldScheduleQEAAXXZ_140013A84.h" />
    <ClInclude Include="headers\j_lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_14000B23F.h" />
    <ClInclude Include="headers\j_lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAU_14000E35E.h" />
    <ClInclude Include="headers\j_max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KX_1400129C2.h" />
    <ClInclude Include="headers\j_max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_K_14000A894.h" />
    <ClInclude Include="headers\j_max_sizeallocatorVCMoveMapLimitRightInfostdQEBA__140003788.h" />
    <ClInclude Include="headers\j_max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEA_14000A26D.h" />
    <ClInclude Include="headers\j_max_sizevectorPEAVCMoveMapLimitRightVallocatorPE_14000991C.h" />
    <ClInclude Include="headers\j_max_sizevectorVCMoveMapLimitRightInfoVallocatorV_14000FFC4.h" />
    <ClInclude Include="headers\j_max_size_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140009B33.h" />
    <ClInclude Include="headers\j_mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeon_14001007D.h" />
    <ClInclude Include="headers\j_mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDung_14000C9F0.h" />
    <ClInclude Include="headers\j_mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkH_140004ABB.h" />
    <ClInclude Include="headers\j_mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDun_140002BC1.h" />
    <ClInclude Include="headers\j_MoveLimitMapZoneRequestCMoveMapLimitManagerQEAA__140003BC0.h" />
    <ClInclude Include="headers\j_MoveScreenPointCMapExtendQEAAXPEAVCPointZ_14001005A.h" />
    <ClInclude Include="headers\j_MoveToOwnStoneMapRequestCNetworkEXAEAA_NHPEADZ_140008C8D.h" />
    <ClInclude Include="headers\j_nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_140007130.h" />
    <ClInclude Include="headers\j_NPCDialogRequestCNetworkEXAEAA_NHPEADZ_1400047D2.h" />
    <ClInclude Include="headers\j_NPCQuestListRequestCNetworkEXAEAA_NHPEADZ_14000907F.h" />
    <ClInclude Include="headers\j_NPCQuestRequestCNetworkEXAEAA_NHPEADZ_1400128AF.h" />
    <ClInclude Include="headers\j_NPCWatchingRequestCNetworkEXAEAA_NHPEADZ_140004CE1.h" />
    <ClInclude Include="headers\j_OffDisplayCMapDisplayQEAA_NXZ_140001221.h" />
    <ClInclude Include="headers\j_OnButtonMapchangeCMapTabIEAAXXZ_140010028.h" />
    <ClInclude Include="headers\j_OnButtonMonsterCGameServerViewQEAAXXZ_14000A367.h" />
    <ClInclude Include="headers\j_OnChildMonsterCreateCMonsterHierarchyQEAAXPEAU_m_140002A09.h" />
    <ClInclude Include="headers\j_OnChildMonsterDestroyCMonsterHierarchyQEAAXXZ_14000B9FB.h" />
    <ClInclude Include="headers\j_OnChildRegenLoopCMonsterHierarchyQEAAXXZ_140013D95.h" />
    <ClInclude Include="headers\j_OnDisplayCMapDisplayQEAA_NPEAVCMapDataGZ_140011A81.h" />
    <ClInclude Include="headers\j_OnLoopCMapDataQEAAXXZ_14000EC87.h" />
    <ClInclude Include="headers\j_OnLoopCMapOperationQEAAXXZ_14000A2E5.h" />
    <ClInclude Include="headers\j_OnSetActiveCMapTabUEAAHXZ_140004507.h" />
    <ClInclude Include="headers\j_OpenMapCMapDataQEAA_NPEADPEAU_map_fld_NZ_14000B25D.h" />
    <ClInclude Include="headers\j_OpenWorldFailureResultCNetworkEXAEAA_NKPEADZ_140001235.h" />
    <ClInclude Include="headers\j_OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_140005B78.h" />
    <ClInclude Include="headers\j_OutOfSecCMonsterUEAAXXZ_14000CF2C.h" />
    <ClInclude Include="headers\j_PassOneStepCWorldScheduleQEAAXXZ_14000C3B5.h" />
    <ClInclude Include="headers\j_pc_AlterWorldServiceCMainThreadQEAAX_NZ_14000D15C.h" />
    <ClInclude Include="headers\j_pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_14000649C.h" />
    <ClInclude Include="headers\j_pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1400082E7.h" />
    <ClInclude Include="headers\j_pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1400132C8.h" />
    <ClInclude Include="headers\j_PopChildMonAllCMonsterHierarchyQEAAXXZ_14000B956.h" />
    <ClInclude Include="headers\j_PopChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_14000A9A2.h" />
    <ClInclude Include="headers\j_ProcessCMonsterAggroMgrQEAAXXZ_14000D5FD.h" />
    <ClInclude Include="headers\j_ProcForceMoveHQCMoveMapLimitInfoPortalAEAAEHPEAD_140005781.h" />
    <ClInclude Include="headers\j_ProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHPE_14000FE1B.h" />
    <ClInclude Include="headers\j_ProcUseMoveScrollCMoveMapLimitInfoPortalAEAAEHPE_14000E5D4.h" />
    <ClInclude Include="headers\j_PushChildMonCMonsterHierarchyQEAAHHPEAVCMonsterZ_140010E5B.h" />
    <ClInclude Include="headers\j_pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCM_140004705.h" />
    <ClInclude Include="headers\j_push_backvectorPEAVCMoveMapLimitRightVallocatorP_140009917.h" />
    <ClInclude Include="headers\j_push_functorPEAVCLuaSignalReActorVCMonsterlua_ti_1400031B6.h" />
    <ClInclude Include="headers\j_push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAX_14000DA1C.h" />
    <ClInclude Include="headers\j_qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDunge_1400066DB.h" />
    <ClInclude Include="headers\j_qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140011D5B.h" />
    <ClInclude Include="headers\j_readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua__140010726.h" />
    <ClInclude Include="headers\j_RegistCMoveMapLimitRightInfoQEAA_NHZ_140004EB7.h" />
    <ClInclude Include="headers\j_ReleaseDisplayCMapDisplayQEAAJXZ_140002586.h" />
    <ClInclude Include="headers\j_RequestCMoveMapLimitInfoListQEAAEHHHKHPEADPEAVCM_14000D9C2.h" />
    <ClInclude Include="headers\j_RequestCMoveMapLimitInfoPortalUEAAEHHPEADPEAVCMo_14000760D.h" />
    <ClInclude Include="headers\j_RequestCMoveMapLimitManagerQEAAEHHHKHPEADZ_140003184.h" />
    <ClInclude Include="headers\j_RequestElanMapUserForceMoveHQCMoveMapLimitManage_1400095BB.h" />
    <ClInclude Include="headers\j_ResetAggroCMonsterAggroMgrQEAAXXZ_140004ECB.h" />
    <ClInclude Include="headers\j_RespawnMonsterCMapOperationAEAAXXZ_140012B5C.h" />
    <ClInclude Include="headers\j_SaveAllBossSchedule_MapQEAA_NXZ_14000A08D.h" />
    <ClInclude Include="headers\j_ScrollMapDownCMapExtendQEAAXHHZ_140006D39.h" />
    <ClInclude Include="headers\j_ScrollMapLeftCMapExtendQEAAXHZ_140012797.h" />
    <ClInclude Include="headers\j_ScrollMapRightCMapExtendQEAAXHHZ_14000F57E.h" />
    <ClInclude Include="headers\j_ScrollMapUpCMapExtendQEAAXHZ_1400067FD.h" />
    <ClInclude Include="headers\j_SearchChildMonCMonsterHierarchyQEAAHPEAVCMonster_1400104E7.h" />
    <ClInclude Include="headers\j_SearchEmptyMonsterYAPEAVCMonster_NZ_140011F86.h" />
    <ClInclude Include="headers\j_SearchNearMonsterByDistanceCMonsterHelperSAPEAVC_140011BDA.h" />
    <ClInclude Include="headers\j_SearchNearMonsterCMonsterHelperSAKPEAVCMonsterPE_14000D125.h" />
    <ClInclude Include="headers\j_SearchPathACPathMgrQEAAHPEAVCMonsterQEAMHZ_140003D5A.h" />
    <ClInclude Include="headers\j_SearchPatrollPathDfAIMgrSAXPEAVCMonsterAIPEAVCMo_140003FE9.h" />
    <ClInclude Include="headers\j_SearchPatrolMovePosCMonsterHelperSAHPEAVCMonster_140007883.h" />
    <ClInclude Include="headers\j_SelectObjectCMapDisplayQEAAPEAVCGameObjectPEAVCP_140006217.h" />
    <ClInclude Include="headers\j_SetBlock_mon_blockQEAA_NPEAU_mon_block_fldPEAVCM_14000731F.h" />
    <ClInclude Include="headers\j_SetCurBspMapCGameObjectQEAA_NPEAVCMapDataZ_14000F1EB.h" />
    <ClInclude Include="headers\j_SetDefPartCMonsterQEAAXPEAU_monster_fldZ_140004269.h" />
    <ClInclude Include="headers\j_SetDummyPointCDummyDrawQEAAXPEAVCMapDataPEAMHPEA_1400077DE.h" />
    <ClInclude Include="headers\j_SetDummyRangeCDummyDrawQEAAXPEAVCMapDataPEAM111H_140011324.h" />
    <ClInclude Include="headers\j_SetEmotionStateCMonsterQEAAXEZ_14000709A.h" />
    <ClInclude Include="headers\j_SetEventRespawnCMonsterEventRespawnQEAA_NXZ_1400071D5.h" />
    <ClInclude Include="headers\j_SetFlagCMoveMapLimitRightInfoQEAAXHH_NZ_14000E052.h" />
    <ClInclude Include="headers\j_SetFlagCMoveMapLimitRightPortalUEAAXH_NZ_140004241.h" />
    <ClInclude Include="headers\j_SetFlagCMoveMapLimitRightUEAAXH_NZ_14000FD71.h" />
    <ClInclude Include="headers\j_SetGroupMapPointRequestCNetworkEXAEAA_NHPEADZ_140003201.h" />
    <ClInclude Include="headers\j_SetHPCMonsterUEAA_NH_NZ_1400129DB.h" />
    <ClInclude Include="headers\j_SetMoveTypeCMonsterQEAAXEZ_1400119DC.h" />
    <ClInclude Include="headers\j_SetMyDataCMonsterAIUEAAHPEAVUsStateTBLPEAXZ_140012EE0.h" />
    <ClInclude Include="headers\j_SetParentCMonsterHierarchyQEAAHPEAVCMonsterZ_140009B6F.h" />
    <ClInclude Include="headers\j_SetRoomMapInfoCGuildRoomInfoQEAAXPEAVCMapDataGEE_14000410B.h" />
    <ClInclude Include="headers\j_SetStunCMonsterUEAAX_NZ_140008E7C.h" />
    <ClInclude Include="headers\j_set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__140006654.h" />
    <ClInclude Include="headers\j_ShortRankDelayCMonsterAggroMgrQEAAXKZ_14000B2FD.h" />
    <ClInclude Include="headers\j_ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectD_14000AB8C.h" />
    <ClInclude Include="headers\j_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1400080A3.h" />
    <ClInclude Include="headers\j_sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCM_140004D81.h" />
    <ClInclude Include="headers\j_sizevectorVCMoveMapLimitRightInfoVallocatorVCMov_14000D3D7.h" />
    <ClInclude Include="headers\j_size_add_char_result_zoneQEAAHXZ_1400047E1.h" />
    <ClInclude Include="headers\j_size_del_char_result_zoneQEAAHXZ_14000C180.h" />
    <ClInclude Include="headers\j_size_enter_world_request_wracQEAAHXZ_14000B924.h" />
    <ClInclude Include="headers\j_size_enter_world_result_zoneQEAAHXZ_14000B06E.h" />
    <ClInclude Include="headers\j_size_moveout_user_result_zoneQEAAHXZ_140006D34.h" />
    <ClInclude Include="headers\j_size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400015A5.h" />
    <ClInclude Include="headers\j_size_move_to_own_stonemap_result_zoclQEAAHXZ_140007C7F.h" />
    <ClInclude Include="headers\j_size_open_world_request_wracQEAAHXZ_140009F61.h" />
    <ClInclude Include="headers\j_size_reged_char_result_zoneQEAAHXZ_140003044.h" />
    <ClInclude Include="headers\j_size_sel_char_result_zoneQEAAHXZ_140012468.h" />
    <ClInclude Include="headers\j_size_server_notify_inform_zoneQEAAHXZ_140005187.h" />
    <ClInclude Include="headers\j_size_start_world_request_wracQEAAHXZ_14000BF55.h" />
    <ClInclude Include="headers\j_size_stop_world_request_wracQEAAHXZ_140005D3F.h" />
    <ClInclude Include="headers\j_size_target_monster_contsf_allinform_zoclQEAAHXZ_14000B2D0.h" />
    <ClInclude Include="headers\j_size_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14001127A.h" />
    <ClInclude Include="headers\j_size_world_account_ping_wracQEAAHXZ_14000276B.h" />
    <ClInclude Include="headers\j_StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0_140008896.h" />
    <ClInclude Include="headers\j_StartScreenPointCMapExtendQEAAXPEAVCPointPEAVCMa_1400055EC.h" />
    <ClInclude Include="headers\j_StopEventSetCMonsterEventSetQEAA_NPEAD0Z_140012E54.h" />
    <ClInclude Include="headers\j_StopRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_140009377.h" />
    <ClInclude Include="headers\j_SubProcForceMoveHQCMoveMapLimitInfoPortalAEAAXXZ_140006555.h" />
    <ClInclude Include="headers\j_SubProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAE_140009994.h" />
    <ClInclude Include="headers\j_SubProcNotifyForceMoveHQCMoveMapLimitInfoPortalA_140009683.h" />
    <ClInclude Include="headers\j_TakeCGravityStoneRegenerQEAAEPEAVCMapDataPEAMZ_140007BDA.h" />
    <ClInclude Include="headers\j_TransPortCMonsterHelperSAXPEAVCMonsterQEAMZ_14000E589.h" />
    <ClInclude Include="headers\j_type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePE_140004C2D.h" />
    <ClInclude Include="headers\j_unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1st_14000D84B.h" />
    <ClInclude Include="headers\j_unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1s_14000F281.h" />
    <ClInclude Include="headers\j_unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1std_140001276.h" />
    <ClInclude Include="headers\j_unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1s_140007C61.h" />
    <ClInclude Include="headers\j_unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1_140004D5E.h" />
    <ClInclude Include="headers\j_unregist_from_mapAutominePersonalQEAA_NEZ_14000267B.h" />
    <ClInclude Include="headers\j_UpdateLookAtPosCMonsterQEAAXQEAMZ_140008F94.h" />
    <ClInclude Include="headers\j_UpdateLookAtPosCMonsterQEAAXXZ_140010514.h" />
    <ClInclude Include="headers\j_UpdateSecterListCMapDataQEAA_NPEAVCGameObjectKKZ_1400051EB.h" />
    <ClInclude Include="headers\j_UpdateSFContCMonsterUEAAXXZ_1400114AA.h" />
    <ClInclude Include="headers\j_UpdateTabCMapTabQEAAXXZ_14000A051.h" />
    <ClInclude Include="headers\j_upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6AP_14000A439.h" />
    <ClInclude Include="headers\j_upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua__14000F0FB.h" />
    <ClInclude Include="headers\j_wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_14000B4BA.h" />
    <ClInclude Include="headers\j_wa_ExitWorldYAXPEAU_CLIDZ_14000FFAB.h" />
    <ClInclude Include="headers\j_WorldExitInformCNetworkEXAEAA_NKPEADZ_14000CDD8.h" />
    <ClInclude Include="headers\j_WorldMsgInformCNetworkEXAEAA_NKPEADZ_1400138DB.h" />
    <ClInclude Include="headers\j_WorldServiceInformCNetworkEXAEAA_NKPEADZ_14000FC36.h" />
    <ClInclude Include="headers\j_Y_Vector_const_iteratorPEAVCMoveMapLimitRightVal_1400092C3.h" />
    <ClInclude Include="headers\j_Y_Vector_iteratorPEAVCMoveMapLimitRightVallocato_14000622B.h" />
    <ClInclude Include="headers\j_ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_14000D19D.h" />
    <ClInclude Include="headers\j__AllocatePEAVCMoveMapLimitInfostdYAPEAPEAVCMoveM_14000F452.h" />
    <ClInclude Include="headers\j__AllocatePEAVCMoveMapLimitRightstdYAPEAPEAVCMove_1400131F6.h" />
    <ClInclude Include="headers\j__AllocateU_Node_Tree_nodV_Tmap_traitsVbasic_stri_14000FF60.h" />
    <ClInclude Include="headers\j__AllocateVCMoveMapLimitRightInfostdYAPEAVCMoveMa_14000F9BB.h" />
    <ClInclude Include="headers\j__Assign_nvectorPEAVCMoveMapLimitInfoVallocatorPE_1400091F1.h" />
    <ClInclude Include="headers\j__Assign_nvectorVCMoveMapLimitRightInfoVallocator_14000D90E.h" />
    <ClInclude Include="headers\j__BossBirthWriteLogCMonsterQEAAXXZ_14000B582.h" />
    <ClInclude Include="headers\j__BossDieWriteLog_EndCMonsterQEAAXXZ_140012710.h" />
    <ClInclude Include="headers\j__BossDieWriteLog_StartCMonsterQEAAXEPEAVCGameObj_140004D40.h" />
    <ClInclude Include="headers\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140001E65.h" />
    <ClInclude Include="headers\j__Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140004FA7.h" />
    <ClInclude Include="headers\j__BuyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_140012251.h" />
    <ClInclude Include="headers\j__BuyvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_14000AD8F.h" />
    <ClInclude Include="headers\j__BuyvectorVCMoveMapLimitRightInfoVallocatorVCMov_14000A38A.h" />
    <ClInclude Include="headers\j__CheckDestMonsterLimitLvYA_NHHEZ_140011400.h" />
    <ClInclude Include="headers\j__Color_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140002B85.h" />
    <ClInclude Include="headers\j__ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic__14000A7B3.h" />
    <ClInclude Include="headers\j__ConstructPEAVCMoveMapLimitRightPEAV1stdYAXPEAPE_14000378D.h" />
    <ClInclude Include="headers\j__ConstructVCMoveMapLimitRightInfoV1stdYAXPEAVCMo_1400022D9.h" />
    <ClInclude Include="headers\j__Copy_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140004BD8.h" />
    <ClInclude Include="headers\j__Copy_backward_optPEAPEAVCMoveMapLimitRightPEAPE_140010A5A.h" />
    <ClInclude Include="headers\j__Copy_backward_optPEAVCMoveMapLimitRightInfoPEAV_140010E3D.h" />
    <ClInclude Include="headers\j__Copy_optPEAPEAVCMoveMapLimitInfoPEAPEAV1Urandom_140003F5D.h" />
    <ClInclude Include="headers\j__Copy_optPEAPEAVCMoveMapLimitRightPEAPEAV1Urando_14000B1FE.h" />
    <ClInclude Include="headers\j__Copy_optPEAVCMoveMapLimitRightInfoPEAV1Urandom__14000F128.h" />
    <ClInclude Include="headers\j__CreateMonYAPEAVCMonsterPEAD0MMMZ_1400074BE.h" />
    <ClInclude Include="headers\j__Decconst_iterator_TreeV_Tmap_traitsVbasic_strin_140012B48.h" />
    <ClInclude Include="headers\j__DestroyPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140011798.h" />
    <ClInclude Include="headers\j__DestroyPEAVCMoveMapLimitRightstdYAXPEAPEAVCMove_140006AAF.h" />
    <ClInclude Include="headers\j__DestroySDMCMonsterSAXXZ_14000CCA7.h" />
    <ClInclude Include="headers\j__DestroyU_Node_Tree_nodV_Tmap_traitsVbasic_strin_14000BD57.h" />
    <ClInclude Include="headers\j__DestroyVCMoveMapLimitRightInfostdYAXPEAVCMoveMa_140001451.h" />
    <ClInclude Include="headers\j__DestroyvectorPEAVCMoveMapLimitInfoVallocatorPEA_1400083FA.h" />
    <ClInclude Include="headers\j__DestroyvectorPEAVCMoveMapLimitRightVallocatorPE_14000D94A.h" />
    <ClInclude Include="headers\j__DestroyvectorVCMoveMapLimitRightInfoVallocatorV_140012C65.h" />
    <ClInclude Include="headers\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_140004F20.h" />
    <ClInclude Include="headers\j__Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEA_14000F69B.h" />
    <ClInclude Include="headers\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_140008323.h" />
    <ClInclude Include="headers\j__Destroy_rangePEAVCMoveMapLimitRightVallocatorPE_1400090E3.h" />
    <ClInclude Include="headers\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_140005E98.h" />
    <ClInclude Include="headers\j__Destroy_rangeVCMoveMapLimitRightInfoVallocatorV_14000C0DB.h" />
    <ClInclude Include="headers\j__DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCS_140001799.h" />
    <ClInclude Include="headers\j__ECCircleZoneUEAAPEAXIZ_1400060EB.h" />
    <ClInclude Include="headers\j__ECMapDataUEAAPEAXIZ_140011BFD.h" />
    <ClInclude Include="headers\j__ECMapDisplayUEAAPEAXIZ_0_140007923.h" />
    <ClInclude Include="headers\j__ECMapDisplayUEAAPEAXIZ_140003332.h" />
    <ClInclude Include="headers\j__ECMapTabUEAAPEAXIZ_0_1400076B7.h" />
    <ClInclude Include="headers\j__ECMapTabUEAAPEAXIZ_140001B8B.h" />
    <ClInclude Include="headers\j__ECMonsterEventSetUEAAPEAXIZ_0_14000DA53.h" />
    <ClInclude Include="headers\j__ECMonsterEventSetUEAAPEAXIZ_14000D7C9.h" />
    <ClInclude Include="headers\j__ECMonsterUEAAPEAXIZ_14000546B.h" />
    <ClInclude Include="headers\j__Erase_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140005D94.h" />
    <ClInclude Include="headers\j__FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVC_14000FA6F.h" />
    <ClInclude Include="headers\j__FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1400126CF.h" />
    <ClInclude Include="headers\j__FillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMove_14000FB8C.h" />
    <ClInclude Include="headers\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAP_140001EF6.h" />
    <ClInclude Include="headers\j__Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1Urandom_ac_1400054C5.h" />
    <ClInclude Include="headers\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1stdYAXPEA_140013C91.h" />
    <ClInclude Include="headers\j__Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1Urandom_a_140012F85.h" />
    <ClInclude Include="headers\j__FindV_Vector_iteratorPEAVCMoveMapLimitRightVall_14000AE43.h" />
    <ClInclude Include="headers\j__GBossSchedule_MapQEAAPEAXIZ_140002BFD.h" />
    <ClInclude Include="headers\j__GCCircleZoneUEAAPEAXIZ_140009CEB.h" />
    <ClInclude Include="headers\j__GCMapDataUEAAPEAXIZ_1400069B5.h" />
    <ClInclude Include="headers\j__GCMapOperationUEAAPEAXIZ_0_14001042E.h" />
    <ClInclude Include="headers\j__GCMapOperationUEAAPEAXIZ_14000A984.h" />
    <ClInclude Include="headers\j__GCMonsterAIUEAAPEAXIZ_0_140013697.h" />
    <ClInclude Include="headers\j__GCMonsterAIUEAAPEAXIZ_14000E4CB.h" />
    <ClInclude Include="headers\j__GCMonsterEventRespawnUEAAPEAXIZ_0_14000C635.h" />
    <ClInclude Include="headers\j__GCMonsterEventRespawnUEAAPEAXIZ_14000216C.h" />
    <ClInclude Include="headers\j__GCMonsterHierarchyUEAAPEAXIZ_0_14000E633.h" />
    <ClInclude Include="headers\j__GCMonsterHierarchyUEAAPEAXIZ_14000B5E1.h" />
    <ClInclude Include="headers\j__GCMonsterUEAAPEAXIZ_1400109BF.h" />
    <ClInclude Include="headers\j__GCMoveMapLimitInfoQEAAPEAXIZ_14000E200.h" />
    <ClInclude Include="headers\j__GCMoveMapLimitManagerQEAAPEAXIZ_1400042B9.h" />
    <ClInclude Include="headers\j__GCMoveMapLimitRightInfoQEAAPEAXIZ_140012FC6.h" />
    <ClInclude Include="headers\j__GCMoveMapLimitRightQEAAPEAXIZ_1400129E5.h" />
    <ClInclude Include="headers\j__GCRFMonsterAIMgrQEAAPEAXIZ_14000934A.h" />
    <ClInclude Include="headers\j__GetBaseClassCMapTabKAPEAUCRuntimeClassXZ_140006366.h" />
    <ClInclude Include="headers\j__GetBlinkNodeCMonsterAggroMgrIEAAPEAUCAggroNodeX_14000BFC3.h" />
    <ClInclude Include="headers\j__GetMonsterContTimeYAGEEZ_14000AF9C.h" />
    <ClInclude Include="headers\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_0_14000E74B.h" />
    <ClInclude Include="headers\j__Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_140005C36.h" />
    <ClInclude Include="headers\j__G_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_1400011AE.h" />
    <ClInclude Include="headers\j__G__change_monsterQEAAPEAXIZ_140002572.h" />
    <ClInclude Include="headers\j__Incconst_iterator_TreeV_Tmap_traitsVbasic_strin_14000FA0B.h" />
    <ClInclude Include="headers\j__Insert_nvectorPEAVCMoveMapLimitInfoVallocatorPE_140012D5A.h" />
    <ClInclude Include="headers\j__Insert_nvectorPEAVCMoveMapLimitRightVallocatorP_140001749.h" />
    <ClInclude Include="headers\j__Insert_nvectorVCMoveMapLimitRightInfoVallocator_14000B460.h" />
    <ClInclude Include="headers\j__Insert_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000ECAA.h" />
    <ClInclude Include="headers\j__Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400042E6.h" />
    <ClInclude Include="headers\j__Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_a_14001348A.h" />
    <ClInclude Include="headers\j__Iter_catPEAPEAVCMoveMapLimitRightstdYAAUrandom__14000A146.h" />
    <ClInclude Include="headers\j__Iter_randomPEAPEAVCMoveMapLimitInfoPEAPEAV1stdY_14000CC9D.h" />
    <ClInclude Include="headers\j__Iter_randomPEAPEAVCMoveMapLimitRightPEAPEAV1std_14000E4B2.h" />
    <ClInclude Include="headers\j__Iter_randomPEAVCMoveMapLimitRightInfoPEAV1stdYA_140008E72.h" />
    <ClInclude Include="headers\j__Key_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140011793.h" />
    <ClInclude Include="headers\j__Kfn_Tmap_traitsVbasic_stringDUchar_traitsDstdVa_14000D7D8.h" />
    <ClInclude Include="headers\j__Lbound_TreeV_Tmap_traitsVbasic_stringDUchar_tra_1400019DD.h" />
    <ClInclude Include="headers\j__Left_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000543E.h" />
    <ClInclude Include="headers\j__Lmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000DEC7.h" />
    <ClInclude Include="headers\j__LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_14000C162.h" />
    <ClInclude Include="headers\j__LoadPortalCMapDataAEAA_NPEADZ_14000ADCB.h" />
    <ClInclude Include="headers\j__LoadQuestCMapDataAEAA_NPEADZ_140009AB6.h" />
    <ClInclude Include="headers\j__LoadResourceCMapDataAEAA_NPEADZ_140001500.h" />
    <ClInclude Include="headers\j__LoadSafeCMapDataAEAA_NPEADZ_140001762.h" />
    <ClInclude Include="headers\j__LoadStartCMapDataAEAA_NPEADZ_1400059C0.h" />
    <ClInclude Include="headers\j__LoadStoreDummyCMapDataAEAA_NPEADZ_140003ACB.h" />
    <ClInclude Include="headers\j__Lrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_14000BB27.h" />
    <ClInclude Include="headers\j__Max_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140009DC2.h" />
    <ClInclude Include="headers\j__Min_TreeV_Tmap_traitsVbasic_stringDUchar_traits_1400133A9.h" />
    <ClInclude Include="headers\j__Move_backward_optPEAPEAVCMoveMapLimitInfoPEAPEA_140002347.h" />
    <ClInclude Include="headers\j__Move_backward_optPEAPEAVCMoveMapLimitRightPEAPE_14000B2B2.h" />
    <ClInclude Include="headers\j__Move_backward_optPEAVCMoveMapLimitRightInfoPEAV_140004570.h" />
    <ClInclude Include="headers\j__Move_catPEAPEAVCMoveMapLimitInfostdYAAU_Undefin_14000BC71.h" />
    <ClInclude Include="headers\j__Move_catPEAPEAVCMoveMapLimitRightstdYAAU_Undefi_140008D19.h" />
    <ClInclude Include="headers\j__Move_catPEAVCMoveMapLimitRightInfostdYAAU_Undef_14000DADA.h" />
    <ClInclude Include="headers\j__Mynodeconst_iterator_TreeV_Tmap_traitsVbasic_st_140004E62.h" />
    <ClInclude Include="headers\j__Myval_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1400099C6.h" />
    <ClInclude Include="headers\j__Parent_TreeV_Tmap_traitsVbasic_stringDUchar_tra_14000E4DA.h" />
    <ClInclude Include="headers\j__Ptr_catPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAAU__140011D1A.h" />
    <ClInclude Include="headers\j__Ptr_catPEAPEAVCMoveMapLimitRightPEAPEAV1stdYAAU_14000409D.h" />
    <ClInclude Include="headers\j__Ptr_catPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_N_140005272.h" />
    <ClInclude Include="headers\j__Ptr_catV_Vector_const_iteratorPEAVCMoveMapLimit_140010640.h" />
    <ClInclude Include="headers\j__Right_TreeV_Tmap_traitsVbasic_stringDUchar_trai_14000B753.h" />
    <ClInclude Include="headers\j__Rmost_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140003E18.h" />
    <ClInclude Include="headers\j__Root_TreeV_Tmap_traitsVbasic_stringDUchar_trait_14000949E.h" />
    <ClInclude Include="headers\j__Rrotate_TreeV_Tmap_traitsVbasic_stringDUchar_tr_140013926.h" />
    <ClInclude Include="headers\j__ShortRankCMonsterAggroMgrIEAAXXZ_140004381.h" />
    <ClInclude Include="headers\j__TidyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140010DB6.h" />
    <ClInclude Include="headers\j__TidyvectorPEAVCMoveMapLimitRightVallocatorPEAVC_140013971.h" />
    <ClInclude Include="headers\j__TidyvectorVCMoveMapLimitRightInfoVallocatorVCMo_140013F25.h" />
    <ClInclude Include="headers\j__Tidy_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140007BAD.h" />
    <ClInclude Include="headers\j__UcopyV_Vector_const_iteratorPEAVCMoveMapLimitRi_14001024E.h" />
    <ClInclude Include="headers\j__UfillvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1400104D8.h" />
    <ClInclude Include="headers\j__UfillvectorPEAVCMoveMapLimitRightVallocatorPEAV_14000CF5E.h" />
    <ClInclude Include="headers\j__UfillvectorVCMoveMapLimitRightInfoVallocatorVCM_140013ACA.h" />
    <ClInclude Include="headers\j__UmovePEAPEAVCMoveMapLimitInfovectorPEAVCMoveMap_14000C469.h" />
    <ClInclude Include="headers\j__UmovePEAPEAVCMoveMapLimitRightvectorPEAVCMoveMa_14000E98A.h" />
    <ClInclude Include="headers\j__UmovePEAVCMoveMapLimitRightInfovectorVCMoveMapL_1400126D4.h" />
    <ClInclude Include="headers\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitInfo_140012AA8.h" />
    <ClInclude Include="headers\j__Unchecked_move_backwardPEAPEAVCMoveMapLimitRigh_14000807B.h" />
    <ClInclude Include="headers\j__Unchecked_move_backwardPEAVCMoveMapLimitRightIn_14000C856.h" />
    <ClInclude Include="headers\j__XlenvectorPEAVCMoveMapLimitInfoVallocatorPEAVCM_140008D91.h" />
    <ClInclude Include="headers\j__XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVC_14000B005.h" />
    <ClInclude Include="headers\j__XlenvectorVCMoveMapLimitRightInfoVallocatorVCMo_140008DFA.h" />
    <ClInclude Include="headers\LightMappingTex1YAXPEAU_BSP_MAT_GROUPZ_1404EFAF0.h" />
    <ClInclude Include="headers\LinkEventRespawnCMonsterQEAAXPEAU_event_respawnZ_1402A78D0.h" />
    <ClInclude Include="headers\LinkEventSetCMonsterQEAAXPEAU_event_setZ_1402AA080.h" />
    <ClInclude Include="headers\LoadAllBossSchedule_MapQEAA_NXZ_14041A070.h" />
    <ClInclude Include="headers\LoadDummyCMapDataQEAA_NPEADPEAU_dummy_positionZ_140184250.h" />
    <ClInclude Include="headers\LoadEntitiesCBspQEAAXPEAU_READ_MAP_ENTITIES_LISTZ_1404F96C0.h" />
    <ClInclude Include="headers\LoadEventSetCMonsterEventSetQEAA_NPEADZ_1402A79E0.h" />
    <ClInclude Include="headers\LoadEventSetLootingCMonsterEventSetQEAA_NXZ_1402A91E0.h" />
    <ClInclude Include="headers\LoadINICMoveMapLimitInfoPortalAEAA_NXZ_1403A56F0.h" />
    <ClInclude Include="headers\LoadLightMapYAXPEADZ_1405023A0.h" />
    <ClInclude Include="headers\LoadMapsCMapOperationAEAA_NXZ_140196750.h" />
    <ClInclude Include="headers\LoadMonsterSetInfoDataQEAAHPEBDZ_14015C7E0.h" />
    <ClInclude Include="headers\LoadR3TLightMapYAPEAPEAU_LIGHTMAPPEAUR3TextureW4_D_140500910.h" />
    <ClInclude Include="headers\LoadRegionCMapOperationAEAA_NXZ_140196C40.h" />
    <ClInclude Include="headers\LoadWorldInfoINICMainThreadAEAAHXZ_1401E6BF0.h" />
    <ClInclude Include="headers\LoopCMonsterUEAAXXZ_140147C90.h" />
    <ClInclude Include="headers\LoopCMoveMapLimitInfoListQEAAXXZ_1403A54D0.h" />
    <ClInclude Include="headers\LoopCMoveMapLimitInfoPortalUEAAXXZ_1403A43D0.h" />
    <ClInclude Include="headers\LoopCMoveMapLimitInfoUEAAXXZ_1403A6F40.h" />
    <ClInclude Include="headers\LoopCMoveMapLimitManagerQEAAXXZ_1403A1B40.h" />
    <ClInclude Include="headers\LoopCWorldScheduleQEAAXXZ_1403F3A30.h" />
    <ClInclude Include="headers\lower_bound_TreeV_Tmap_traitsVbasic_stringDUchar_t_14018EB80.h" />
    <ClInclude Include="headers\lua2typePEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlu_14040AFF0.h" />
    <ClInclude Include="headers\MakeMipMapYAXGGPEAG0Z_1405005B0.h" />
    <ClInclude Include="headers\MakeMipMapYAXGGPEAGPEAEZ_140500770.h" />
    <ClInclude Include="headers\MapChannelEqualityComparisonFilterCryptoPPAEBAIAEB_140654950.h" />
    <ClInclude Include="headers\MapViewOfFile_0_140676E1C.h" />
    <ClInclude Include="headers\max_sizeallocatorPEAVCMoveMapLimitInfostdQEBA_KXZ_1403A31C0.h" />
    <ClInclude Include="headers\max_sizeallocatorPEAVCMoveMapLimitRightstdQEBA_KXZ_1403B0CC0.h" />
    <ClInclude Include="headers\max_sizeallocatorVCMoveMapLimitRightInfostdQEBA_KX_1403A3230.h" />
    <ClInclude Include="headers\max_sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403A2A70.h" />
    <ClInclude Include="headers\max_sizevectorPEAVCMoveMapLimitRightVallocatorPEAV_1403AFD20.h" />
    <ClInclude Include="headers\max_sizevectorVCMoveMapLimitRightInfoVallocatorVCM_1403A2CE0.h" />
    <ClInclude Include="headers\max_size_TreeV_Tmap_traitsVbasic_stringDUchar_trai_1401913D0.h" />
    <ClInclude Include="headers\mc_AddMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeonQu_140275730.h" />
    <ClInclude Include="headers\mc_ChangeMonsterYA_NPEAUstrFILEPEAVCDarkHoleDungeo_140276050.h" />
    <ClInclude Include="headers\mc_RespawnMonsterOptionYA_NPEAUstrFILEPEAVCDarkHol_140275E60.h" />
    <ClInclude Include="headers\mc_RespawnMonsterYA_NPEAUstrFILEPEAVCDarkHoleDunge_1402759D0.h" />
    <ClInclude Include="headers\MoveLimitMapZoneRequestCMoveMapLimitManagerQEAA_NH_1403A1A60.h" />
    <ClInclude Include="headers\MoveScreenPointCMapExtendQEAAXPEAVCPointZ_1401A1730.h" />
    <ClInclude Include="headers\MoveToOwnStoneMapRequestCNetworkEXAEAA_NHPEADZ_1401CF500.h" />
    <ClInclude Include="headers\nameclass_nameVCMonsterlua_tinkerSAPEBDPEBDZ_1404082A0.h" />
    <ClInclude Include="headers\NPCDialogRequestCNetworkEXAEAA_NHPEADZ_1401CF580.h" />
    <ClInclude Include="headers\NPCQuestListRequestCNetworkEXAEAA_NHPEADZ_1401CEF70.h" />
    <ClInclude Include="headers\NPCQuestRequestCNetworkEXAEAA_NHPEADZ_1401CEE40.h" />
    <ClInclude Include="headers\NPCWatchingRequestCNetworkEXAEAA_NHPEADZ_1401CF690.h" />
    <ClInclude Include="headers\OffDisplayCMapDisplayQEAA_NXZ_14019EF70.h" />
    <ClInclude Include="headers\OnButtonMapchangeCMapTabIEAAXXZ_14002EC30.h" />
    <ClInclude Include="headers\OnButtonMonsterCGameServerViewQEAAXXZ_14002B190.h" />
    <ClInclude Include="headers\OnChildMonsterCreateCMonsterHierarchyQEAAXPEAU_mon_140157450.h" />
    <ClInclude Include="headers\OnChildMonsterDestroyCMonsterHierarchyQEAAXXZ_140157870.h" />
    <ClInclude Include="headers\OnChildRegenLoopCMonsterHierarchyQEAAXXZ_140157590.h" />
    <ClInclude Include="headers\OnDisplayCMapDisplayQEAA_NPEAVCMapDataGZ_14019EEB0.h" />
    <ClInclude Include="headers\OnLoopCMapDataQEAAXXZ_140181510.h" />
    <ClInclude Include="headers\OnLoopCMapOperationQEAAXXZ_140196F30.h" />
    <ClInclude Include="headers\OnSetActiveCMapTabUEAAHXZ_14002E680.h" />
    <ClInclude Include="headers\OpenFileMappingA_0_140676E28.h" />
    <ClInclude Include="headers\OpenMapCMapDataQEAA_NPEADPEAU_map_fld_NZ_140180D80.h" />
    <ClInclude Include="headers\OpenWorldFailureResultCNetworkEXAEAA_NKPEADZ_1401C0420.h" />
    <ClInclude Include="headers\OpenWorldSuccessResultCNetworkEXAEAA_NKPEADZ_1401C0340.h" />
    <ClInclude Include="headers\OutOfSecCMonsterUEAAXXZ_14014B990.h" />
    <ClInclude Include="headers\PassOneStepCWorldScheduleQEAAXXZ_1403F3E30.h" />
    <ClInclude Include="headers\pc_AlterWorldServiceCMainThreadQEAAX_NZ_1401F61B0.h" />
    <ClInclude Include="headers\pc_EnterWorldResultCMainThreadQEAAXEPEAU_CLIDZ_1401F5B30.h" />
    <ClInclude Include="headers\pc_OpenWorldFailureResultCMainThreadQEAAXPEADZ_1401F56F0.h" />
    <ClInclude Include="headers\pc_OpenWorldSuccessResultCMainThreadQEAAXEPEAD0Z_1401F5580.h" />
    <ClInclude Include="headers\PopChildMonAllCMonsterHierarchyQEAAXXZ_140157BE0.h" />
    <ClInclude Include="headers\PopChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_140157AA0.h" />
    <ClInclude Include="headers\ProcessCMonsterAggroMgrQEAAXXZ_14015E120.h" />
    <ClInclude Include="headers\ProcForceMoveHQCMoveMapLimitInfoPortalAEAAEHPEADPE_1403A44B0.h" />
    <ClInclude Include="headers\ProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHPEAD_1403A47B0.h" />
    <ClInclude Include="headers\ProcUseMoveScrollCMoveMapLimitInfoPortalAEAAEHPEAD_1403A4520.h" />
    <ClInclude Include="headers\PushChildMonCMonsterHierarchyQEAAHHPEAVCMonsterZ_140157990.h" />
    <ClInclude Include="headers\pushPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAVCMon_14040A3F0.h" />
    <ClInclude Include="headers\push_backvectorPEAVCMoveMapLimitRightVallocatorPEA_1403AEB10.h" />
    <ClInclude Include="headers\push_functorPEAVCLuaSignalReActorVCMonsterlua_tink_1404089C0.h" />
    <ClInclude Include="headers\push_functorPEAVCMonsterPEADPEADMMMlua_tinkerYAXPE_140408A80.h" />
    <ClInclude Include="headers\qc_monsterGroupYA_NPEAUstrFILEPEAVCDarkHoleDungeon_140273B40.h" />
    <ClInclude Include="headers\qc_UseMapYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_1402733E0.h" />
    <ClInclude Include="headers\readPEAVCMonsterlua_tinkerYAPEAVCMonsterPEAUlua_St_14040A210.h" />
    <ClInclude Include="headers\RegistCMoveMapLimitRightInfoQEAA_NHZ_1403AC960.h" />
    <ClInclude Include="headers\ReleaseDisplayCMapDisplayQEAAJXZ_14019EBC0.h" />
    <ClInclude Include="headers\ReleaseLightMapYAXXZ_140502480.h" />
    <ClInclude Include="headers\RequestCMoveMapLimitInfoListQEAAEHHHKHPEADPEAVCMov_1403A5F80.h" />
    <ClInclude Include="headers\RequestCMoveMapLimitInfoPortalUEAAEHHPEADPEAVCMove_1403A4200.h" />
    <ClInclude Include="headers\RequestCMoveMapLimitManagerQEAAEHHHKHPEADZ_1403A19D0.h" />
    <ClInclude Include="headers\RequestElanMapUserForceMoveHQCMoveMapLimitManagerQ_140284700.h" />
    <ClInclude Include="headers\ResetAggroCMonsterAggroMgrQEAAXXZ_14015E900.h" />
    <ClInclude Include="headers\RespawnMonsterCMapOperationAEAAXXZ_140197190.h" />
    <ClInclude Include="headers\SaveAllBossSchedule_MapQEAA_NXZ_140419FB0.h" />
    <ClInclude Include="headers\ScrollMapDownCMapExtendQEAAXHHZ_1401A2000.h" />
    <ClInclude Include="headers\ScrollMapLeftCMapExtendQEAAXHZ_1401A2120.h" />
    <ClInclude Include="headers\ScrollMapRightCMapExtendQEAAXHHZ_1401A2220.h" />
    <ClInclude Include="headers\ScrollMapUpCMapExtendQEAAXHZ_1401A1EF0.h" />
    <ClInclude Include="headers\SearchChildMonCMonsterHierarchyQEAAHPEAVCMonsterZ_140157D00.h" />
    <ClInclude Include="headers\SearchEmptyMonsterYAPEAVCMonster_NZ_140148F20.h" />
    <ClInclude Include="headers\SearchNearMonsterByDistanceCMonsterHelperSAPEAVCMo_140159540.h" />
    <ClInclude Include="headers\SearchNearMonsterCMonsterHelperSAKPEAVCMonsterPEAU_140158DE0.h" />
    <ClInclude Include="headers\SearchPathACPathMgrQEAAHPEAVCMonsterQEAMHZ_140155C90.h" />
    <ClInclude Include="headers\SearchPatrollPathDfAIMgrSAXPEAVCMonsterAIPEAVCMons_1401532F0.h" />
    <ClInclude Include="headers\SearchPatrolMovePosCMonsterHelperSAHPEAVCMonsterAE_140159F20.h" />
    <ClInclude Include="headers\SelectObjectCMapDisplayQEAAPEAVCGameObjectPEAVCPoi_14019F340.h" />
    <ClInclude Include="headers\SetBlock_mon_blockQEAA_NPEAU_mon_block_fldPEAVCMap_140189E60.h" />
    <ClInclude Include="headers\SetCurBspMapCGameObjectQEAA_NPEAVCMapDataZ_14017AF30.h" />
    <ClInclude Include="headers\SetDefPartCMonsterQEAAXPEAU_monster_fldZ_140142B40.h" />
    <ClInclude Include="headers\SetDummyPointCDummyDrawQEAAXPEAVCMapDataPEAMHPEAVC_14019C560.h" />
    <ClInclude Include="headers\SetDummyRangeCDummyDrawQEAAXPEAVCMapDataPEAM111HPE_14019C7B0.h" />
    <ClInclude Include="headers\SetEmotionStateCMonsterQEAAXEZ_1401437D0.h" />
    <ClInclude Include="headers\SetEventRespawnCMonsterEventRespawnQEAA_NXZ_1402A5DE0.h" />
    <ClInclude Include="headers\SetFlagCMoveMapLimitRightInfoQEAAXHH_NZ_1403ACC50.h" />
    <ClInclude Include="headers\SetFlagCMoveMapLimitRightPortalUEAAXH_NZ_1403AC790.h" />
    <ClInclude Include="headers\SetFlagCMoveMapLimitRightUEAAXH_NZ_1403AE500.h" />
    <ClInclude Include="headers\SetGroupMapPointRequestCNetworkEXAEAA_NHPEADZ_1401D8360.h" />
    <ClInclude Include="headers\SetHPCMonsterUEAA_NH_NZ_140146200.h" />
    <ClInclude Include="headers\SetLightMapYAXJZ_1404EDF30.h" />
    <ClInclude Include="headers\SetMapMode_0_140676F48.h" />
    <ClInclude Include="headers\SetMoveTypeCMonsterQEAAXEZ_140143770.h" />
    <ClInclude Include="headers\SetMyDataCMonsterAIUEAAHPEAVUsStateTBLPEAXZ_14014FB70.h" />
    <ClInclude Include="headers\SetParentCMonsterHierarchyQEAAHPEAVCMonsterZ_140157960.h" />
    <ClInclude Include="headers\SetRoomMapInfoCGuildRoomInfoQEAAXPEAVCMapDataGEEZ_1402E5A80.h" />
    <ClInclude Include="headers\SetStunCMonsterUEAAX_NZ_140146130.h" />
    <ClInclude Include="headers\SetWorldViewMatrixVSYAXQEAY03MZ_140515870.h" />
    <ClInclude Include="headers\set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.h" />
    <ClInclude Include="headers\ShortRankDelayCMonsterAggroMgrQEAAXKZ_14015E0C0.h" />
    <ClInclude Include="headers\ShowBitmapCDisplayQEAAJPEAUHBITMAP__PEAUIDirectDra_140434220.h" />
    <ClInclude Include="headers\sizevectorPEAVCMoveMapLimitInfoVallocatorPEAVCMove_1403A78D0.h" />
    <ClInclude Include="headers\sizevectorPEAVCMoveMapLimitRightVallocatorPEAVCMov_1403AE9F0.h" />
    <ClInclude Include="headers\sizevectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A21E0.h" />
    <ClInclude Include="headers\size_add_char_result_zoneQEAAHXZ_14011F870.h" />
    <ClInclude Include="headers\size_del_char_result_zoneQEAAHXZ_14011F880.h" />
    <ClInclude Include="headers\size_enter_world_request_wracQEAAHXZ_14011F240.h" />
    <ClInclude Include="headers\size_enter_world_result_zoneQEAAHXZ_14011F250.h" />
    <ClInclude Include="headers\size_moveout_user_result_zoneQEAAHXZ_14011FBD0.h" />
    <ClInclude Include="headers\size_move_to_own_stonemap_inform_zoclQEAAHXZ_1400F03D0.h" />
    <ClInclude Include="headers\size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.h" />
    <ClInclude Include="headers\size_open_world_request_wracQEAAHXZ_1402080C0.h" />
    <ClInclude Include="headers\size_reged_char_result_zoneQEAAHXZ_14011F6F0.h" />
    <ClInclude Include="headers\size_sel_char_result_zoneQEAAHXZ_14011F8D0.h" />
    <ClInclude Include="headers\size_server_notify_inform_zoneQEAAHXZ_14011F1E0.h" />
    <ClInclude Include="headers\size_start_world_request_wracQEAAHXZ_1402080D0.h" />
    <ClInclude Include="headers\size_stop_world_request_wracQEAAHXZ_1402080E0.h" />
    <ClInclude Include="headers\size_target_monster_contsf_allinform_zoclQEAAHXZ_1400F0140.h" />
    <ClInclude Include="headers\size_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_14018FFA0.h" />
    <ClInclude Include="headers\size_world_account_ping_wracQEAAHXZ_1402080F0.h" />
    <ClInclude Include="headers\StartRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6B90.h" />
    <ClInclude Include="headers\StartScreenPointCMapExtendQEAAXPEAVCPointPEAVCMapD_1401A1670.h" />
    <ClInclude Include="headers\StopEventSetCMonsterEventSetQEAA_NPEAD0Z_1402A8870.h" />
    <ClInclude Include="headers\StopRespawnEventCMonsterEventRespawnQEAA_NPEAD0Z_1402A6E50.h" />
    <ClInclude Include="headers\SubProcForceMoveHQCMoveMapLimitInfoPortalAEAAXXZ_1403A4A50.h" />
    <ClInclude Include="headers\SubProcGotoLimitZoneCMoveMapLimitInfoPortalAEAAEHP_1403A4D10.h" />
    <ClInclude Include="headers\SubProcNotifyForceMoveHQCMoveMapLimitInfoPortalAEA_1403A4880.h" />
    <ClInclude Include="headers\TakeCGravityStoneRegenerQEAAEPEAVCMapDataPEAMZ_14012EB20.h" />
    <ClInclude Include="headers\TransPortCMonsterHelperSAXPEAVCMonsterQEAMZ_14015A310.h" />
    <ClInclude Include="headers\type2luaPEAVCMonsterlua_tinkerYAXPEAUlua_StatePEAV_14040B0E0.h" />
    <ClInclude Include="headers\unchecked_copyPEAPEAVCMoveMapLimitInfoPEAPEAV1stde_1403AAB40.h" />
    <ClInclude Include="headers\unchecked_copyPEAPEAVCMoveMapLimitRightPEAPEAV1std_1403B1C00.h" />
    <ClInclude Include="headers\unchecked_copyPEAVCMoveMapLimitRightInfoPEAV1stdex_1403B21A0.h" />
    <ClInclude Include="headers\unchecked_fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1std_1403ABFD0.h" />
    <ClInclude Include="headers\unchecked_fill_nPEAPEAVCMoveMapLimitRight_KPEAV1st_1403B3280.h" />
    <ClInclude Include="headers\UnLightMappingTex1YAXXZ_1404EFB90.h" />
    <ClInclude Include="headers\UnmapViewOfFile_0_140676E16.h" />
    <ClInclude Include="headers\unregist_from_mapAutominePersonalQEAA_NEZ_1402DB110.h" />
    <ClInclude Include="headers\UpdateLookAtPosCMonsterQEAAXQEAMZ_140148220.h" />
    <ClInclude Include="headers\UpdateLookAtPosCMonsterQEAAXXZ_140148090.h" />
    <ClInclude Include="headers\UpdateSecterListCMapDataQEAA_NPEAVCGameObjectKKZ_140184BE0.h" />
    <ClInclude Include="headers\UpdateSFContCMonsterUEAAXXZ_140147170.h" />
    <ClInclude Include="headers\UpdateTabCMapTabQEAAXXZ_14002E6E0.h" />
    <ClInclude Include="headers\upvalue_P6APEAVCMonsterPEAD0MMMZlua_tinkerYAP6APEA_14040A3A0.h" />
    <ClInclude Include="headers\upvalue_P8CMonsterEAAPEAVCLuaSignalReActorXZlua_ti_14040A260.h" />
    <ClInclude Include="headers\wa_EnterWorldYAXPEAU_WA_AVATOR_CODEGZ_140046110.h" />
    <ClInclude Include="headers\wa_ExitWorldYAXPEAU_CLIDZ_140046190.h" />
    <ClInclude Include="headers\WorldExitInformCNetworkEXAEAA_NKPEADZ_1401C09A0.h" />
    <ClInclude Include="headers\WorldMsgInformCNetworkEXAEAA_NKPEADZ_1401C09F0.h" />
    <ClInclude Include="headers\WorldServiceInformCNetworkEXAEAA_NKPEADZ_1401C0940.h" />
    <ClInclude Include="headers\Y_Vector_const_iteratorPEAVCMoveMapLimitRightVallo_1403B17C0.h" />
    <ClInclude Include="headers\Y_Vector_iteratorPEAVCMoveMapLimitRightVallocatorP_1403B1550.h" />
    <ClInclude Include="headers\ZoneAliveCheckRequestCNetworkEXAEAA_NHPEADZ_1401C3D90.h" />
    <ClInclude Include="headers\_AllocatePEAVCMoveMapLimitInfostdYAPEAPEAVCMoveMap_1403A3390.h" />
    <ClInclude Include="headers\_AllocatePEAVCMoveMapLimitRightstdYAPEAPEAVCMoveMa_1403B20F0.h" />
    <ClInclude Include="headers\_AllocateU_Node_Tree_nodV_Tmap_traitsVbasic_string_140192B40.h" />
    <ClInclude Include="headers\_AllocateVCMoveMapLimitRightInfostdYAPEAVCMoveMapL_1403A34C0.h" />
    <ClInclude Include="headers\_Assign_nvectorPEAVCMoveMapLimitInfoVallocatorPEAV_1403A84E0.h" />
    <ClInclude Include="headers\_Assign_nvectorVCMoveMapLimitRightInfoVallocatorVC_1403AF5E0.h" />
    <ClInclude Include="headers\_BossBirthWriteLogCMonsterQEAAXXZ_140143910.h" />
    <ClInclude Include="headers\_BossDieWriteLog_EndCMonsterQEAAXXZ_1401440C0.h" />
    <ClInclude Include="headers\_BossDieWriteLog_StartCMonsterQEAAXEPEAVCGameObjec_1401439D0.h" />
    <ClInclude Include="headers\_BossSchedule_Map_BossSchedule_Map__1_dtor0_14041B480.h" />
    <ClInclude Include="headers\_Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191820.h" />
    <ClInclude Include="headers\_Buynode_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140193E70.h" />
    <ClInclude Include="headers\_BuyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMove_1403A2350.h" />
    <ClInclude Include="headers\_BuyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMov_1403AF3E0.h" />
    <ClInclude Include="headers\_BuyvectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A25B0.h" />
    <ClInclude Include="headers\_CDisplayCreateSurfaceFromBitmap__1_dtor0_140433DE0.h" />
    <ClInclude Include="headers\_CDisplayShowBitmap__1_dtor0_140434370.h" />
    <ClInclude Include="headers\_CheckDestMonsterLimitLvYA_NHHEZ_140099540.h" />
    <ClInclude Include="headers\_CMainThreadLoadWorldInfoINI__1_dtor0_1401E70F0.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor0_1401802F0.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor10_1401804D0.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor11_140180500.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor12_140180530.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor13_140180560.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor1_140180320.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor2_140180350.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor3_140180380.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor4_1401803B0.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor5_1401803E0.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor6_140180410.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor7_140180440.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor8_140180470.h" />
    <ClInclude Include="headers\_CMapDataCMapData__1_dtor9_1401804A0.h" />
    <ClInclude Include="headers\_CMapDataOpenMap__1_dtor0_140181480.h" />
    <ClInclude Include="headers\_CMapDataOpenMap__1_dtor1_1401814B0.h" />
    <ClInclude Include="headers\_CMapDataOpenMap__1_dtor2_1401814E0.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor0_140180A60.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor10_140180C40.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor11_140180C70.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor12_140180CA0.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor13_140180CD0.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor14_140180D00.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor1_140180A90.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor2_140180AC0.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor3_140180AF0.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor4_140180B20.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor5_140180B50.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor6_140180B80.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor7_140180BB0.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor8_140180BE0.h" />
    <ClInclude Include="headers\_CMapData_CMapData__1_dtor9_140180C10.h" />
    <ClInclude Include="headers\_CMapData_LoadMonBlk__1_dtor0_140182550.h" />
    <ClInclude Include="headers\_CMapData_LoadMonBlk__1_dtor1_140182580.h" />
    <ClInclude Include="headers\_CMapData_LoadPortal__1_dtor0_1401829E0.h" />
    <ClInclude Include="headers\_CMapData_LoadQuest__1_dtor0_140183E00.h" />
    <ClInclude Include="headers\_CMapData_LoadResource__1_dtor0_140183B30.h" />
    <ClInclude Include="headers\_CMapData_LoadSafe__1_dtor0_140184220.h" />
    <ClInclude Include="headers\_CMapData_LoadStart__1_dtor0_1401832B0.h" />
    <ClInclude Include="headers\_CMapData_LoadStoreDummy__1_dtor0_140182EB0.h" />
    <ClInclude Include="headers\_CMapDisplayCMapDisplay__1_dtor0_14019D8B0.h" />
    <ClInclude Include="headers\_CMapDisplayCMapDisplay__1_dtor1_14019D8E0.h" />
    <ClInclude Include="headers\_CMapDisplayCMapDisplay__1_dtor2_14019D920.h" />
    <ClInclude Include="headers\_CMapDisplayCMapDisplay__1_dtor3_14019D950.h" />
    <ClInclude Include="headers\_CMapDisplayCMapDisplay__1_dtor4_14019D980.h" />
    <ClInclude Include="headers\_CMapDisplay_CMapDisplay__1_dtor0_14019DB80.h" />
    <ClInclude Include="headers\_CMapDisplay_CMapDisplay__1_dtor1_14019DBB0.h" />
    <ClInclude Include="headers\_CMapDisplay_CMapDisplay__1_dtor2_14019DBF0.h" />
    <ClInclude Include="headers\_CMapDisplay_CMapDisplay__1_dtor3_14019DC20.h" />
    <ClInclude Include="headers\_CMapDisplay_CMapDisplay__1_dtor4_14019DC50.h" />
    <ClInclude Include="headers\_CMapOperationCMapOperation__1_dtor0_140195FD0.h" />
    <ClInclude Include="headers\_CMapOperationCMapOperation__1_dtor1_140196000.h" />
    <ClInclude Include="headers\_CMapOperationCMapOperation__1_dtor2_140196030.h" />
    <ClInclude Include="headers\_CMapOperationCMapOperation__1_dtor3_140196060.h" />
    <ClInclude Include="headers\_CMapOperationCMapOperation__1_dtor4_140196090.h" />
    <ClInclude Include="headers\_CMapOperationLoadRegion__1_dtor0_140196F00.h" />
    <ClInclude Include="headers\_CMapOperation_CMapOperation__1_dtor0_140196210.h" />
    <ClInclude Include="headers\_CMapOperation_CMapOperation__1_dtor1_140196240.h" />
    <ClInclude Include="headers\_CMapOperation_CMapOperation__1_dtor2_140196270.h" />
    <ClInclude Include="headers\_CMapOperation_CMapOperation__1_dtor3_1401962A0.h" />
    <ClInclude Include="headers\_CMapOperation_CMapOperation__1_dtor4_1401962D0.h" />
    <ClInclude Include="headers\_CMapTabCMapTab__1_dtor0_14002E500.h" />
    <ClInclude Include="headers\_CMapTabCreateObject__1_dtor0_14002E3D0.h" />
    <ClInclude Include="headers\_CMapTabUpdateTab__1_dtor0_14002EC00.h" />
    <ClInclude Include="headers\_CMapTab_CMapTab__1_dtor0_14002E590.h" />
    <ClInclude Include="headers\_CMonsterAICMonsterAI__1_dtor0_14014FA00.h" />
    <ClInclude Include="headers\_CMonsterAI_CMonsterAI__1_dtor0_14014FA90.h" />
    <ClInclude Include="headers\_CMonsterCMonster__1_dtor0_140141660.h" />
    <ClInclude Include="headers\_CMonsterCMonster__1_dtor1_140141690.h" />
    <ClInclude Include="headers\_CMonsterCMonster__1_dtor2_1401416C0.h" />
    <ClInclude Include="headers\_CMonsterCMonster__1_dtor3_1401416F0.h" />
    <ClInclude Include="headers\_CMonsterCMonster__1_dtor4_140141720.h" />
    <ClInclude Include="headers\_CMonsterCMonster__1_dtor5_140141750.h" />
    <ClInclude Include="headers\_CMonsterCreateAI__1_dtor0_1401424C0.h" />
    <ClInclude Include="headers\_CMonster_CMonster__1_dtor0_140141850.h" />
    <ClInclude Include="headers\_CMonster_CMonster__1_dtor1_140141880.h" />
    <ClInclude Include="headers\_CMonster_CMonster__1_dtor2_1401418B0.h" />
    <ClInclude Include="headers\_CMonster_CMonster__1_dtor3_1401418E0.h" />
    <ClInclude Include="headers\_CMonster_CMonster__1_dtor4_140141910.h" />
    <ClInclude Include="headers\_CMonster_CMonster__1_dtor5_140141940.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoCreate__1_dtor0_1403A3E40.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListCleanUp__1_dtor0_1403A6430.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListCleanUp__1_dtor1_1403A6460.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListCleanUp__1_dtor2_1403A6490.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListCleanUp__1_dtor3_1403A64C0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListGet__1_dtor0_1403A61D0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListGet__1_dtor1_1403A6200.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListGet__1_dtor2_1403A6230.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListGet__1_dtor3_1403A6260.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLoad__1_dtor0_1403A5A60.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLoad__1_dtor1_1403A5A90.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLoad__1_dtor2_1403A5AC0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLoad__1_dtor3_1403A5AF0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLogOut__1_dtor0_1403A5EC0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLogOut__1_dtor1_1403A5EF0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLogOut__1_dtor2_1403A5F20.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLogOut__1_dtor3_1403A5F50.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLoop__1_dtor0_1403A5630.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLoop__1_dtor1_1403A5660.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLoop__1_dtor2_1403A5690.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoListLoop__1_dtor3_1403A56C0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoPortalCMoveMapLimitInfoPortal__1_1403A3FA0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor0_1403A46F0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor1_1403A4720.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor2_1403A4750.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor3_1403A4780.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoPortal_CMoveMapLimitInfoPortal___1403A40C0.h" />
    <ClInclude Include="headers\_CMoveMapLimitInfoPortal_CMoveMapLimitInfoPortal___1403A40F0.h" />
    <ClInclude Include="headers\_CMoveMapLimitManagerCMoveMapLimitManager__1_dtor0_1403A1D70.h" />
    <ClInclude Include="headers\_CMoveMapLimitManagerInstance__1_dtor0_1403A1680.h" />
    <ClInclude Include="headers\_CMoveMapLimitManager_CMoveMapLimitManager__1_dtor_1403A1F60.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightCreate__1_dtor0_1403AC670.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoCreateComplete__1_dtor0_1403AD2B0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoCreateComplete__1_dtor1_1403AD2E0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoCreateComplete__1_dtor2_1403AD310.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoCreateComplete__1_dtor3_1403AD340.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLoad__1_dtor0_1403ACE70.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLoad__1_dtor1_1403ACEA0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLoad__1_dtor2_1403ACED0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLoad__1_dtor3_1403ACF00.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLogOut__1_dtor0_1403AD4D0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLogOut__1_dtor1_1403AD500.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLogOut__1_dtor2_1403AD530.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoLogOut__1_dtor3_1403AD560.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfooperator___1_dtor0_1403AD6B0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfooperator___1_dtor1_1403AD6E0.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoRegist__1_dtor0_1403ACB10.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoRegist__1_dtor2_1403ACB40.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfoRegist__1_dtor3_1403ACB70.h" />
    <ClInclude Include="headers\_CMoveMapLimitRightInfo_CMoveMapLimitRightInfo__1__1403A39E0.h" />
    <ClInclude Include="headers\_Color_TreeV_Tmap_traitsVbasic_stringDUchar_traits_1401913B0.h" />
    <ClInclude Include="headers\_ConstructPEAU_Node_Tree_nodV_Tmap_traitsVbasic_st_140194330.h" />
    <ClInclude Include="headers\_ConstructPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAV_1403B3BA0.h" />
    <ClInclude Include="headers\_ConstructVCMoveMapLimitRightInfoV1stdYAXPEAVCMove_1403B37C0.h" />
    <ClInclude Include="headers\_Copy_backward_optPEAPEAVCMoveMapLimitInfoPEAPEAV1_1403ABF00.h" />
    <ClInclude Include="headers\_Copy_backward_optPEAPEAVCMoveMapLimitRightPEAPEAV_1403B35E0.h" />
    <ClInclude Include="headers\_Copy_backward_optPEAVCMoveMapLimitRightInfoPEAV1U_1403B3720.h" />
    <ClInclude Include="headers\_Copy_optPEAPEAVCMoveMapLimitInfoPEAPEAV1Urandom_a_1403AB540.h" />
    <ClInclude Include="headers\_Copy_optPEAPEAVCMoveMapLimitRightPEAPEAV1Urandom__1403B27E0.h" />
    <ClInclude Include="headers\_Copy_optPEAVCMoveMapLimitRightInfoPEAV1Urandom_ac_1403B2D90.h" />
    <ClInclude Include="headers\_CreateMonYAPEAVCMonsterPEAD0MMMZ_140406260.h" />
    <ClInclude Include="headers\_CRFMonsterAIMgrInstance__1_dtor0_14014C190.h" />
    <ClInclude Include="headers\_CWorldScheduleCWorldSchedule__1_dtor0_1403F3560.h" />
    <ClInclude Include="headers\_CWorldSchedule_CWorldSchedule__1_dtor0_1403F4690.h" />
    <ClInclude Include="headers\_Decconst_iterator_TreeV_Tmap_traitsVbasic_stringD_140191F90.h" />
    <ClInclude Include="headers\_DestroyPEAU_Node_Tree_nodV_Tmap_traitsVbasic_stri_1401942E0.h" />
    <ClInclude Include="headers\_DestroyPEAVCMoveMapLimitRightstdYAXPEAPEAVCMoveMa_1403B3C40.h" />
    <ClInclude Include="headers\_DestroySDMCMonsterSAXXZ_140149460.h" />
    <ClInclude Include="headers\_DestroyU_Node_Tree_nodV_Tmap_traitsVbasic_stringD_140195D10.h" />
    <ClInclude Include="headers\_DestroyVCMoveMapLimitRightInfostdYAXPEAVCMoveMapL_1403A38D0.h" />
    <ClInclude Include="headers\_DestroyvectorPEAVCMoveMapLimitInfoVallocatorPEAVC_1403A2AC0.h" />
    <ClInclude Include="headers\_DestroyvectorPEAVCMoveMapLimitRightVallocatorPEAV_1403A3B40.h" />
    <ClInclude Include="headers\_DestroyvectorVCMoveMapLimitRightInfoVallocatorVCM_1403A2D30.h" />
    <ClInclude Include="headers\_Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEAVC_1403A3310.h" />
    <ClInclude Include="headers\_Destroy_rangePEAVCMoveMapLimitInfoVallocatorPEAVC_1403A3700.h" />
    <ClInclude Include="headers\_Destroy_rangePEAVCMoveMapLimitRightVallocatorPEAV_1403A3C00.h" />
    <ClInclude Include="headers\_Destroy_rangePEAVCMoveMapLimitRightVallocatorPEAV_1403A3CE0.h" />
    <ClInclude Include="headers\_Destroy_rangeVCMoveMapLimitRightInfoVallocatorVCM_1403A3440.h" />
    <ClInclude Include="headers\_Destroy_rangeVCMoveMapLimitRightInfoVallocatorVCM_1403A3780.h" />
    <ClInclude Include="headers\_DrawObjectCMapDisplayAEAAJPEAVCGameObjectPEAVCSur_14019FDE0.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__CMonsters_logTrace_1406E8580.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__CMonsters_logTrace_1406E85C0.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__g_MapDisplay___1406E8740.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__g_MapOper___1406E8700.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__g_MonsterEventResp_1406E8EA0.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__g_strLMapMap___1406E86C0.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__g_strMapMap___1406E8680.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__g_WorldSch___1406E9680.h" />
    <ClInclude Include="headers\_ECCircleZoneUEAAPEAXIZ_1403F0010.h" />
    <ClInclude Include="headers\_ECMapDataUEAAPEAXIZ_140199070.h" />
    <ClInclude Include="headers\_ECMapDisplayUEAAPEAXIZ_1401A1240.h" />
    <ClInclude Include="headers\_ECMapTabUEAAPEAXIZ_14002EFB0.h" />
    <ClInclude Include="headers\_ECMonsterEventSetUEAAPEAXIZ_1402AA010.h" />
    <ClInclude Include="headers\_ECMonsterUEAAPEAXIZ_140204E60.h" />
    <ClInclude Include="headers\_Erase_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140195B70.h" />
    <ClInclude Include="headers\_FillPEAPEAVCMoveMapLimitInfoPEAV1stdYAXPEAPEAVCMo_1403AB9A0.h" />
    <ClInclude Include="headers\_FillPEAPEAVCMoveMapLimitRightPEAV1stdYAXPEAPEAVCM_1403B2BD0.h" />
    <ClInclude Include="headers\_FillPEAVCMoveMapLimitRightInfoV1stdYAXPEAVCMoveMa_1403B2EE0.h" />
    <ClInclude Include="headers\_Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1stdYAXPEAPEA_1403AC570.h" />
    <ClInclude Include="headers\_Fill_nPEAPEAVCMoveMapLimitInfo_KPEAV1Urandom_acce_1403AC310.h" />
    <ClInclude Include="headers\_Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1stdYAXPEAPE_1403B3C50.h" />
    <ClInclude Include="headers\_Fill_nPEAPEAVCMoveMapLimitRight_KPEAV1Urandom_acc_1403B39C0.h" />
    <ClInclude Include="headers\_FindV_Vector_iteratorPEAVCMoveMapLimitRightValloc_1403B25D0.h" />
    <ClInclude Include="headers\_GBossSchedule_MapQEAAPEAXIZ_14041B3C0.h" />
    <ClInclude Include="headers\_GCCircleZoneUEAAPEAXIZ_14012E3C0.h" />
    <ClInclude Include="headers\_GCMapDataUEAAPEAXIZ_1401880E0.h" />
    <ClInclude Include="headers\_GCMapOperationUEAAPEAXIZ_140198890.h" />
    <ClInclude Include="headers\_GCMonsterAIUEAAPEAXIZ_14014FCA0.h" />
    <ClInclude Include="headers\_GCMonsterEventRespawnUEAAPEAXIZ_1402A7860.h" />
    <ClInclude Include="headers\_GCMonsterHierarchyUEAAPEAXIZ_14014B690.h" />
    <ClInclude Include="headers\_GCMonsterUEAAPEAXIZ_14014BB90.h" />
    <ClInclude Include="headers\_GCMoveMapLimitInfoQEAAPEAXIZ_1403A74C0.h" />
    <ClInclude Include="headers\_GCMoveMapLimitManagerQEAAPEAXIZ_1403A1EA0.h" />
    <ClInclude Include="headers\_GCMoveMapLimitRightInfoQEAAPEAXIZ_1403A3920.h" />
    <ClInclude Include="headers\_GCMoveMapLimitRightQEAAPEAXIZ_1403AE6D0.h" />
    <ClInclude Include="headers\_GCRFMonsterAIMgrQEAAPEAXIZ_140203390.h" />
    <ClInclude Include="headers\_GetBaseClassCMapTabKAPEAUCRuntimeClassXZ_14002E410.h" />
    <ClInclude Include="headers\_GetBlinkNodeCMonsterAggroMgrIEAAPEAUCAggroNodeXZ_14015E2E0.h" />
    <ClInclude Include="headers\_GetMonsterContTimeYAGEEZ_140147560.h" />
    <ClInclude Include="headers\_Gptr2userVCMonsterlua_tinkerUEAAPEAXIZ_14040B9D0.h" />
    <ClInclude Include="headers\_GrowmapdequeIVallocatorIstdstdIEAAX_KZ_140657610.h" />
    <ClInclude Include="headers\_Growmapdeque_KVallocator_KstdstdIEAAX_KZ_140656D90.h" />
    <ClInclude Include="headers\_G_Node_Tree_nodV_Tmap_traitsVbasic_stringDUchar_t_140195D60.h" />
    <ClInclude Include="headers\_G__change_monsterQEAAPEAXIZ_140272DF0.h" />
    <ClInclude Include="headers\_Incconst_iterator_TreeV_Tmap_traitsVbasic_stringD_140192190.h" />
    <ClInclude Include="headers\_Insert_nvectorPEAVCMoveMapLimitInfoVallocatorPEAV_1403A9E40.h" />
    <ClInclude Include="headers\_Insert_nvectorPEAVCMoveMapLimitRightVallocatorPEA_1403AFD70.h" />
    <ClInclude Include="headers\_Insert_nvectorVCMoveMapLimitRightInfoVallocatorVC_1403B0D30.h" />
    <ClInclude Include="headers\_Insert_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190690.h" />
    <ClInclude Include="headers\_Isnil_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEC0.h" />
    <ClInclude Include="headers\_Iter_catPEAPEAVCMoveMapLimitInfostdYAAUrandom_acc_1403AC2B0.h" />
    <ClInclude Include="headers\_Iter_catPEAPEAVCMoveMapLimitRightstdYAAUrandom_ac_1403B3960.h" />
    <ClInclude Include="headers\_Iter_randomPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAA_1403AB4E0.h" />
    <ClInclude Include="headers\_Iter_randomPEAPEAVCMoveMapLimitRightPEAPEAV1stdYA_1403B2780.h" />
    <ClInclude Include="headers\_Iter_randomPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_1403B2D30.h" />
    <ClInclude Include="headers\_Key_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_14018D650.h" />
    <ClInclude Include="headers\_Kfn_Tmap_traitsVbasic_stringDUchar_traitsDstdVall_140190EB0.h" />
    <ClInclude Include="headers\_Lbound_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190D70.h" />
    <ClInclude Include="headers\_Left_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140190680.h" />
    <ClInclude Include="headers\_Lmost_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140191420.h" />
    <ClInclude Include="headers\_LoadMonBlkCMapDataAEAA_NPEADPEAU_map_fldZ_140181850.h" />
    <ClInclude Include="headers\_LoadPortalCMapDataAEAA_NPEADZ_1401825B0.h" />
    <ClInclude Include="headers\_LoadQuestCMapDataAEAA_NPEADZ_140183B60.h" />
    <ClInclude Include="headers\_LoadResourceCMapDataAEAA_NPEADZ_1401835B0.h" />
    <ClInclude Include="headers\_LoadSafeCMapDataAEAA_NPEADZ_140183F80.h" />
    <ClInclude Include="headers\_LoadStartCMapDataAEAA_NPEADZ_140182EE0.h" />
    <ClInclude Include="headers\_LoadStoreDummyCMapDataAEAA_NPEADZ_140182A10.h" />
    <ClInclude Include="headers\_Lrotate_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191470.h" />
    <ClInclude Include="headers\_lua_tinkerptr2lua_CMonster_invoke__1_dtor0_14040B720.h" />
    <ClInclude Include="headers\_Max_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_140192110.h" />
    <ClInclude Include="headers\_mc_AddMonster__1_dtor0_1402759A0.h" />
    <ClInclude Include="headers\_mc_ChangeMonster__1_dtor0_140276380.h" />
    <ClInclude Include="headers\_mc_RespawnMonster__1_dtor0_140275E30.h" />
    <ClInclude Include="headers\_Min_TreeV_Tmap_traitsVbasic_stringDUchar_traitsDs_1401922B0.h" />
    <ClInclude Include="headers\_Move_backward_optPEAPEAVCMoveMapLimitInfoPEAPEAV1_1403ABA50.h" />
    <ClInclude Include="headers\_Move_backward_optPEAPEAVCMoveMapLimitRightPEAPEAV_1403B2C80.h" />
    <ClInclude Include="headers\_Move_backward_optPEAVCMoveMapLimitRightInfoPEAV1U_1403B2FC0.h" />
    <ClInclude Include="headers\_Move_catPEAPEAVCMoveMapLimitInfostdYAAU_Undefined_1403AB9F0.h" />
    <ClInclude Include="headers\_Move_catPEAPEAVCMoveMapLimitRightstdYAAU_Undefine_1403B2C20.h" />
    <ClInclude Include="headers\_Move_catPEAVCMoveMapLimitRightInfostdYAAU_Undefin_1403B2F60.h" />
    <ClInclude Include="headers\_Mynodeconst_iterator_TreeV_Tmap_traitsVbasic_stri_14018ED10.h" />
    <ClInclude Include="headers\_Myval_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FF00.h" />
    <ClInclude Include="headers\_Parent_TreeV_Tmap_traitsVbasic_stringDUchar_trait_140190D50.h" />
    <ClInclude Include="headers\_Ptr_catPEAPEAVCMoveMapLimitInfoPEAPEAV1stdYAAU_Sc_1403A36A0.h" />
    <ClInclude Include="headers\_Ptr_catPEAPEAVCMoveMapLimitRightPEAPEAV1stdYAAU_S_1403A3C80.h" />
    <ClInclude Include="headers\_Ptr_catPEAVCMoveMapLimitRightInfoPEAV1stdYAAU_Non_1403A3720.h" />
    <ClInclude Include="headers\_Ptr_catV_Vector_const_iteratorPEAVCMoveMapLimitRi_1403B3320.h" />
    <ClInclude Include="headers\_qc_monsterGroup__1_dtor0_140273E80.h" />
    <ClInclude Include="headers\_Right_TreeV_Tmap_traitsVbasic_stringDUchar_traits_14018FEE0.h" />
    <ClInclude Include="headers\_Rmost_TreeV_Tmap_traitsVbasic_stringDUchar_traits_140190E60.h" />
    <ClInclude Include="headers\_Root_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140191620.h" />
    <ClInclude Include="headers\_Rrotate_TreeV_Tmap_traitsVbasic_stringDUchar_trai_140191670.h" />
    <ClInclude Include="headers\_ShortRankCMonsterAggroMgrIEAAXXZ_14015E370.h" />
    <ClInclude Include="headers\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1A70.h" />
    <ClInclude Include="headers\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1AA0.h" />
    <ClInclude Include="headers\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1AD0.h" />
    <ClInclude Include="headers\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1B00.h" />
    <ClInclude Include="headers\_stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1B30.h" />
    <ClInclude Include="headers\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C680.h" />
    <ClInclude Include="headers\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C6B0.h" />
    <ClInclude Include="headers\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C6F0.h" />
    <ClInclude Include="headers\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C720.h" />
    <ClInclude Include="headers\_stdmap_stdbasic_string_char_stdchar_traits_char___14018C750.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A2BC0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A7AA0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A83F0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8420.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8450.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A8620.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403A9240.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA350.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA380.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA3E0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403A2E30.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403AF740.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403AF770.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B07D0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0900.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0930.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B0960.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B1280.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B12B0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B12E0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRightInfo_stdallocator_CMo_1403B1340.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AECE0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF090.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF0C0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF100.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF130.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF2F0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF320.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AF350.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AFC40.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403AFC70.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0280.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B02B0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0310.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B0590.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1E70.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1EA0.h" />
    <ClInclude Include="headers\_stdvector_CMoveMapLimitRight_____ptr64_stdallocat_1403B1ED0.h" />
    <ClInclude Include="headers\_std_Construct_CMoveMapLimitRightInfo_CMoveMapLimi_1403B3850.h" />
    <ClInclude Include="headers\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B2690.h" />
    <ClInclude Include="headers\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B26C0.h" />
    <ClInclude Include="headers\_std_Find_std_Vector_iterator_CMoveMapLimitRight___1403B26F0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E560.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E590.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E5C0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E600.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E630.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E660.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E6A0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E6D0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E700.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_14018E740.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190400.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190430.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190470.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401904A0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401904D0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190500.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140190BD0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140191900.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140191930.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140193FB0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401945D0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194910.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194940.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140194970.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401949B0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401949F0.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195430.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195460.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195490.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195A50.h" />
    <ClInclude Include="headers\_std_Tree_std_Tmap_traits_stdbasic_string_char_std_140195A80.h" />
    <ClInclude Include="headers\_std_Vector_iterator_CMoveMapLimitRight_____ptr64__1403B0A90.h" />
    <ClInclude Include="headers\_std_Vector_iterator_CMoveMapLimitRight_____ptr64__1403B0AC0.h" />
    <ClInclude Include="headers\_TidyvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A2450.h" />
    <ClInclude Include="headers\_TidyvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403A3A60.h" />
    <ClInclude Include="headers\_TidyvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403A26B0.h" />
    <ClInclude Include="headers\_Tidy_TreeV_Tmap_traitsVbasic_stringDUchar_traitsD_140194450.h" />
    <ClInclude Include="headers\_UcopyV_Vector_const_iteratorPEAVCMoveMapLimitRigh_1403B1D70.h" />
    <ClInclude Include="headers\_UfillvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMo_1403AA6E0.h" />
    <ClInclude Include="headers\_UfillvectorPEAVCMoveMapLimitRightVallocatorPEAVCM_1403AF4E0.h" />
    <ClInclude Include="headers\_UfillvectorVCMoveMapLimitRightInfoVallocatorVCMov_1403B1730.h" />
    <ClInclude Include="headers\_UmovePEAPEAVCMoveMapLimitInfovectorPEAVCMoveMapLi_1403AAF70.h" />
    <ClInclude Include="headers\_UmovePEAPEAVCMoveMapLimitRightvectorPEAVCMoveMapL_1403B1F60.h" />
    <ClInclude Include="headers\_UmovePEAVCMoveMapLimitRightInfovectorVCMoveMapLim_1403B2260.h" />
    <ClInclude Include="headers\_Unchecked_move_backwardPEAPEAVCMoveMapLimitInfoPE_1403AB040.h" />
    <ClInclude Include="headers\_Unchecked_move_backwardPEAPEAVCMoveMapLimitRightP_1403B2030.h" />
    <ClInclude Include="headers\_Unchecked_move_backwardPEAVCMoveMapLimitRightInfo_1403B2330.h" />
    <ClInclude Include="headers\_XlenvectorPEAVCMoveMapLimitInfoVallocatorPEAVCMov_1403A2B30.h" />
    <ClInclude Include="headers\_XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403B0500.h" />
    <ClInclude Include="headers\_XlenvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403A2DA0.h" />
  </ItemGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>