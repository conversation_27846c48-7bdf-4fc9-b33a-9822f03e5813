<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{E3B21A2A-972A-482F-A942-C66BCD6E48EB}</ProjectGuid>
    <RootNamespace>NexusProCombat</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusPro.Combat</ProjectName>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  
  <ImportGroup Label="Shared">
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <PropertyGroup Label="UserMacros" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Debug\</OutDir>
    <IntDir>$(SolutionDir)obj\Debug\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Release\</OutDir>
    <IntDir>$(SolutionDir)obj\Release\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemGroup>
    <ClCompile Include="source\0allocatorVCGuildBattleRewardItemGUILD_BATTLEstdQE_1403D1530.cpp" />
    <ClCompile Include="source\0allocatorVCGuildBattleRewardItemGUILD_BATTLEstdQE_1403D1900.cpp" />
    <ClCompile Include="source\0CashItemRemoteStoreQEAAXZ_1402F3800.cpp" />
    <ClCompile Include="source\0CBattleTournamentInfoQEAAXZ_1403FEA40.cpp" />
    <ClCompile Include="source\0CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAXZ_1403CDE40.cpp" />
    <ClCompile Include="source\0CGuildBattleControllerIEAAXZ_1403D5680.cpp" />
    <ClCompile Include="source\0CGuildBattleGUILD_BATTLEQEAAXZ_1403EB010.cpp" />
    <ClCompile Include="source\0CGuildBattleLoggerGUILD_BATTLEIEAAXZ_1403CE6F0.cpp" />
    <ClCompile Include="source\0CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1403CA2F0.cpp" />
    <ClCompile Include="source\0CGuildBattleReservedScheduleGUILD_BATTLEQEAAIZ_1403DAB60.cpp" />
    <ClCompile Include="source\0CGuildBattleReservedScheduleListManagerGUILD_BATT_1403CD260.cpp" />
    <ClCompile Include="source\0CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DBA50.cpp" />
    <ClCompile Include="source\0CGuildBattleRewardItemGUILD_BATTLEQEAAXZ_1403C8EF0.cpp" />
    <ClCompile Include="source\0CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_1403C93A0.cpp" />
    <ClCompile Include="source\0CGuildBattleScheduleGUILD_BATTLEQEAAKZ_1403D9B00.cpp" />
    <ClCompile Include="source\0CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_1403DC830.cpp" />
    <ClCompile Include="source\0CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1403DA430.cpp" />
    <ClCompile Include="source\0CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_1403DEDA0.cpp" />
    <ClCompile Include="source\0CGuildBattleStateGUILD_BATTLEQEAAXZ_1403DEE30.cpp" />
    <ClCompile Include="source\0CGuildBattleStateListGUILD_BATTLEQEAAHHIZ_1403DEF90.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1403EB7B0.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1403EE250.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleGuildGUILD_BATTLEQEAAEZ_1403E04C0.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1403DF960.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleGUILD_BATTLEQEAAKZ_1403E2E40.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_1403EB070.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1403D33C0.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateCountDownGUILD_BATTLEQEAAX_1403F0820.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_1403F0D20.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_1403F0F20.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_1403F3120.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateInBattleGUILD_BATTLEQEAAXZ_1403F0950.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_1403F1E80.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateListPoolGUILD_BATTLEIEAAXZ_1403F22B0.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_1403F06B0.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_1403F0770.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_1403F0E10.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1403F0FD0.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateRoundListGUILD_BATTLEQEAAX_1403F2150.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateRoundProcessGUILD_BATTLEQE_1403F1770.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateRoundReturnStartPosGUILD_B_1403F1B10.cpp" />
    <ClCompile Include="source\0CNormalGuildBattleStateRoundStartGUILD_BATTLEQEAA_1403F1350.cpp" />
    <ClCompile Include="source\0CPossibleBattleGuildListManagerGUILD_BATTLEIEAAXZ_1403C9530.cpp" />
    <ClCompile Include="source\0CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAXZ_1403CCBC0.cpp" />
    <ClCompile Include="source\0CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAXZ_1403CC340.cpp" />
    <ClCompile Include="source\0CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_1403CBC30.cpp" />
    <ClCompile Include="source\0CRFCashItemDatabaseQEAAXZ_1402F2AE0.cpp" />
    <ClCompile Include="source\0MonsterSFContDamageToleracneQEAAXZ_140157E80.cpp" />
    <ClCompile Include="source\0vectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0C20.cpp" />
    <ClCompile Include="source\0_attack_count_result_zoclQEAAXZ_1400EEED0.cpp" />
    <ClCompile Include="source\0_ATTACK_DELAY_CHECKERQEAAXZ_140072D80.cpp" />
    <ClCompile Include="source\0_attack_force_result_zoclQEAAXZ_1400EEDD0.cpp" />
    <ClCompile Include="source\0_attack_gen_result_zoclQEAAXZ_1400EECD0.cpp" />
    <ClCompile Include="source\0_attack_keeper_inform_zoclQEAAXZ_140136C00.cpp" />
    <ClCompile Include="source\0_attack_paramQEAAXZ_14008E4A0.cpp" />
    <ClCompile Include="source\0_attack_selfdestruction_result_zoclQEAAXZ_1400EEF50.cpp" />
    <ClCompile Include="source\0_attack_siege_result_zoclQEAAXZ_1400EEFD0.cpp" />
    <ClCompile Include="source\0_attack_trap_inform_zoclQEAAXZ_140141400.cpp" />
    <ClCompile Include="source\0_attack_unit_result_zoclQEAAXZ_1400EEE50.cpp" />
    <ClCompile Include="source\0_be_damaged_charQEAAXZ_14013E400.cpp" />
    <ClCompile Include="source\0_eff_list_ATTACK_DELAY_CHECKERQEAAXZ_140072E20.cpp" />
    <ClCompile Include="source\0_guild_battle_suggest_matterQEAAXZ_14025CF40.cpp" />
    <ClCompile Include="source\0_mas_list_ATTACK_DELAY_CHECKERQEAAXZ_140072E40.cpp" />
    <ClCompile Include="source\0_param_cashitem_dblogQEAAKZ_140304CC0.cpp" />
    <ClCompile Include="source\0_personal_automine_attacked_zoclQEAAXZ_1402DE2F0.cpp" />
    <ClCompile Include="source\0_possible_battle_guild_list_result_zoclQEAAXZ_1403D07E0.cpp" />
    <ClCompile Include="source\0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D1610.cpp" />
    <ClCompile Include="source\0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D2430.cpp" />
    <ClCompile Include="source\0_remain_num_of_goodCashItemRemoteStoreQEAAXZ_1403047C0.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D15A0.cpp" />
    <ClCompile Include="source\0_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D2360.cpp" />
    <ClCompile Include="source\0_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D1540.cpp" />
    <ClCompile Include="source\0_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D19C0.cpp" />
    <ClCompile Include="source\0_Vector_valVCGuildBattleRewardItemGUILD_BATTLEVal_1403D14C0.cpp" />
    <ClCompile Include="source\1CashItemRemoteStoreQEAAXZ_1402F3A90.cpp" />
    <ClCompile Include="source\1CBattleTournamentInfoQEAAXZ_1403FEAB0.cpp" />
    <ClCompile Include="source\1CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAXZ_1403CDE80.cpp" />
    <ClCompile Include="source\1CGuildBattleControllerIEAAXZ_1403D56A0.cpp" />
    <ClCompile Include="source\1CGuildBattleGUILD_BATTLEQEAAXZ_1403EB040.cpp" />
    <ClCompile Include="source\1CGuildBattleLoggerGUILD_BATTLEIEAAXZ_1403CE710.cpp" />
    <ClCompile Include="source\1CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1403CA330.cpp" />
    <ClCompile Include="source\1CGuildBattleReservedScheduleGUILD_BATTLEQEAAXZ_1403DABB0.cpp" />
    <ClCompile Include="source\1CGuildBattleReservedScheduleListManagerGUILD_BATT_1403CD300.cpp" />
    <ClCompile Include="source\1CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DBA90.cpp" />
    <ClCompile Include="source\1CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_1403C93E0.cpp" />
    <ClCompile Include="source\1CGuildBattleScheduleGUILD_BATTLEQEAAXZ_1403D9B80.cpp" />
    <ClCompile Include="source\1CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_1403DC8E0.cpp" />
    <ClCompile Include="source\1CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1403DA470.cpp" />
    <ClCompile Include="source\1CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_1403DEE20.cpp" />
    <ClCompile Include="source\1CGuildBattleStateGUILD_BATTLEQEAAXZ_14007F740.cpp" />
    <ClCompile Include="source\1CGuildBattleStateListGUILD_BATTLEQEAAXZ_14007F810.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1403EB830.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1403EE2C0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleGuildGUILD_BATTLEQEAAXZ_1403E05A0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1403DF9A0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleGUILD_BATTLEQEAAXZ_1403E2FB0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_1403CEBE0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1403D3430.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateCountDownGUILD_BATTLEQEAAX_14007FD60.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_140080100.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140080280.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14007FB50.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateInBattleGUILD_BATTLEQEAAXZ_14007FE30.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_14007F850.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateListPoolGUILD_BATTLEIEAAXZ_1403F22E0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_14007FAF0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14007FC90.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_1400801C0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1403F31E0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateRoundListGUILD_BATTLEQEAAX_14007FEE0.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateRoundProcessGUILD_BATTLEQE_1403F1890.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateRoundReturnStartPosGUILD_B_1403F1C30.cpp" />
    <ClCompile Include="source\1CNormalGuildBattleStateRoundStartGUILD_BATTLEQEAA_1403F1470.cpp" />
    <ClCompile Include="source\1CPossibleBattleGuildListManagerGUILD_BATTLEIEAAXZ_1403C9580.cpp" />
    <ClCompile Include="source\1CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAXZ_1403CCBF0.cpp" />
    <ClCompile Include="source\1CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAXZ_1403CC3C0.cpp" />
    <ClCompile Include="source\1CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_1403CBCE0.cpp" />
    <ClCompile Include="source\1CRFCashItemDatabaseUEAAXZ_1402F2BB0.cpp" />
    <ClCompile Include="source\1vectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0CA0.cpp" />
    <ClCompile Include="source\1_param_cashitem_dblogQEAAXZ_140304D50.cpp" />
    <ClCompile Include="source\1_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D0FF0.cpp" />
    <ClCompile Include="source\1_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D0FB0.cpp" />
    <ClCompile Include="source\1_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D0F70.cpp" />
    <ClCompile Include="source\4CGuildBattleReservedScheduleGUILD_BATTLEQEAAAEBV0_1403DB6E0.cpp" />
    <ClCompile Include="source\4CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DC790.cpp" />
    <ClCompile Include="source\4CGuildBattleScheduleGUILD_BATTLEQEAAAEBV01AEBV01Z_1403DA350.cpp" />
    <ClCompile Include="source\8_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D23C0.cpp" />
    <ClCompile Include="source\9_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D1A20.cpp" />
    <ClCompile Include="source\accHitTestCFormViewUEAAJJJPEAUtagVARIANTZ_14002BB60.cpp" />
    <ClCompile Include="source\accHitTestCWndUEAAJJJPEAUtagVARIANTZ_0_1404DBCBE.cpp" />
    <ClCompile Include="source\Action_Attack_OnLoopDfAIMgrSAXPEAVUs_HFSMKPEAXZ_1401518E0.cpp" />
    <ClCompile Include="source\AddCGuildBattleControllerQEAAEPEAVCGuild0KEKZ_14025D2A0.cpp" />
    <ClCompile Include="source\AddCGuildBattleControllerQEAAEPEAVCGuild0KKEKZ_1403D5DB0.cpp" />
    <ClCompile Include="source\AddCGuildBattleReservedScheduleGUILD_BATTLEQEAAEKK_1403DAC40.cpp" />
    <ClCompile Include="source\AddCGuildBattleReservedScheduleMapGroupGUILD_BATTL_1403DC440.cpp" />
    <ClCompile Include="source\AddCGuildBattleScheduleManagerGUILD_BATTLEQEAAEIKK_1403D9180.cpp" />
    <ClCompile Include="source\AddCNormalGuildBattleManagerGUILD_BATTLEQEAAEPEAVC_1403D3DC0.cpp" />
    <ClCompile Include="source\AddCompleteCGuildBattleControllerQEAAXEIKKZ_1403D6E20.cpp" />
    <ClCompile Include="source\AddCompleteCNormalGuildBattleGUILD_BATTLEQEAAXEZ_1403E3910.cpp" />
    <ClCompile Include="source\AddCompleteCNormalGuildBattleManagerGUILD_BATTLEQE_1403D4060.cpp" />
    <ClCompile Include="source\AddDefaultDBRecordCNormalGuildBattleManagerGUILD_B_1403D4FD0.cpp" />
    <ClCompile Include="source\AddDefaultDBTableCGuildBattleScheduleManagerGUILD__1403DD320.cpp" />
    <ClCompile Include="source\AddGoldCntCNormalGuildBattleGuildMemberGUILD_BATTL_1403EAE50.cpp" />
    <ClCompile Include="source\AddGuildBattleSchduleCMainThreadQEAAXPEAU_DB_QRY_S_1401F4170.cpp" />
    <ClCompile Include="source\AddKillCntCNormalGuildBattleGuildMemberGUILD_BATTL_1403EADF0.cpp" />
    <ClCompile Include="source\AddScheduleCGuildBattleControllerQEAAEPEADZ_1403D6AD0.cpp" />
    <ClCompile Include="source\AdvanceCGuildBattleStateListGUILD_BATTLEIEAAXHZ_1403DF610.cpp" />
    <ClCompile Include="source\AdvanceRegenStateCNormalGuildBattleStateInBattleGU_1403EB290.cpp" />
    <ClCompile Include="source\AdvanceRegenStateCNormalGuildBattleStateListGUILD__1403EB220.cpp" />
    <ClCompile Include="source\allocateallocatorVCGuildBattleRewardItemGUILD_BATT_1403D1970.cpp" />
    <ClCompile Include="source\AreaDamageProcCAttackQEAAXHHPEAMH_NZ_14016C320.cpp" />
    <ClCompile Include="source\AskJoinCNormalGuildBattleGuildGUILD_BATTLEIEAAXHPE_1403E2C80.cpp" />
    <ClCompile Include="source\AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKE_1403E0D20.cpp" />
    <ClCompile Include="source\AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E1ED0.cpp" />
    <ClCompile Include="source\AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E3F40.cpp" />
    <ClCompile Include="source\AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E49E0.cpp" />
    <ClCompile Include="source\assignvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D0D10.cpp" />
    <ClCompile Include="source\AttackableHeightCAnimusUEAAHXZ_140129880.cpp" />
    <ClCompile Include="source\AttackableHeightCGameObjectUEAAHXZ_14012E340.cpp" />
    <ClCompile Include="source\AttackableHeightCGuardTowerUEAAHXZ_1401328E0.cpp" />
    <ClCompile Include="source\AttackableHeightCMonsterUEAAHXZ_1401468D0.cpp" />
    <ClCompile Include="source\AttackableHeightCTrapUEAAHXZ_1401412D0.cpp" />
    <ClCompile Include="source\AttackCAnimusQEAA_NKZ_140126AC0.cpp" />
    <ClCompile Include="source\AttackCNuclearBombQEAAXHHZ_14013C4F0.cpp" />
    <ClCompile Include="source\AttackForceCAttackQEAAXPEAU_attack_param_NZ_14016A210.cpp" />
    <ClCompile Include="source\AttackForceRequestCNetworkEXAEAA_NHPEADZ_1401C1A50.cpp" />
    <ClCompile Include="source\AttackGenCAttackQEAAXPEAU_attack_param_N1Z_140169520.cpp" />
    <ClCompile Include="source\AttackMonsterForceCMonsterAttackQEAAXPEAU_attack_p_14015BA60.cpp" />
    <ClCompile Include="source\AttackMonsterGenCMonsterAttackQEAAXPEAU_attack_par_14015B300.cpp" />
    <ClCompile Include="source\AttackObjectCMonsterQEAAHHPEAVCGameObjectZ_140142A60.cpp" />
    <ClCompile Include="source\AttackPersonalRequestCNetworkEXAEAA_NHPEADZ_1401C1680.cpp" />
    <ClCompile Include="source\AttackSiegeRequestCNetworkEXAEAA_NHPEADZ_1401C1F20.cpp" />
    <ClCompile Include="source\AttackTestRequestCNetworkEXAEAA_NHPEADZ_1401C1D60.cpp" />
    <ClCompile Include="source\AttackUnitRequestCNetworkEXAEAA_NHPEADZ_1401C1C00.cpp" />
    <ClCompile Include="source\AvectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0CE0.cpp" />
    <ClCompile Include="source\beginvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1030.cpp" />
    <ClCompile Include="source\BuyByCashCashItemRemoteStoreAEAA_NGPEADZ_1402FE0D0.cpp" />
    <ClCompile Include="source\BuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_1402FECC0.cpp" />
    <ClCompile Include="source\BuyCashItemRemoteStoreQEAA_NGPEADZ_1402FE050.cpp" />
    <ClCompile Include="source\BuyLimSaleCashItemRemoteStoreQEAAGEKZ_1402FD950.cpp" />
    <ClCompile Include="source\buy_to_inven_cashitemCMgrAvatorItemHistoryQEAAXEGH_14023DF10.cpp" />
    <ClCompile Include="source\CalcAvgDamageCAttackQEAAXXZ_14016DBB0.cpp" />
    <ClCompile Include="source\CallProc_InsertCashItemLogCRFCashItemDatabaseQEAA__140483530.cpp" />
    <ClCompile Include="source\CallProc_RFOnlineAvg_EventCRFCashItemDatabaseQEAAH_1404832B0.cpp" />
    <ClCompile Include="source\CallProc_RFOnlineUseCRFCashItemDatabaseQEAAHAEAU_p_140482840.cpp" />
    <ClCompile Include="source\CallProc_RFOnlineUse_JapCRFCashItemDatabaseQEAAHAE_140483A70.cpp" />
    <ClCompile Include="source\CallProc_RFONLINE_CancelCRFCashItemDatabaseQEAAHAE_140482D90.cpp" />
    <ClCompile Include="source\CallProc_RFONLINE_Cancel_JapCRFCashItemDatabaseQEA_140484090.cpp" />
    <ClCompile Include="source\CancelSuggestedMatter_guild_battle_suggest_matterQ_14025D350.cpp" />
    <ClCompile Include="source\capacityvectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D2480.cpp" />
    <ClCompile Include="source\cashitem_del_from_invenCMgrAvatorItemHistoryQEAAXE_14023DD70.cpp" />
    <ClCompile Include="source\ChangeDiscountEventTimeCashItemRemoteStoreQEAA_NXZ_1402F7B10.cpp" />
    <ClCompile Include="source\ChangeEventTimeCashItemRemoteStoreQEAA_NEZ_1402FD050.cpp" />
    <ClCompile Include="source\Change_Conditional_Event_StatusCashItemRemoteStore_1402FBF00.cpp" />
    <ClCompile Include="source\CheatBuyCashItemRemoteStoreQEAA_NGPEBDHZ_1402F5B50.cpp" />
    <ClCompile Include="source\CheatDestroyStoneCNormalGuildBattleFieldGUILD_BATT_1403ED6C0.cpp" />
    <ClCompile Include="source\CheatLoadCashAmountCashItemRemoteStoreQEAA_NGHZ_1402F5990.cpp" />
    <ClCompile Include="source\CheatRegenStoneCNormalGuildBattleFieldGUILD_BATTLE_1403ED490.cpp" />
    <ClCompile Include="source\CheckAttackCHolyKeeperQEAA_NXZ_1401338D0.cpp" />
    <ClCompile Include="source\CheckBallTakeLimitTimeCNormalGuildBattleFieldGUILD_1403ED0F0.cpp" />
    <ClCompile Include="source\CheckCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1403D9DE0.cpp" />
    <ClCompile Include="source\CheckGetGravityStoneCNormalGuildBattleManagerGUILD_1403D4800.cpp" />
    <ClCompile Include="source\CheckGoalCNormalGuildBattleManagerGUILD_BATTLEQEAA_1403D4910.cpp" />
    <ClCompile Include="source\CheckGuildBattleLimitCAttackIEAA_NPEAVCGameObjectP_14016B570.cpp" />
    <ClCompile Include="source\CheckGuildBattleSuggestRequestToDestGuildCGuildQEA_1402579F0.cpp" />
    <ClCompile Include="source\CheckIsInTownCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED070.cpp" />
    <ClCompile Include="source\CheckLoopCGuildBattleStateListGUILD_BATTLEIEAAHXZ_1403DF4D0.cpp" />
    <ClCompile Include="source\CheckNextEventCGuildBattleReservedScheduleGUILD_BA_1403DB750.cpp" />
    <ClCompile Include="source\CheckRecordCGuildBattleRankManagerGUILD_BATTLEIEAA_1403CB7C0.cpp" />
    <ClCompile Include="source\CheckTakeGravityStoneCNormalGuildBattleManagerGUIL_1403D4700.cpp" />
    <ClCompile Include="source\Check_CashEvent_INICashItemRemoteStoreQEAA_NEZ_1402F8770.cpp" />
    <ClCompile Include="source\Check_CashEvent_StatusCashItemRemoteStoreQEAAXEZ_1402F93C0.cpp" />
    <ClCompile Include="source\check_cash_discount_iniCashItemRemoteStoreQEAAXXZ_1402F6970.cpp" />
    <ClCompile Include="source\check_cash_discount_statusCashItemRemoteStoreQEAAX_1402F6C30.cpp" />
    <ClCompile Include="source\Check_Conditional_Event_INICashItemRemoteStoreQEAA_1402FC480.cpp" />
    <ClCompile Include="source\Check_Conditional_Event_StatusCashItemRemoteStoreQ_1402FC060.cpp" />
    <ClCompile Include="source\Check_GrosssalesCashItemRemoteStoreQEAAXKZ_1402FB6D0.cpp" />
    <ClCompile Include="source\check_loaded_cde_statusCashItemRemoteStoreQEAAXXZ_1402F5F30.cpp" />
    <ClCompile Include="source\Check_Loaded_Event_StatusCashItemRemoteStoreQEAAXE_1402F8140.cpp" />
    <ClCompile Include="source\Check_Total_SellingCashItemRemoteStoreQEAAXXZ_1402FB640.cpp" />
    <ClCompile Include="source\CleanUpBattleCNormalGuildBattleGuildGUILD_BATTLEQE_1403E1E50.cpp" />
    <ClCompile Include="source\CleanUpBattleCNormalGuildBattleGuildMemberGUILD_BA_1403E00D0.cpp" />
    <ClCompile Include="source\CleanUpBattleCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E6FE0.cpp" />
    <ClCompile Include="source\CleanUpCCurrentGuildBattleInfoManagerGUILD_BATTLEI_1403CE510.cpp" />
    <ClCompile Include="source\CleanUpCGuildBattleControllerIEAAXXZ_1403D7900.cpp" />
    <ClCompile Include="source\CleanUpCGuildBattleRankManagerGUILD_BATTLEIEAAXXZ_1403CB3F0.cpp" />
    <ClCompile Include="source\CleanUpDanglingReservedScheduleCGuildBattleReserve_1403DB570.cpp" />
    <ClCompile Include="source\CleanUpDanglingReservedScheduleCGuildBattleReserve_1403DC5F0.cpp" />
    <ClCompile Include="source\CleanUpDanglingReservedScheduleCGuildBattleSchedul_1403DD1C0.cpp" />
    <ClCompile Include="source\ClearAllCGuildBattleSchedulePoolGUILD_BATTLEQEAAXX_1403DA890.cpp" />
    <ClCompile Include="source\ClearBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_N_1403ED140.cpp" />
    <ClCompile Include="source\ClearByDayIDCGuildBattleSchedulePoolGUILD_BATTLEQE_1403DA930.cpp" />
    <ClCompile Include="source\ClearCCurrentGuildBattleInfoManagerGUILD_BATTLEQEA_1403CE220.cpp" />
    <ClCompile Include="source\ClearCGuildBattleControllerQEAAXXZ_1403D62F0.cpp" />
    <ClCompile Include="source\ClearCGuildBattleRankManagerGUILD_BATTLEIEAAXEZ_1403CB530.cpp" />
    <ClCompile Include="source\ClearCGuildBattleRankManagerGUILD_BATTLEQEAAXXZ_1403CB4B0.cpp" />
    <ClCompile Include="source\ClearCGuildBattleReservedScheduleGUILD_BATTLEQEAAX_1403DAE20.cpp" />
    <ClCompile Include="source\ClearCGuildBattleReservedScheduleGUILD_BATTLEQEAA__1403DB420.cpp" />
    <ClCompile Include="source\ClearCGuildBattleReservedScheduleListManagerGUILD__1403CD970.cpp" />
    <ClCompile Include="source\ClearCGuildBattleReservedScheduleMapGroupGUILD_BAT_1403DC020.cpp" />
    <ClCompile Include="source\ClearCGuildBattleReservedScheduleMapGroupGUILD_BAT_1403DC230.cpp" />
    <ClCompile Include="source\ClearCGuildBattleScheduleGUILD_BATTLEQEAAXXZ_1403D9E90.cpp" />
    <ClCompile Include="source\ClearCGuildBattleScheduleManagerGUILD_BATTLEAEAAXX_1403DD480.cpp" />
    <ClCompile Include="source\ClearCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403DF030.cpp" />
    <ClCompile Include="source\ClearCNormalGuildBattleGuildGUILD_BATTLEQEAAXXZ_1403E0600.cpp" />
    <ClCompile Include="source\ClearCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DF9B0.cpp" />
    <ClCompile Include="source\ClearCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E3660.cpp" />
    <ClCompile Include="source\ClearCNormalGuildBattleManagerGUILD_BATTLEIEAAXPEA_1403D5400.cpp" />
    <ClCompile Include="source\ClearCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D4280.cpp" />
    <ClCompile Include="source\ClearCNormalGuildBattleStateListPoolGUILD_BATTLEQE_1403F2640.cpp" />
    <ClCompile Include="source\ClearCPossibleBattleGuildListManagerGUILD_BATTLEQE_1403D98E0.cpp" />
    <ClCompile Include="source\ClearCReservedGuildScheduleDayGroupGUILD_BATTLEQEA_1403CCE80.cpp" />
    <ClCompile Include="source\ClearCReservedGuildScheduleMapGroupGUILD_BATTLEQEA_1403CC4C0.cpp" />
    <ClCompile Include="source\ClearCReservedGuildSchedulePageGUILD_BATTLEQEAA_NX_1403CC220.cpp" />
    <ClCompile Include="source\ClearDBCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403D9F10.cpp" />
    <ClCompile Include="source\ClearDBRecordCNormalGuildBattleGUILD_BATTLEQEAA_NX_1403E3740.cpp" />
    <ClCompile Include="source\ClearElapsedScheduleCGuildBattleReservedScheduleGU_1403DB950.cpp" />
    <ClCompile Include="source\ClearGuildBattleCGuildQEAAXXZ_140258290.cpp" />
    <ClCompile Include="source\ClearInBattleStateCNormalGuildBattleGuildGUILD_BAT_1403E1680.cpp" />
    <ClCompile Include="source\ClearRegenCNormalGuildBattleFieldGUILD_BATTLEQEAA__1403ED190.cpp" />
    <ClCompile Include="source\ClearTommorowScheduleByIDCGuildBattleScheduleManag_1403DD0D0.cpp" />
    <ClCompile Include="source\Clear_guild_battle_suggest_matterQEAAXXZ_1402075A0.cpp" />
    <ClCompile Include="source\CompleteClearGuildBattleRankCGuildBattleController_1403D7030.cpp" />
    <ClCompile Include="source\CompleteCreateGuildBattleRankTableCGuildBattleCont_1403D6FB0.cpp" />
    <ClCompile Include="source\CompleteLoadGuildBattleTotalRecordCMainThreadAEAAX_1401EDBD0.cpp" />
    <ClCompile Include="source\CompleteOutGuildbattleCostCGuildQEAAXKKKKZ_140257B00.cpp" />
    <ClCompile Include="source\CompleteUpdateRankCGuildBattleControllerQEAAXEEPEA_1403D6EC0.cpp" />
    <ClCompile Include="source\CompleteUpdateReservedScheduleCGuildBattleControll_1403D6F60.cpp" />
    <ClCompile Include="source\constructallocatorVCGuildBattleRewardItemGUILD_BAT_1403D2F40.cpp" />
    <ClCompile Include="source\CopyUseTimeFieldCGuildBattleReservedScheduleGUILD__1403DAD20.cpp" />
    <ClCompile Include="source\CopyUseTimeFieldCGuildBattleReservedScheduleMapGro_1403DC4F0.cpp" />
    <ClCompile Include="source\CreateFieldObjectCNormalGuildBattleFieldGUILD_BATT_1403EC630.cpp" />
    <ClCompile Include="source\CreateGuildBattleRankTableCRFWorldDatabaseQEAA_NPE_1404A3810.cpp" />
    <ClCompile Include="source\CreateLogFileCGuildBattleLoggerGUILD_BATTLEQEAAXPE_1403CE9E0.cpp" />
    <ClCompile Include="source\CreateLogFileCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E7040.cpp" />
    <ClCompile Include="source\CreateLogFileCNormalGuildBattleLoggerGUILD_BATTLEQ_1403CED50.cpp" />
    <ClCompile Include="source\CreateLoggerCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9020.cpp" />
    <ClCompile Include="source\ct_StopBattleCHolyStoneSystemQEAA_NXZ_1402815B0.cpp" />
    <ClCompile Include="source\deallocateallocatorVCGuildBattleRewardItemGUILD_BA_1403D1920.cpp" />
    <ClCompile Include="source\DecideColorInxCNormalGuildBattleGUILD_BATTLEQEAAXX_1403E3E20.cpp" />
    <ClCompile Include="source\DecideWinCNormalGuildBattleGUILD_BATTLEIEAAEXZ_1403E76F0.cpp" />
    <ClCompile Include="source\DecPvpPointCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403E1860.cpp" />
    <ClCompile Include="source\DecPvpPointCNormalGuildBattleGuildMemberGUILD_BATT_1403DFE10.cpp" />
    <ClCompile Include="source\DeleteGuildBattleInfoCRFWorldDatabaseQEAA_NXZ_1404A2410.cpp" />
    <ClCompile Include="source\DeleteGuildBattleScheduleInfoCRFWorldDatabaseQEAA__1404A0FD0.cpp" />
    <ClCompile Include="source\DestGuildIsAvailableBattleRequestStateCGuildQEAAEX_140257960.cpp" />
    <ClCompile Include="source\destroyallocatorVCGuildBattleRewardItemGUILD_BATTL_1403D2FA0.cpp" />
    <ClCompile Include="source\DestroyCCurrentGuildBattleInfoManagerGUILD_BATTLES_1403CDF80.cpp" />
    <ClCompile Include="source\DestroyCGuildBattleControllerSAXXZ_1403D57A0.cpp" />
    <ClCompile Include="source\DestroyCGuildBattleLoggerGUILD_BATTLESAXXZ_1403CE850.cpp" />
    <ClCompile Include="source\DestroyCGuildBattleRankManagerGUILD_BATTLESAXXZ_1403CA430.cpp" />
    <ClCompile Include="source\DestroyCGuildBattleReservedScheduleListManagerGUIL_1403CD420.cpp" />
    <ClCompile Include="source\DestroyCGuildBattleScheduleManagerGUILD_BATTLESAXX_1403DCAD0.cpp" />
    <ClCompile Include="source\DestroyCGuildBattleSchedulePoolGUILD_BATTLESAXXZ_1403DA640.cpp" />
    <ClCompile Include="source\DestroyCGuildBattleSchedulerGUILD_BATTLESAXXZ_1403DD7C0.cpp" />
    <ClCompile Include="source\DestroyCNormalGuildBattleFieldGUILD_BATTLEAEAAXXZ_1403EDED0.cpp" />
    <ClCompile Include="source\DestroyCNormalGuildBattleFieldListGUILD_BATTLESAXX_1403EE420.cpp" />
    <ClCompile Include="source\DestroyCNormalGuildBattleManagerGUILD_BATTLESAXXZ_1403D35F0.cpp" />
    <ClCompile Include="source\DestroyCNormalGuildBattleStateListPoolGUILD_BATTLE_1403F2430.cpp" />
    <ClCompile Include="source\DestroyCPossibleBattleGuildListManagerGUILD_BATTLE_1403C9710.cpp" />
    <ClCompile Include="source\DestroyFieldObjectCNormalGuildBattleFieldGUILD_BAT_1403EC810.cpp" />
    <ClCompile Include="source\dhRExtractSubStringCRFCashItemDatabaseQEAAXPEAD0HZ_140483420.cpp" />
    <ClCompile Include="source\DividePvpPointCNormalGuildBattleGUILD_BATTLEQEAAXX_1403E6490.cpp" />
    <ClCompile Include="source\DoDayChangedWorkCNormalGuildBattleManagerGUILD_BAT_1403D4220.cpp" />
    <ClCompile Include="source\DoDayChangedWorkCPossibleBattleGuildListManagerGUI_1403C9AE0.cpp" />
    <ClCompile Include="source\DropGravityStoneCNormalGuildBattleGUILD_BATTLEQEAA_1403E5830.cpp" />
    <ClCompile Include="source\DropGravityStoneCNormalGuildBattleManagerGUILD_BAT_1403D4A10.cpp" />
    <ClCompile Include="source\dtor00OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C00.cpp" />
    <ClCompile Include="source\dtor10OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C20.cpp" />
    <ClCompile Include="source\dtor20OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C40.cpp" />
    <ClCompile Include="source\endvectorVCGuildBattleRewardItemGUILD_BATTLEValloc_1403D10A0.cpp" />
    <ClCompile Include="source\EnterCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildB_14007F760.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateCountDownGUILD_BATTLEM_1403F0870.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVC_14007FBC0.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVC_1403F0380.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateInBattleGUILD_BATTLEME_1403F0A00.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateNotifyGUILD_BATTLEMEAA_1403F0700.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateReadyGUILD_BATTLEMEAAH_1403F07C0.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateRoundGUILD_BATTLEMEAAH_1403F3180.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateRoundGUILD_BATTLEUEAAH_1403F1020.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateRoundProcessGUILD_BATT_1403F1970.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateRoundReturnStartPosGUI_1403F1D10.cpp" />
    <ClCompile Include="source\EnterCNormalGuildBattleStateRoundStartGUILD_BATTLE_1403F1550.cpp" />
    <ClCompile Include="source\erasevectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1110.cpp" />
    <ClCompile Include="source\fillPEAVCGuildBattleRewardItemGUILD_BATTLEV12stdYA_1403D27F0.cpp" />
    <ClCompile Include="source\fill_eff_list_ATTACK_DELAY_CHECKERQEAA_NXZ_14008EB90.cpp" />
    <ClCompile Include="source\fill_mas_list_ATTACK_DELAY_CHECKERQEAA_NXZ_14008EBC0.cpp" />
    <ClCompile Include="source\FinCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildBat_14007F7A0.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateDivideGUILD_BATTLEMEAAHP_1403F0D70.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateFinGUILD_BATTLEMEAAHPEAV_1403F0F70.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVCNo_14007FC00.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVCGu_1403F0460.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateInBattleGUILD_BATTLEMEAA_1403F0B50.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateReturnGUILD_BATTLEMEAAHP_1403F0E60.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateRoundGUILD_BATTLEMEAAHPE_1403F31C0.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateRoundGUILD_BATTLEUEAAHPE_1403F1100.cpp" />
    <ClCompile Include="source\FinCNormalGuildBattleStateRoundStartGUILD_BATTLEME_1403F1660.cpp" />
    <ClCompile Include="source\FindCashRecCashItemRemoteStoreSAPEBU_CashShop_fldH_1402F48F0.cpp" />
    <ClCompile Include="source\FindCGuildBattleRankManagerGUILD_BATTLEIEAA_NEKAEA_1403CB6F0.cpp" />
    <ClCompile Include="source\FindCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCFA0.cpp" />
    <ClCompile Include="source\FindCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCA30.cpp" />
    <ClCompile Include="source\FindCReservedGuildSchedulePageGUILD_BATTLEQEAA_NKZ_1403CBD80.cpp" />
    <ClCompile Include="source\FlashDamageProcCAttackIEAAXHHHH_NZ_14016B6F0.cpp" />
    <ClCompile Include="source\FlipCGuildBattleControllerQEAAXXZ_1403D6260.cpp" />
    <ClCompile Include="source\FlipCGuildBattleReservedScheduleGUILD_BATTLEQEAAXX_1403DB4B0.cpp" />
    <ClCompile Include="source\FlipCGuildBattleReservedScheduleListManagerGUILD_B_1403CD8F0.cpp" />
    <ClCompile Include="source\FlipCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DC1A0.cpp" />
    <ClCompile Include="source\FlipCGuildBattleScheduleManagerGUILD_BATTLEAEAAXXZ_1403DD3B0.cpp" />
    <ClCompile Include="source\FlipCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D41C0.cpp" />
    <ClCompile Include="source\FlipCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CD1C0.cpp" />
    <ClCompile Include="source\FlipCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCB50.cpp" />
    <ClCompile Include="source\FlipCReservedGuildSchedulePageGUILD_BATTLEQEAAXXZ_1403CC090.cpp" />
    <ClCompile Include="source\ForceNextCGuildBattleStateListGUILD_BATTLEIEAAXXZ_1403DF6A0.cpp" />
    <ClCompile Include="source\force_endup_cash_discount_eventCashItemRemoteStore_1402F7850.cpp" />
    <ClCompile Include="source\Get1PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNormal_1403D9360.cpp" />
    <ClCompile Include="source\Get2PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNormal_1403D9380.cpp" />
    <ClCompile Include="source\GetAccessibilityHitTestCWndQEAAJJJPEAUtagVARIANTZ__1404DC1DA.cpp" />
    <ClCompile Include="source\GetAmountCGuildBattleRewardItemGUILD_BATTLEQEBAEXZ_1403EAE80.cpp" />
    <ClCompile Include="source\GetANSIGuildNameCNormalGuildBattleGuildGUILD_BATTL_1403E0770.cpp" />
    <ClCompile Include="source\GetAttackDelay_WEAPON_PARAMQEAAKHHZ_14008E730.cpp" />
    <ClCompile Include="source\GetAttackDPCAnimusUEAAHXZ_14012CDF0.cpp" />
    <ClCompile Include="source\GetAttackDPCGameObjectUEAAHXZ_14012E360.cpp" />
    <ClCompile Include="source\GetAttackDPCGuardTowerUEAAHXZ_1401328C0.cpp" />
    <ClCompile Include="source\GetAttackDPCHolyKeeperUEAAHXZ_140136B30.cpp" />
    <ClCompile Include="source\GetAttackDPCHolyStoneUEAAHXZ_140138EB0.cpp" />
    <ClCompile Include="source\GetAttackDPCMonsterUEAAHXZ_14014BB10.cpp" />
    <ClCompile Include="source\GetAttackDPCTrapUEAAHXZ_1401412B0.cpp" />
    <ClCompile Include="source\GetAttackPartCAnimusQEAAHXZ_1401264E0.cpp" />
    <ClCompile Include="source\GetAttackPartCMonsterQEAAHXZ_14014DDC0.cpp" />
    <ClCompile Include="source\GetAttackPivotCHolyKeeperQEAAPEAMXZ_140133E40.cpp" />
    <ClCompile Include="source\GetAttackRangeCAnimusUEAAMXZ_1401295C0.cpp" />
    <ClCompile Include="source\GetAttackRangeCGameObjectUEAAMXZ_14012E300.cpp" />
    <ClCompile Include="source\GetAttackRangeCGuardTowerUEAAMXZ_140132670.cpp" />
    <ClCompile Include="source\GetAttackRangeCHolyKeeperUEAAMXZ_140136880.cpp" />
    <ClCompile Include="source\GetAttackRangeCMonsterUEAAMXZ_140146660.cpp" />
    <ClCompile Include="source\GetAttackRangeCTrapUEAAMXZ_140141070.cpp" />
    <ClCompile Include="source\GetAttackToolType_WEAPON_PARAMQEAAHXZ_1400349B0.cpp" />
    <ClCompile Include="source\GetBattleByGuildSerialCNormalGuildBattleManagerGUI_1403D5580.cpp" />
    <ClCompile Include="source\GetBattleCNormalGuildBattleManagerGUILD_BATTLEIEAA_1403D5520.cpp" />
    <ClCompile Include="source\GetBattleModeTimeCMonsterAIQEAAKXZ_140155890.cpp" />
    <ClCompile Include="source\GetBattleTimeCGuildBattleScheduleGUILD_BATTLEQEAAA_1403D9120.cpp" />
    <ClCompile Include="source\GetBattleTurmCGuildBattleScheduleGUILD_BATTLEQEAAJ_1403D9440.cpp" />
    <ClCompile Include="source\GetBlueCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_1403F30E0.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataBRUEAAHPEAU_Cash_14022F020.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataCNUEAAHPEAU_Cash_140230920.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataESUEAAHPEAU_Cash_1402318F0.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataGBUEAAHPEAU_Cash_14022C0D0.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataIDUEAAHPEAU_Cash_14022CA60.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataJPUEAAHPEAU_Cash_14022D3E0.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataKRUEAAHPEAU_Cash_14022B4A0.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataNULLUEAAHPEAU_Ca_1402130B0.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataPHUEAAHPEAU_Cash_14022DE90.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataRUUEAAHPEAU_Cash_14022E820.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataTHUEAAHPEAU_Cash_140232270.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataTWUEAAHPEAU_Cash_1402300F0.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataUEAAHPEAU_CashSh_140212910.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingDataUSUEAAHPEAU_Cash_1402313F0.cpp" />
    <ClCompile Include="source\GetCashItemPriceCNationSettingManagerQEAAHPEAU_Cas_140304810.cpp" />
    <ClCompile Include="source\GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVCGu_1403DAA30.cpp" />
    <ClCompile Include="source\GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVCGu_1403DEA50.cpp" />
    <ClCompile Include="source\GetCircleZoneCGuildBattleControllerQEAAPEAVCGameOb_1403D6860.cpp" />
    <ClCompile Include="source\GetCircleZoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ECA10.cpp" />
    <ClCompile Include="source\GetCircleZoneCNormalGuildBattleFieldListGUILD_BATT_1403EED90.cpp" />
    <ClCompile Include="source\GetCNormalGuildBattleStateListPoolGUILD_BATTLEQEAA_1403F26D0.cpp" />
    <ClCompile Include="source\GetColorInxCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403EAFC0.cpp" />
    <ClCompile Include="source\GetColorNameCNormalGuildBattleGuildGUILD_BATTLEQEA_1403EB320.cpp" />
    <ClCompile Include="source\GetCombatStateCMonsterQEAAEXZ_140143870.cpp" />
    <ClCompile Include="source\GetCurScheduleIDCGuildBattleReservedScheduleGUILD__1403DB640.cpp" />
    <ClCompile Include="source\GetCurScheduleIDCGuildBattleReservedScheduleMapGro_1403DC710.cpp" />
    <ClCompile Include="source\GetCurScheduleIDCGuildBattleScheduleManagerGUILD_B_1403DD2D0.cpp" />
    <ClCompile Include="source\GetDamagedObjNumCNuclearBombQEAAHXZ_14013E3A0.cpp" />
    <ClCompile Include="source\GetDayIDCGuildBattleReservedScheduleMapGroupGUILD__1403D9A90.cpp" />
    <ClCompile Include="source\GetEmptyMemberCNormalGuildBattleGuildGUILD_BATTLEI_1403E29E0.cpp" />
    <ClCompile Include="source\GetEvnetTimeCashItemRemoteStoreQEAAXPEAU_cash_even_1402FBA00.cpp" />
    <ClCompile Include="source\GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EE870.cpp" />
    <ClCompile Include="source\GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EE950.cpp" />
    <ClCompile Include="source\GetFieldCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_1400A6A80.cpp" />
    <ClCompile Include="source\GetFirstMapFieldByRaceCNormalGuildBattleFieldListG_1403EEB10.cpp" />
    <ClCompile Include="source\GetFirstMapInxByRaceCNormalGuildBattleFieldListGUI_1403EEA80.cpp" />
    <ClCompile Include="source\GetGoalCntCNormalGuildBattleGuildGUILD_BATTLEQEAAK_1403EB150.cpp" />
    <ClCompile Include="source\GetGoalCountCNormalGuildBattleGuildMemberGUILD_BAT_1403EB380.cpp" />
    <ClCompile Include="source\GetGravityStoneCNormalGuildBattleGUILD_BATTLEQEAAE_1403E4B80.cpp" />
    <ClCompile Include="source\GetGuildBattleNumberCNormalGuildBattleGUILD_BATTLE_1403D93A0.cpp" />
    <ClCompile Include="source\GetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAPEA_1403EB130.cpp" />
    <ClCompile Include="source\GetGuildCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_1403E3AB0.cpp" />
    <ClCompile Include="source\GetGuildNameCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0710.cpp" />
    <ClCompile Include="source\GetGuildRaceCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0830.cpp" />
    <ClCompile Include="source\GetGuildSerialCNormalGuildBattleGuildGUILD_BATTLEQ_1403E07D0.cpp" />
    <ClCompile Include="source\GetIDCGuildBattleReservedScheduleGUILD_BATTLEQEAAI_1403DEC40.cpp" />
    <ClCompile Include="source\GetIDCNormalGuildBattleGUILD_BATTLEQEAAKXZ_1403D9340.cpp" />
    <ClCompile Include="source\GetIndexCNormalGuildBattleGuildMemberGUILD_BATTLEQ_1403E0300.cpp" />
    <ClCompile Include="source\GetInfoCNormalGuildBattleGUILD_BATTLEQEAA_NAEAU_gu_1403E39A0.cpp" />
    <ClCompile Include="source\GetItemCodeCGuildBattleRewardItemGUILD_BATTLEQEBAP_1403C9270.cpp" />
    <ClCompile Include="source\GetJoinMemberCntCNormalGuildBattleGuildGUILD_BATTL_1403E2B20.cpp" />
    <ClCompile Include="source\GetKillCountCNormalGuildBattleGuildMemberGUILD_BAT_1403EB360.cpp" />
    <ClCompile Include="source\GetKillCountSumCNormalGuildBattleGuildGUILD_BATTLE_1403EB3F0.cpp" />
    <ClCompile Include="source\GetLeftTimeCCurrentGuildBattleInfoManagerGUILD_BAT_1403CE5C0.cpp" />
    <ClCompile Include="source\GetLeftTimeCGuildBattleScheduleGUILD_BATTLEQEAA_NA_1403DA220.cpp" />
    <ClCompile Include="source\GetLimDiscoutCashItemRemoteStoreQEAAEXZ_1402FD930.cpp" />
    <ClCompile Include="source\GetLoggerCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNo_1403F3100.cpp" />
    <ClCompile Include="source\GetMapCNormalGuildBattleFieldGUILD_BATTLEQEAAPEAVC_1400A6A60.cpp" />
    <ClCompile Include="source\GetMapCntCNormalGuildBattleFieldListGUILD_BATTLEQE_1403D0B10.cpp" />
    <ClCompile Include="source\GetMapCodeCNormalGuildBattleFieldGUILD_BATTLEQEAAH_1403EC950.cpp" />
    <ClCompile Include="source\GetMapIDCNormalGuildBattleFieldGUILD_BATTLEQEAAKXZ_1403EB0F0.cpp" />
    <ClCompile Include="source\GetMapInxCNormalGuildBattleFieldListGUILD_BATTLEQE_1403EE990.cpp" />
    <ClCompile Include="source\GetMapInxListCNormalGuildBattleFieldListGUILD_BATT_1403EEB70.cpp" />
    <ClCompile Include="source\GetMapStrCodeCNormalGuildBattleFieldGUILD_BATTLEQE_1403EC910.cpp" />
    <ClCompile Include="source\GetMaxJoinMemberCountCNormalGuildBattleGuildGUILD__1403EB410.cpp" />
    <ClCompile Include="source\GetMaxPageCReservedGuildScheduleMapGroupGUILD_BATT_1403D0A80.cpp" />
    <ClCompile Include="source\GetMemberCNormalGuildBattleGuildGUILD_BATTLEIEAAHK_1403E2A60.cpp" />
    <ClCompile Include="source\GetMemberPtrCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0A00.cpp" />
    <ClCompile Include="source\GetObjTypeCGuildBattleGUILD_BATTLEUEAAHXZ_1403EB060.cpp" />
    <ClCompile Include="source\GetObjTypeCNormalGuildBattleGUILD_BATTLEUEAAHXZ_1403EB090.cpp" />
    <ClCompile Include="source\GetPortalIndexInfoCNormalGuildBattleFieldGUILD_BAT_1403ECAF0.cpp" />
    <ClCompile Include="source\GetRealStartTimeCGuildBattleScheduleGUILD_BATTLEQE_1403D93F0.cpp" />
    <ClCompile Include="source\GetRedCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorma_1403F30C0.cpp" />
    <ClCompile Include="source\GetRefCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAV_1403DAB00.cpp" />
    <ClCompile Include="source\GetRegenerCGuildBattleControllerQEAAPEAVCGameObjec_1403D6810.cpp" />
    <ClCompile Include="source\GetRegenerCNormalGuildBattleFieldGUILD_BATTLEQEAAP_1403EC980.cpp" />
    <ClCompile Include="source\GetRegenerCNormalGuildBattleFieldListGUILD_BATTLEQ_1403EECF0.cpp" />
    <ClCompile Include="source\GetRemainNumOfGoodCashItemRemoteStoreQEAAHGZ_1402F5CD0.cpp" />
    <ClCompile Include="source\GetRemainNumOfGoodCashItemRemoteStoreQEAAHQEADZ_1402F5D60.cpp" />
    <ClCompile Include="source\GetScoreCNormalGuildBattleGuildGUILD_BATTLEQEAAKXZ_1403EB170.cpp" />
    <ClCompile Include="source\GetSerialCNormalGuildBattleGuildMemberGUILD_BATTLE_1403EAFA0.cpp" />
    <ClCompile Include="source\GetSetDiscoutCashItemRemoteStoreQEAAEEZ_1402FB0A0.cpp" />
    <ClCompile Include="source\GetSIDCGuildBattleScheduleGUILD_BATTLEQEAAKXZ_1403D9100.cpp" />
    <ClCompile Include="source\GetSIDCGuildBattleSchedulePoolGUILD_BATTLEQEAAKIKZ_1403DEC80.cpp" />
    <ClCompile Include="source\GetSLIDCGuildBattleReservedScheduleMapGroupGUILD_B_1403DC680.cpp" />
    <ClCompile Include="source\GetStartBattleTickTimeCHolyStoneSystemQEAAKXZ_14027B5C0.cpp" />
    <ClCompile Include="source\GetStateCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1403D93D0.cpp" />
    <ClCompile Include="source\GetStoneCGuildBattleControllerQEAAPEAVCGameObjectH_1403D67C0.cpp" />
    <ClCompile Include="source\GetStoneCNormalGuildBattleFieldGUILD_BATTLEQEAAPEA_1403F0360.cpp" />
    <ClCompile Include="source\GetStoneCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EEC50.cpp" />
    <ClCompile Include="source\GetTermCGuildBattleStateGUILD_BATTLEUEAAAVCTimeSpa_14007F7C0.cpp" />
    <ClCompile Include="source\GetTermCGuildBattleStateListGUILD_BATTLEQEAAAVCTim_1403DF470.cpp" />
    <ClCompile Include="source\GetTermCNormalGuildBattleStateCountDownGUILD_BATTL_14007FDC0.cpp" />
    <ClCompile Include="source\GetTermCNormalGuildBattleStateDivideGUILD_BATTLEUE_140080160.cpp" />
    <ClCompile Include="source\GetTermCNormalGuildBattleStateFinGUILD_BATTLEUEAAA_1400802E0.cpp" />
    <ClCompile Include="source\GetTermCNormalGuildBattleStateInBattleGUILD_BATTLE_1400800D0.cpp" />
    <ClCompile Include="source\GetTermCNormalGuildBattleStateNotifyGUILD_BATTLEUE_14007FC20.cpp" />
    <ClCompile Include="source\GetTermCNormalGuildBattleStateReadyGUILD_BATTLEUEA_14007FCF0.cpp" />
    <ClCompile Include="source\GetTermCNormalGuildBattleStateReturnGUILD_BATTLEUE_140080220.cpp" />
    <ClCompile Include="source\GetTimeCGuildBattleScheduleGUILD_BATTLEQEAAAVCTime_1403DEAC0.cpp" />
    <ClCompile Include="source\GetTodayDayIDCGuildBattleScheduleManagerGUILD_BATT_1403D9A40.cpp" />
    <ClCompile Include="source\GetTodaySLIDByMapCGuildBattleScheduleManagerGUILD__1403DD230.cpp" />
    <ClCompile Include="source\GetToleranceProbMonsterSFContDamageToleracneQEAAMX_14014CAF0.cpp" />
    <ClCompile Include="source\GetTomorrowDayIDCGuildBattleScheduleManagerGUILD_B_1403D9AB0.cpp" />
    <ClCompile Include="source\GetTomorrowSLIDByMapCGuildBattleScheduleManagerGUI_1403DD280.cpp" />
    <ClCompile Include="source\GetTopGoalMemberCNormalGuildBattleGuildGUILD_BATTL_1403E0B00.cpp" />
    <ClCompile Include="source\GetTopKillMemberCNormalGuildBattleGuildGUILD_BATTL_1403E0A70.cpp" />
    <ClCompile Include="source\GetWinnerGradeCBattleTournamentInfoQEAAEKPEADZ_1403FEC30.cpp" />
    <ClCompile Include="source\Get_CashEvent_StatusCashItemRemoteStoreQEAAEEZ_1402FAC70.cpp" />
    <ClCompile Include="source\get_cde_statusCashItemRemoteStoreQEAAEXZ_1402F7380.cpp" />
    <ClCompile Include="source\Get_Conditional_Event_NameCashItemRemoteStoreQEAAX_1402FC8A0.cpp" />
    <ClCompile Include="source\Get_Conditional_Event_StatusCashItemRemoteStoreQEA_1402FC460.cpp" />
    <ClCompile Include="source\GoalCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEAVCN_1403E1600.cpp" />
    <ClCompile Include="source\GoalCNormalGuildBattleGUILD_BATTLEQEAAEKHZ_1403E4CA0.cpp" />
    <ClCompile Include="source\GoalCNormalGuildBattleStateGUILD_BATTLEUEAAXXZ_14007FBB0.cpp" />
    <ClCompile Include="source\GoodsListBuyByCashCashItemRemoteStoreAEAA_NGPEADZ_1403009C0.cpp" />
    <ClCompile Include="source\GoodsListBuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_140300C60.cpp" />
    <ClCompile Include="source\GoodsListCashItemRemoteStoreQEAA_NGPEADZ_1402F5220.cpp" />
    <ClCompile Include="source\GotoCGuildBattleStateListGUILD_BATTLEQEAAHXZ_1403DF2C0.cpp" />
    <ClCompile Include="source\GotoStateCGuildBattleStateListGUILD_BATTLEQEAA_NHZ_1403F3370.cpp" />
    <ClCompile Include="source\GuildBattleBlockReportCNetworkEXAEAA_NHPEADZ_1401D7F40.cpp" />
    <ClCompile Include="source\GuildBattleCurrentBattleInfoRequestCNetworkEXAEAA__1401C8770.cpp" />
    <ClCompile Include="source\GuildBattleGetGravityStoneRequestCNetworkEXAEAA_NH_1401C8940.cpp" />
    <ClCompile Include="source\GuildBattleGoalRequestCNetworkEXAEAA_NHPEADZ_1401C89E0.cpp" />
    <ClCompile Include="source\GuildBattleJoinGuildBattleRequestCNetworkEXAEAA_NH_1401C8820.cpp" />
    <ClCompile Include="source\GuildBattlePossibleGuildBattleListCNetworkEXAEAA_N_1401C84A0.cpp" />
    <ClCompile Include="source\GuildBattleRankListRequestCNetworkEXAEAA_NHPEADZ_1401C8560.cpp" />
    <ClCompile Include="source\GuildBattleReservedScheduleRequestCNetworkEXAEAA_N_1401C8670.cpp" />
    <ClCompile Include="source\GuildBattleResultLogCNormalGuildBattleGUILD_BATTLE_1403E6E10.cpp" />
    <ClCompile Include="source\GuildBattleResultLogNotifyWebCNormalGuildBattleGUI_1403E83E0.cpp" />
    <ClCompile Include="source\GuildBattleResultLogPushDBLogCNormalGuildBattleGUI_1403E7D30.cpp" />
    <ClCompile Include="source\GuildBattleSuggestRequestToDestGuildCGuildQEAAEKKK_140257C30.cpp" />
    <ClCompile Include="source\GuildBattleTakeGravityStoneRequestCNetworkEXAEAA_N_1401C88B0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E7FF0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E82F0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E83B0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8780.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8880.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8A50.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8B90.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8DE0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9040.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9240.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9300.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E93C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9480.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9500.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E95C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8070.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8370.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8430.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8800.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8900.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8AD0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8C10.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8E60.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E90C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E92C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9380.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9440.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9580.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9640.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8030.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8330.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E83F0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E87C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E88C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8A90.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8BD0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8E20.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9080.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9280.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9340.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9400.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E94C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9540.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9600.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DA120.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DA8F0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DAA70.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DD460.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DDFB0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DF520.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DF7C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E0540.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E1270.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E3F80.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4140.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E42C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4440.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4570.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4770.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DA1D0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DA9A0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DAB20.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DD510.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DE060.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DF5D0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DF870.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E05F0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E1320.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4030.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E41F0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4370.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4620.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4820.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DA170.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DA940.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DAAC0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DD4B0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DE000.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DF570.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DF810.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E0590.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E12C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E3FD0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4190.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4310.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4490.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E45C0.cpp" />
    <ClCompile Include="source\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E47C0.cpp" />
    <ClCompile Include="source\IncPvpPointCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403E1730.cpp" />
    <ClCompile Include="source\IncPvpPointCNormalGuildBattleGuildMemberGUILD_BATT_1403DFCE0.cpp" />
    <ClCompile Include="source\IncVerCReservedGuildSchedulePageGUILD_BATTLEQEAAXX_1403CBD40.cpp" />
    <ClCompile Include="source\inform_cashdiscount_eventCashItemRemoteStoreQEAAXG_1402F6F60.cpp" />
    <ClCompile Include="source\inform_cashdiscount_status_allCashItemRemoteStoreQ_1402F6FC0.cpp" />
    <ClCompile Include="source\Inform_CashEventCashItemRemoteStoreQEAAXGZ_1402FAFC0.cpp" />
    <ClCompile Include="source\Inform_CashEvent_Status_AllCashItemRemoteStoreQEAA_1402FADC0.cpp" />
    <ClCompile Include="source\Inform_ConditionalEventCashItemRemoteStoreQEAAXGZ_1402FC970.cpp" />
    <ClCompile Include="source\Inform_ConditionalEvent_Status_AllCashItemRemoteSt_1402FC640.cpp" />
    <ClCompile Include="source\InGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_SYN_D_1401F46D0.cpp" />
    <ClCompile Include="source\InGuildbattleRewardMoneyCMainThreadQEAAXPEAU_DB_QR_1401F4AD0.cpp" />
    <ClCompile Include="source\InitCBattleTournamentInfoQEAAXXZ_1403FEAC0.cpp" />
    <ClCompile Include="source\InitCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA_1403CE000.cpp" />
    <ClCompile Include="source\InitCGuildBattleControllerQEAA_NXZ_1403D5820.cpp" />
    <ClCompile Include="source\InitCGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_1403CE8D0.cpp" />
    <ClCompile Include="source\InitCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_1403CA4B0.cpp" />
    <ClCompile Include="source\InitCGuildBattleReservedScheduleListManagerGUILD_B_1403CD4A0.cpp" />
    <ClCompile Include="source\InitCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DBB90.cpp" />
    <ClCompile Include="source\InitCGuildBattleRewardItemGUILD_BATTLEQEAA_NGZ_1403C8F30.cpp" />
    <ClCompile Include="source\InitCGuildBattleRewardItemManagerGUILD_BATTLEQEAA__1403C9420.cpp" />
    <ClCompile Include="source\InitCGuildBattleScheduleManagerGUILD_BATTLEQEAA_NI_1403DCB50.cpp" />
    <ClCompile Include="source\InitCGuildBattleSchedulePoolGUILD_BATTLEQEAA_NIZ_1403DA6C0.cpp" />
    <ClCompile Include="source\InitCGuildBattleSchedulerGUILD_BATTLEQEAA_NXZ_1403DD840.cpp" />
    <ClCompile Include="source\InitCNormalGuildBattleFieldGUILD_BATTLEQEAA_NIZ_1403EB870.cpp" />
    <ClCompile Include="source\InitCNormalGuildBattleFieldListGUILD_BATTLEQEAA_NX_1403EE4A0.cpp" />
    <ClCompile Include="source\InitCNormalGuildBattleGUILD_BATTLEQEAAXPEAVCGuild0_1403E30D0.cpp" />
    <ClCompile Include="source\InitCNormalGuildBattleGUILD_BATTLEQEAA_N_NIKKKKEZ_1403E3180.cpp" />
    <ClCompile Include="source\InitCNormalGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_1403CEC70.cpp" />
    <ClCompile Include="source\InitCNormalGuildBattleManagerGUILD_BATTLEQEAA_NXZ_1403D3670.cpp" />
    <ClCompile Include="source\InitCNormalGuildBattleStateListPoolGUILD_BATTLEQEA_1403F24B0.cpp" />
    <ClCompile Include="source\InitCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403C9790.cpp" />
    <ClCompile Include="source\InitCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCC90.cpp" />
    <ClCompile Include="source\InitCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CC420.cpp" />
    <ClCompile Include="source\InitCReservedGuildSchedulePageGUILD_BATTLEQEAA_NEZ_1403CC170.cpp" />
    <ClCompile Include="source\InitializeCashItemRemoteStoreQEAA_NXZ_1402F4EF0.cpp" />
    <ClCompile Include="source\InitMonsterSFContDamageToleracneQEAAXMZ_140157EF0.cpp" />
    <ClCompile Include="source\InitUseFieldCNormalGuildBattleFieldListGUILD_BATTL_1403EEE30.cpp" />
    <ClCompile Include="source\Init_ATTACK_DELAY_CHECKERQEAAXXZ_140072E60.cpp" />
    <ClCompile Include="source\init_eff_list_ATTACK_DELAY_CHECKERQEAAXXZ_140072F70.cpp" />
    <ClCompile Include="source\init_mas_list_ATTACK_DELAY_CHECKERQEAAXXZ_140072F90.cpp" />
    <ClCompile Include="source\InsertGuildBattleDefaultRecordCRFWorldDatabaseQEAA_1404A24B0.cpp" />
    <ClCompile Include="source\InsertGuildBattleRankRecordCRFWorldDatabaseQEAA_NK_1404A3760.cpp" />
    <ClCompile Include="source\InsertGuildBattleScheduleDefaultRecordCRFWorldData_1404A1070.cpp" />
    <ClCompile Include="source\insertvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D16C0.cpp" />
    <ClCompile Include="source\Insert_PatrirchItemChargeRefundCRFWorldDatabaseQEA_1404BE8B0.cpp" />
    <ClCompile Include="source\Insert_PatrirchItemChargeRefundPatriarchElectProce_1402BC040.cpp" />
    <ClCompile Include="source\Insert_RaceBattleLogCRFWorldDatabaseQEAA_NPEAU_rac_1404C1670.cpp" />
    <ClCompile Include="source\InstanceCashItemRemoteStoreSAPEAV1XZ_140079810.cpp" />
    <ClCompile Include="source\InstanceCCurrentGuildBattleInfoManagerGUILD_BATTLE_1403CDEC0.cpp" />
    <ClCompile Include="source\InstanceCGuildBattleControllerSAPEAV1XZ_1403D56E0.cpp" />
    <ClCompile Include="source\InstanceCGuildBattleLoggerGUILD_BATTLESAPEAV12XZ_1403CE790.cpp" />
    <ClCompile Include="source\InstanceCGuildBattleRankManagerGUILD_BATTLESAPEAV1_1403CA370.cpp" />
    <ClCompile Include="source\InstanceCGuildBattleReservedScheduleListManagerGUI_1403CD360.cpp" />
    <ClCompile Include="source\InstanceCGuildBattleRewardItemManagerGUILD_BATTLES_1403D9790.cpp" />
    <ClCompile Include="source\InstanceCGuildBattleScheduleManagerGUILD_BATTLESAP_1403DCA10.cpp" />
    <ClCompile Include="source\InstanceCGuildBattleSchedulePoolGUILD_BATTLESAPEAV_1403DA580.cpp" />
    <ClCompile Include="source\InstanceCGuildBattleSchedulerGUILD_BATTLESAPEAV12X_1403DD700.cpp" />
    <ClCompile Include="source\InstanceCNormalGuildBattleFieldListGUILD_BATTLESAP_1403EE360.cpp" />
    <ClCompile Include="source\InstanceCNormalGuildBattleManagerGUILD_BATTLESAPEA_1403D3530.cpp" />
    <ClCompile Include="source\InstanceCNormalGuildBattleStateListPoolGUILD_BATTL_1403F2370.cpp" />
    <ClCompile Include="source\InstanceCPossibleBattleGuildListManagerGUILD_BATTL_1403C9650.cpp" />
    <ClCompile Include="source\IsAttackableInTownCGameObjectUEAA_NXZ_140072B70.cpp" />
    <ClCompile Include="source\IsAttackableInTownCMonsterUEAA_NXZ_14014BB30.cpp" />
    <ClCompile Include="source\IsAvailableSuggestCGuildBattleControllerQEAAEPEAVC_1403D5BD0.cpp" />
    <ClCompile Include="source\IsBattleModeUseCRecallRequestQEAA_NXZ_14024FD00.cpp" />
    <ClCompile Include="source\IsBeAttackedAbleAutominePersonalUEAA_N_NZ_1402DDD00.cpp" />
    <ClCompile Include="source\IsBeAttackedAbleCAnimusUEAA_N_NZ_14012CE10.cpp" />
    <ClCompile Include="source\IsBeAttackedAbleCGameObjectUEAA_N_NZ_14012E390.cpp" />
    <ClCompile Include="source\IsBeAttackedAbleCGuardTowerUEAA_N_NZ_1401328F0.cpp" />
    <ClCompile Include="source\IsBeAttackedAbleCHolyKeeperUEAA_N_NZ_140136B00.cpp" />
    <ClCompile Include="source\IsBeAttackedAbleCHolyStoneUEAA_N_NZ_140138ED0.cpp" />
    <ClCompile Include="source\IsBeAttackedAbleCMonsterUEAA_N_NZ_1401468F0.cpp" />
    <ClCompile Include="source\IsBeAttackedAbleCTrapUEAA_N_NZ_1401412E0.cpp" />
    <ClCompile Include="source\IsBuyCashItemByGoldCashItemRemoteStoreQEAA_NXZ_1400F0860.cpp" />
    <ClCompile Include="source\IsCashItemYAHEKZ_14003F600.cpp" />
    <ClCompile Include="source\IsCharInSectorCAttackSAHQEAM00MMZ_14016D920.cpp" />
    <ClCompile Include="source\IsCommitteeMemberCNormalGuildBattleGuildMemberGUIL_1403E0220.cpp" />
    <ClCompile Include="source\IsCompleteBattle_guild_battle_suggest_matterQEAA_N_1403D0800.cpp" />
    <ClCompile Include="source\isConEventTimeCashItemRemoteStoreQEAA_NXZ_1402FBB80.cpp" />
    <ClCompile Include="source\IsDayChangedCGuildBattleScheduleManagerGUILD_BATTL_1403DD530.cpp" />
    <ClCompile Include="source\IsDelay_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008EC60.cpp" />
    <ClCompile Include="source\IsDoneCGuildBattleReservedScheduleGUILD_BATTLEQEAA_1403DEC60.cpp" />
    <ClCompile Include="source\IsDoneCGuildBattleReservedScheduleMapGroupGUILD_BA_1403DED60.cpp" />
    <ClCompile Include="source\IsDoneCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DE9F0.cpp" />
    <ClCompile Include="source\IsEmptyCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DE990.cpp" />
    <ClCompile Include="source\IsEmptyCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1403DF8E0.cpp" />
    <ClCompile Include="source\IsEmptyCNormalGuildBattleGuildMemberGUILD_BATTLEQE_1403EAD50.cpp" />
    <ClCompile Include="source\IsEmptyCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9630.cpp" />
    <ClCompile Include="source\IsEmptyTimeCGuildBattleReservedScheduleGUILD_BATTL_1403DABC0.cpp" />
    <ClCompile Include="source\IsEmptyTimeCGuildBattleReservedScheduleMapGroupGUI_1403DC3C0.cpp" />
    <ClCompile Include="source\IsEmptyTimeCGuildBattleScheduleManagerGUILD_BATTLE_1403D9870.cpp" />
    <ClCompile Include="source\IsEnableStartCNormalGuildBattleGuildMemberGUILD_BA_1403E01B0.cpp" />
    <ClCompile Include="source\IsEventTimeCashItemRemoteStoreQEAA_NEZ_1402FACA0.cpp" />
    <ClCompile Include="source\IsExistCNormalGuildBattleGuildMemberGUILD_BATTLEQE_1403E0130.cpp" />
    <ClCompile Include="source\IsInBattleCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14007C0A0.cpp" />
    <ClCompile Include="source\IsInBattleCNormalGuildBattleStateListGUILD_BATTLEQ_14007C0F0.cpp" />
    <ClCompile Include="source\IsInBattleRegenStateCNormalGuildBattleGUILD_BATTLE_1403D94B0.cpp" />
    <ClCompile Include="source\IsInBattleRegenStateCNormalGuildBattleStateInBattl_1403D9570.cpp" />
    <ClCompile Include="source\IsInBattleRegenStateCNormalGuildBattleStateListGUI_1403D9500.cpp" />
    <ClCompile Include="source\IsInBattleRegenStateCNormalGuildBattleStateRoundLi_1403D95C0.cpp" />
    <ClCompile Include="source\IsJoinMemberCNormalGuildBattleGuildGUILD_BATTLEIEA_1403E2BC0.cpp" />
    <ClCompile Include="source\IsMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA_NK_1403EB1B0.cpp" />
    <ClCompile Include="source\IsMemberGuildCNormalGuildBattleGUILD_BATTLEQEAA_NK_1403D9690.cpp" />
    <ClCompile Include="source\IsNullCGuildBattleRewardItemGUILD_BATTLEQEBA_NXZ_1403EAEA0.cpp" />
    <ClCompile Include="source\IsPreAttackAbleMonCMonsterQEAAHXZ_140155460.cpp" />
    <ClCompile Include="source\IsProcCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DEAF0.cpp" />
    <ClCompile Include="source\IsProcCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1403D9250.cpp" />
    <ClCompile Include="source\IsProcCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9200.cpp" />
    <ClCompile Include="source\IsReadyOrCountStateCNormalGuildBattleGUILD_BATTLEQ_14007BFD0.cpp" />
    <ClCompile Include="source\IsReadyOrCountStateCNormalGuildBattleStateListGUIL_14007C020.cpp" />
    <ClCompile Include="source\IsRegistedMapInxCNormalGuildBattleFieldListGUILD_B_1403EF0C0.cpp" />
    <ClCompile Include="source\IsReStartCNormalGuildBattleGuildGUILD_BATTLEQEAAHK_1403E0970.cpp" />
    <ClCompile Include="source\IsReStartCNormalGuildBattleGuildMemberGUILD_BATTLE_1403EADD0.cpp" />
    <ClCompile Include="source\IsReStartCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E41A0.cpp" />
    <ClCompile Include="source\IsSFContDamageMonsterSFContDamageToleracneQEAA_NXZ_140157F90.cpp" />
    <ClCompile Include="source\IsStorageCodeWithItemKindYAHHHZ_14003BD10.cpp" />
    <ClCompile Include="source\IsWaitCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DEB50.cpp" />
    <ClCompile Include="source\IsWhiteSpaceCondensedTiXmlBaseSA_NXZ_140530810.cpp" />
    <ClCompile Include="source\IsWhiteSpaceTiXmlBaseKA_NDZ_140530820.cpp" />
    <ClCompile Include="source\Is_Battle_ModeCGameObjectUEAA_NXZ_14012CBC0.cpp" />
    <ClCompile Include="source\is_cde_timeCashItemRemoteStoreQEAA_NXZ_1402F7070.cpp" />
    <ClCompile Include="source\JoinCNormalGuildBattleGuildGUILD_BATTLEQEAAEKEAEAH_1403E0B90.cpp" />
    <ClCompile Include="source\JoinCNormalGuildBattleGuildMemberGUILD_BATTLEQEAAX_1403DFA30.cpp" />
    <ClCompile Include="source\JoinCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E3B90.cpp" />
    <ClCompile Include="source\JoinCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKKZ_1403D4420.cpp" />
    <ClCompile Include="source\JoinGuildCGuildBattleControllerQEAAXHKKZ_1400AD450.cpp" />
    <ClCompile Include="source\JoinGuildCNormalGuildBattleManagerGUILD_BATTLEQEAA_1403D4BB0.cpp" />
    <ClCompile Include="source\JudgeBattleCNormalGuildBattleGUILD_BATTLEQEAAEXZ_1403E6400.cpp" />
    <ClCompile Include="source\j_0allocatorVCGuildBattleRewardItemGUILD_BATTLEstd_1400029C8.cpp" />
    <ClCompile Include="source\j_0allocatorVCGuildBattleRewardItemGUILD_BATTLEstd_14000CC5C.cpp" />
    <ClCompile Include="source\j_0CashItemRemoteStoreQEAAXZ_140013930.cpp" />
    <ClCompile Include="source\j_0CBattleTournamentInfoQEAAXZ_140008C01.cpp" />
    <ClCompile Include="source\j_0CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAX_1400017BC.cpp" />
    <ClCompile Include="source\j_0CGuildBattleControllerIEAAXZ_140002748.cpp" />
    <ClCompile Include="source\j_0CGuildBattleGUILD_BATTLEQEAAXZ_140011EE1.cpp" />
    <ClCompile Include="source\j_0CGuildBattleLoggerGUILD_BATTLEIEAAXZ_140001870.cpp" />
    <ClCompile Include="source\j_0CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1400064FB.cpp" />
    <ClCompile Include="source\j_0CGuildBattleReservedScheduleGUILD_BATTLEQEAAIZ_14000ED36.cpp" />
    <ClCompile Include="source\j_0CGuildBattleReservedScheduleListManagerGUILD_BA_14000559C.cpp" />
    <ClCompile Include="source\j_0CGuildBattleReservedScheduleMapGroupGUILD_BATTL_14000AD85.cpp" />
    <ClCompile Include="source\j_0CGuildBattleRewardItemGUILD_BATTLEQEAAXZ_14000764E.cpp" />
    <ClCompile Include="source\j_0CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_140010DCA.cpp" />
    <ClCompile Include="source\j_0CGuildBattleScheduleGUILD_BATTLEQEAAKZ_140013C32.cpp" />
    <ClCompile Include="source\j_0CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_140004E4E.cpp" />
    <ClCompile Include="source\j_0CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_14000697E.cpp" />
    <ClCompile Include="source\j_0CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_140005D30.cpp" />
    <ClCompile Include="source\j_0CGuildBattleStateGUILD_BATTLEQEAAXZ_140011E50.cpp" />
    <ClCompile Include="source\j_0CGuildBattleStateListGUILD_BATTLEQEAAHHIZ_140008C9C.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1400114D2.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1400080D5.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleGuildGUILD_BATTLEQEAAEZ_14000B596.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1400058E4.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleGUILD_BATTLEQEAAKZ_140011E4B.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_140011388.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_140005673.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateCountDownGUILD_BATTLEQEA_14000BC80.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_14000C1C6.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140010FDC.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14000CC11.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateInBattleGUILD_BATTLEQEAA_14000253B.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_1400127B0.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateListPoolGUILD_BATTLEIEAA_14000323D.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_140005BB4.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14000A943.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_14000FCB3.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1400020C7.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateRoundListGUILD_BATTLEQEA_140002F04.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateRoundProcessGUILD_BATTLE_140003ABC.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateRoundReturnStartPosGUILD_1400118DD.cpp" />
    <ClCompile Include="source\j_0CNormalGuildBattleStateRoundStartGUILD_BATTLEQE_1400085DF.cpp" />
    <ClCompile Include="source\j_0CPossibleBattleGuildListManagerGUILD_BATTLEIEAA_14000CCF7.cpp" />
    <ClCompile Include="source\j_0CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAX_14000DBC5.cpp" />
    <ClCompile Include="source\j_0CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAX_140006799.cpp" />
    <ClCompile Include="source\j_0CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_140012198.cpp" />
    <ClCompile Include="source\j_0CRFCashItemDatabaseQEAAXZ_140013EA8.cpp" />
    <ClCompile Include="source\j_0MonsterSFContDamageToleracneQEAAXZ_140011072.cpp" />
    <ClCompile Include="source\j_0vectorVCGuildBattleRewardItemGUILD_BATTLEValloc_140011A18.cpp" />
    <ClCompile Include="source\j_0_attack_count_result_zoclQEAAXZ_14000E692.cpp" />
    <ClCompile Include="source\j_0_ATTACK_DELAY_CHECKERQEAAXZ_14000C0E0.cpp" />
    <ClCompile Include="source\j_0_attack_force_result_zoclQEAAXZ_1400039FE.cpp" />
    <ClCompile Include="source\j_0_attack_gen_result_zoclQEAAXZ_14000C086.cpp" />
    <ClCompile Include="source\j_0_attack_keeper_inform_zoclQEAAXZ_1400013E8.cpp" />
    <ClCompile Include="source\j_0_attack_paramQEAAXZ_14000AE4D.cpp" />
    <ClCompile Include="source\j_0_attack_selfdestruction_result_zoclQEAAXZ_140012C56.cpp" />
    <ClCompile Include="source\j_0_attack_siege_result_zoclQEAAXZ_140010F8C.cpp" />
    <ClCompile Include="source\j_0_attack_trap_inform_zoclQEAAXZ_1400033F5.cpp" />
    <ClCompile Include="source\j_0_attack_unit_result_zoclQEAAXZ_14000B6FE.cpp" />
    <ClCompile Include="source\j_0_be_damaged_charQEAAXZ_14000A335.cpp" />
    <ClCompile Include="source\j_0_eff_list_ATTACK_DELAY_CHECKERQEAAXZ_14000A560.cpp" />
    <ClCompile Include="source\j_0_guild_battle_suggest_matterQEAAXZ_140004B56.cpp" />
    <ClCompile Include="source\j_0_mas_list_ATTACK_DELAY_CHECKERQEAAXZ_14000A82B.cpp" />
    <ClCompile Include="source\j_0_param_cashitem_dblogQEAAKZ_14000CE0F.cpp" />
    <ClCompile Include="source\j_0_personal_automine_attacked_zoclQEAAXZ_14000471E.cpp" />
    <ClCompile Include="source\j_0_possible_battle_guild_list_result_zoclQEAAXZ_14000DC92.cpp" />
    <ClCompile Include="source\j_0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_140001DF2.cpp" />
    <ClCompile Include="source\j_0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_1400085AD.cpp" />
    <ClCompile Include="source\j_0_remain_num_of_goodCashItemRemoteStoreQEAAXZ_14000560F.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorVCGuildBattleRewardItemGU_140001393.cpp" />
    <ClCompile Include="source\j_0_Vector_const_iteratorVCGuildBattleRewardItemGU_140006127.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_140002E2D.cpp" />
    <ClCompile Include="source\j_0_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_14000F79F.cpp" />
    <ClCompile Include="source\j_0_Vector_valVCGuildBattleRewardItemGUILD_BATTLEV_140007DC4.cpp" />
    <ClCompile Include="source\j_1CashItemRemoteStoreQEAAXZ_140011815.cpp" />
    <ClCompile Include="source\j_1CBattleTournamentInfoQEAAXZ_14000B604.cpp" />
    <ClCompile Include="source\j_1CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAX_14000AD17.cpp" />
    <ClCompile Include="source\j_1CGuildBattleControllerIEAAXZ_14000C4FA.cpp" />
    <ClCompile Include="source\j_1CGuildBattleGUILD_BATTLEQEAAXZ_14000B41A.cpp" />
    <ClCompile Include="source\j_1CGuildBattleLoggerGUILD_BATTLEIEAAXZ_14000FE0C.cpp" />
    <ClCompile Include="source\j_1CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_140007243.cpp" />
    <ClCompile Include="source\j_1CGuildBattleReservedScheduleGUILD_BATTLEQEAAXZ_14000C540.cpp" />
    <ClCompile Include="source\j_1CGuildBattleReservedScheduleListManagerGUILD_BA_14000C4B4.cpp" />
    <ClCompile Include="source\j_1CGuildBattleReservedScheduleMapGroupGUILD_BATTL_140009075.cpp" />
    <ClCompile Include="source\j_1CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_140007DE2.cpp" />
    <ClCompile Include="source\j_1CGuildBattleScheduleGUILD_BATTLEQEAAXZ_1400037A1.cpp" />
    <ClCompile Include="source\j_1CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_140013327.cpp" />
    <ClCompile Include="source\j_1CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1400074C3.cpp" />
    <ClCompile Include="source\j_1CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_14000BF82.cpp" />
    <ClCompile Include="source\j_1CGuildBattleStateGUILD_BATTLEQEAAXZ_140010217.cpp" />
    <ClCompile Include="source\j_1CGuildBattleStateListGUILD_BATTLEQEAAXZ_14000CEA5.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_140010145.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_14000E2FF.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleGuildGUILD_BATTLEQEAAXZ_140013A1B.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_14001133D.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleGUILD_BATTLEQEAAXZ_14000FF38.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_14000A533.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1400066B3.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateCountDownGUILD_BATTLEQEA_140009CF0.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_140002185.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140005C1D.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14000D648.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateInBattleGUILD_BATTLEQEAA_140009831.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_140004D45.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateListPoolGUILD_BATTLEIEAA_14000D463.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_140007A90.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14000448F.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_14000312F.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_14000358F.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateRoundListGUILD_BATTLEQEA_14000696F.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateRoundProcessGUILD_BATTLE_14000EF7F.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateRoundReturnStartPosGUILD_14000A349.cpp" />
    <ClCompile Include="source\j_1CNormalGuildBattleStateRoundStartGUILD_BATTLEQE_1400012BC.cpp" />
    <ClCompile Include="source\j_1CPossibleBattleGuildListManagerGUILD_BATTLEIEAA_1400069E7.cpp" />
    <ClCompile Include="source\j_1CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAX_140006E6A.cpp" />
    <ClCompile Include="source\j_1CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAX_14000ABE1.cpp" />
    <ClCompile Include="source\j_1CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_14000DD32.cpp" />
    <ClCompile Include="source\j_1CRFCashItemDatabaseUEAAXZ_14000F1AF.cpp" />
    <ClCompile Include="source\j_1vectorVCGuildBattleRewardItemGUILD_BATTLEValloc_14000CA36.cpp" />
    <ClCompile Include="source\j_1_param_cashitem_dblogQEAAXZ_14000ACE0.cpp" />
    <ClCompile Include="source\j_1_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_140013AE3.cpp" />
    <ClCompile Include="source\j_1_Vector_const_iteratorVCGuildBattleRewardItemGU_1400069C9.cpp" />
    <ClCompile Include="source\j_1_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_14000C54A.cpp" />
    <ClCompile Include="source\j_4CGuildBattleReservedScheduleGUILD_BATTLEQEAAAEB_14000ECE6.cpp" />
    <ClCompile Include="source\j_4CGuildBattleReservedScheduleMapGroupGUILD_BATTL_14000983B.cpp" />
    <ClCompile Include="source\j_4CGuildBattleScheduleGUILD_BATTLEQEAAAEBV01AEBV0_14000E138.cpp" />
    <ClCompile Include="source\j_8_Vector_const_iteratorVCGuildBattleRewardItemGU_1400104F1.cpp" />
    <ClCompile Include="source\j_9_Vector_const_iteratorVCGuildBattleRewardItemGU_14000C7DE.cpp" />
    <ClCompile Include="source\j_accHitTestCFormViewUEAAJJJPEAUtagVARIANTZ_140006DC5.cpp" />
    <ClCompile Include="source\j_Action_Attack_OnLoopDfAIMgrSAXPEAVUs_HFSMKPEAXZ_140008D23.cpp" />
    <ClCompile Include="source\j_AddCGuildBattleControllerQEAAEPEAVCGuild0KEKZ_14000ECFF.cpp" />
    <ClCompile Include="source\j_AddCGuildBattleControllerQEAAEPEAVCGuild0KKEKZ_1400092CD.cpp" />
    <ClCompile Include="source\j_AddCGuildBattleReservedScheduleGUILD_BATTLEQEAAE_140012021.cpp" />
    <ClCompile Include="source\j_AddCGuildBattleReservedScheduleMapGroupGUILD_BAT_14000BC5D.cpp" />
    <ClCompile Include="source\j_AddCGuildBattleScheduleManagerGUILD_BATTLEQEAAEI_14000774D.cpp" />
    <ClCompile Include="source\j_AddCNormalGuildBattleManagerGUILD_BATTLEQEAAEPEA_1400042EB.cpp" />
    <ClCompile Include="source\j_AddCompleteCGuildBattleControllerQEAAXEIKKZ_14000FB91.cpp" />
    <ClCompile Include="source\j_AddCompleteCNormalGuildBattleGUILD_BATTLEQEAAXEZ_14001316F.cpp" />
    <ClCompile Include="source\j_AddCompleteCNormalGuildBattleManagerGUILD_BATTLE_140003477.cpp" />
    <ClCompile Include="source\j_AddDefaultDBRecordCNormalGuildBattleManagerGUILD_140013B5B.cpp" />
    <ClCompile Include="source\j_AddDefaultDBTableCGuildBattleScheduleManagerGUIL_1400086F2.cpp" />
    <ClCompile Include="source\j_AddGoldCntCNormalGuildBattleGuildMemberGUILD_BAT_140001898.cpp" />
    <ClCompile Include="source\j_AddGuildBattleSchduleCMainThreadQEAAXPEAU_DB_QRY_140009BF6.cpp" />
    <ClCompile Include="source\j_AddKillCntCNormalGuildBattleGuildMemberGUILD_BAT_14000B249.cpp" />
    <ClCompile Include="source\j_AddScheduleCGuildBattleControllerQEAAEPEADZ_140007D47.cpp" />
    <ClCompile Include="source\j_AdvanceCGuildBattleStateListGUILD_BATTLEIEAAXHZ_140009665.cpp" />
    <ClCompile Include="source\j_AdvanceRegenStateCNormalGuildBattleStateInBattle_1400026E4.cpp" />
    <ClCompile Include="source\j_AdvanceRegenStateCNormalGuildBattleStateListGUIL_14000F628.cpp" />
    <ClCompile Include="source\j_allocateallocatorVCGuildBattleRewardItemGUILD_BA_140009ECB.cpp" />
    <ClCompile Include="source\j_AreaDamageProcCAttackQEAAXHHPEAMH_NZ_140006B09.cpp" />
    <ClCompile Include="source\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEIEAAXH_14000ED54.cpp" />
    <ClCompile Include="source\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXH_14001347B.cpp" />
    <ClCompile Include="source\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_1400119C3.cpp" />
    <ClCompile Include="source\j_AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_14000EA02.cpp" />
    <ClCompile Include="source\j_AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXXZ_14000FB6E.cpp" />
    <ClCompile Include="source\j_assignvectorVCGuildBattleRewardItemGUILD_BATTLEV_14000E3DB.cpp" />
    <ClCompile Include="source\j_AttackableHeightCAnimusUEAAHXZ_140010000.cpp" />
    <ClCompile Include="source\j_AttackableHeightCGameObjectUEAAHXZ_14000B3E3.cpp" />
    <ClCompile Include="source\j_AttackableHeightCGuardTowerUEAAHXZ_14000AF4C.cpp" />
    <ClCompile Include="source\j_AttackableHeightCMonsterUEAAHXZ_1400048E0.cpp" />
    <ClCompile Include="source\j_AttackableHeightCTrapUEAAHXZ_14000C5B3.cpp" />
    <ClCompile Include="source\j_AttackCAnimusQEAA_NKZ_140001C94.cpp" />
    <ClCompile Include="source\j_AttackCNuclearBombQEAAXHHZ_14000F88F.cpp" />
    <ClCompile Include="source\j_AttackForceCAttackQEAAXPEAU_attack_param_NZ_14001098D.cpp" />
    <ClCompile Include="source\j_AttackForceRequestCNetworkEXAEAA_NHPEADZ_1400067B7.cpp" />
    <ClCompile Include="source\j_AttackGenCAttackQEAAXPEAU_attack_param_N1Z_140013714.cpp" />
    <ClCompile Include="source\j_AttackMonsterForceCMonsterAttackQEAAXPEAU_attack_14000AF65.cpp" />
    <ClCompile Include="source\j_AttackMonsterGenCMonsterAttackQEAAXPEAU_attack_p_14000999E.cpp" />
    <ClCompile Include="source\j_AttackObjectCMonsterQEAAHHPEAVCGameObjectZ_140008F58.cpp" />
    <ClCompile Include="source\j_AttackPersonalRequestCNetworkEXAEAA_NHPEADZ_14000EEA3.cpp" />
    <ClCompile Include="source\j_AttackSiegeRequestCNetworkEXAEAA_NHPEADZ_14000510F.cpp" />
    <ClCompile Include="source\j_AttackTestRequestCNetworkEXAEAA_NHPEADZ_1400111E4.cpp" />
    <ClCompile Include="source\j_AttackUnitRequestCNetworkEXAEAA_NHPEADZ_14000B609.cpp" />
    <ClCompile Include="source\j_AvectorVCGuildBattleRewardItemGUILD_BATTLEValloc_14000E36D.cpp" />
    <ClCompile Include="source\j_beginvectorVCGuildBattleRewardItemGUILD_BATTLEVa_140013AC0.cpp" />
    <ClCompile Include="source\j_BuyByCashCashItemRemoteStoreAEAA_NGPEADZ_140013D81.cpp" />
    <ClCompile Include="source\j_BuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_140001078.cpp" />
    <ClCompile Include="source\j_BuyCashItemRemoteStoreQEAA_NGPEADZ_140006EE2.cpp" />
    <ClCompile Include="source\j_BuyLimSaleCashItemRemoteStoreQEAAGEKZ_140013737.cpp" />
    <ClCompile Include="source\j_buy_to_inven_cashitemCMgrAvatorItemHistoryQEAAXE_140011126.cpp" />
    <ClCompile Include="source\j_CalcAvgDamageCAttackQEAAXXZ_1400123A0.cpp" />
    <ClCompile Include="source\j_CallProc_InsertCashItemLogCRFCashItemDatabaseQEA_140007E0A.cpp" />
    <ClCompile Include="source\j_CallProc_RFOnlineAvg_EventCRFCashItemDatabaseQEA_140013FD9.cpp" />
    <ClCompile Include="source\j_CallProc_RFOnlineUseCRFCashItemDatabaseQEAAHAEAU_140008DAA.cpp" />
    <ClCompile Include="source\j_CallProc_RFOnlineUse_JapCRFCashItemDatabaseQEAAH_140013FB1.cpp" />
    <ClCompile Include="source\j_CallProc_RFONLINE_CancelCRFCashItemDatabaseQEAAH_140004A61.cpp" />
    <ClCompile Include="source\j_CallProc_RFONLINE_Cancel_JapCRFCashItemDatabaseQ_140008067.cpp" />
    <ClCompile Include="source\j_CancelSuggestedMatter_guild_battle_suggest_matte_14001192D.cpp" />
    <ClCompile Include="source\j_capacityvectorVCGuildBattleRewardItemGUILD_BATTL_140010884.cpp" />
    <ClCompile Include="source\j_cashitem_del_from_invenCMgrAvatorItemHistoryQEAA_14000DF35.cpp" />
    <ClCompile Include="source\j_ChangeDiscountEventTimeCashItemRemoteStoreQEAA_N_1400104CE.cpp" />
    <ClCompile Include="source\j_ChangeEventTimeCashItemRemoteStoreQEAA_NEZ_1400049DA.cpp" />
    <ClCompile Include="source\j_Change_Conditional_Event_StatusCashItemRemoteSto_14000F8E9.cpp" />
    <ClCompile Include="source\j_CheatBuyCashItemRemoteStoreQEAA_NGPEBDHZ_140008FD5.cpp" />
    <ClCompile Include="source\j_CheatDestroyStoneCNormalGuildBattleFieldGUILD_BA_14000BFB4.cpp" />
    <ClCompile Include="source\j_CheatLoadCashAmountCashItemRemoteStoreQEAA_NGHZ_1400047AA.cpp" />
    <ClCompile Include="source\j_CheatRegenStoneCNormalGuildBattleFieldGUILD_BATT_140002FE0.cpp" />
    <ClCompile Include="source\j_CheckAttackCHolyKeeperQEAA_NXZ_140004FAC.cpp" />
    <ClCompile Include="source\j_CheckBallTakeLimitTimeCNormalGuildBattleFieldGUI_14000B672.cpp" />
    <ClCompile Include="source\j_CheckCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_14000A1E6.cpp" />
    <ClCompile Include="source\j_CheckGetGravityStoneCNormalGuildBattleManagerGUI_14000BF50.cpp" />
    <ClCompile Include="source\j_CheckGoalCNormalGuildBattleManagerGUILD_BATTLEQE_14000DBC0.cpp" />
    <ClCompile Include="source\j_CheckGuildBattleLimitCAttackIEAA_NPEAVCGameObjec_140005E20.cpp" />
    <ClCompile Include="source\j_CheckGuildBattleSuggestRequestToDestGuildCGuildQ_14000927D.cpp" />
    <ClCompile Include="source\j_CheckIsInTownCNormalGuildBattleFieldGUILD_BATTLE_1400060A0.cpp" />
    <ClCompile Include="source\j_CheckLoopCGuildBattleStateListGUILD_BATTLEIEAAHX_140008DB9.cpp" />
    <ClCompile Include="source\j_CheckNextEventCGuildBattleReservedScheduleGUILD__14000AA0B.cpp" />
    <ClCompile Include="source\j_CheckRecordCGuildBattleRankManagerGUILD_BATTLEIE_140001712.cpp" />
    <ClCompile Include="source\j_CheckTakeGravityStoneCNormalGuildBattleManagerGU_14000B1A4.cpp" />
    <ClCompile Include="source\j_Check_CashEvent_INICashItemRemoteStoreQEAA_NEZ_1400016FE.cpp" />
    <ClCompile Include="source\j_Check_CashEvent_StatusCashItemRemoteStoreQEAAXEZ_140005F9C.cpp" />
    <ClCompile Include="source\j_check_cash_discount_iniCashItemRemoteStoreQEAAXX_14000C234.cpp" />
    <ClCompile Include="source\j_check_cash_discount_statusCashItemRemoteStoreQEA_1400110B3.cpp" />
    <ClCompile Include="source\j_Check_Conditional_Event_INICashItemRemoteStoreQE_1400088DC.cpp" />
    <ClCompile Include="source\j_Check_Conditional_Event_StatusCashItemRemoteStor_14000B4FB.cpp" />
    <ClCompile Include="source\j_Check_GrosssalesCashItemRemoteStoreQEAAXKZ_140002B7B.cpp" />
    <ClCompile Include="source\j_check_loaded_cde_statusCashItemRemoteStoreQEAAXX_14000F6AF.cpp" />
    <ClCompile Include="source\j_Check_Loaded_Event_StatusCashItemRemoteStoreQEAA_14000F1D2.cpp" />
    <ClCompile Include="source\j_Check_Total_SellingCashItemRemoteStoreQEAAXXZ_14000AB50.cpp" />
    <ClCompile Include="source\j_CleanUpBattleCNormalGuildBattleGuildGUILD_BATTLE_140011671.cpp" />
    <ClCompile Include="source\j_CleanUpBattleCNormalGuildBattleGuildMemberGUILD__140013CA0.cpp" />
    <ClCompile Include="source\j_CleanUpBattleCNormalGuildBattleGUILD_BATTLEQEAAX_1400080BC.cpp" />
    <ClCompile Include="source\j_CleanUpCCurrentGuildBattleInfoManagerGUILD_BATTL_140003571.cpp" />
    <ClCompile Include="source\j_CleanUpCGuildBattleControllerIEAAXXZ_14000FDF3.cpp" />
    <ClCompile Include="source\j_CleanUpCGuildBattleRankManagerGUILD_BATTLEIEAAXX_14000A835.cpp" />
    <ClCompile Include="source\j_CleanUpDanglingReservedScheduleCGuildBattleReser_140002F86.cpp" />
    <ClCompile Include="source\j_CleanUpDanglingReservedScheduleCGuildBattleReser_1400039AE.cpp" />
    <ClCompile Include="source\j_CleanUpDanglingReservedScheduleCGuildBattleSched_140013D68.cpp" />
    <ClCompile Include="source\j_ClearAllCGuildBattleSchedulePoolGUILD_BATTLEQEAA_14000B343.cpp" />
    <ClCompile Include="source\j_ClearBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_14000F6AA.cpp" />
    <ClCompile Include="source\j_ClearByDayIDCGuildBattleSchedulePoolGUILD_BATTLE_14000DA9E.cpp" />
    <ClCompile Include="source\j_ClearCCurrentGuildBattleInfoManagerGUILD_BATTLEQ_140004C23.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleControllerQEAAXXZ_14000ACE5.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleRankManagerGUILD_BATTLEIEAAXEZ_140012440.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleRankManagerGUILD_BATTLEQEAAXXZ_14000C67B.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleReservedScheduleGUILD_BATTLEQEA_140008A35.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleReservedScheduleGUILD_BATTLEQEA_140012742.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleReservedScheduleListManagerGUIL_140010CE9.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleReservedScheduleMapGroupGUILD_B_140007036.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleReservedScheduleMapGroupGUILD_B_1400128A5.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleScheduleGUILD_BATTLEQEAAXXZ_140006573.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleScheduleManagerGUILD_BATTLEAEAA_140012B02.cpp" />
    <ClCompile Include="source\j_ClearCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1400016F9.cpp" />
    <ClCompile Include="source\j_ClearCNormalGuildBattleGuildGUILD_BATTLEQEAAXXZ_14000289C.cpp" />
    <ClCompile Include="source\j_ClearCNormalGuildBattleGuildMemberGUILD_BATTLEQE_14000AA4C.cpp" />
    <ClCompile Include="source\j_ClearCNormalGuildBattleGUILD_BATTLEQEAAXXZ_14000DC1F.cpp" />
    <ClCompile Include="source\j_ClearCNormalGuildBattleManagerGUILD_BATTLEIEAAXP_14000BB1D.cpp" />
    <ClCompile Include="source\j_ClearCNormalGuildBattleManagerGUILD_BATTLEQEAAXX_14000D76F.cpp" />
    <ClCompile Include="source\j_ClearCNormalGuildBattleStateListPoolGUILD_BATTLE_140011216.cpp" />
    <ClCompile Include="source\j_ClearCPossibleBattleGuildListManagerGUILD_BATTLE_140007464.cpp" />
    <ClCompile Include="source\j_ClearCReservedGuildScheduleDayGroupGUILD_BATTLEQ_1400090A2.cpp" />
    <ClCompile Include="source\j_ClearCReservedGuildScheduleMapGroupGUILD_BATTLEQ_14000D062.cpp" />
    <ClCompile Include="source\j_ClearCReservedGuildSchedulePageGUILD_BATTLEQEAA__140003C92.cpp" />
    <ClCompile Include="source\j_ClearDBCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_140003BC5.cpp" />
    <ClCompile Include="source\j_ClearDBRecordCNormalGuildBattleGUILD_BATTLEQEAA__140008ABC.cpp" />
    <ClCompile Include="source\j_ClearElapsedScheduleCGuildBattleReservedSchedule_140009E71.cpp" />
    <ClCompile Include="source\j_ClearGuildBattleCGuildQEAAXXZ_140008D87.cpp" />
    <ClCompile Include="source\j_ClearInBattleStateCNormalGuildBattleGuildGUILD_B_14000693D.cpp" />
    <ClCompile Include="source\j_ClearRegenCNormalGuildBattleFieldGUILD_BATTLEQEA_140002E96.cpp" />
    <ClCompile Include="source\j_ClearTommorowScheduleByIDCGuildBattleScheduleMan_14000427D.cpp" />
    <ClCompile Include="source\j_Clear_guild_battle_suggest_matterQEAAXXZ_14000AFDD.cpp" />
    <ClCompile Include="source\j_CompleteClearGuildBattleRankCGuildBattleControll_1400139D0.cpp" />
    <ClCompile Include="source\j_CompleteCreateGuildBattleRankTableCGuildBattleCo_140012355.cpp" />
    <ClCompile Include="source\j_CompleteLoadGuildBattleTotalRecordCMainThreadAEA_140011158.cpp" />
    <ClCompile Include="source\j_CompleteOutGuildbattleCostCGuildQEAAXKKKKZ_14000A59C.cpp" />
    <ClCompile Include="source\j_CompleteUpdateRankCGuildBattleControllerQEAAXEEP_140006A0A.cpp" />
    <ClCompile Include="source\j_CompleteUpdateReservedScheduleCGuildBattleContro_14001106D.cpp" />
    <ClCompile Include="source\j_constructallocatorVCGuildBattleRewardItemGUILD_B_140011EA0.cpp" />
    <ClCompile Include="source\j_CopyUseTimeFieldCGuildBattleReservedScheduleGUIL_140001140.cpp" />
    <ClCompile Include="source\j_CopyUseTimeFieldCGuildBattleReservedScheduleMapG_1400047E6.cpp" />
    <ClCompile Include="source\j_CreateFieldObjectCNormalGuildBattleFieldGUILD_BA_140004EEE.cpp" />
    <ClCompile Include="source\j_CreateGuildBattleRankTableCRFWorldDatabaseQEAA_N_140012512.cpp" />
    <ClCompile Include="source\j_CreateLogFileCGuildBattleLoggerGUILD_BATTLEQEAAX_1400118D3.cpp" />
    <ClCompile Include="source\j_CreateLogFileCNormalGuildBattleGUILD_BATTLEQEAAX_140011C7F.cpp" />
    <ClCompile Include="source\j_CreateLogFileCNormalGuildBattleLoggerGUILD_BATTL_14000E237.cpp" />
    <ClCompile Include="source\j_CreateLoggerCNormalGuildBattleGUILD_BATTLEQEAA_N_14000D29C.cpp" />
    <ClCompile Include="source\j_ct_StopBattleCHolyStoneSystemQEAA_NXZ_14000812F.cpp" />
    <ClCompile Include="source\j_deallocateallocatorVCGuildBattleRewardItemGUILD__14000BC21.cpp" />
    <ClCompile Include="source\j_DecideColorInxCNormalGuildBattleGUILD_BATTLEQEAA_14000DACB.cpp" />
    <ClCompile Include="source\j_DecideWinCNormalGuildBattleGUILD_BATTLEIEAAEXZ_140003148.cpp" />
    <ClCompile Include="source\j_DecPvpPointCNormalGuildBattleGuildGUILD_BATTLEQE_140009D3B.cpp" />
    <ClCompile Include="source\j_DecPvpPointCNormalGuildBattleGuildMemberGUILD_BA_140010375.cpp" />
    <ClCompile Include="source\j_DeleteGuildBattleInfoCRFWorldDatabaseQEAA_NXZ_14000EBCE.cpp" />
    <ClCompile Include="source\j_DeleteGuildBattleScheduleInfoCRFWorldDatabaseQEA_140009B74.cpp" />
    <ClCompile Include="source\j_DestGuildIsAvailableBattleRequestStateCGuildQEAA_140012472.cpp" />
    <ClCompile Include="source\j_destroyallocatorVCGuildBattleRewardItemGUILD_BAT_140013174.cpp" />
    <ClCompile Include="source\j_DestroyCCurrentGuildBattleInfoManagerGUILD_BATTL_140008F35.cpp" />
    <ClCompile Include="source\j_DestroyCGuildBattleControllerSAXXZ_140003EEF.cpp" />
    <ClCompile Include="source\j_DestroyCGuildBattleLoggerGUILD_BATTLESAXXZ_140005EC0.cpp" />
    <ClCompile Include="source\j_DestroyCGuildBattleRankManagerGUILD_BATTLESAXXZ_14000B668.cpp" />
    <ClCompile Include="source\j_DestroyCGuildBattleReservedScheduleListManagerGU_14000DC65.cpp" />
    <ClCompile Include="source\j_DestroyCGuildBattleScheduleManagerGUILD_BATTLESA_14000BBA4.cpp" />
    <ClCompile Include="source\j_DestroyCGuildBattleSchedulePoolGUILD_BATTLESAXXZ_140010CB7.cpp" />
    <ClCompile Include="source\j_DestroyCGuildBattleSchedulerGUILD_BATTLESAXXZ_140011905.cpp" />
    <ClCompile Include="source\j_DestroyCNormalGuildBattleFieldGUILD_BATTLEAEAAXX_140008F62.cpp" />
    <ClCompile Include="source\j_DestroyCNormalGuildBattleFieldListGUILD_BATTLESA_1400083F5.cpp" />
    <ClCompile Include="source\j_DestroyCNormalGuildBattleManagerGUILD_BATTLESAXX_14000361B.cpp" />
    <ClCompile Include="source\j_DestroyCNormalGuildBattleStateListPoolGUILD_BATT_1400010FA.cpp" />
    <ClCompile Include="source\j_DestroyCPossibleBattleGuildListManagerGUILD_BATT_14000D41D.cpp" />
    <ClCompile Include="source\j_DestroyFieldObjectCNormalGuildBattleFieldGUILD_B_140011BBC.cpp" />
    <ClCompile Include="source\j_dhRExtractSubStringCRFCashItemDatabaseQEAAXPEAD0_1400107C1.cpp" />
    <ClCompile Include="source\j_DividePvpPointCNormalGuildBattleGUILD_BATTLEQEAA_14000305D.cpp" />
    <ClCompile Include="source\j_DoDayChangedWorkCNormalGuildBattleManagerGUILD_B_140008CBA.cpp" />
    <ClCompile Include="source\j_DoDayChangedWorkCPossibleBattleGuildListManagerG_140006302.cpp" />
    <ClCompile Include="source\j_DropGravityStoneCNormalGuildBattleGUILD_BATTLEQE_140006BF9.cpp" />
    <ClCompile Include="source\j_DropGravityStoneCNormalGuildBattleManagerGUILD_B_140011090.cpp" />
    <ClCompile Include="source\j_endvectorVCGuildBattleRewardItemGUILD_BATTLEVall_140004642.cpp" />
    <ClCompile Include="source\j_EnterCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuil_1400117C5.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateCountDownGUILD_BATTL_140010BF4.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateGUILD_BATTLEMEAAHPEA_140005FC9.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateGUILD_BATTLEUEAAHPEA_14000DC1A.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateInBattleGUILD_BATTLE_140008869.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateNotifyGUILD_BATTLEME_140012B34.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateReadyGUILD_BATTLEMEA_140005A10.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateRoundGUILD_BATTLEMEA_14000F25E.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateRoundGUILD_BATTLEUEA_140009043.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateRoundProcessGUILD_BA_14000E7FF.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateRoundReturnStartPosG_14000FD0D.cpp" />
    <ClCompile Include="source\j_EnterCNormalGuildBattleStateRoundStartGUILD_BATT_14001307F.cpp" />
    <ClCompile Include="source\j_erasevectorVCGuildBattleRewardItemGUILD_BATTLEVa_14000BD70.cpp" />
    <ClCompile Include="source\j_fillPEAVCGuildBattleRewardItemGUILD_BATTLEV12std_140009642.cpp" />
    <ClCompile Include="source\j_fill_eff_list_ATTACK_DELAY_CHECKERQEAA_NXZ_140013B60.cpp" />
    <ClCompile Include="source\j_fill_mas_list_ATTACK_DELAY_CHECKERQEAA_NXZ_1400088D7.cpp" />
    <ClCompile Include="source\j_FinCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildB_1400062A8.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateDivideGUILD_BATTLEMEAA_140009E26.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateFinGUILD_BATTLEMEAAHPE_140010CF3.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVC_14000B32F.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVC_1400084E0.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateInBattleGUILD_BATTLEME_14000F4E3.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateReturnGUILD_BATTLEMEAA_1400056CD.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateRoundGUILD_BATTLEMEAAH_1400031D9.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateRoundGUILD_BATTLEUEAAH_140011CA2.cpp" />
    <ClCompile Include="source\j_FinCNormalGuildBattleStateRoundStartGUILD_BATTLE_1400139E9.cpp" />
    <ClCompile Include="source\j_FindCashRecCashItemRemoteStoreSAPEBU_CashShop_fl_1400070D1.cpp" />
    <ClCompile Include="source\j_FindCGuildBattleRankManagerGUILD_BATTLEIEAA_NEKA_140005326.cpp" />
    <ClCompile Include="source\j_FindCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000CBB2.cpp" />
    <ClCompile Include="source\j_FindCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000E4DF.cpp" />
    <ClCompile Include="source\j_FindCReservedGuildSchedulePageGUILD_BATTLEQEAA_N_14000C662.cpp" />
    <ClCompile Include="source\j_FlashDamageProcCAttackIEAAXHHHH_NZ_14000B186.cpp" />
    <ClCompile Include="source\j_FlipCGuildBattleControllerQEAAXXZ_140007798.cpp" />
    <ClCompile Include="source\j_FlipCGuildBattleReservedScheduleGUILD_BATTLEQEAA_14000249B.cpp" />
    <ClCompile Include="source\j_FlipCGuildBattleReservedScheduleListManagerGUILD_140002432.cpp" />
    <ClCompile Include="source\j_FlipCGuildBattleReservedScheduleMapGroupGUILD_BA_14000B069.cpp" />
    <ClCompile Include="source\j_FlipCGuildBattleScheduleManagerGUILD_BATTLEAEAAX_140010546.cpp" />
    <ClCompile Include="source\j_FlipCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_140003094.cpp" />
    <ClCompile Include="source\j_FlipCReservedGuildScheduleDayGroupGUILD_BATTLEQE_140008AA8.cpp" />
    <ClCompile Include="source\j_FlipCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000FFDD.cpp" />
    <ClCompile Include="source\j_FlipCReservedGuildSchedulePageGUILD_BATTLEQEAAXX_140005DE9.cpp" />
    <ClCompile Include="source\j_ForceNextCGuildBattleStateListGUILD_BATTLEIEAAXX_14001065E.cpp" />
    <ClCompile Include="source\j_force_endup_cash_discount_eventCashItemRemoteSto_1400070D6.cpp" />
    <ClCompile Include="source\j_Get1PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_140006037.cpp" />
    <ClCompile Include="source\j_Get2PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_1400134B2.cpp" />
    <ClCompile Include="source\j_GetAmountCGuildBattleRewardItemGUILD_BATTLEQEBAE_140007B8F.cpp" />
    <ClCompile Include="source\j_GetANSIGuildNameCNormalGuildBattleGuildGUILD_BAT_14000A303.cpp" />
    <ClCompile Include="source\j_GetAttackDelay_WEAPON_PARAMQEAAKHHZ_140008F3A.cpp" />
    <ClCompile Include="source\j_GetAttackDPCAnimusUEAAHXZ_14000E417.cpp" />
    <ClCompile Include="source\j_GetAttackDPCGameObjectUEAAHXZ_1400026C6.cpp" />
    <ClCompile Include="source\j_GetAttackDPCGuardTowerUEAAHXZ_14000A8F3.cpp" />
    <ClCompile Include="source\j_GetAttackDPCHolyKeeperUEAAHXZ_140009F20.cpp" />
    <ClCompile Include="source\j_GetAttackDPCHolyStoneUEAAHXZ_140007C11.cpp" />
    <ClCompile Include="source\j_GetAttackDPCMonsterUEAAHXZ_140011725.cpp" />
    <ClCompile Include="source\j_GetAttackDPCTrapUEAAHXZ_1400110F9.cpp" />
    <ClCompile Include="source\j_GetAttackPartCAnimusQEAAHXZ_140006D57.cpp" />
    <ClCompile Include="source\j_GetAttackPartCMonsterQEAAHXZ_140005BD2.cpp" />
    <ClCompile Include="source\j_GetAttackPivotCHolyKeeperQEAAPEAMXZ_14000B63B.cpp" />
    <ClCompile Include="source\j_GetAttackRangeCAnimusUEAAMXZ_1400091E2.cpp" />
    <ClCompile Include="source\j_GetAttackRangeCGameObjectUEAAMXZ_140010334.cpp" />
    <ClCompile Include="source\j_GetAttackRangeCGuardTowerUEAAMXZ_140013642.cpp" />
    <ClCompile Include="source\j_GetAttackRangeCHolyKeeperUEAAMXZ_14000E34F.cpp" />
    <ClCompile Include="source\j_GetAttackRangeCMonsterUEAAMXZ_140008BF7.cpp" />
    <ClCompile Include="source\j_GetAttackRangeCTrapUEAAMXZ_140004462.cpp" />
    <ClCompile Include="source\j_GetAttackToolType_WEAPON_PARAMQEAAHXZ_14000F047.cpp" />
    <ClCompile Include="source\j_GetBattleByGuildSerialCNormalGuildBattleManagerG_140013101.cpp" />
    <ClCompile Include="source\j_GetBattleCNormalGuildBattleManagerGUILD_BATTLEIE_14000A4E8.cpp" />
    <ClCompile Include="source\j_GetBattleModeTimeCMonsterAIQEAAKXZ_140002801.cpp" />
    <ClCompile Include="source\j_GetBattleTimeCGuildBattleScheduleGUILD_BATTLEQEA_14000DB7F.cpp" />
    <ClCompile Include="source\j_GetBattleTurmCGuildBattleScheduleGUILD_BATTLEQEA_140010A3C.cpp" />
    <ClCompile Include="source\j_GetBlueCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNo_1400129EF.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataBRUEAAHPEAU_Ca_14000F547.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataCNUEAAHPEAU_Ca_1400135E8.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataESUEAAHPEAU_Ca_1400070A4.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataGBUEAAHPEAU_Ca_140009B51.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataIDUEAAHPEAU_Ca_1400099DA.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataJPUEAAHPEAU_Ca_14000A358.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataKRUEAAHPEAU_Ca_140009F8E.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataNULLUEAAHPEAU__140005FB0.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataPHUEAAHPEAU_Ca_1400076AD.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataRUUEAAHPEAU_Ca_140009E44.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataTHUEAAHPEAU_Ca_140010A05.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataTWUEAAHPEAU_Ca_1400062CB.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataUEAAHPEAU_Cash_14000D92C.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingDataUSUEAAHPEAU_Ca_140001352.cpp" />
    <ClCompile Include="source\j_GetCashItemPriceCNationSettingManagerQEAAHPEAU_C_1400088B9.cpp" />
    <ClCompile Include="source\j_GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVC_1400077B6.cpp" />
    <ClCompile Include="source\j_GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVC_140012AD0.cpp" />
    <ClCompile Include="source\j_GetCircleZoneCGuildBattleControllerQEAAPEAVCGame_1400020B3.cpp" />
    <ClCompile Include="source\j_GetCircleZoneCNormalGuildBattleFieldGUILD_BATTLE_140009AE3.cpp" />
    <ClCompile Include="source\j_GetCircleZoneCNormalGuildBattleFieldListGUILD_BA_140010B72.cpp" />
    <ClCompile Include="source\j_GetCNormalGuildBattleStateListPoolGUILD_BATTLEQE_14001139C.cpp" />
    <ClCompile Include="source\j_GetColorInxCNormalGuildBattleGuildGUILD_BATTLEQE_140006EAB.cpp" />
    <ClCompile Include="source\j_GetColorNameCNormalGuildBattleGuildGUILD_BATTLEQ_1400011A4.cpp" />
    <ClCompile Include="source\j_GetCombatStateCMonsterQEAAEXZ_140008C4C.cpp" />
    <ClCompile Include="source\j_GetCurScheduleIDCGuildBattleReservedScheduleGUIL_14000675D.cpp" />
    <ClCompile Include="source\j_GetCurScheduleIDCGuildBattleReservedScheduleMapG_140006EB0.cpp" />
    <ClCompile Include="source\j_GetCurScheduleIDCGuildBattleScheduleManagerGUILD_14000C6BC.cpp" />
    <ClCompile Include="source\j_GetDamagedObjNumCNuclearBombQEAAHXZ_14000ABB9.cpp" />
    <ClCompile Include="source\j_GetDayIDCGuildBattleReservedScheduleMapGroupGUIL_140001B6D.cpp" />
    <ClCompile Include="source\j_GetEmptyMemberCNormalGuildBattleGuildGUILD_BATTL_140009D90.cpp" />
    <ClCompile Include="source\j_GetEvnetTimeCashItemRemoteStoreQEAAXPEAU_cash_ev_14000C423.cpp" />
    <ClCompile Include="source\j_GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQ_1400038F5.cpp" />
    <ClCompile Include="source\j_GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQ_14000A42A.cpp" />
    <ClCompile Include="source\j_GetFieldCNormalGuildBattleGUILD_BATTLEQEAAPEAVCN_14000F5DD.cpp" />
    <ClCompile Include="source\j_GetFirstMapFieldByRaceCNormalGuildBattleFieldLis_14000D2BF.cpp" />
    <ClCompile Include="source\j_GetFirstMapInxByRaceCNormalGuildBattleFieldListG_14000B2E9.cpp" />
    <ClCompile Include="source\j_GetGoalCntCNormalGuildBattleGuildGUILD_BATTLEQEA_140011CED.cpp" />
    <ClCompile Include="source\j_GetGoalCountCNormalGuildBattleGuildMemberGUILD_B_140008D8C.cpp" />
    <ClCompile Include="source\j_GetGravityStoneCNormalGuildBattleGUILD_BATTLEQEA_140013C0F.cpp" />
    <ClCompile Include="source\j_GetGuildBattleNumberCNormalGuildBattleGUILD_BATT_14000BAC3.cpp" />
    <ClCompile Include="source\j_GetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAP_1400048A4.cpp" />
    <ClCompile Include="source\j_GetGuildCNormalGuildBattleGUILD_BATTLEQEAAPEAVCN_140003120.cpp" />
    <ClCompile Include="source\j_GetGuildNameCNormalGuildBattleGuildGUILD_BATTLEQ_14000BD1B.cpp" />
    <ClCompile Include="source\j_GetGuildRaceCNormalGuildBattleGuildGUILD_BATTLEQ_14000C937.cpp" />
    <ClCompile Include="source\j_GetGuildSerialCNormalGuildBattleGuildGUILD_BATTL_140005560.cpp" />
    <ClCompile Include="source\j_GetIDCGuildBattleReservedScheduleGUILD_BATTLEQEA_1400069F6.cpp" />
    <ClCompile Include="source\j_GetIDCNormalGuildBattleGUILD_BATTLEQEAAKXZ_140008175.cpp" />
    <ClCompile Include="source\j_GetIndexCNormalGuildBattleGuildMemberGUILD_BATTL_14000DCE2.cpp" />
    <ClCompile Include="source\j_GetInfoCNormalGuildBattleGUILD_BATTLEQEAA_NAEAU__14000ECC3.cpp" />
    <ClCompile Include="source\j_GetItemCodeCGuildBattleRewardItemGUILD_BATTLEQEB_1400038AF.cpp" />
    <ClCompile Include="source\j_GetJoinMemberCntCNormalGuildBattleGuildGUILD_BAT_1400037BA.cpp" />
    <ClCompile Include="source\j_GetKillCountCNormalGuildBattleGuildMemberGUILD_B_14000F0D3.cpp" />
    <ClCompile Include="source\j_GetKillCountSumCNormalGuildBattleGuildGUILD_BATT_14000D260.cpp" />
    <ClCompile Include="source\j_GetLeftTimeCCurrentGuildBattleInfoManagerGUILD_B_140009E8F.cpp" />
    <ClCompile Include="source\j_GetLeftTimeCGuildBattleScheduleGUILD_BATTLEQEAA__14000B843.cpp" />
    <ClCompile Include="source\j_GetLimDiscoutCashItemRemoteStoreQEAAEXZ_140005D8F.cpp" />
    <ClCompile Include="source\j_GetLoggerCNormalGuildBattleGUILD_BATTLEQEAAPEAVC_140004ACF.cpp" />
    <ClCompile Include="source\j_GetMapCNormalGuildBattleFieldGUILD_BATTLEQEAAPEA_14000D972.cpp" />
    <ClCompile Include="source\j_GetMapCntCNormalGuildBattleFieldListGUILD_BATTLE_140011027.cpp" />
    <ClCompile Include="source\j_GetMapCodeCNormalGuildBattleFieldGUILD_BATTLEQEA_140007135.cpp" />
    <ClCompile Include="source\j_GetMapIDCNormalGuildBattleFieldGUILD_BATTLEQEAAK_14000877E.cpp" />
    <ClCompile Include="source\j_GetMapInxCNormalGuildBattleFieldListGUILD_BATTLE_14000FA2E.cpp" />
    <ClCompile Include="source\j_GetMapInxListCNormalGuildBattleFieldListGUILD_BA_14001089D.cpp" />
    <ClCompile Include="source\j_GetMapStrCodeCNormalGuildBattleFieldGUILD_BATTLE_140006005.cpp" />
    <ClCompile Include="source\j_GetMaxJoinMemberCountCNormalGuildBattleGuildGUIL_140006136.cpp" />
    <ClCompile Include="source\j_GetMaxPageCReservedGuildScheduleMapGroupGUILD_BA_14000E3F9.cpp" />
    <ClCompile Include="source\j_GetMemberCNormalGuildBattleGuildGUILD_BATTLEIEAA_1400057D1.cpp" />
    <ClCompile Include="source\j_GetMemberPtrCNormalGuildBattleGuildGUILD_BATTLEQ_14000B681.cpp" />
    <ClCompile Include="source\j_GetObjTypeCGuildBattleGUILD_BATTLEUEAAHXZ_1400086CF.cpp" />
    <ClCompile Include="source\j_GetObjTypeCNormalGuildBattleGUILD_BATTLEUEAAHXZ_140002608.cpp" />
    <ClCompile Include="source\j_GetPortalIndexInfoCNormalGuildBattleFieldGUILD_B_140009061.cpp" />
    <ClCompile Include="source\j_GetRealStartTimeCGuildBattleScheduleGUILD_BATTLE_1400070F4.cpp" />
    <ClCompile Include="source\j_GetRedCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_14000BADC.cpp" />
    <ClCompile Include="source\j_GetRefCGuildBattleSchedulePoolGUILD_BATTLEQEAAPE_14000E89F.cpp" />
    <ClCompile Include="source\j_GetRegenerCGuildBattleControllerQEAAPEAVCGameObj_14000D6F7.cpp" />
    <ClCompile Include="source\j_GetRegenerCNormalGuildBattleFieldGUILD_BATTLEQEA_14000EDE0.cpp" />
    <ClCompile Include="source\j_GetRegenerCNormalGuildBattleFieldListGUILD_BATTL_14000787E.cpp" />
    <ClCompile Include="source\j_GetRemainNumOfGoodCashItemRemoteStoreQEAAHGZ_140009228.cpp" />
    <ClCompile Include="source\j_GetRemainNumOfGoodCashItemRemoteStoreQEAAHQEADZ_1400100AF.cpp" />
    <ClCompile Include="source\j_GetScoreCNormalGuildBattleGuildGUILD_BATTLEQEAAK_140002883.cpp" />
    <ClCompile Include="source\j_GetSerialCNormalGuildBattleGuildMemberGUILD_BATT_14000FE98.cpp" />
    <ClCompile Include="source\j_GetSetDiscoutCashItemRemoteStoreQEAAEEZ_14000B54B.cpp" />
    <ClCompile Include="source\j_GetSIDCGuildBattleScheduleGUILD_BATTLEQEAAKXZ_14000522C.cpp" />
    <ClCompile Include="source\j_GetSIDCGuildBattleSchedulePoolGUILD_BATTLEQEAAKI_14000FAB0.cpp" />
    <ClCompile Include="source\j_GetSLIDCGuildBattleReservedScheduleMapGroupGUILD_1400064A6.cpp" />
    <ClCompile Include="source\j_GetStartBattleTickTimeCHolyStoneSystemQEAAKXZ_14000DC5B.cpp" />
    <ClCompile Include="source\j_GetStateCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1400057B3.cpp" />
    <ClCompile Include="source\j_GetStoneCGuildBattleControllerQEAAPEAVCGameObjec_140004DEF.cpp" />
    <ClCompile Include="source\j_GetStoneCNormalGuildBattleFieldGUILD_BATTLEQEAAP_140009D04.cpp" />
    <ClCompile Include="source\j_GetStoneCNormalGuildBattleFieldListGUILD_BATTLEQ_140006F1E.cpp" />
    <ClCompile Include="source\j_GetTermCGuildBattleStateGUILD_BATTLEUEAAAVCTimeS_140007C0C.cpp" />
    <ClCompile Include="source\j_GetTermCGuildBattleStateListGUILD_BATTLEQEAAAVCT_14000B0A5.cpp" />
    <ClCompile Include="source\j_GetTermCNormalGuildBattleStateCountDownGUILD_BAT_140003404.cpp" />
    <ClCompile Include="source\j_GetTermCNormalGuildBattleStateDivideGUILD_BATTLE_140012DFA.cpp" />
    <ClCompile Include="source\j_GetTermCNormalGuildBattleStateFinGUILD_BATTLEUEA_1400089AE.cpp" />
    <ClCompile Include="source\j_GetTermCNormalGuildBattleStateInBattleGUILD_BATT_14000EA2F.cpp" />
    <ClCompile Include="source\j_GetTermCNormalGuildBattleStateNotifyGUILD_BATTLE_14000EC8C.cpp" />
    <ClCompile Include="source\j_GetTermCNormalGuildBattleStateReadyGUILD_BATTLEU_140008BB1.cpp" />
    <ClCompile Include="source\j_GetTermCNormalGuildBattleStateReturnGUILD_BATTLE_1400025B8.cpp" />
    <ClCompile Include="source\j_GetTimeCGuildBattleScheduleGUILD_BATTLEQEAAAVCTi_140001FF5.cpp" />
    <ClCompile Include="source\j_GetTodayDayIDCGuildBattleScheduleManagerGUILD_BA_140011748.cpp" />
    <ClCompile Include="source\j_GetTodaySLIDByMapCGuildBattleScheduleManagerGUIL_14000294B.cpp" />
    <ClCompile Include="source\j_GetToleranceProbMonsterSFContDamageToleracneQEAA_140012E3B.cpp" />
    <ClCompile Include="source\j_GetTomorrowDayIDCGuildBattleScheduleManagerGUILD_14000FBF5.cpp" />
    <ClCompile Include="source\j_GetTomorrowSLIDByMapCGuildBattleScheduleManagerG_140006E15.cpp" />
    <ClCompile Include="source\j_GetTopGoalMemberCNormalGuildBattleGuildGUILD_BAT_1400118C9.cpp" />
    <ClCompile Include="source\j_GetTopKillMemberCNormalGuildBattleGuildGUILD_BAT_1400132B9.cpp" />
    <ClCompile Include="source\j_GetWinnerGradeCBattleTournamentInfoQEAAEKPEADZ_140012A49.cpp" />
    <ClCompile Include="source\j_Get_CashEvent_StatusCashItemRemoteStoreQEAAEEZ_14000346D.cpp" />
    <ClCompile Include="source\j_get_cde_statusCashItemRemoteStoreQEAAEXZ_14001028F.cpp" />
    <ClCompile Include="source\j_Get_Conditional_Event_NameCashItemRemoteStoreQEA_140008C7E.cpp" />
    <ClCompile Include="source\j_Get_Conditional_Event_StatusCashItemRemoteStoreQ_140004DC2.cpp" />
    <ClCompile Include="source\j_GoalCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEAV_14000BAA5.cpp" />
    <ClCompile Include="source\j_GoalCNormalGuildBattleGUILD_BATTLEQEAAEKHZ_14000214E.cpp" />
    <ClCompile Include="source\j_GoalCNormalGuildBattleStateGUILD_BATTLEUEAAXXZ_14000C117.cpp" />
    <ClCompile Include="source\j_GoodsListBuyByCashCashItemRemoteStoreAEAA_NGPEAD_140011252.cpp" />
    <ClCompile Include="source\j_GoodsListBuyByGoldCashItemRemoteStoreAEAA_NGPEAD_140010DE3.cpp" />
    <ClCompile Include="source\j_GoodsListCashItemRemoteStoreQEAA_NGPEADZ_140001A82.cpp" />
    <ClCompile Include="source\j_GotoCGuildBattleStateListGUILD_BATTLEQEAAHXZ_14000C63F.cpp" />
    <ClCompile Include="source\j_GotoStateCGuildBattleStateListGUILD_BATTLEQEAA_N_140013246.cpp" />
    <ClCompile Include="source\j_GuildBattleBlockReportCNetworkEXAEAA_NHPEADZ_140011856.cpp" />
    <ClCompile Include="source\j_GuildBattleCurrentBattleInfoRequestCNetworkEXAEA_14000C46E.cpp" />
    <ClCompile Include="source\j_GuildBattleGetGravityStoneRequestCNetworkEXAEAA__14000F989.cpp" />
    <ClCompile Include="source\j_GuildBattleGoalRequestCNetworkEXAEAA_NHPEADZ_140012FA8.cpp" />
    <ClCompile Include="source\j_GuildBattleJoinGuildBattleRequestCNetworkEXAEAA__140005867.cpp" />
    <ClCompile Include="source\j_GuildBattlePossibleGuildBattleListCNetworkEXAEAA_140005A06.cpp" />
    <ClCompile Include="source\j_GuildBattleRankListRequestCNetworkEXAEAA_NHPEADZ_140013BAB.cpp" />
    <ClCompile Include="source\j_GuildBattleReservedScheduleRequestCNetworkEXAEAA_14000E377.cpp" />
    <ClCompile Include="source\j_GuildBattleResultLogCNormalGuildBattleGUILD_BATT_14000E804.cpp" />
    <ClCompile Include="source\j_GuildBattleResultLogNotifyWebCNormalGuildBattleG_14000B83E.cpp" />
    <ClCompile Include="source\j_GuildBattleResultLogPushDBLogCNormalGuildBattleG_14000C80B.cpp" />
    <ClCompile Include="source\j_GuildBattleSuggestRequestToDestGuildCGuildQEAAEK_140011F59.cpp" />
    <ClCompile Include="source\j_GuildBattleTakeGravityStoneRequestCNetworkEXAEAA_140007F18.cpp" />
    <ClCompile Include="source\j_IncPvpPointCNormalGuildBattleGuildGUILD_BATTLEQE_14000D206.cpp" />
    <ClCompile Include="source\j_IncPvpPointCNormalGuildBattleGuildMemberGUILD_BA_14000E020.cpp" />
    <ClCompile Include="source\j_IncVerCReservedGuildSchedulePageGUILD_BATTLEQEAA_14000770C.cpp" />
    <ClCompile Include="source\j_inform_cashdiscount_eventCashItemRemoteStoreQEAA_140012B3E.cpp" />
    <ClCompile Include="source\j_inform_cashdiscount_status_allCashItemRemoteStor_14000B59B.cpp" />
    <ClCompile Include="source\j_Inform_CashEventCashItemRemoteStoreQEAAXGZ_140012A30.cpp" />
    <ClCompile Include="source\j_Inform_CashEvent_Status_AllCashItemRemoteStoreQE_14001369C.cpp" />
    <ClCompile Include="source\j_Inform_ConditionalEventCashItemRemoteStoreQEAAXG_14000FDA3.cpp" />
    <ClCompile Include="source\j_Inform_ConditionalEvent_Status_AllCashItemRemote_14000EC19.cpp" />
    <ClCompile Include="source\j_InGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_SYN_14000EE3A.cpp" />
    <ClCompile Include="source\j_InGuildbattleRewardMoneyCMainThreadQEAAXPEAU_DB__1400060CD.cpp" />
    <ClCompile Include="source\j_InitCBattleTournamentInfoQEAAXXZ_140005EE3.cpp" />
    <ClCompile Include="source\j_InitCCurrentGuildBattleInfoManagerGUILD_BATTLEQE_14000C4A0.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleControllerQEAA_NXZ_14000FD4E.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_140008341.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_14000A466.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleReservedScheduleListManagerGUILD_14000FEAC.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleReservedScheduleMapGroupGUILD_BA_140001186.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleRewardItemGUILD_BATTLEQEAA_NGZ_140010AE1.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleRewardItemManagerGUILD_BATTLEQEA_140012FBC.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleScheduleManagerGUILD_BATTLEQEAA__140003D73.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleSchedulePoolGUILD_BATTLEQEAA_NIZ_140012ACB.cpp" />
    <ClCompile Include="source\j_InitCGuildBattleSchedulerGUILD_BATTLEQEAA_NXZ_1400125EE.cpp" />
    <ClCompile Include="source\j_InitCNormalGuildBattleFieldGUILD_BATTLEQEAA_NIZ_140003D46.cpp" />
    <ClCompile Include="source\j_InitCNormalGuildBattleFieldListGUILD_BATTLEQEAA__140001A14.cpp" />
    <ClCompile Include="source\j_InitCNormalGuildBattleGUILD_BATTLEQEAAXPEAVCGuil_140010A37.cpp" />
    <ClCompile Include="source\j_InitCNormalGuildBattleGUILD_BATTLEQEAA_N_NIKKKKE_14000F100.cpp" />
    <ClCompile Include="source\j_InitCNormalGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_140004C55.cpp" />
    <ClCompile Include="source\j_InitCNormalGuildBattleManagerGUILD_BATTLEQEAA_NX_14000351C.cpp" />
    <ClCompile Include="source\j_InitCNormalGuildBattleStateListPoolGUILD_BATTLEQ_14000CB71.cpp" />
    <ClCompile Include="source\j_InitCPossibleBattleGuildListManagerGUILD_BATTLEQ_140008355.cpp" />
    <ClCompile Include="source\j_InitCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000B97E.cpp" />
    <ClCompile Include="source\j_InitCReservedGuildScheduleMapGroupGUILD_BATTLEQE_140003599.cpp" />
    <ClCompile Include="source\j_InitCReservedGuildSchedulePageGUILD_BATTLEQEAA_N_140008125.cpp" />
    <ClCompile Include="source\j_InitializeCashItemRemoteStoreQEAA_NXZ_14000D742.cpp" />
    <ClCompile Include="source\j_InitMonsterSFContDamageToleracneQEAAXMZ_1400035C6.cpp" />
    <ClCompile Include="source\j_InitUseFieldCNormalGuildBattleFieldListGUILD_BAT_14000AD80.cpp" />
    <ClCompile Include="source\j_Init_ATTACK_DELAY_CHECKERQEAAXXZ_140013DB3.cpp" />
    <ClCompile Include="source\j_init_eff_list_ATTACK_DELAY_CHECKERQEAAXXZ_14000FF29.cpp" />
    <ClCompile Include="source\j_init_mas_list_ATTACK_DELAY_CHECKERQEAAXXZ_140001E60.cpp" />
    <ClCompile Include="source\j_InsertGuildBattleDefaultRecordCRFWorldDatabaseQE_1400118B5.cpp" />
    <ClCompile Include="source\j_InsertGuildBattleRankRecordCRFWorldDatabaseQEAA__140010B9F.cpp" />
    <ClCompile Include="source\j_InsertGuildBattleScheduleDefaultRecordCRFWorldDa_140003012.cpp" />
    <ClCompile Include="source\j_insertvectorVCGuildBattleRewardItemGUILD_BATTLEV_1400126E3.cpp" />
    <ClCompile Include="source\j_Insert_PatrirchItemChargeRefundCRFWorldDatabaseQ_1400099F8.cpp" />
    <ClCompile Include="source\j_Insert_PatrirchItemChargeRefundPatriarchElectPro_14000DDD2.cpp" />
    <ClCompile Include="source\j_Insert_RaceBattleLogCRFWorldDatabaseQEAA_NPEAU_r_14001004B.cpp" />
    <ClCompile Include="source\j_InstanceCashItemRemoteStoreSAPEAV1XZ_140010320.cpp" />
    <ClCompile Include="source\j_InstanceCCurrentGuildBattleInfoManagerGUILD_BATT_1400028B5.cpp" />
    <ClCompile Include="source\j_InstanceCGuildBattleControllerSAPEAV1XZ_140013458.cpp" />
    <ClCompile Include="source\j_InstanceCGuildBattleLoggerGUILD_BATTLESAPEAV12XZ_14000E5C0.cpp" />
    <ClCompile Include="source\j_InstanceCGuildBattleRankManagerGUILD_BATTLESAPEA_140001E5B.cpp" />
    <ClCompile Include="source\j_InstanceCGuildBattleReservedScheduleListManagerG_140004660.cpp" />
    <ClCompile Include="source\j_InstanceCGuildBattleRewardItemManagerGUILD_BATTL_140008747.cpp" />
    <ClCompile Include="source\j_InstanceCGuildBattleScheduleManagerGUILD_BATTLES_140002DE2.cpp" />
    <ClCompile Include="source\j_InstanceCGuildBattleSchedulePoolGUILD_BATTLESAPE_1400075F4.cpp" />
    <ClCompile Include="source\j_InstanceCGuildBattleSchedulerGUILD_BATTLESAPEAV1_14000C2BB.cpp" />
    <ClCompile Include="source\j_InstanceCNormalGuildBattleFieldListGUILD_BATTLES_14000D4D6.cpp" />
    <ClCompile Include="source\j_InstanceCNormalGuildBattleManagerGUILD_BATTLESAP_140003A76.cpp" />
    <ClCompile Include="source\j_InstanceCNormalGuildBattleStateListPoolGUILD_BAT_14000C5C2.cpp" />
    <ClCompile Include="source\j_InstanceCPossibleBattleGuildListManagerGUILD_BAT_140007E96.cpp" />
    <ClCompile Include="source\j_IsAttackableInTownCGameObjectUEAA_NXZ_14000BC03.cpp" />
    <ClCompile Include="source\j_IsAttackableInTownCMonsterUEAA_NXZ_140013151.cpp" />
    <ClCompile Include="source\j_IsAvailableSuggestCGuildBattleControllerQEAAEPEA_14000898B.cpp" />
    <ClCompile Include="source\j_IsBattleModeUseCRecallRequestQEAA_NXZ_14000D02B.cpp" />
    <ClCompile Include="source\j_IsBeAttackedAbleAutominePersonalUEAA_N_NZ_1400021B2.cpp" />
    <ClCompile Include="source\j_IsBeAttackedAbleCAnimusUEAA_N_NZ_140002C98.cpp" />
    <ClCompile Include="source\j_IsBeAttackedAbleCGameObjectUEAA_N_NZ_14000CDA6.cpp" />
    <ClCompile Include="source\j_IsBeAttackedAbleCGuardTowerUEAA_N_NZ_14000250E.cpp" />
    <ClCompile Include="source\j_IsBeAttackedAbleCHolyKeeperUEAA_N_NZ_1400075AE.cpp" />
    <ClCompile Include="source\j_IsBeAttackedAbleCHolyStoneUEAA_N_NZ_14001366F.cpp" />
    <ClCompile Include="source\j_IsBeAttackedAbleCMonsterUEAA_N_NZ_140006C8F.cpp" />
    <ClCompile Include="source\j_IsBeAttackedAbleCTrapUEAA_N_NZ_14000CDCE.cpp" />
    <ClCompile Include="source\j_IsBuyCashItemByGoldCashItemRemoteStoreQEAA_NXZ_14000CFB3.cpp" />
    <ClCompile Include="source\j_IsCashItemYAHEKZ_1400022A2.cpp" />
    <ClCompile Include="source\j_IsCharInSectorCAttackSAHQEAM00MMZ_140006EBF.cpp" />
    <ClCompile Include="source\j_IsCommitteeMemberCNormalGuildBattleGuildMemberGU_1400103FC.cpp" />
    <ClCompile Include="source\j_IsCompleteBattle_guild_battle_suggest_matterQEAA_140012E4A.cpp" />
    <ClCompile Include="source\j_isConEventTimeCashItemRemoteStoreQEAA_NXZ_140009BA6.cpp" />
    <ClCompile Include="source\j_IsDayChangedCGuildBattleScheduleManagerGUILD_BAT_140010267.cpp" />
    <ClCompile Include="source\j_IsDelay_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14000FD26.cpp" />
    <ClCompile Include="source\j_IsDoneCGuildBattleReservedScheduleGUILD_BATTLEQE_14000280B.cpp" />
    <ClCompile Include="source\j_IsDoneCGuildBattleReservedScheduleMapGroupGUILD__1400087FB.cpp" />
    <ClCompile Include="source\j_IsDoneCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000818E.cpp" />
    <ClCompile Include="source\j_IsEmptyCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000F2CC.cpp" />
    <ClCompile Include="source\j_IsEmptyCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1400111CB.cpp" />
    <ClCompile Include="source\j_IsEmptyCNormalGuildBattleGuildMemberGUILD_BATTLE_140004B06.cpp" />
    <ClCompile Include="source\j_IsEmptyCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000763A.cpp" />
    <ClCompile Include="source\j_IsEmptyTimeCGuildBattleReservedScheduleGUILD_BAT_140009787.cpp" />
    <ClCompile Include="source\j_IsEmptyTimeCGuildBattleReservedScheduleMapGroupG_14000A0A6.cpp" />
    <ClCompile Include="source\j_IsEmptyTimeCGuildBattleScheduleManagerGUILD_BATT_140010933.cpp" />
    <ClCompile Include="source\j_IsEnableStartCNormalGuildBattleGuildMemberGUILD__1400067DA.cpp" />
    <ClCompile Include="source\j_IsEventTimeCashItemRemoteStoreQEAA_NEZ_140005D85.cpp" />
    <ClCompile Include="source\j_IsExistCNormalGuildBattleGuildMemberGUILD_BATTLE_140006159.cpp" />
    <ClCompile Include="source\j_IsInBattleCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_140011FCC.cpp" />
    <ClCompile Include="source\j_IsInBattleCNormalGuildBattleStateListGUILD_BATTL_1400103A7.cpp" />
    <ClCompile Include="source\j_IsInBattleRegenStateCNormalGuildBattleGUILD_BATT_14000AC5E.cpp" />
    <ClCompile Include="source\j_IsInBattleRegenStateCNormalGuildBattleStateInBat_14000BEE7.cpp" />
    <ClCompile Include="source\j_IsInBattleRegenStateCNormalGuildBattleStateListG_140001861.cpp" />
    <ClCompile Include="source\j_IsInBattleRegenStateCNormalGuildBattleStateRound_1400119F0.cpp" />
    <ClCompile Include="source\j_IsJoinMemberCNormalGuildBattleGuildGUILD_BATTLEI_1400096CE.cpp" />
    <ClCompile Include="source\j_IsMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA__14000D995.cpp" />
    <ClCompile Include="source\j_IsMemberGuildCNormalGuildBattleGUILD_BATTLEQEAA__14000C92D.cpp" />
    <ClCompile Include="source\j_IsNullCGuildBattleRewardItemGUILD_BATTLEQEBA_NXZ_14000923C.cpp" />
    <ClCompile Include="source\j_IsPreAttackAbleMonCMonsterQEAAHXZ_140002982.cpp" />
    <ClCompile Include="source\j_IsProcCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000ACB8.cpp" />
    <ClCompile Include="source\j_IsProcCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_14000E223.cpp" />
    <ClCompile Include="source\j_IsProcCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000E03E.cpp" />
    <ClCompile Include="source\j_IsReadyOrCountStateCNormalGuildBattleGUILD_BATTL_140001A7D.cpp" />
    <ClCompile Include="source\j_IsReadyOrCountStateCNormalGuildBattleStateListGU_1400075EF.cpp" />
    <ClCompile Include="source\j_IsRegistedMapInxCNormalGuildBattleFieldListGUILD_14000AAB5.cpp" />
    <ClCompile Include="source\j_IsReStartCNormalGuildBattleGuildGUILD_BATTLEQEAA_140010FB9.cpp" />
    <ClCompile Include="source\j_IsReStartCNormalGuildBattleGuildMemberGUILD_BATT_14000533A.cpp" />
    <ClCompile Include="source\j_IsReStartCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_14000CEDC.cpp" />
    <ClCompile Include="source\j_IsSFContDamageMonsterSFContDamageToleracneQEAA_N_1400018C0.cpp" />
    <ClCompile Include="source\j_IsStorageCodeWithItemKindYAHHHZ_140003724.cpp" />
    <ClCompile Include="source\j_IsWaitCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000BA96.cpp" />
    <ClCompile Include="source\j_Is_Battle_ModeCGameObjectUEAA_NXZ_14000DE27.cpp" />
    <ClCompile Include="source\j_is_cde_timeCashItemRemoteStoreQEAA_NXZ_140010866.cpp" />
    <ClCompile Include="source\j_JoinCNormalGuildBattleGuildGUILD_BATTLEQEAAEKEAE_14000534E.cpp" />
    <ClCompile Include="source\j_JoinCNormalGuildBattleGuildMemberGUILD_BATTLEQEA_14000FA83.cpp" />
    <ClCompile Include="source\j_JoinCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_140003D87.cpp" />
    <ClCompile Include="source\j_JoinCNormalGuildBattleManagerGUILD_BATTLEQEAAXHK_140012DB9.cpp" />
    <ClCompile Include="source\j_JoinGuildCGuildBattleControllerQEAAXHKKZ_1400082CE.cpp" />
    <ClCompile Include="source\j_JoinGuildCNormalGuildBattleManagerGUILD_BATTLEQE_14000F3EE.cpp" />
    <ClCompile Include="source\j_JudgeBattleCNormalGuildBattleGUILD_BATTLEQEAAEXZ_1400108E8.cpp" />
    <ClCompile Include="source\j_KillCNormalGuildBattleGuildGUILD_BATTLEQEAAHPEAV_14000D148.cpp" />
    <ClCompile Include="source\j_KillCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_140010BB3.cpp" />
    <ClCompile Include="source\j_KillCNormalGuildBattleManagerGUILD_BATTLEQEAAHKK_140003FCB.cpp" />
    <ClCompile Include="source\j_LeaveGuildCNormalGuildBattleGuildGUILD_BATTLEQEA_1400035E4.cpp" />
    <ClCompile Include="source\j_LimitedSale_check_countCashItemRemoteStoreQEAA_N_140005C27.cpp" />
    <ClCompile Include="source\j_LoadBuyCashModeCashItemRemoteStoreAEAA_NXZ_140002ADB.cpp" />
    <ClCompile Include="source\j_LoadCGuildBattleControllerQEAA_NXZ_140010C53.cpp" />
    <ClCompile Include="source\j_LoadCGuildBattleRankManagerGUILD_BATTLEIEAA_NEZ_140012044.cpp" />
    <ClCompile Include="source\j_LoadCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_14000521D.cpp" />
    <ClCompile Include="source\j_LoadCGuildBattleReservedScheduleGUILD_BATTLEQEAA_14000F08D.cpp" />
    <ClCompile Include="source\j_LoadCGuildBattleReservedScheduleListManagerGUILD_140002B2B.cpp" />
    <ClCompile Include="source\j_LoadCGuildBattleReservedScheduleMapGroupGUILD_BA_140007739.cpp" />
    <ClCompile Include="source\j_LoadCGuildBattleScheduleGUILD_BATTLEQEAA_N_NKE_J_14000A713.cpp" />
    <ClCompile Include="source\j_LoadCGuildBattleScheduleManagerGUILD_BATTLEQEAA__1400014A6.cpp" />
    <ClCompile Include="source\j_LoadCNormalGuildBattleManagerGUILD_BATTLEIEAA_N__140005FCE.cpp" />
    <ClCompile Include="source\j_LoadCNormalGuildBattleManagerGUILD_BATTLEQEAA_NH_1400044A3.cpp" />
    <ClCompile Include="source\j_LoadCPossibleBattleGuildListManagerGUILD_BATTLEQ_14000A286.cpp" />
    <ClCompile Include="source\j_LoadCReservedGuildScheduleDayGroupGUILD_BATTLEQE_140005CA4.cpp" />
    <ClCompile Include="source\j_LoadCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000A678.cpp" />
    <ClCompile Include="source\j_LoadDBGuildBattleInfoCNormalGuildBattleManagerGU_14000889B.cpp" />
    <ClCompile Include="source\j_LoadDummysCNormalGuildBattleFieldGUILD_BATTLEAEA_140005D17.cpp" />
    <ClCompile Include="source\j_LoadGuildBattleInfoCRFWorldDatabaseQEAA_NKKPEAU__140010037.cpp" />
    <ClCompile Include="source\j_LoadGuildBattleScheduleInfoCRFWorldDatabaseQEAAE_1400080F3.cpp" />
    <ClCompile Include="source\j_LoadINICGuildBattleControllerAEAA_NAEAIAEAH111Z_140008AB2.cpp" />
    <ClCompile Include="source\j_LoadNationalPriceCashItemRemoteStoreAEAA_NAEAVCR_14000C0C2.cpp" />
    <ClCompile Include="source\j_LoadTodayScheduleCGuildBattleReservedScheduleLis_1400056EB.cpp" />
    <ClCompile Include="source\j_LoadTomorrowScheduleCGuildBattleReservedSchedule_14000F0B0.cpp" />
    <ClCompile Include="source\j_load_cash_discount_eventCashItemRemoteStoreQEAAX_140010F4B.cpp" />
    <ClCompile Include="source\j_Load_Cash_EventCashItemRemoteStoreQEAAXXZ_14001208A.cpp" />
    <ClCompile Include="source\j_load_cde_iniCashItemRemoteStoreQEAAXPEAU_cash_di_140006A1E.cpp" />
    <ClCompile Include="source\j_Load_Conditional_EventCashItemRemoteStoreQEAAXXZ_140001505.cpp" />
    <ClCompile Include="source\j_load_con_event_iniCashItemRemoteStoreQEAAXPEAU_c_1400125C6.cpp" />
    <ClCompile Include="source\j_Load_Event_INICashItemRemoteStoreQEAAXPEAU_cash__140001C30.cpp" />
    <ClCompile Include="source\j_Load_LimitedSale_Event_INICashItemRemoteStoreQEA_140004ED5.cpp" />
    <ClCompile Include="source\j_LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_140010802.cpp" />
    <ClCompile Include="source\j_LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZZ_1400030E4.cpp" />
    <ClCompile Include="source\j_LogCGuildBattleStateGUILD_BATTLEIEAAXPEADZ_140001D2A.cpp" />
    <ClCompile Include="source\j_LogCGuildBattleStateListGUILD_BATTLEIEAAXPEADZ_140006523.cpp" />
    <ClCompile Include="source\j_LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEAD_14000726B.cpp" />
    <ClCompile Include="source\j_LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEA__140004AF7.cpp" />
    <ClCompile Include="source\j_LogCNormalGuildBattleStateGUILD_BATTLEIEAAXPEAVC_140005B87.cpp" />
    <ClCompile Include="source\j_LogCNormalGuildBattleStateRoundGUILD_BATTLEIEAAX_14000F4B1.cpp" />
    <ClCompile Include="source\j_log_about_cash_eventCashItemRemoteStoreQEAAXPEAD_1400060FA.cpp" />
    <ClCompile Include="source\j_LoopCGuildBattleControllerQEAAXXZ_140005DDA.cpp" />
    <ClCompile Include="source\j_LoopCGuildBattleReservedScheduleGUILD_BATTLEQEAA_1400074FA.cpp" />
    <ClCompile Include="source\j_LoopCGuildBattleReservedScheduleMapGroupGUILD_BA_14000175D.cpp" />
    <ClCompile Include="source\j_LoopCGuildBattleScheduleManagerGUILD_BATTLEQEAAX_14000ABAA.cpp" />
    <ClCompile Include="source\j_LoopCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuild_1400111FD.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_140002059.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAV_140010FFA.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAV_1400065BE.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleStateInBattleGUILD_BATTLEU_14000FD08.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleStateRoundGUILD_BATTLEMEAA_14001372D.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleStateRoundGUILD_BATTLEUEAA_140009755.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleStateRoundProcessGUILD_BAT_140009291.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleStateRoundReturnStartPosGU_1400124D1.cpp" />
    <ClCompile Include="source\j_LoopCNormalGuildBattleStateRoundStartGUILD_BATTL_140002F9A.cpp" />
    <ClCompile Include="source\j_loop_cash_discount_eventCashItemRemoteStoreQEAAX_1400092FA.cpp" />
    <ClCompile Include="source\j_Loop_Cash_EventCashItemRemoteStoreQEAAXXZ_140012E81.cpp" />
    <ClCompile Include="source\j_Loop_Check_Total_SellingCashItemRemoteStoreQEAAX_140009D27.cpp" />
    <ClCompile Include="source\j_Loop_ContEventCashItemRemoteStoreQEAAXXZ_14000B398.cpp" />
    <ClCompile Include="source\j_Loop_TatalCashEventCashItemRemoteStoreQEAAXXZ_14000BA4B.cpp" />
    <ClCompile Include="source\j_MakePageCPossibleBattleGuildListManagerGUILD_BAT_14000F9CF.cpp" />
    <ClCompile Include="source\j_ManageAcceptORRefuseGuildBattleCGuildQEAAE_NZ_14001272E.cpp" />
    <ClCompile Include="source\j_ManageProposeGuildBattleCGuildQEAAEKKKKZ_140010DD4.cpp" />
    <ClCompile Include="source\j_max_elementPEAVCNormalGuildBattleGuildMemberGUIL_14000382D.cpp" />
    <ClCompile Include="source\j_max_elementPEAVCNormalGuildBattleGuildMemberGUIL_140011DE7.cpp" />
    <ClCompile Include="source\j_max_sizeallocatorVCGuildBattleRewardItemGUILD_BA_14000D544.cpp" />
    <ClCompile Include="source\j_max_sizevectorVCGuildBattleRewardItemGUILD_BATTL_140007F1D.cpp" />
    <ClCompile Include="source\j_ModifyMonsterAttFcCMonsterAttackIEAAMMZ_140003FDF.cpp" />
    <ClCompile Include="source\j_MoveMapCNormalGuildBattleGuildGUILD_BATTLEQEAAXI_140004FC0.cpp" />
    <ClCompile Include="source\j_MoveMemberCNormalGuildBattleGuildGUILD_BATTLEQEA_14000B717.cpp" />
    <ClCompile Include="source\j_NetCloseCNormalGuildBattleGuildGUILD_BATTLEQEAA__140002FE5.cpp" />
    <ClCompile Include="source\j_NetCloseCNormalGuildBattleGuildMemberGUILD_BATTL_1400138BD.cpp" />
    <ClCompile Include="source\j_NextCGuildBattleReservedScheduleGUILD_BATTLEAEAA_14000CA63.cpp" />
    <ClCompile Include="source\j_NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_140011B3F.cpp" />
    <ClCompile Include="source\j_NotifyBallPositionCNormalGuildBattleGUILD_BATTLE_14000C56D.cpp" />
    <ClCompile Include="source\j_NotifyBattleResultCNormalGuildBattleGUILD_BATTLE_140003C6A.cpp" />
    <ClCompile Include="source\j_NotifyBeforeStartCNormalGuildBattleGUILD_BATTLEQ_140005E8E.cpp" />
    <ClCompile Include="source\j_NotifyCommitteeMemberPositionCNormalGuildBattleG_14000C2FC.cpp" />
    <ClCompile Include="source\j_NotifyDestoryBallCNormalGuildBattleGUILD_BATTLEQ_140004827.cpp" />
    <ClCompile Include="source\j_NotifyLeftMinuteBeforeStartCNormalGuildBattleGui_14000A9BB.cpp" />
    <ClCompile Include="source\j_NotifyPassGravityStoneLimitTimeCNormalGuildBattl_14000E2CD.cpp" />
    <ClCompile Include="source\j_OnlyOnceInitMonsterSFContDamageToleracneQEAAXPEA_140009A84.cpp" />
    <ClCompile Include="source\j_On_HS_SCENE_BATTLE_END_WAIT_TIMECHolyStoneSystem_140006497.cpp" />
    <ClCompile Include="source\j_On_HS_SCENE_BATTLE_TIMECHolyStoneSystemIEAAXXZ_14000E1DD.cpp" />
    <ClCompile Include="source\j_On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSyst_14000B523.cpp" />
    <ClCompile Include="source\j_On_HS_SCENE_KEEPER_DEATTACKABLE_TIMECHolyStoneSy_14000A2D6.cpp" />
    <ClCompile Include="source\j_OutDestGuildbattleCostCMainThreadQEAAXPEAU_DB_QR_140007B17.cpp" />
    <ClCompile Include="source\j_OutSrcGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_14000565F.cpp" />
    <ClCompile Include="source\j_ProcCheckGetGravityStoneCNormalGuildBattleManage_14000B7CB.cpp" />
    <ClCompile Include="source\j_ProcCheckGoalCNormalGuildBattleManagerGUILD_BATT_140013908.cpp" />
    <ClCompile Include="source\j_ProcCheckTakeGravityStoneCNormalGuildBattleManag_14000C1E4.cpp" />
    <ClCompile Include="source\j_ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_140008F3F.cpp" />
    <ClCompile Include="source\j_ProcessCGuildBattleStateListGUILD_BATTLEQEAAXPEA_140009B9C.cpp" />
    <ClCompile Include="source\j_ProcessCNormalGuildBattleGUILD_BATTLEQEAAXXZ_140008422.cpp" />
    <ClCompile Include="source\j_ProcJoinCNormalGuildBattleManagerGUILD_BATTLEIEA_140001AA0.cpp" />
    <ClCompile Include="source\j_PushClearGuildBattleRankCGuildBattleControllerQE_140005637.cpp" />
    <ClCompile Include="source\j_PushClearGuildBattleRankCGuildBattleRankManagerG_1400108FC.cpp" />
    <ClCompile Include="source\j_PushCreateGuildBattleRankTableCGuildBattleContro_140002978.cpp" />
    <ClCompile Include="source\j_PushCreateGuildBattleRankTableCGuildBattleRankMa_1400065EB.cpp" />
    <ClCompile Include="source\j_PushDQSCGuildBattleReservedScheduleListManagerGU_14000740F.cpp" />
    <ClCompile Include="source\j_PushDQSClearCGuildBattleReservedScheduleMapGroup_140009D4F.cpp" />
    <ClCompile Include="source\j_PushDQSDataCNormalGuildBattleManagerGUILD_BATTLE_14000C5F9.cpp" />
    <ClCompile Include="source\j_PushDQSDestGuildOutputGuildBattleCostCGuildQEAAX_14000141A.cpp" />
    <ClCompile Include="source\j_PushDQSDrawRankCNormalGuildBattleGUILD_BATTLEIEA_140005470.cpp" />
    <ClCompile Include="source\j_PushDQSInGuildBattleCostCGuildQEAAXXZ_140011D06.cpp" />
    <ClCompile Include="source\j_PushDQSInGuildBattleRewardMoneyCGuildQEAAXXZ_1400124BD.cpp" />
    <ClCompile Include="source\j_PushDQSPvpPointCNormalGuildBattleGuildMemberGUIL_140008995.cpp" />
    <ClCompile Include="source\j_PushDQSSourceGuildOutputGuildBattleCostCGuildQEA_14000527C.cpp" />
    <ClCompile Include="source\j_PushDQSWinLoseRankCNormalGuildBattleGUILD_BATTLE_140003017.cpp" />
    <ClCompile Include="source\j_PushItemCObjectListQEAA_NPEAU_object_list_pointZ_140001C49.cpp" />
    <ClCompile Include="source\j_PushItem_TRAP_PARAMQEAA_NPEAVCTrapKZ_1400036CF.cpp" />
    <ClCompile Include="source\j_RCTopGoalPrediCateCNormalGuildBattleGuildGUILD_B_140001104.cpp" />
    <ClCompile Include="source\j_RCTopKillPrediCateCNormalGuildBattleGuildGUILD_B_14000658C.cpp" />
    <ClCompile Include="source\j_RegenBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_14000F317.cpp" />
    <ClCompile Include="source\j_RequestSubProcSetRaceBattleResultCRaceBuffByHoly_140012F80.cpp" />
    <ClCompile Include="source\j_ReturnBindPosAllCNormalGuildBattleGuildGUILD_BAT_14000C75C.cpp" />
    <ClCompile Include="source\j_ReturnBindPosCNormalGuildBattleGuildMemberGUILD__14000580D.cpp" />
    <ClCompile Include="source\j_ReturnHQPosAllCNormalGuildBattleGuildGUILD_BATTL_140006FA5.cpp" />
    <ClCompile Include="source\j_ReturnStartPosAllCNormalGuildBattleGuildGUILD_BA_140005EAC.cpp" />
    <ClCompile Include="source\j_ReturnStartPosCNormalGuildBattleGuildMemberGUILD_140004511.cpp" />
    <ClCompile Include="source\j_RewardGuildBattleMoneyCNormalGuildBattleGUILD_BA_14000FEB6.cpp" />
    <ClCompile Include="source\j_RewardItemCNormalGuildBattleGuildGUILD_BATTLEQEA_14000E1F6.cpp" />
    <ClCompile Include="source\j_RewardItemCNormalGuildBattleGUILD_BATTLEQEAAXXZ_140010FA5.cpp" />
    <ClCompile Include="source\j_rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD__140013034.cpp" />
    <ClCompile Include="source\j_SaveCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000F817.cpp" />
    <ClCompile Include="source\j_SaveCNormalGuildBattleManagerGUILD_BATTLEQEAA_NK_14000BE38.cpp" />
    <ClCompile Include="source\j_SaveINICGuildBattleControllerAEAA_NXZ_140009430.cpp" />
    <ClCompile Include="source\j_SearchItemAddSpeedCEquipItemSFAgentIEAAMPEAU_db__14000D45E.cpp" />
    <ClCompile Include="source\j_SectorDamageProcCAttackIEAAXHHHHHH_NZ_1400057C7.cpp" />
    <ClCompile Include="source\j_SelectGuildBattleRankListCGuildBattleRankManager_140012314.cpp" />
    <ClCompile Include="source\j_SelectGuildBattleRankListCRFWorldDatabaseQEAA_NE_1400037E2.cpp" />
    <ClCompile Include="source\j_SelectGuildBattleRankRecordCRFWorldDatabaseQEAA__140012C15.cpp" />
    <ClCompile Include="source\j_SelectGuildBattleRerservedListCRFWorldDatabaseQE_140003EEA.cpp" />
    <ClCompile Include="source\j_SelectGuildBattleScheduleInfoIDCRFWorldDatabaseQ_14001019F.cpp" />
    <ClCompile Include="source\j_SelectRowCountGuildBattleInfoCRFWorldDatabaseQEA_140004DF9.cpp" />
    <ClCompile Include="source\j_SelectRowCountGuildBattleScheduleInfoCRFWorldDat_140009048.cpp" />
    <ClCompile Include="source\j_Select_BattleResultLogLatestCRFWorldDatabaseQEAA_14000CD56.cpp" />
    <ClCompile Include="source\j_Select_BattleTournamentInfoCRFWorldDatabaseQEAA__140001910.cpp" />
    <ClCompile Include="source\j_Select_FailBattleCountCRFWorldDatabaseQEAAHEKAEA_1400040DE.cpp" />
    <ClCompile Include="source\j_Select_GuildBattleRecordCRFWorldDatabaseQEAA_NKP_140012C51.cpp" />
    <ClCompile Include="source\j_Select_LoseBattleCountCRFWorldDatabaseQEAAHEKAEA_1400103CF.cpp" />
    <ClCompile Include="source\j_Select_WinBattleCountCRFWorldDatabaseQEAAHEKAEAK_140013601.cpp" />
    <ClCompile Include="source\j_SellCashItemRemoteStoreQEAA_NGPEADZ_14000EDCC.cpp" />
    <ClCompile Include="source\j_SetActiveSuccCAttackQEAAX_NZ_14000AC2C.cpp" />
    <ClCompile Include="source\j_SetAttackPartCGameObjectUEAAXHZ_140012738.cpp" />
    <ClCompile Include="source\j_SetBattleModeTimeCMonsterAIQEAAXKZ_140008012.cpp" />
    <ClCompile Include="source\j_SetBattleStateCNormalGuildBattleGuildMemberGUILD_140005D49.cpp" />
    <ClCompile Include="source\j_SetBattleTimeCNormalGuildBattleStateInBattleGUIL_1400122CE.cpp" />
    <ClCompile Include="source\j_SetBattleTimeCNormalGuildBattleStateListGUILD_BA_1400066D6.cpp" />
    <ClCompile Include="source\j_SetCCurrentGuildBattleInfoManagerGUILD_BATTLEQEA_1400029B9.cpp" />
    <ClCompile Include="source\j_SetCGuildBattleScheduleGUILD_BATTLEQEAAEKKZ_1400038EB.cpp" />
    <ClCompile Include="source\j_SetColorInxCNormalGuildBattleGuildGUILD_BATTLEQE_14000574F.cpp" />
    <ClCompile Include="source\j_SetCombatStateCMonsterQEAAXEZ_14000C9BE.cpp" />
    <ClCompile Include="source\j_SetCopmlteGuildBattleSuggestCGuildQEAAXXZ_140013D5E.cpp" />
    <ClCompile Include="source\j_SetDamageAbleStateCHolyKeeperQEAAX_NZ_14000D9EA.cpp" />
    <ClCompile Include="source\j_SetDelay_ATTACK_DELAY_CHECKERQEAAXKZ_14000E7B4.cpp" />
    <ClCompile Include="source\j_SetGotoRegenStartCNormalGuildBattleGUILD_BATTLEQ_14000CB8F.cpp" />
    <ClCompile Include="source\j_SetGotoRegenStateCNormalGuildBattleStateInBattle_140008C15.cpp" />
    <ClCompile Include="source\j_SetGotoRegenStateCNormalGuildBattleStateListGUIL_14000E8C2.cpp" />
    <ClCompile Include="source\j_SetGuildBattleMatterCGuildQEAAXKKKKZ_14000FC86.cpp" />
    <ClCompile Include="source\j_SetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAX_140009223.cpp" />
    <ClCompile Include="source\j_SetItemCGuildBattleRewardItemGUILD_BATTLEIEAA_NP_140009C37.cpp" />
    <ClCompile Include="source\j_SetLoadCBattleTournamentInfoQEAAX_NZ_14000187A.cpp" />
    <ClCompile Include="source\j_SetNextDiscountEventTimeCashItemRemoteStoreQEAA__140006BA4.cpp" />
    <ClCompile Include="source\j_SetNextEventCNormalGuildBattleManagerGUILD_BATTL_1400057D6.cpp" />
    <ClCompile Include="source\j_SetNextEventTimeCashItemRemoteStoreQEAA_NEZ_140002D92.cpp" />
    <ClCompile Include="source\j_SetNextEvnetCGuildBattleScheduleManagerGUILD_BAT_140005DE4.cpp" />
    <ClCompile Include="source\j_SetNextStateCGuildBattleStateListGUILD_BATTLEMEA_140008B2A.cpp" />
    <ClCompile Include="source\j_SetNextStateCNormalGuildBattleStateListGUILD_BAT_14000E9FD.cpp" />
    <ClCompile Include="source\j_SetNextStateCNormalGuildBattleStateRoundListGUIL_1400041F1.cpp" />
    <ClCompile Include="source\j_SetOrderViewAttackStateCPvpOrderViewQEAAXXZ_140002CC5.cpp" />
    <ClCompile Include="source\j_SetOrderViewDamagedStateCPvpOrderViewQEAAXXZ_14000ECE1.cpp" />
    <ClCompile Include="source\j_SetPortalInxCNormalGuildBattleFieldGUILD_BATTLEA_14000E87C.cpp" />
    <ClCompile Include="source\j_SetProcStateCGuildBattleScheduleGUILD_BATTLEQEAA_14000A308.cpp" />
    <ClCompile Include="source\j_SetReadyCGuildBattleStateListGUILD_BATTLEQEAAXXZ_140001780.cpp" />
    <ClCompile Include="source\j_SetReadyStateCNormalGuildBattleGUILD_BATTLEQEAAX_140001118.cpp" />
    <ClCompile Include="source\j_SetReadyStateCNormalGuildBattleManagerGUILD_BATT_14000CBC6.cpp" />
    <ClCompile Include="source\j_SetReStartFlagCNormalGuildBattleGuildGUILD_BATTL_140001893.cpp" />
    <ClCompile Include="source\j_SetReStartFlagCNormalGuildBattleGuildMemberGUILD_140001AEB.cpp" />
    <ClCompile Include="source\j_SetSFDamageToleracne_VariationMonsterSFContDamag_140003620.cpp" />
    <ClCompile Include="source\j_SetStateListCGuildBattleScheduleGUILD_BATTLEQEAA_14000B6BD.cpp" />
    <ClCompile Include="source\j_SetStaticMemberCAttackSAXPEAVCRecordDataZ_1400035B2.cpp" />
    <ClCompile Include="source\j_SetWaitCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1400049B7.cpp" />
    <ClCompile Include="source\j_SetWinnerInfoCBattleTournamentInfoQEAA_NKPEADEZ_140011AE0.cpp" />
    <ClCompile Include="source\j_Set_CashEvent_StatusCashItemRemoteStoreQEAAXEEZ_14000DE31.cpp" />
    <ClCompile Include="source\j_set_cde_statusCashItemRemoteStoreQEAAXEZ_14000B767.cpp" />
    <ClCompile Include="source\j_Set_Conditional_Evnet_StatusCashItemRemoteStoreQ_14000241E.cpp" />
    <ClCompile Include="source\j_Set_DB_LimitedSale_EventCashItemRemoteStoreQEAAX_140011469.cpp" />
    <ClCompile Include="source\j_Set_FROMDB_LimitedSale_EventCashItemRemoteStoreQ_14000E30E.cpp" />
    <ClCompile Include="source\j_Set_LimitedSale_countCashItemRemoteStoreQEAAXEKZ_140002F9F.cpp" />
    <ClCompile Include="source\j_Set_LimitedSale_DCKCashItemRemoteStoreQEAAXEEZ_140004C19.cpp" />
    <ClCompile Include="source\j_Set_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_140013B7E.cpp" />
    <ClCompile Include="source\j_Set_LimitedSale_Event_IniCashItemRemoteStoreQEAA_140012DCD.cpp" />
    <ClCompile Include="source\j_sizevectorVCGuildBattleRewardItemGUILD_BATTLEVal_14000AAD8.cpp" />
    <ClCompile Include="source\j_size_attack_count_result_zoclQEAAHXZ_140010F19.cpp" />
    <ClCompile Include="source\j_size_attack_force_result_zoclQEAAHXZ_140013D63.cpp" />
    <ClCompile Include="source\j_size_attack_gen_result_zoclQEAAHXZ_140011FB3.cpp" />
    <ClCompile Include="source\j_size_attack_keeper_inform_zoclQEAAHXZ_14001157C.cpp" />
    <ClCompile Include="source\j_size_attack_selfdestruction_result_zoclQEAAHXZ_14000FA24.cpp" />
    <ClCompile Include="source\j_size_attack_siege_result_zoclQEAAHXZ_140006875.cpp" />
    <ClCompile Include="source\j_size_attack_trap_inform_zoclQEAAHXZ_14000F13C.cpp" />
    <ClCompile Include="source\j_size_attack_unit_result_zoclQEAAHXZ_1400026B7.cpp" />
    <ClCompile Include="source\j_size_guild_battle_get_gravity_stone_result_zoclQ_140006E10.cpp" />
    <ClCompile Include="source\j_size_guild_battle_goal_result_zoclQEAAHXZ_14000AF2E.cpp" />
    <ClCompile Include="source\j_size_guild_battle_rank_list_result_zoclQEAAHXZ_14000A0D3.cpp" />
    <ClCompile Include="source\j_size_guild_battle_reserved_schedule_result_zoclQ_140001055.cpp" />
    <ClCompile Include="source\j_size_guild_battle_suggest_request_result_zoclQEA_140010EE7.cpp" />
    <ClCompile Include="source\j_size_notify_not_use_premium_cashitem_zoclQEAAHXZ_14001203F.cpp" />
    <ClCompile Include="source\j_size_param_cashitem_dblogQEAAHXZ_140001AB9.cpp" />
    <ClCompile Include="source\j_size_personal_automine_attacked_zoclQEAAHXZ_14000EAFC.cpp" />
    <ClCompile Include="source\j_size_possible_battle_guild_list_result_zoclQEAAH_1400045A2.cpp" />
    <ClCompile Include="source\j_size_qry_case_addguildbattlescheduleQEAAHXZ_1400017B7.cpp" />
    <ClCompile Include="source\j_size_qry_case_dest_guild_out_guildbattlecostQEAA_14000E700.cpp" />
    <ClCompile Include="source\j_size_qry_case_in_guildbattlecostQEAAHXZ_140012B16.cpp" />
    <ClCompile Include="source\j_size_qry_case_in_guildbattlerewardmoneyQEAAHXZ_140013E49.cpp" />
    <ClCompile Include="source\j_size_qry_case_loadguildbattlerankQEAAHXZ_140002B30.cpp" />
    <ClCompile Include="source\j_size_qry_case_load_guildbattle_totalrecordQEAAHX_140007FEA.cpp" />
    <ClCompile Include="source\j_size_qry_case_src_guild_out_guildbattlecostQEAAH_14000D837.cpp" />
    <ClCompile Include="source\j_size_qry_case_updateclearguildbattleDayInfoQEAAH_1400117ED.cpp" />
    <ClCompile Include="source\j_size_qry_case_updatedrawguildbattlerankQEAAHXZ_1400060A5.cpp" />
    <ClCompile Include="source\j_size_qry_case_updatewinloseguildbattlerankQEAAHX_14000D5E9.cpp" />
    <ClCompile Include="source\j_SrcGuildIsAvailableBattleRequestStateCGuildQEAAE_1400053B2.cpp" />
    <ClCompile Include="source\j_start_casheventCashItemRemoteStoreQEAA_NHHHHEZ_1400016F4.cpp" />
    <ClCompile Include="source\j_start_cdeCashItemRemoteStoreQEAA_NHHHHZ_140004F7A.cpp" />
    <ClCompile Include="source\j_start_coneventCashItemRemoteStoreQEAA_NHHEZ_140004F61.cpp" />
    <ClCompile Include="source\j_StockOldInfoCNormalGuildBattleGuildMemberGUILD_B_14000909D.cpp" />
    <ClCompile Include="source\j_TakeGravityStoneCNormalGuildBattleGUILD_BATTLEQE_1400067DF.cpp" />
    <ClCompile Include="source\j_unchecked_copyPEAVCGuildBattleRewardItemGUILD_BA_14000BF28.cpp" />
    <ClCompile Include="source\j_unchecked_uninitialized_copyPEAVCGuildBattleRewa_140010965.cpp" />
    <ClCompile Include="source\j_unchecked_uninitialized_fill_nPEAVCGuildBattleRe_14001087F.cpp" />
    <ClCompile Include="source\j_UpdateCGuildBattleRankManagerGUILD_BATTLEQEAA_NE_14000F8BC.cpp" />
    <ClCompile Include="source\j_UpdateClearGuildBattleDayInfoCNormalGuildBattleM_1400047B9.cpp" />
    <ClCompile Include="source\j_UpdateClearGuildBattleInfoCRFWorldDatabaseQEAA_N_140004192.cpp" />
    <ClCompile Include="source\j_UpdateClearGuildBattleRankCRFWorldDatabaseQEAA_N_1400108C0.cpp" />
    <ClCompile Include="source\j_UpdateClearGuildBattleScheduleDayInfoCGuildBattl_1400013ED.cpp" />
    <ClCompile Include="source\j_UpdateClearGuildBattleScheduleInfoCRFWorldDataba_1400014C4.cpp" />
    <ClCompile Include="source\j_UpdateClearGuildBattleScheduleInfoCRFWorldDataba_14001141E.cpp" />
    <ClCompile Include="source\j_UpdateClearRerservedDayInfoCGuildBattleControlle_1400042A5.cpp" />
    <ClCompile Include="source\j_UpdateDayChangedWorkCGuildBattleScheduleManagerG_14000998F.cpp" />
    <ClCompile Include="source\j_UpdateDrawCGuildBattleControllerQEAA_NEKEKZ_140008F67.cpp" />
    <ClCompile Include="source\j_UpdateDrawCGuildBattleRankManagerGUILD_BATTLEQEA_14000CABD.cpp" />
    <ClCompile Include="source\j_UpdateDrawGuildBattleResultCRFWorldDatabaseQEAA__140003594.cpp" />
    <ClCompile Include="source\j_UpdateGoalCntCCurrentGuildBattleInfoManagerGUILD_140010BA9.cpp" />
    <ClCompile Include="source\j_UpdateGuildBattleDrawRankInfoCMainThreadQEAAXPEA_14000FA88.cpp" />
    <ClCompile Include="source\j_UpdateGuildBattleInfoCRFWorldDatabaseQEAA_NKKKKE_140001F1E.cpp" />
    <ClCompile Include="source\j_UpdateGuildBattleScheduleInfoCRFWorldDatabaseQEA_14000E46C.cpp" />
    <ClCompile Include="source\j_UpdateGuildBattleWinCntCGuildQEAAXKKKZ_14000ED40.cpp" />
    <ClCompile Include="source\j_UpdateGuildBattleWinLoseRankInfoCMainThreadQEAAX_14000B578.cpp" />
    <ClCompile Include="source\j_UpdateGuildListCPossibleBattleGuildListManagerGU_1400016EF.cpp" />
    <ClCompile Include="source\j_UpdateLoadGuildBattleRankCMainThreadQEAAXPEAU_DB_14000EFE8.cpp" />
    <ClCompile Include="source\j_UpdateLoseGuildBattleResultCRFWorldDatabaseQEAA__140008AF8.cpp" />
    <ClCompile Include="source\j_UpdateMonsterSFContDamageToleracneQEAAXXZ_140009827.cpp" />
    <ClCompile Include="source\j_UpdatePossibleBattleGuildListCGuildBattleControl_14000D8B4.cpp" />
    <ClCompile Include="source\j_UpdateRankCGuildBattleControllerQEAA_NEPEAEZ_140006FC3.cpp" />
    <ClCompile Include="source\j_UpdateReservedGuildBattleScheduleCGuildBattleCon_14000D4EF.cpp" />
    <ClCompile Include="source\j_UpdateReservedGuildBattleScheduleCMainThreadQEAA_14000994E.cpp" />
    <ClCompile Include="source\j_UpdateReservedSheduleCGuildBattleReservedSchedul_140005182.cpp" />
    <ClCompile Include="source\j_UpdateScoreCCurrentGuildBattleInfoManagerGUILD_B_140002022.cpp" />
    <ClCompile Include="source\j_UpdateScoreCNormalGuildBattleGuildGUILD_BATTLEIE_14000E610.cpp" />
    <ClCompile Include="source\j_UpdateTodayScheduleCGuildBattleReservedScheduleL_14000D274.cpp" />
    <ClCompile Include="source\j_UpdateTomorrowCompleteCGuildBattleReservedSchedu_1400074E6.cpp" />
    <ClCompile Include="source\j_UpdateTomorrowScheduleCGuildBattleReservedSchedu_140001E06.cpp" />
    <ClCompile Include="source\j_UpdateUseFieldCGuildBattleReservedScheduleGUILD__140004020.cpp" />
    <ClCompile Include="source\j_UpdateUseFlagCGuildBattleReservedScheduleGUILD_B_140011C84.cpp" />
    <ClCompile Include="source\j_UpdateUseFlagCGuildBattleReservedScheduleMapGrou_14000585D.cpp" />
    <ClCompile Include="source\j_UpdateUseFlagCGuildBattleScheduleManagerGUILD_BA_140010A2D.cpp" />
    <ClCompile Include="source\j_UpdateWinGuildBattleResultCRFWorldDatabaseQEAA_N_14000321F.cpp" />
    <ClCompile Include="source\j_UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_140003C0B.cpp" />
    <ClCompile Include="source\j_UpdateWinLoseCGuildBattleRankManagerGUILD_BATTLE_14000142E.cpp" />
    <ClCompile Include="source\j_Update_BattleResultLogBattleResultAndPvpPointCRF_140011DEC.cpp" />
    <ClCompile Include="source\j_Update_CristalBattleCharInfoCRFWorldDatabaseQEAA_140007D06.cpp" />
    <ClCompile Include="source\j_update_cristalbattle_dateCRFWorldDatabaseQEAA_NK_140001046.cpp" />
    <ClCompile Include="source\j_Update_IncreaseWeeklyGuildGuildBattlePvpPointSum_140004868.cpp" />
    <ClCompile Include="source\j_update_iniCashItemRemoteStoreQEAAXPEAU_cash_disc_140008184.cpp" />
    <ClCompile Include="source\j_Update_INICashItemRemoteStoreQEAAXPEAU_cash_even_1400026DA.cpp" />
    <ClCompile Include="source\j_WriteLogPer10Min_CombatCHolyStoneSystemIEAAXXZ_14000515F.cpp" />
    <ClCompile Include="source\j__AllocateVCGuildBattleRewardItemGUILD_BATTLEstdY_14000FF9C.cpp" />
    <ClCompile Include="source\j__Assign_nvectorVCGuildBattleRewardItemGUILD_BATT_14000C199.cpp" />
    <ClCompile Include="source\j__buybygold_buy_single_item_calc_price_discountCa_140002518.cpp" />
    <ClCompile Include="source\j__buybygold_buy_single_item_calc_price_limitsaleC_14000AE48.cpp" />
    <ClCompile Include="source\j__buybygold_buy_single_item_calc_price_one_n_oneC_14000FA74.cpp" />
    <ClCompile Include="source\j__buybygold_buy_single_item_setbuydblogCashItemRe_14000C64E.cpp" />
    <ClCompile Include="source\j__BuyvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1400111EE.cpp" />
    <ClCompile Include="source\j__CalcForceAttPntCAttackIEAAH_NZ_1400030C1.cpp" />
    <ClCompile Include="source\j__CalcGenAttPntCAttackIEAAH_NZ_1400132FA.cpp" />
    <ClCompile Include="source\j__check_buyitemCashItemRemoteStoreAEAAAW4CS_RCODE_1400056FA.cpp" />
    <ClCompile Include="source\j__complete_tsk_cashitem_buy_dblogCashDbWorkerIEAA_1400022DE.cpp" />
    <ClCompile Include="source\j__ConstructVCGuildBattleRewardItemGUILD_BATTLEV12_14000670D.cpp" />
    <ClCompile Include="source\j__Copy_backward_optPEAVCGuildBattleRewardItemGUIL_14000188E.cpp" />
    <ClCompile Include="source\j__Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEP_14000C6AD.cpp" />
    <ClCompile Include="source\j__db_Load_BattleTournamentInfoCMainThreadAEAAXXZ_140003EAE.cpp" />
    <ClCompile Include="source\j__db_load_losebattlecountCMainThreadAEAAEKPEAU_AV_14000ECFA.cpp" />
    <ClCompile Include="source\j__delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_1400055CE.cpp" />
    <ClCompile Include="source\j__DestroyVCGuildBattleRewardItemGUILD_BATTLEstdYA_140008FA3.cpp" />
    <ClCompile Include="source\j__DestroyvectorVCGuildBattleRewardItemGUILD_BATTL_14000AF6F.cpp" />
    <ClCompile Include="source\j__Destroy_rangeVCGuildBattleRewardItemGUILD_BATTL_140004D27.cpp" />
    <ClCompile Include="source\j__Destroy_rangeVCGuildBattleRewardItemGUILD_BATTL_1400116D0.cpp" />
    <ClCompile Include="source\j__ECNormalGuildBattleFieldGUILD_BATTLEQEAAPEAXIZ_140007608.cpp" />
    <ClCompile Include="source\j__ECNormalGuildBattleStateListGUILD_BATTLEQEAAPEA_140003846.cpp" />
    <ClCompile Include="source\j__ECReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_14000C0E5.cpp" />
    <ClCompile Include="source\j__FillPEAVCGuildBattleRewardItemGUILD_BATTLEV12st_14000904D.cpp" />
    <ClCompile Include="source\j__GCCurrentGuildBattleInfoManagerGUILD_BATTLEIEAA_140011207.cpp" />
    <ClCompile Include="source\j__GCGuildBattleControllerIEAAPEAXIZ_140010406.cpp" />
    <ClCompile Include="source\j__GCGuildBattleLoggerGUILD_BATTLEIEAAPEAXIZ_14000BFF5.cpp" />
    <ClCompile Include="source\j__GCGuildBattleRankManagerGUILD_BATTLEIEAAPEAXIZ_140002BC6.cpp" />
    <ClCompile Include="source\j__GCGuildBattleReservedScheduleGUILD_BATTLEQEAAPE_140012DC8.cpp" />
    <ClCompile Include="source\j__GCGuildBattleReservedScheduleListManagerGUILD_B_140009D5E.cpp" />
    <ClCompile Include="source\j__GCGuildBattleScheduleGUILD_BATTLEQEAAPEAXIZ_140009FD4.cpp" />
    <ClCompile Include="source\j__GCGuildBattleScheduleManagerGUILD_BATTLEIEAAPEA_140007F8B.cpp" />
    <ClCompile Include="source\j__GCGuildBattleSchedulePoolGUILD_BATTLEIEAAPEAXIZ_14000EBF1.cpp" />
    <ClCompile Include="source\j__GCGuildBattleSchedulerGUILD_BATTLEIEAAPEAXIZ_14000BA7D.cpp" />
    <ClCompile Include="source\j__GCNormalGuildBattleFieldListGUILD_BATTLEIEAAPEA_1400116AD.cpp" />
    <ClCompile Include="source\j__GCNormalGuildBattleGUILD_BATTLEQEAAPEAXIZ_140009CDC.cpp" />
    <ClCompile Include="source\j__GCNormalGuildBattleManagerGUILD_BATTLEIEAAPEAXI_140009764.cpp" />
    <ClCompile Include="source\j__GCNormalGuildBattleStateListPoolGUILD_BATTLEIEA_1400123F0.cpp" />
    <ClCompile Include="source\j__GCPossibleBattleGuildListManagerGUILD_BATTLEIEA_14001128E.cpp" />
    <ClCompile Include="source\j__GCRFCashItemDatabaseUEAAPEAXIZ_0_140010613.cpp" />
    <ClCompile Include="source\j__GCRFCashItemDatabaseUEAAPEAXIZ_14000A637.cpp" />
    <ClCompile Include="source\j__InitLoggersCashItemRemoteStoreAEAA_NXZ_140013FFC.cpp" />
    <ClCompile Include="source\j__Insert_nvectorVCGuildBattleRewardItemGUILD_BATT_14000CD4C.cpp" />
    <ClCompile Include="source\j__Iter_randomPEAVCGuildBattleRewardItemGUILD_BATT_140013921.cpp" />
    <ClCompile Include="source\j__MakeLinkTableCashItemRemoteStoreAEAA_NPEADHZ_14001249F.cpp" />
    <ClCompile Include="source\j__Max_elementPEAVCNormalGuildBattleGuildMemberGUI_140005B32.cpp" />
    <ClCompile Include="source\j__Max_elementPEAVCNormalGuildBattleGuildMemberGUI_1400114E1.cpp" />
    <ClCompile Include="source\j__Move_backward_optPEAVCGuildBattleRewardItemGUIL_140012BB1.cpp" />
    <ClCompile Include="source\j__Move_catPEAVCGuildBattleRewardItemGUILD_BATTLEs_14001233C.cpp" />
    <ClCompile Include="source\j__Ptr_catPEAVCGuildBattleRewardItemGUILD_BATTLEPE_140009435.cpp" />
    <ClCompile Include="source\j__PushItemCutLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LT_14000C135.cpp" />
    <ClCompile Include="source\j__PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B.cpp" />
    <ClCompile Include="source\j__ReadGoodsCashItemRemoteStoreAEAA_NXZ_140004606.cpp" />
    <ClCompile Include="source\j__TidyvectorVCGuildBattleRewardItemGUILD_BATTLEVa_1400041AB.cpp" />
    <ClCompile Include="source\j__UfillvectorVCGuildBattleRewardItemGUILD_BATTLEV_140006C3A.cpp" />
    <ClCompile Include="source\j__UmovePEAVCGuildBattleRewardItemGUILD_BATTLEvect_14000B7D0.cpp" />
    <ClCompile Include="source\j__Unchecked_move_backwardPEAVCGuildBattleRewardIt_14000239C.cpp" />
    <ClCompile Include="source\j__Unchecked_uninitialized_movePEAVCGuildBattleRew_14000F358.cpp" />
    <ClCompile Include="source\j__Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATT_140011E82.cpp" />
    <ClCompile Include="source\j__Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BA_14000B6C2.cpp" />
    <ClCompile Include="source\j__Uninit_movePEAVCGuildBattleRewardItemGUILD_BATT_140012D1E.cpp" />
    <ClCompile Include="source\j__XlenvectorVCGuildBattleRewardItemGUILD_BATTLEVa_1400131A1.cpp" />
    <ClCompile Include="source\j___CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecord_14000F484.cpp" />
    <ClCompile Include="source\KillCNormalGuildBattleGuildGUILD_BATTLEQEAAHPEAVCN_1403E1560.cpp" />
    <ClCompile Include="source\KillCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E5C30.cpp" />
    <ClCompile Include="source\KillCNormalGuildBattleManagerGUILD_BATTLEQEAAHKKKZ_1403D4B20.cpp" />
    <ClCompile Include="source\LeaveGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAX_1403E1460.cpp" />
    <ClCompile Include="source\LimitedSale_check_countCashItemRemoteStoreQEAA_NEK_1402FDD90.cpp" />
    <ClCompile Include="source\LoadBuyCashModeCashItemRemoteStoreAEAA_NXZ_1402F4D90.cpp" />
    <ClCompile Include="source\LoadCGuildBattleControllerQEAA_NXZ_1403D5950.cpp" />
    <ClCompile Include="source\LoadCGuildBattleRankManagerGUILD_BATTLEIEAA_NEZ_1403CB820.cpp" />
    <ClCompile Include="source\LoadCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_1403CA610.cpp" />
    <ClCompile Include="source\LoadCGuildBattleReservedScheduleGUILD_BATTLEQEAA_N_1403DAEA0.cpp" />
    <ClCompile Include="source\LoadCGuildBattleReservedScheduleListManagerGUILD_B_1403CD540.cpp" />
    <ClCompile Include="source\LoadCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DBDC0.cpp" />
    <ClCompile Include="source\LoadCGuildBattleScheduleGUILD_BATTLEQEAA_N_NKE_JGZ_1403D9F60.cpp" />
    <ClCompile Include="source\LoadCGuildBattleScheduleManagerGUILD_BATTLEQEAA_NH_1403DCDD0.cpp" />
    <ClCompile Include="source\LoadCNormalGuildBattleManagerGUILD_BATTLEIEAA_N_NI_1403D4D90.cpp" />
    <ClCompile Include="source\LoadCNormalGuildBattleManagerGUILD_BATTLEQEAA_NHIH_1403D38F0.cpp" />
    <ClCompile Include="source\LoadCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403C99F0.cpp" />
    <ClCompile Include="source\LoadCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCF20.cpp" />
    <ClCompile Include="source\LoadCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CC540.cpp" />
    <ClCompile Include="source\LoadDBGuildBattleInfoCNormalGuildBattleManagerGUIL_1403D4F40.cpp" />
    <ClCompile Include="source\LoadDummysCNormalGuildBattleFieldGUILD_BATTLEAEAA__1403ED930.cpp" />
    <ClCompile Include="source\LoadGuildBattleInfoCRFWorldDatabaseQEAA_NKKPEAU_wo_1404A25D0.cpp" />
    <ClCompile Include="source\LoadGuildBattleScheduleInfoCRFWorldDatabaseQEAAEII_1404A15E0.cpp" />
    <ClCompile Include="source\LoadINICGuildBattleControllerAEAA_NAEAIAEAH111Z_1403D7BB0.cpp" />
    <ClCompile Include="source\LoadNationalPriceCashItemRemoteStoreAEAA_NAEAVCRec_1402F4C90.cpp" />
    <ClCompile Include="source\LoadTodayScheduleCGuildBattleReservedScheduleListM_1403CDD00.cpp" />
    <ClCompile Include="source\LoadTomorrowScheduleCGuildBattleReservedScheduleLi_1403CDDA0.cpp" />
    <ClCompile Include="source\load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.cpp" />
    <ClCompile Include="source\Load_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7F10.cpp" />
    <ClCompile Include="source\load_cde_iniCashItemRemoteStoreQEAAXPEAU_cash_disc_1402F60C0.cpp" />
    <ClCompile Include="source\Load_Conditional_EventCashItemRemoteStoreQEAAXXZ_1402FC330.cpp" />
    <ClCompile Include="source\load_con_event_iniCashItemRemoteStoreQEAAXPEAU_con_1402FBC10.cpp" />
    <ClCompile Include="source\Load_Event_INICashItemRemoteStoreQEAAXPEAU_cash_ev_1402F99B0.cpp" />
    <ClCompile Include="source\Load_LimitedSale_Event_INICashItemRemoteStoreQEAAX_1402FD4A0.cpp" />
    <ClCompile Include="source\LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_1403CEAC0.cpp" />
    <ClCompile Include="source\LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZZ_1403CEB50.cpp" />
    <ClCompile Include="source\LogCGuildBattleStateGUILD_BATTLEIEAAXPEADZ_1403DEE60.cpp" />
    <ClCompile Include="source\LogCGuildBattleStateListGUILD_BATTLEIEAAXPEADZ_1403DF340.cpp" />
    <ClCompile Include="source\LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_1403CEE40.cpp" />
    <ClCompile Include="source\LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZ_1403CEED0.cpp" />
    <ClCompile Include="source\LogCNormalGuildBattleStateGUILD_BATTLEIEAAXPEAVCNo_1403F04D0.cpp" />
    <ClCompile Include="source\LogCNormalGuildBattleStateRoundGUILD_BATTLEIEAAXPE_1403F1170.cpp" />
    <ClCompile Include="source\log_about_cash_eventCashItemRemoteStoreQEAAXPEADPE_1402F73A0.cpp" />
    <ClCompile Include="source\LoopCGuildBattleControllerQEAAXXZ_1403D6760.cpp" />
    <ClCompile Include="source\LoopCGuildBattleReservedScheduleGUILD_BATTLEQEAA_N_1403DAD80.cpp" />
    <ClCompile Include="source\LoopCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DC0D0.cpp" />
    <ClCompile Include="source\LoopCGuildBattleScheduleManagerGUILD_BATTLEQEAAXXZ_1403DCD30.cpp" />
    <ClCompile Include="source\LoopCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildBa_14007F780.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D4110.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVCN_14007FBE0.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVCG_1403F03F0.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleStateInBattleGUILD_BATTLEUEA_140080070.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleStateRoundGUILD_BATTLEMEAAHP_1403F31A0.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleStateRoundGUILD_BATTLEUEAAHP_1403F1090.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleStateRoundProcessGUILD_BATTL_1403F1A00.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleStateRoundReturnStartPosGUIL_1403F1DA0.cpp" />
    <ClCompile Include="source\LoopCNormalGuildBattleStateRoundStartGUILD_BATTLEM_1403F15E0.cpp" />
    <ClCompile Include="source\loop_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F6920.cpp" />
    <ClCompile Include="source\Loop_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7E80.cpp" />
    <ClCompile Include="source\Loop_Check_Total_SellingCashItemRemoteStoreQEAAXXZ_1402FB620.cpp" />
    <ClCompile Include="source\Loop_ContEventCashItemRemoteStoreQEAAXXZ_1402FB5D0.cpp" />
    <ClCompile Include="source\Loop_TatalCashEventCashItemRemoteStoreQEAAXXZ_1402FB560.cpp" />
    <ClCompile Include="source\MakePageCPossibleBattleGuildListManagerGUILD_BATTL_1403C9D70.cpp" />
    <ClCompile Include="source\ManageAcceptORRefuseGuildBattleCGuildQEAAE_NZ_140259EF0.cpp" />
    <ClCompile Include="source\ManageProposeGuildBattleCGuildQEAAEKKKKZ_1402593D0.cpp" />
    <ClCompile Include="source\max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB430.cpp" />
    <ClCompile Include="source\max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB4A0.cpp" />
    <ClCompile Include="source\max_sizeallocatorVCGuildBattleRewardItemGUILD_BATT_1403D22F0.cpp" />
    <ClCompile Include="source\max_sizevectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D1670.cpp" />
    <ClCompile Include="source\ModifyMonsterAttFcCMonsterAttackIEAAMMZ_1401615B0.cpp" />
    <ClCompile Include="source\MoveMapCNormalGuildBattleGuildGUILD_BATTLEQEAAXIPE_1403E0F60.cpp" />
    <ClCompile Include="source\MoveMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA__1403E1140.cpp" />
    <ClCompile Include="source\NetCloseCNormalGuildBattleGuildGUILD_BATTLEQEAA_N__1403E13B0.cpp" />
    <ClCompile Include="source\NetCloseCNormalGuildBattleGuildMemberGUILD_BATTLEQ_1403DFAE0.cpp" />
    <ClCompile Include="source\NextCGuildBattleReservedScheduleGUILD_BATTLEAEAA_N_1403DB7F0.cpp" />
    <ClCompile Include="source\NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_1403DF220.cpp" />
    <ClCompile Include="source\NotifyBallPositionCNormalGuildBattleGUILD_BATTLEQE_1403E53E0.cpp" />
    <ClCompile Include="source\NotifyBattleResultCNormalGuildBattleGUILD_BATTLEQE_1403E6A90.cpp" />
    <ClCompile Include="source\NotifyBeforeStartCNormalGuildBattleGUILD_BATTLEQEA_1403E50E0.cpp" />
    <ClCompile Include="source\NotifyCommitteeMemberPositionCNormalGuildBattleGUI_1403E55B0.cpp" />
    <ClCompile Include="source\NotifyDestoryBallCNormalGuildBattleGUILD_BATTLEQEA_1403E5510.cpp" />
    <ClCompile Include="source\NotifyLeftMinuteBeforeStartCNormalGuildBattleGuild_1403E2050.cpp" />
    <ClCompile Include="source\NotifyPassGravityStoneLimitTimeCNormalGuildBattleG_1403E5700.cpp" />
    <ClCompile Include="source\OnlyOnceInitMonsterSFContDamageToleracneQEAAXPEAVC_140157ED0.cpp" />
    <ClCompile Include="source\OnToolHitTestCWndUEBA_JVCPointPEAUtagTOOLINFOAZ_0_1404DBC28.cpp" />
    <ClCompile Include="source\On_HS_SCENE_BATTLE_END_WAIT_TIMECHolyStoneSystemIE_14027C3E0.cpp" />
    <ClCompile Include="source\On_HS_SCENE_BATTLE_TIMECHolyStoneSystemIEAAXXZ_14027C120.cpp" />
    <ClCompile Include="source\On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSystem_14027C540.cpp" />
    <ClCompile Include="source\On_HS_SCENE_KEEPER_DEATTACKABLE_TIMECHolyStoneSyst_14027C610.cpp" />
    <ClCompile Include="source\OutDestGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY__1401F4970.cpp" />
    <ClCompile Include="source\OutSrcGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_S_1401F47E0.cpp" />
    <ClCompile Include="source\ProcCheckGetGravityStoneCNormalGuildBattleManagerG_1403D52C0.cpp" />
    <ClCompile Include="source\ProcCheckGoalCNormalGuildBattleManagerGUILD_BATTLE_1403D5360.cpp" />
    <ClCompile Include="source\ProcCheckTakeGravityStoneCNormalGuildBattleManager_1403D5220.cpp" />
    <ClCompile Include="source\ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_1403DA3B0.cpp" />
    <ClCompile Include="source\ProcessCGuildBattleStateListGUILD_BATTLEQEAAXPEAVC_1403DF090.cpp" />
    <ClCompile Include="source\ProcessCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403D92F0.cpp" />
    <ClCompile Include="source\ProcJoinCNormalGuildBattleManagerGUILD_BATTLEIEAAH_1403D5190.cpp" />
    <ClCompile Include="source\PushClearGuildBattleRankCGuildBattleControllerQEAA_1402CFCA0.cpp" />
    <ClCompile Include="source\PushClearGuildBattleRankCGuildBattleRankManagerGUI_1403CB380.cpp" />
    <ClCompile Include="source\PushCreateGuildBattleRankTableCGuildBattleControll_1402CFC50.cpp" />
    <ClCompile Include="source\PushCreateGuildBattleRankTableCGuildBattleRankMana_1403CB250.cpp" />
    <ClCompile Include="source\PushDQSCGuildBattleReservedScheduleListManagerGUIL_1403CD690.cpp" />
    <ClCompile Include="source\PushDQSClearCGuildBattleReservedScheduleMapGroupGU_1403DC2E0.cpp" />
    <ClCompile Include="source\PushDQSDataCNormalGuildBattleManagerGUILD_BATTLEIE_1403D5050.cpp" />
    <ClCompile Include="source\PushDQSDestGuildOutputGuildBattleCostCGuildQEAAXXZ_140258020.cpp" />
    <ClCompile Include="source\PushDQSDrawRankCNormalGuildBattleGUILD_BATTLEIEAAX_1403E7B70.cpp" />
    <ClCompile Include="source\PushDQSInGuildBattleCostCGuildQEAAXXZ_140257DB0.cpp" />
    <ClCompile Include="source\PushDQSInGuildBattleRewardMoneyCGuildQEAAXXZ_140258180.cpp" />
    <ClCompile Include="source\PushDQSPvpPointCNormalGuildBattleGuildMemberGUILD__1403E03F0.cpp" />
    <ClCompile Include="source\PushDQSSourceGuildOutputGuildBattleCostCGuildQEAAX_140257EC0.cpp" />
    <ClCompile Include="source\PushDQSWinLoseRankCNormalGuildBattleGUILD_BATTLEIE_1403E7C40.cpp" />
    <ClCompile Include="source\PushItemCObjectListQEAA_NPEAU_object_list_pointZ_140189C30.cpp" />
    <ClCompile Include="source\PushItem_TRAP_PARAMQEAA_NPEAVCTrapKZ_1400791F0.cpp" />
    <ClCompile Include="source\RCTopGoalPrediCateCNormalGuildBattleGuildGUILD_BAT_1403EB720.cpp" />
    <ClCompile Include="source\RCTopKillPrediCateCNormalGuildBattleGuildGUILD_BAT_1403EB5D0.cpp" />
    <ClCompile Include="source\RegenBallCNormalGuildBattleFieldGUILD_BATTLEQEAAHX_1403ECBE0.cpp" />
    <ClCompile Include="source\RequestSubProcSetRaceBattleResultCRaceBuffByHolyQu_1403B66E0.cpp" />
    <ClCompile Include="source\ReturnBindPosAllCNormalGuildBattleGuildGUILD_BATTL_1403E1CC0.cpp" />
    <ClCompile Include="source\ReturnBindPosCNormalGuildBattleGuildMemberGUILD_BA_1403E0020.cpp" />
    <ClCompile Include="source\ReturnHQPosAllCNormalGuildBattleGuildGUILD_BATTLEQ_1403E1B50.cpp" />
    <ClCompile Include="source\ReturnStartPosAllCNormalGuildBattleGuildGUILD_BATT_1403E1D50.cpp" />
    <ClCompile Include="source\ReturnStartPosCNormalGuildBattleGuildMemberGUILD_B_1403DFFC0.cpp" />
    <ClCompile Include="source\RewardGuildBattleMoneyCNormalGuildBattleGUILD_BATT_1403E6840.cpp" />
    <ClCompile Include="source\RewardItemCNormalGuildBattleGuildGUILD_BATTLEQEAAX_1403E19B0.cpp" />
    <ClCompile Include="source\RewardItemCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E6960.cpp" />
    <ClCompile Include="source\rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD_K0_14023E160.cpp" />
    <ClCompile Include="source\SaveCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403E37C0.cpp" />
    <ClCompile Include="source\SaveCNormalGuildBattleManagerGUILD_BATTLEQEAA_NKZ_1403D3D10.cpp" />
    <ClCompile Include="source\SaveINICGuildBattleControllerAEAA_NXZ_1403D7960.cpp" />
    <ClCompile Include="source\SearchItemAddSpeedCEquipItemSFAgentIEAAMPEAU_db_co_1401217E0.cpp" />
    <ClCompile Include="source\SectorDamageProcCAttackIEAAXHHHHHH_NZ_14016CDD0.cpp" />
    <ClCompile Include="source\SelectGuildBattleRankListCGuildBattleRankManagerGU_1403CAF70.cpp" />
    <ClCompile Include="source\SelectGuildBattleRankListCRFWorldDatabaseQEAA_NEPE_1404A2B70.cpp" />
    <ClCompile Include="source\SelectGuildBattleRankRecordCRFWorldDatabaseQEAA_NK_1404A3460.cpp" />
    <ClCompile Include="source\SelectGuildBattleRerservedListCRFWorldDatabaseQEAA_1404A38C0.cpp" />
    <ClCompile Include="source\SelectGuildBattleScheduleInfoIDCRFWorldDatabaseQEA_1404A1CF0.cpp" />
    <ClCompile Include="source\SelectRowCountGuildBattleInfoCRFWorldDatabaseQEAAH_1404A20D0.cpp" />
    <ClCompile Include="source\SelectRowCountGuildBattleScheduleInfoCRFWorldDatab_1404A12B0.cpp" />
    <ClCompile Include="source\Select_BattleResultLogLatestCRFWorldDatabaseQEAAEP_1404B0DE0.cpp" />
    <ClCompile Include="source\Select_BattleTournamentInfoCRFWorldDatabaseQEAA_NP_1404C5EA0.cpp" />
    <ClCompile Include="source\Select_FailBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C12B0.cpp" />
    <ClCompile Include="source\Select_GuildBattleRecordCRFWorldDatabaseQEAA_NKPEA_1404AA8F0.cpp" />
    <ClCompile Include="source\Select_LoseBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C0B30.cpp" />
    <ClCompile Include="source\Select_WinBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C0EF0.cpp" />
    <ClCompile Include="source\SellCashItemRemoteStoreQEAA_NGPEADZ_1402F5CB0.cpp" />
    <ClCompile Include="source\SetActiveSuccCAttackQEAAX_NZ_14008E710.cpp" />
    <ClCompile Include="source\SetAttackPartCGameObjectUEAAXHZ_14012C700.cpp" />
    <ClCompile Include="source\SetBattleModeTimeCMonsterAIQEAAXKZ_1401555F0.cpp" />
    <ClCompile Include="source\SetBattleStateCNormalGuildBattleGuildMemberGUILD_B_1403DFC80.cpp" />
    <ClCompile Include="source\SetBattleTimeCNormalGuildBattleStateInBattleGUILD__1403D90D0.cpp" />
    <ClCompile Include="source\SetBattleTimeCNormalGuildBattleStateListGUILD_BATT_1403D9070.cpp" />
    <ClCompile Include="source\SetCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA__1403CE160.cpp" />
    <ClCompile Include="source\SetCGuildBattleScheduleGUILD_BATTLEQEAAEKKZ_1403D9B90.cpp" />
    <ClCompile Include="source\SetColorInxCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403EB190.cpp" />
    <ClCompile Include="source\SetCombatStateCMonsterQEAAXEZ_140143830.cpp" />
    <ClCompile Include="source\SetCopmlteGuildBattleSuggestCGuildQEAAXXZ_1402582E0.cpp" />
    <ClCompile Include="source\SetDamageAbleStateCHolyKeeperQEAAX_NZ_140284770.cpp" />
    <ClCompile Include="source\SetDelay_ATTACK_DELAY_CHECKERQEAAXKZ_14008E760.cpp" />
    <ClCompile Include="source\SetGotoRegenStartCNormalGuildBattleGUILD_BATTLEQEA_1403F3240.cpp" />
    <ClCompile Include="source\SetGotoRegenStateCNormalGuildBattleStateInBattleGU_1403F3300.cpp" />
    <ClCompile Include="source\SetGotoRegenStateCNormalGuildBattleStateListGUILD__1403F3290.cpp" />
    <ClCompile Include="source\SetGuildBattleMatterCGuildQEAAXKKKKZ_140259E40.cpp" />
    <ClCompile Include="source\SetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAXPE_1403EB0A0.cpp" />
    <ClCompile Include="source\SetItemCGuildBattleRewardItemGUILD_BATTLEIEAA_NPEA_1403C92D0.cpp" />
    <ClCompile Include="source\SetLoadCBattleTournamentInfoQEAAX_NZ_1403FEB50.cpp" />
    <ClCompile Include="source\SetNextDiscountEventTimeCashItemRemoteStoreQEAA_NX_1402F7900.cpp" />
    <ClCompile Include="source\SetNextEventCNormalGuildBattleManagerGUILD_BATTLEQ_1403DED80.cpp" />
    <ClCompile Include="source\SetNextEventTimeCashItemRemoteStoreQEAA_NEZ_1402FCD30.cpp" />
    <ClCompile Include="source\SetNextEvnetCGuildBattleScheduleManagerGUILD_BATTL_1403DD6C0.cpp" />
    <ClCompile Include="source\SetNextStateCGuildBattleStateListGUILD_BATTLEMEAAX_14007F830.cpp" />
    <ClCompile Include="source\SetNextStateCNormalGuildBattleStateListGUILD_BATTL_140080340.cpp" />
    <ClCompile Include="source\SetNextStateCNormalGuildBattleStateRoundListGUILD__140080040.cpp" />
    <ClCompile Include="source\SetOrderViewAttackStateCPvpOrderViewQEAAXXZ_1403F7AA0.cpp" />
    <ClCompile Include="source\SetOrderViewDamagedStateCPvpOrderViewQEAAXXZ_1403F7AF0.cpp" />
    <ClCompile Include="source\SetPortalInxCNormalGuildBattleFieldGUILD_BATTLEAEA_1403EDD60.cpp" />
    <ClCompile Include="source\SetProcStateCGuildBattleScheduleGUILD_BATTLEQEAAXX_1403DEBB0.cpp" />
    <ClCompile Include="source\SetReadyCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403EB0D0.cpp" />
    <ClCompile Include="source\SetReadyStateCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E3B30.cpp" />
    <ClCompile Include="source\SetReadyStateCNormalGuildBattleManagerGUILD_BATTLE_1403D5480.cpp" />
    <ClCompile Include="source\SetReStartFlagCNormalGuildBattleGuildGUILD_BATTLEQ_1403E0900.cpp" />
    <ClCompile Include="source\SetReStartFlagCNormalGuildBattleGuildMemberGUILD_B_1403EADB0.cpp" />
    <ClCompile Include="source\SetSFDamageToleracne_VariationMonsterSFContDamageT_140158000.cpp" />
    <ClCompile Include="source\SetStateListCGuildBattleScheduleGUILD_BATTLEQEAAXP_1403D9150.cpp" />
    <ClCompile Include="source\SetStaticMemberCAttackSAXPEAVCRecordDataZ_14016D860.cpp" />
    <ClCompile Include="source\SetWaitCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403EB110.cpp" />
    <ClCompile Include="source\SetWinnerInfoCBattleTournamentInfoQEAA_NKPEADEZ_1403FEB70.cpp" />
    <ClCompile Include="source\Set_CashEvent_StatusCashItemRemoteStoreQEAAXEEZ_1402FAB90.cpp" />
    <ClCompile Include="source\set_cde_statusCashItemRemoteStoreQEAAXEZ_1402F72D0.cpp" />
    <ClCompile Include="source\Set_Conditional_Evnet_StatusCashItemRemoteStoreQEA_1402FC3E0.cpp" />
    <ClCompile Include="source\Set_DB_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_1402FDAD0.cpp" />
    <ClCompile Include="source\Set_FROMDB_LimitedSale_EventCashItemRemoteStoreQEA_1402FDE70.cpp" />
    <ClCompile Include="source\Set_LimitedSale_countCashItemRemoteStoreQEAAXEKZ_1402FDA00.cpp" />
    <ClCompile Include="source\Set_LimitedSale_DCKCashItemRemoteStoreQEAAXEEZ_1402FD750.cpp" />
    <ClCompile Include="source\Set_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_1402FDFD0.cpp" />
    <ClCompile Include="source\Set_LimitedSale_Event_IniCashItemRemoteStoreQEAAXP_1402FD780.cpp" />
    <ClCompile Include="source\sizevectorVCGuildBattleRewardItemGUILD_BATTLEVallo_1403D2270.cpp" />
    <ClCompile Include="source\size_attack_count_result_zoclQEAAHXZ_1400EEEF0.cpp" />
    <ClCompile Include="source\size_attack_force_result_zoclQEAAHXZ_1400EEDF0.cpp" />
    <ClCompile Include="source\size_attack_gen_result_zoclQEAAHXZ_1400EECF0.cpp" />
    <ClCompile Include="source\size_attack_keeper_inform_zoclQEAAHXZ_140136C20.cpp" />
    <ClCompile Include="source\size_attack_selfdestruction_result_zoclQEAAHXZ_1400EEF70.cpp" />
    <ClCompile Include="source\size_attack_siege_result_zoclQEAAHXZ_1400EEFF0.cpp" />
    <ClCompile Include="source\size_attack_trap_inform_zoclQEAAHXZ_140141420.cpp" />
    <ClCompile Include="source\size_attack_unit_result_zoclQEAAHXZ_1400EEE70.cpp" />
    <ClCompile Include="source\size_guild_battle_get_gravity_stone_result_zoclQEA_1403EAFE0.cpp" />
    <ClCompile Include="source\size_guild_battle_goal_result_zoclQEAAHXZ_1403EB3A0.cpp" />
    <ClCompile Include="source\size_guild_battle_rank_list_result_zoclQEAAHXZ_1403D0930.cpp" />
    <ClCompile Include="source\size_guild_battle_reserved_schedule_result_zoclQEA_1403D0970.cpp" />
    <ClCompile Include="source\size_guild_battle_suggest_request_result_zoclQEAAH_14025D5F0.cpp" />
    <ClCompile Include="source\size_notify_not_use_premium_cashitem_zoclQEAAHXZ_1400F0850.cpp" />
    <ClCompile Include="source\size_param_cashitem_dblogQEAAHXZ_140304D90.cpp" />
    <ClCompile Include="source\size_personal_automine_attacked_zoclQEAAHXZ_1402DE340.cpp" />
    <ClCompile Include="source\size_possible_battle_guild_list_result_zoclQEAAHXZ_1403D0860.cpp" />
    <ClCompile Include="source\size_qry_case_addguildbattlescheduleQEAAHXZ_1403D93C0.cpp" />
    <ClCompile Include="source\size_qry_case_dest_guild_out_guildbattlecostQEAAHX_14025D5D0.cpp" />
    <ClCompile Include="source\size_qry_case_in_guildbattlecostQEAAHXZ_14025D5B0.cpp" />
    <ClCompile Include="source\size_qry_case_in_guildbattlerewardmoneyQEAAHXZ_14025D5E0.cpp" />
    <ClCompile Include="source\size_qry_case_loadguildbattlerankQEAAHXZ_140207580.cpp" />
    <ClCompile Include="source\size_qry_case_load_guildbattle_totalrecordQEAAHXZ_140207590.cpp" />
    <ClCompile Include="source\size_qry_case_src_guild_out_guildbattlecostQEAAHXZ_14025D5C0.cpp" />
    <ClCompile Include="source\size_qry_case_updateclearguildbattleDayInfoQEAAHXZ_1403DECE0.cpp" />
    <ClCompile Include="source\size_qry_case_updatedrawguildbattlerankQEAAHXZ_1403EB3D0.cpp" />
    <ClCompile Include="source\size_qry_case_updatewinloseguildbattlerankQEAAHXZ_1403EB3E0.cpp" />
    <ClCompile Include="source\SkipWhiteSpaceTiXmlBaseKAPEBDPEBDW4TiXmlEncodingZ_140530B60.cpp" />
    <ClCompile Include="source\SrcGuildIsAvailableBattleRequestStateCGuildQEAAEXZ_1402578D0.cpp" />
    <ClCompile Include="source\start_casheventCashItemRemoteStoreQEAA_NHHHHEZ_1402FB0D0.cpp" />
    <ClCompile Include="source\start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590.cpp" />
    <ClCompile Include="source\start_coneventCashItemRemoteStoreQEAA_NHHEZ_1402FCAE0.cpp" />
    <ClCompile Include="source\StockOldInfoCNormalGuildBattleGuildMemberGUILD_BAT_1403DFB40.cpp" />
    <ClCompile Include="source\TakeGravityStoneCNormalGuildBattleGUILD_BATTLEQEAA_1403E4A60.cpp" />
    <ClCompile Include="source\unchecked_copyPEAVCGuildBattleRewardItemGUILD_BATT_1403D2590.cpp" />
    <ClCompile Include="source\unchecked_uninitialized_copyPEAVCGuildBattleReward_1403D31F0.cpp" />
    <ClCompile Include="source\unchecked_uninitialized_fill_nPEAVCGuildBattleRewa_1403D2910.cpp" />
    <ClCompile Include="source\UpdateCGuildBattleRankManagerGUILD_BATTLEQEAA_NEPE_1403CA680.cpp" />
    <ClCompile Include="source\UpdateClearGuildBattleDayInfoCNormalGuildBattleMan_1403D42E0.cpp" />
    <ClCompile Include="source\UpdateClearGuildBattleInfoCRFWorldDatabaseQEAA_NKK_1404A2AC0.cpp" />
    <ClCompile Include="source\UpdateClearGuildBattleRankCRFWorldDatabaseQEAA_NXZ_1404A31B0.cpp" />
    <ClCompile Include="source\UpdateClearGuildBattleScheduleDayInfoCGuildBattleS_1403DD8B0.cpp" />
    <ClCompile Include="source\UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A1C40.cpp" />
    <ClCompile Include="source\UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A2020.cpp" />
    <ClCompile Include="source\UpdateClearRerservedDayInfoCGuildBattleControllerQ_1403D6CF0.cpp" />
    <ClCompile Include="source\UpdateDayChangedWorkCGuildBattleScheduleManagerGUI_1403DD650.cpp" />
    <ClCompile Include="source\UpdateDrawCGuildBattleControllerQEAA_NEKEKZ_1403D6C80.cpp" />
    <ClCompile Include="source\UpdateDrawCGuildBattleRankManagerGUILD_BATTLEQEAA__1403CB120.cpp" />
    <ClCompile Include="source\UpdateDrawGuildBattleResultCRFWorldDatabaseQEAA_NK_1404A33B0.cpp" />
    <ClCompile Include="source\UpdateGoalCntCCurrentGuildBattleInfoManagerGUILD_B_1403CE340.cpp" />
    <ClCompile Include="source\UpdateGuildBattleDrawRankInfoCMainThreadQEAAXPEAU__1401F4470.cpp" />
    <ClCompile Include="source\UpdateGuildBattleInfoCRFWorldDatabaseQEAA_NKKKKEZ_1404A29E0.cpp" />
    <ClCompile Include="source\UpdateGuildBattleScheduleInfoCRFWorldDatabaseQEAA__1404A1AA0.cpp" />
    <ClCompile Include="source\UpdateGuildBattleWinCntCGuildQEAAXKKKZ_140253190.cpp" />
    <ClCompile Include="source\UpdateGuildBattleWinLoseRankInfoCMainThreadQEAAXPE_1401F4290.cpp" />
    <ClCompile Include="source\UpdateGuildListCPossibleBattleGuildListManagerGUIL_1403C9B90.cpp" />
    <ClCompile Include="source\UpdateLoadGuildBattleRankCMainThreadQEAAXPEAU_DB_Q_1401F4650.cpp" />
    <ClCompile Include="source\UpdateLoseGuildBattleResultCRFWorldDatabaseQEAA_NK_1404A3300.cpp" />
    <ClCompile Include="source\UpdateMonsterSFContDamageToleracneQEAAXXZ_140158080.cpp" />
    <ClCompile Include="source\UpdatePossibleBattleGuildListCGuildBattleControlle_1403D6370.cpp" />
    <ClCompile Include="source\UpdateRankCGuildBattleControllerQEAA_NEPEAEZ_1403D6D80.cpp" />
    <ClCompile Include="source\UpdateReservedGuildBattleScheduleCGuildBattleContr_1403D6DD0.cpp" />
    <ClCompile Include="source\UpdateReservedGuildBattleScheduleCMainThreadQEAAXP_1401F4BE0.cpp" />
    <ClCompile Include="source\UpdateReservedSheduleCGuildBattleReservedScheduleL_1403CD720.cpp" />
    <ClCompile Include="source\UpdateScoreCCurrentGuildBattleInfoManagerGUILD_BAT_1403CE2B0.cpp" />
    <ClCompile Include="source\UpdateScoreCNormalGuildBattleGuildGUILD_BATTLEIEAA_1403EAE20.cpp" />
    <ClCompile Include="source\UpdateTodayScheduleCGuildBattleReservedScheduleLis_1403CD9C0.cpp" />
    <ClCompile Include="source\UpdateTomorrowCompleteCGuildBattleReservedSchedule_1403CD7A0.cpp" />
    <ClCompile Include="source\UpdateTomorrowScheduleCGuildBattleReservedSchedule_1403CDB60.cpp" />
    <ClCompile Include="source\UpdateUseFieldCGuildBattleReservedScheduleGUILD_BA_1403DB8D0.cpp" />
    <ClCompile Include="source\UpdateUseFlagCGuildBattleReservedScheduleGUILD_BAT_1403DB4F0.cpp" />
    <ClCompile Include="source\UpdateUseFlagCGuildBattleReservedScheduleMapGroupG_1403DC560.cpp" />
    <ClCompile Include="source\UpdateUseFlagCGuildBattleScheduleManagerGUILD_BATT_1403DD120.cpp" />
    <ClCompile Include="source\UpdateWinGuildBattleResultCRFWorldDatabaseQEAA_NKK_1404A3250.cpp" />
    <ClCompile Include="source\UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_1403D6C10.cpp" />
    <ClCompile Include="source\UpdateWinLoseCGuildBattleRankManagerGUILD_BATTLEQE_1403CAFF0.cpp" />
    <ClCompile Include="source\Update_BattleResultLogBattleResultAndPvpPointCRFWo_1404B1100.cpp" />
    <ClCompile Include="source\Update_CristalBattleCharInfoCRFWorldDatabaseQEAA_N_1404A9A90.cpp" />
    <ClCompile Include="source\update_cristalbattle_dateCRFWorldDatabaseQEAA_NKEZ_1404C5160.cpp" />
    <ClCompile Include="source\Update_IncreaseWeeklyGuildGuildBattlePvpPointSumCR_1404A70F0.cpp" />
    <ClCompile Include="source\update_iniCashItemRemoteStoreQEAAXPEAU_cash_discou_1402F7160.cpp" />
    <ClCompile Include="source\Update_INICashItemRemoteStoreQEAAXPEAU_cash_event__1402F8C40.cpp" />
    <ClCompile Include="source\WriteLogPer10Min_CombatCHolyStoneSystemIEAAXXZ_1402804E0.cpp" />
    <ClCompile Include="source\_AllocateVCGuildBattleRewardItemGUILD_BATTLEstdYAP_1403D26D0.cpp" />
    <ClCompile Include="source\_Assign_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D0D70.cpp" />
    <ClCompile Include="source\_buybygold_buy_single_item_calc_price_discountCash_1402FFCE0.cpp" />
    <ClCompile Include="source\_buybygold_buy_single_item_calc_price_limitsaleCas_1402FFE60.cpp" />
    <ClCompile Include="source\_buybygold_buy_single_item_calc_price_one_n_oneCas_1402FFDB0.cpp" />
    <ClCompile Include="source\_buybygold_buy_single_item_setbuydblogCashItemRemo_140300760.cpp" />
    <ClCompile Include="source\_BuyvectorVCGuildBattleRewardItemGUILD_BATTLEVallo_1403D12E0.cpp" />
    <ClCompile Include="source\_CalcForceAttPntCAttackIEAAH_NZ_14016AF70.cpp" />
    <ClCompile Include="source\_CalcGenAttPntCAttackIEAAH_NZ_14016AA00.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreBuyByCash__1_dtor0_1402FEC90.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreBuyByGold__1_dtor0_1402FEF70.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor0_1402F3920.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor1_1402F3960.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor2_1402F3990.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor3_1402F39C0.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor4_1402F39F0.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor5_1402F3A30.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreCashItemRemoteStore__1_dtor6_1402F3A60.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreCheatLoadCashAmount__1_dtor0_1402F5B20.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreFindCashRec__1_dtor0_1402F4A30.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreFindCashRec__1_dtor1_1402F4A60.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreGoodsListBuyByCash__1_dtor0_140300C30.cpp" />
    <ClCompile Include="source\_CashItemRemoteStoreInstance__1_dtor0_1400798A0.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor0_1402F3B80.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor1_1402F3BC0.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor2_1402F3BF0.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor3_1402F3C20.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor4_1402F3C50.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor5_1402F3C90.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_CashItemRemoteStore__1_dtor6_1402F3CC0.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_MakeLinkTable__1_dtor0_1402F48C0.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore_ReadGoods__1_dtor0_1402F4C60.cpp" />
    <ClCompile Include="source\_CashItemRemoteStore__CheckGoods__1_dtor0_1402F4620.cpp" />
    <ClCompile Include="source\_CGuildBattleControllerInstance__1_dtor0_1403D5770.cpp" />
    <ClCompile Include="source\_check_buyitemCashItemRemoteStoreAEAAAW4CS_RCODEEP_1402F4FA0.cpp" />
    <ClCompile Include="source\_complete_tsk_cashitem_buy_dblogCashDbWorkerIEAAXP_1402F0210.cpp" />
    <ClCompile Include="source\_ConstructVCGuildBattleRewardItemGUILD_BATTLEV12st_1403D3120.cpp" />
    <ClCompile Include="source\_Copy_backward_optPEAVCGuildBattleRewardItemGUILD__1403D3060.cpp" />
    <ClCompile Include="source\_Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEPEA_1403D2A80.cpp" />
    <ClCompile Include="source\_db_Load_BattleTournamentInfoCMainThreadAEAAXXZ_1401B7210.cpp" />
    <ClCompile Include="source\_db_load_losebattlecountCMainThreadAEAAEKPEAU_AVAT_1401A99F0.cpp" />
    <ClCompile Include="source\_delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10.cpp" />
    <ClCompile Include="source\_DestroyVCGuildBattleRewardItemGUILD_BATTLEstdYAXP_1403D31E0.cpp" />
    <ClCompile Include="source\_DestroyvectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D17A0.cpp" />
    <ClCompile Include="source\_Destroy_rangeVCGuildBattleRewardItemGUILD_BATTLEV_1403D2650.cpp" />
    <ClCompile Include="source\_Destroy_rangeVCGuildBattleRewardItemGUILD_BATTLEV_1403D2B40.cpp" />
    <ClCompile Include="source\_dynamic_initializer_for__CAttacks_DefParam___1406DC660.cpp" />
    <ClCompile Include="source\_dynamic_initializer_for__GUILD_BATTLECGuildBattle_1406E4080.cpp" />
    <ClCompile Include="source\_ECNormalGuildBattleFieldGUILD_BATTLEQEAAPEAXIZ_1403F0220.cpp" />
    <ClCompile Include="source\_ECNormalGuildBattleStateListGUILD_BATTLEQEAAPEAXI_1403F33B0.cpp" />
    <ClCompile Include="source\_ECReservedGuildScheduleMapGroupGUILD_BATTLEQEAAPE_1403D09B0.cpp" />
    <ClCompile Include="source\_FillPEAVCGuildBattleRewardItemGUILD_BATTLEV12stdY_1403D2C70.cpp" />
    <ClCompile Include="source\_GCCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAPE_1403D0B40.cpp" />
    <ClCompile Include="source\_GCGuildBattleControllerIEAAPEAXIZ_1403D9720.cpp" />
    <ClCompile Include="source\_GCGuildBattleLoggerGUILD_BATTLEIEAAPEAXIZ_1403D0BB0.cpp" />
    <ClCompile Include="source\_GCGuildBattleRankManagerGUILD_BATTLEIEAAPEAXIZ_1403D08C0.cpp" />
    <ClCompile Include="source\_GCGuildBattleReservedScheduleGUILD_BATTLEQEAAPEAX_1403DEBD0.cpp" />
    <ClCompile Include="source\_GCGuildBattleReservedScheduleListManagerGUILD_BAT_1403D0AA0.cpp" />
    <ClCompile Include="source\_GCGuildBattleScheduleGUILD_BATTLEQEAAPEAXIZ_1403DE8B0.cpp" />
    <ClCompile Include="source\_GCGuildBattleScheduleManagerGUILD_BATTLEIEAAPEAXI_1403DECF0.cpp" />
    <ClCompile Include="source\_GCGuildBattleSchedulePoolGUILD_BATTLEIEAAPEAXIZ_1403DE920.cpp" />
    <ClCompile Include="source\_GCGuildBattleSchedulerGUILD_BATTLEIEAAPEAXIZ_1403DEDB0.cpp" />
    <ClCompile Include="source\_GCNormalGuildBattleFieldListGUILD_BATTLEIEAAPEAXI_1403F02F0.cpp" />
    <ClCompile Include="source\_GCNormalGuildBattleGUILD_BATTLEQEAAPEAXIZ_1403D8F40.cpp" />
    <ClCompile Include="source\_GCNormalGuildBattleManagerGUILD_BATTLEIEAAPEAXIZ_1403D8FB0.cpp" />
    <ClCompile Include="source\_GCNormalGuildBattleStateListPoolGUILD_BATTLEIEAAP_1403F3480.cpp" />
    <ClCompile Include="source\_GCPossibleBattleGuildListManagerGUILD_BATTLEIEAAP_1403D0770.cpp" />
    <ClCompile Include="source\_GCRFCashItemDatabaseUEAAPEAXIZ_1402F2B40.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECCurrentGuildBattleInfoManagerInstanc_1403CDF50.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleLoggerInit__1_dtor0_1403CE9B0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleLoggerInstance__1_dtor0_1403CE820.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleRankManagerInstance__1_dt_1403CA400.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleReservedScheduleListManag_1403CD3F0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleReservedScheduleMapGroupI_1403DBD90.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleRewardItemManagerInstance_1403D9820.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleScheduleManagerInit__1_dt_1403DCD00.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleScheduleManagerInstance___1403DCAA0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleScheduleManager_CGuildBat_1403DC9D0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleSchedulePoolInit__1_dtor0_1403DA860.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleSchedulePoolInstance__1_d_1403DA610.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleSchedulerInstance__1_dtor_1403DD790.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleStateListLog__1_dtor0_1403DF440.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECGuildBattleStateLog__1_dtor0_1403DEF60.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2EF0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F20.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F50.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F80.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor0_1403EC510.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor1_1403EC540.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor2_1403EC570.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor3_1403EC5A0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor4_1403EC5D0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor5_1403EC600.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldListInit__1_dt_1403EE840.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldListInstance___1403EE3F0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleFieldLoadDummys__1__1403EDD30.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleGuildCNormalGuildBa_1403E0550.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleLoggerInit__1_dtor0_1403CED20.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleManagerInit__1_dtor_1403D38C0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleManagerInstance__1__1403D35C0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateInBattleCNorma_1403F09D0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateInBattle_CNorm_14007FE90.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2000.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2030.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2060.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2090.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F20C0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F20F0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2120.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListPoolInit___1403F2610.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateListPoolInstan_1403F2400.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F920.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F950.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F980.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F9B0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F9E0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007FA10.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007FA40.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2220.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2250.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2280.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FF70.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FFA0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FFD0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundProcessCN_1403F1830.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundProcessCN_1403F1860.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundProcess_C_1403F1940.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1BD0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1C00.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1CE0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundStartCNor_1403F1410.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundStartCNor_1403F1440.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattleStateRoundStart_CNo_1403F1520.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E3040.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E3070.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E30A0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECPossibleBattleGuildListManagerInit___1403C99C0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECPossibleBattleGuildListManagerInstan_1403C96E0.cpp" />
    <ClCompile Include="source\_GUILD_BATTLECReservedGuildScheduleDayGroupInit__1_1403CCE50.cpp" />
    <ClCompile Include="source\_InitLoggersCashItemRemoteStoreAEAA_NXZ_1402F3CF0.cpp" />
    <ClCompile Include="source\_Insert_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D1A90.cpp" />
    <ClCompile Include="source\_Iter_randomPEAVCGuildBattleRewardItemGUILD_BATTLE_1403D29C0.cpp" />
    <ClCompile Include="source\_MakeLinkTableCashItemRemoteStoreAEAA_NPEADHZ_1402F4650.cpp" />
    <ClCompile Include="source\_Max_elementPEAVCNormalGuildBattleGuildMemberGUILD_1403EB510.cpp" />
    <ClCompile Include="source\_Max_elementPEAVCNormalGuildBattleGuildMemberGUILD_1403EB660.cpp" />
    <ClCompile Include="source\_Move_backward_optPEAVCGuildBattleRewardItemGUILD__1403D2D70.cpp" />
    <ClCompile Include="source\_Move_catPEAVCGuildBattleRewardItemGUILD_BATTLEstd_1403D2D10.cpp" />
    <ClCompile Include="source\_Ptr_catPEAVCGuildBattleRewardItemGUILD_BATTLEPEAV_1403D2A20.cpp" />
    <ClCompile Include="source\_PushItemCutLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTDZ_14024AEB0.cpp" />
    <ClCompile Include="source\_PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTD_14024B300.cpp" />
    <ClCompile Include="source\_ReadGoodsCashItemRemoteStoreAEAA_NXZ_1402F4A90.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D0EE0.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D11F0.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1220.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1250.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1750.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D18A0.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1FE0.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D2010.cpp" />
    <ClCompile Include="source\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D2070.cpp" />
    <ClCompile Include="source\_std_Uninit_copy_GUILD_BATTLECGuildBattleRewardIte_1403D3330.cpp" />
    <ClCompile Include="source\_std_Uninit_fill_n_GUILD_BATTLECGuildBattleRewardI_1403D2EB0.cpp" />
    <ClCompile Include="source\_TidyvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D13E0.cpp" />
    <ClCompile Include="source\_UfillvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D2500.cpp" />
    <ClCompile Include="source\_UmovePEAVCGuildBattleRewardItemGUILD_BATTLEvector_1403D2780.cpp" />
    <ClCompile Include="source\_Unchecked_move_backwardPEAVCGuildBattleRewardItem_1403D2850.cpp" />
    <ClCompile Include="source\_Unchecked_uninitialized_movePEAVCGuildBattleRewar_1403D2BC0.cpp" />
    <ClCompile Include="source\_Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATTLE_1403D32A0.cpp" />
    <ClCompile Include="source\_Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BATT_1403D2E20.cpp" />
    <ClCompile Include="source\_Uninit_movePEAVCGuildBattleRewardItemGUILD_BATTLE_1403D2FF0.cpp" />
    <ClCompile Include="source\_XlenvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1810.cpp" />
    <ClCompile Include="source\__CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecordDa_1402F4150.cpp" />
  </ItemGroup>
  
  <ItemGroup>
    <ClInclude Include="headers\0allocatorVCGuildBattleRewardItemGUILD_BATTLEstdQE_1403D1530.h" />
    <ClInclude Include="headers\0allocatorVCGuildBattleRewardItemGUILD_BATTLEstdQE_1403D1900.h" />
    <ClInclude Include="headers\0CashItemRemoteStoreQEAAXZ_1402F3800.h" />
    <ClInclude Include="headers\0CBattleTournamentInfoQEAAXZ_1403FEA40.h" />
    <ClInclude Include="headers\0CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAXZ_1403CDE40.h" />
    <ClInclude Include="headers\0CGuildBattleControllerIEAAXZ_1403D5680.h" />
    <ClInclude Include="headers\0CGuildBattleGUILD_BATTLEQEAAXZ_1403EB010.h" />
    <ClInclude Include="headers\0CGuildBattleLoggerGUILD_BATTLEIEAAXZ_1403CE6F0.h" />
    <ClInclude Include="headers\0CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1403CA2F0.h" />
    <ClInclude Include="headers\0CGuildBattleReservedScheduleGUILD_BATTLEQEAAIZ_1403DAB60.h" />
    <ClInclude Include="headers\0CGuildBattleReservedScheduleListManagerGUILD_BATT_1403CD260.h" />
    <ClInclude Include="headers\0CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DBA50.h" />
    <ClInclude Include="headers\0CGuildBattleRewardItemGUILD_BATTLEQEAAXZ_1403C8EF0.h" />
    <ClInclude Include="headers\0CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_1403C93A0.h" />
    <ClInclude Include="headers\0CGuildBattleScheduleGUILD_BATTLEQEAAKZ_1403D9B00.h" />
    <ClInclude Include="headers\0CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_1403DC830.h" />
    <ClInclude Include="headers\0CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1403DA430.h" />
    <ClInclude Include="headers\0CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_1403DEDA0.h" />
    <ClInclude Include="headers\0CGuildBattleStateGUILD_BATTLEQEAAXZ_1403DEE30.h" />
    <ClInclude Include="headers\0CGuildBattleStateListGUILD_BATTLEQEAAHHIZ_1403DEF90.h" />
    <ClInclude Include="headers\0CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1403EB7B0.h" />
    <ClInclude Include="headers\0CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1403EE250.h" />
    <ClInclude Include="headers\0CNormalGuildBattleGuildGUILD_BATTLEQEAAEZ_1403E04C0.h" />
    <ClInclude Include="headers\0CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1403DF960.h" />
    <ClInclude Include="headers\0CNormalGuildBattleGUILD_BATTLEQEAAKZ_1403E2E40.h" />
    <ClInclude Include="headers\0CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_1403EB070.h" />
    <ClInclude Include="headers\0CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1403D33C0.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateCountDownGUILD_BATTLEQEAAX_1403F0820.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_1403F0D20.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_1403F0F20.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_1403F3120.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateInBattleGUILD_BATTLEQEAAXZ_1403F0950.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_1403F1E80.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateListPoolGUILD_BATTLEIEAAXZ_1403F22B0.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_1403F06B0.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_1403F0770.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_1403F0E10.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1403F0FD0.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateRoundListGUILD_BATTLEQEAAX_1403F2150.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateRoundProcessGUILD_BATTLEQE_1403F1770.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateRoundReturnStartPosGUILD_B_1403F1B10.h" />
    <ClInclude Include="headers\0CNormalGuildBattleStateRoundStartGUILD_BATTLEQEAA_1403F1350.h" />
    <ClInclude Include="headers\0CPossibleBattleGuildListManagerGUILD_BATTLEIEAAXZ_1403C9530.h" />
    <ClInclude Include="headers\0CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAXZ_1403CCBC0.h" />
    <ClInclude Include="headers\0CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAXZ_1403CC340.h" />
    <ClInclude Include="headers\0CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_1403CBC30.h" />
    <ClInclude Include="headers\0CRFCashItemDatabaseQEAAXZ_1402F2AE0.h" />
    <ClInclude Include="headers\0MonsterSFContDamageToleracneQEAAXZ_140157E80.h" />
    <ClInclude Include="headers\0vectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0C20.h" />
    <ClInclude Include="headers\0_attack_count_result_zoclQEAAXZ_1400EEED0.h" />
    <ClInclude Include="headers\0_ATTACK_DELAY_CHECKERQEAAXZ_140072D80.h" />
    <ClInclude Include="headers\0_attack_force_result_zoclQEAAXZ_1400EEDD0.h" />
    <ClInclude Include="headers\0_attack_gen_result_zoclQEAAXZ_1400EECD0.h" />
    <ClInclude Include="headers\0_attack_keeper_inform_zoclQEAAXZ_140136C00.h" />
    <ClInclude Include="headers\0_attack_paramQEAAXZ_14008E4A0.h" />
    <ClInclude Include="headers\0_attack_selfdestruction_result_zoclQEAAXZ_1400EEF50.h" />
    <ClInclude Include="headers\0_attack_siege_result_zoclQEAAXZ_1400EEFD0.h" />
    <ClInclude Include="headers\0_attack_trap_inform_zoclQEAAXZ_140141400.h" />
    <ClInclude Include="headers\0_attack_unit_result_zoclQEAAXZ_1400EEE50.h" />
    <ClInclude Include="headers\0_be_damaged_charQEAAXZ_14013E400.h" />
    <ClInclude Include="headers\0_eff_list_ATTACK_DELAY_CHECKERQEAAXZ_140072E20.h" />
    <ClInclude Include="headers\0_guild_battle_suggest_matterQEAAXZ_14025CF40.h" />
    <ClInclude Include="headers\0_mas_list_ATTACK_DELAY_CHECKERQEAAXZ_140072E40.h" />
    <ClInclude Include="headers\0_param_cashitem_dblogQEAAKZ_140304CC0.h" />
    <ClInclude Include="headers\0_personal_automine_attacked_zoclQEAAXZ_1402DE2F0.h" />
    <ClInclude Include="headers\0_possible_battle_guild_list_result_zoclQEAAXZ_1403D07E0.h" />
    <ClInclude Include="headers\0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D1610.h" />
    <ClInclude Include="headers\0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D2430.h" />
    <ClInclude Include="headers\0_remain_num_of_goodCashItemRemoteStoreQEAAXZ_1403047C0.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D15A0.h" />
    <ClInclude Include="headers\0_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D2360.h" />
    <ClInclude Include="headers\0_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D1540.h" />
    <ClInclude Include="headers\0_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D19C0.h" />
    <ClInclude Include="headers\0_Vector_valVCGuildBattleRewardItemGUILD_BATTLEVal_1403D14C0.h" />
    <ClInclude Include="headers\1CashItemRemoteStoreQEAAXZ_1402F3A90.h" />
    <ClInclude Include="headers\1CBattleTournamentInfoQEAAXZ_1403FEAB0.h" />
    <ClInclude Include="headers\1CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAXZ_1403CDE80.h" />
    <ClInclude Include="headers\1CGuildBattleControllerIEAAXZ_1403D56A0.h" />
    <ClInclude Include="headers\1CGuildBattleGUILD_BATTLEQEAAXZ_1403EB040.h" />
    <ClInclude Include="headers\1CGuildBattleLoggerGUILD_BATTLEIEAAXZ_1403CE710.h" />
    <ClInclude Include="headers\1CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1403CA330.h" />
    <ClInclude Include="headers\1CGuildBattleReservedScheduleGUILD_BATTLEQEAAXZ_1403DABB0.h" />
    <ClInclude Include="headers\1CGuildBattleReservedScheduleListManagerGUILD_BATT_1403CD300.h" />
    <ClInclude Include="headers\1CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DBA90.h" />
    <ClInclude Include="headers\1CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_1403C93E0.h" />
    <ClInclude Include="headers\1CGuildBattleScheduleGUILD_BATTLEQEAAXZ_1403D9B80.h" />
    <ClInclude Include="headers\1CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_1403DC8E0.h" />
    <ClInclude Include="headers\1CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1403DA470.h" />
    <ClInclude Include="headers\1CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_1403DEE20.h" />
    <ClInclude Include="headers\1CGuildBattleStateGUILD_BATTLEQEAAXZ_14007F740.h" />
    <ClInclude Include="headers\1CGuildBattleStateListGUILD_BATTLEQEAAXZ_14007F810.h" />
    <ClInclude Include="headers\1CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1403EB830.h" />
    <ClInclude Include="headers\1CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1403EE2C0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleGuildGUILD_BATTLEQEAAXZ_1403E05A0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1403DF9A0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleGUILD_BATTLEQEAAXZ_1403E2FB0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_1403CEBE0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1403D3430.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateCountDownGUILD_BATTLEQEAAX_14007FD60.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_140080100.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140080280.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14007FB50.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateInBattleGUILD_BATTLEQEAAXZ_14007FE30.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_14007F850.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateListPoolGUILD_BATTLEIEAAXZ_1403F22E0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_14007FAF0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14007FC90.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_1400801C0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1403F31E0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateRoundListGUILD_BATTLEQEAAX_14007FEE0.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateRoundProcessGUILD_BATTLEQE_1403F1890.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateRoundReturnStartPosGUILD_B_1403F1C30.h" />
    <ClInclude Include="headers\1CNormalGuildBattleStateRoundStartGUILD_BATTLEQEAA_1403F1470.h" />
    <ClInclude Include="headers\1CPossibleBattleGuildListManagerGUILD_BATTLEIEAAXZ_1403C9580.h" />
    <ClInclude Include="headers\1CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAXZ_1403CCBF0.h" />
    <ClInclude Include="headers\1CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAXZ_1403CC3C0.h" />
    <ClInclude Include="headers\1CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_1403CBCE0.h" />
    <ClInclude Include="headers\1CRFCashItemDatabaseUEAAXZ_1402F2BB0.h" />
    <ClInclude Include="headers\1vectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0CA0.h" />
    <ClInclude Include="headers\1_param_cashitem_dblogQEAAXZ_140304D50.h" />
    <ClInclude Include="headers\1_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV12_1403D0FF0.h" />
    <ClInclude Include="headers\1_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D0FB0.h" />
    <ClInclude Include="headers\1_Vector_iteratorVCGuildBattleRewardItemGUILD_BATT_1403D0F70.h" />
    <ClInclude Include="headers\4CGuildBattleReservedScheduleGUILD_BATTLEQEAAAEBV0_1403DB6E0.h" />
    <ClInclude Include="headers\4CGuildBattleReservedScheduleMapGroupGUILD_BATTLEQ_1403DC790.h" />
    <ClInclude Include="headers\4CGuildBattleScheduleGUILD_BATTLEQEAAAEBV01AEBV01Z_1403DA350.h" />
    <ClInclude Include="headers\8_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D23C0.h" />
    <ClInclude Include="headers\9_Vector_const_iteratorVCGuildBattleRewardItemGUIL_1403D1A20.h" />
    <ClInclude Include="headers\accHitTestCFormViewUEAAJJJPEAUtagVARIANTZ_14002BB60.h" />
    <ClInclude Include="headers\accHitTestCWndUEAAJJJPEAUtagVARIANTZ_0_1404DBCBE.h" />
    <ClInclude Include="headers\Action_Attack_OnLoopDfAIMgrSAXPEAVUs_HFSMKPEAXZ_1401518E0.h" />
    <ClInclude Include="headers\AddCGuildBattleControllerQEAAEPEAVCGuild0KEKZ_14025D2A0.h" />
    <ClInclude Include="headers\AddCGuildBattleControllerQEAAEPEAVCGuild0KKEKZ_1403D5DB0.h" />
    <ClInclude Include="headers\AddCGuildBattleReservedScheduleGUILD_BATTLEQEAAEKK_1403DAC40.h" />
    <ClInclude Include="headers\AddCGuildBattleReservedScheduleMapGroupGUILD_BATTL_1403DC440.h" />
    <ClInclude Include="headers\AddCGuildBattleScheduleManagerGUILD_BATTLEQEAAEIKK_1403D9180.h" />
    <ClInclude Include="headers\AddCNormalGuildBattleManagerGUILD_BATTLEQEAAEPEAVC_1403D3DC0.h" />
    <ClInclude Include="headers\AddCompleteCGuildBattleControllerQEAAXEIKKZ_1403D6E20.h" />
    <ClInclude Include="headers\AddCompleteCNormalGuildBattleGUILD_BATTLEQEAAXEZ_1403E3910.h" />
    <ClInclude Include="headers\AddCompleteCNormalGuildBattleManagerGUILD_BATTLEQE_1403D4060.h" />
    <ClInclude Include="headers\AddDefaultDBRecordCNormalGuildBattleManagerGUILD_B_1403D4FD0.h" />
    <ClInclude Include="headers\AddDefaultDBTableCGuildBattleScheduleManagerGUILD__1403DD320.h" />
    <ClInclude Include="headers\AddGoldCntCNormalGuildBattleGuildMemberGUILD_BATTL_1403EAE50.h" />
    <ClInclude Include="headers\AddGuildBattleSchduleCMainThreadQEAAXPEAU_DB_QRY_S_1401F4170.h" />
    <ClInclude Include="headers\AddKillCntCNormalGuildBattleGuildMemberGUILD_BATTL_1403EADF0.h" />
    <ClInclude Include="headers\AddScheduleCGuildBattleControllerQEAAEPEADZ_1403D6AD0.h" />
    <ClInclude Include="headers\AdvanceCGuildBattleStateListGUILD_BATTLEIEAAXHZ_1403DF610.h" />
    <ClInclude Include="headers\AdvanceRegenStateCNormalGuildBattleStateInBattleGU_1403EB290.h" />
    <ClInclude Include="headers\AdvanceRegenStateCNormalGuildBattleStateListGUILD__1403EB220.h" />
    <ClInclude Include="headers\allocateallocatorVCGuildBattleRewardItemGUILD_BATT_1403D1970.h" />
    <ClInclude Include="headers\AreaDamageProcCAttackQEAAXHHPEAMH_NZ_14016C320.h" />
    <ClInclude Include="headers\AskJoinCNormalGuildBattleGuildGUILD_BATTLEIEAAXHPE_1403E2C80.h" />
    <ClInclude Include="headers\AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXHKE_1403E0D20.h" />
    <ClInclude Include="headers\AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E1ED0.h" />
    <ClInclude Include="headers\AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_1403E3F40.h" />
    <ClInclude Include="headers\AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E49E0.h" />
    <ClInclude Include="headers\assignvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D0D10.h" />
    <ClInclude Include="headers\AttackableHeightCAnimusUEAAHXZ_140129880.h" />
    <ClInclude Include="headers\AttackableHeightCGameObjectUEAAHXZ_14012E340.h" />
    <ClInclude Include="headers\AttackableHeightCGuardTowerUEAAHXZ_1401328E0.h" />
    <ClInclude Include="headers\AttackableHeightCMonsterUEAAHXZ_1401468D0.h" />
    <ClInclude Include="headers\AttackableHeightCTrapUEAAHXZ_1401412D0.h" />
    <ClInclude Include="headers\AttackCAnimusQEAA_NKZ_140126AC0.h" />
    <ClInclude Include="headers\AttackCNuclearBombQEAAXHHZ_14013C4F0.h" />
    <ClInclude Include="headers\AttackForceCAttackQEAAXPEAU_attack_param_NZ_14016A210.h" />
    <ClInclude Include="headers\AttackForceRequestCNetworkEXAEAA_NHPEADZ_1401C1A50.h" />
    <ClInclude Include="headers\AttackGenCAttackQEAAXPEAU_attack_param_N1Z_140169520.h" />
    <ClInclude Include="headers\AttackMonsterForceCMonsterAttackQEAAXPEAU_attack_p_14015BA60.h" />
    <ClInclude Include="headers\AttackMonsterGenCMonsterAttackQEAAXPEAU_attack_par_14015B300.h" />
    <ClInclude Include="headers\AttackObjectCMonsterQEAAHHPEAVCGameObjectZ_140142A60.h" />
    <ClInclude Include="headers\AttackPersonalRequestCNetworkEXAEAA_NHPEADZ_1401C1680.h" />
    <ClInclude Include="headers\AttackSiegeRequestCNetworkEXAEAA_NHPEADZ_1401C1F20.h" />
    <ClInclude Include="headers\AttackTestRequestCNetworkEXAEAA_NHPEADZ_1401C1D60.h" />
    <ClInclude Include="headers\AttackUnitRequestCNetworkEXAEAA_NHPEADZ_1401C1C00.h" />
    <ClInclude Include="headers\AvectorVCGuildBattleRewardItemGUILD_BATTLEVallocat_1403D0CE0.h" />
    <ClInclude Include="headers\beginvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1030.h" />
    <ClInclude Include="headers\BuyByCashCashItemRemoteStoreAEAA_NGPEADZ_1402FE0D0.h" />
    <ClInclude Include="headers\BuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_1402FECC0.h" />
    <ClInclude Include="headers\BuyCashItemRemoteStoreQEAA_NGPEADZ_1402FE050.h" />
    <ClInclude Include="headers\BuyLimSaleCashItemRemoteStoreQEAAGEKZ_1402FD950.h" />
    <ClInclude Include="headers\buy_to_inven_cashitemCMgrAvatorItemHistoryQEAAXEGH_14023DF10.h" />
    <ClInclude Include="headers\CalcAvgDamageCAttackQEAAXXZ_14016DBB0.h" />
    <ClInclude Include="headers\CallProc_InsertCashItemLogCRFCashItemDatabaseQEAA__140483530.h" />
    <ClInclude Include="headers\CallProc_RFOnlineAvg_EventCRFCashItemDatabaseQEAAH_1404832B0.h" />
    <ClInclude Include="headers\CallProc_RFOnlineUseCRFCashItemDatabaseQEAAHAEAU_p_140482840.h" />
    <ClInclude Include="headers\CallProc_RFOnlineUse_JapCRFCashItemDatabaseQEAAHAE_140483A70.h" />
    <ClInclude Include="headers\CallProc_RFONLINE_CancelCRFCashItemDatabaseQEAAHAE_140482D90.h" />
    <ClInclude Include="headers\CallProc_RFONLINE_Cancel_JapCRFCashItemDatabaseQEA_140484090.h" />
    <ClInclude Include="headers\CancelSuggestedMatter_guild_battle_suggest_matterQ_14025D350.h" />
    <ClInclude Include="headers\capacityvectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D2480.h" />
    <ClInclude Include="headers\cashitem_del_from_invenCMgrAvatorItemHistoryQEAAXE_14023DD70.h" />
    <ClInclude Include="headers\ChangeDiscountEventTimeCashItemRemoteStoreQEAA_NXZ_1402F7B10.h" />
    <ClInclude Include="headers\ChangeEventTimeCashItemRemoteStoreQEAA_NEZ_1402FD050.h" />
    <ClInclude Include="headers\Change_Conditional_Event_StatusCashItemRemoteStore_1402FBF00.h" />
    <ClInclude Include="headers\CheatBuyCashItemRemoteStoreQEAA_NGPEBDHZ_1402F5B50.h" />
    <ClInclude Include="headers\CheatDestroyStoneCNormalGuildBattleFieldGUILD_BATT_1403ED6C0.h" />
    <ClInclude Include="headers\CheatLoadCashAmountCashItemRemoteStoreQEAA_NGHZ_1402F5990.h" />
    <ClInclude Include="headers\CheatRegenStoneCNormalGuildBattleFieldGUILD_BATTLE_1403ED490.h" />
    <ClInclude Include="headers\CheckAttackCHolyKeeperQEAA_NXZ_1401338D0.h" />
    <ClInclude Include="headers\CheckBallTakeLimitTimeCNormalGuildBattleFieldGUILD_1403ED0F0.h" />
    <ClInclude Include="headers\CheckCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1403D9DE0.h" />
    <ClInclude Include="headers\CheckGetGravityStoneCNormalGuildBattleManagerGUILD_1403D4800.h" />
    <ClInclude Include="headers\CheckGoalCNormalGuildBattleManagerGUILD_BATTLEQEAA_1403D4910.h" />
    <ClInclude Include="headers\CheckGuildBattleLimitCAttackIEAA_NPEAVCGameObjectP_14016B570.h" />
    <ClInclude Include="headers\CheckGuildBattleSuggestRequestToDestGuildCGuildQEA_1402579F0.h" />
    <ClInclude Include="headers\CheckIsInTownCNormalGuildBattleFieldGUILD_BATTLEQE_1403ED070.h" />
    <ClInclude Include="headers\CheckLoopCGuildBattleStateListGUILD_BATTLEIEAAHXZ_1403DF4D0.h" />
    <ClInclude Include="headers\CheckNextEventCGuildBattleReservedScheduleGUILD_BA_1403DB750.h" />
    <ClInclude Include="headers\CheckRecordCGuildBattleRankManagerGUILD_BATTLEIEAA_1403CB7C0.h" />
    <ClInclude Include="headers\CheckTakeGravityStoneCNormalGuildBattleManagerGUIL_1403D4700.h" />
    <ClInclude Include="headers\Check_CashEvent_INICashItemRemoteStoreQEAA_NEZ_1402F8770.h" />
    <ClInclude Include="headers\Check_CashEvent_StatusCashItemRemoteStoreQEAAXEZ_1402F93C0.h" />
    <ClInclude Include="headers\check_cash_discount_iniCashItemRemoteStoreQEAAXXZ_1402F6970.h" />
    <ClInclude Include="headers\check_cash_discount_statusCashItemRemoteStoreQEAAX_1402F6C30.h" />
    <ClInclude Include="headers\Check_Conditional_Event_INICashItemRemoteStoreQEAA_1402FC480.h" />
    <ClInclude Include="headers\Check_Conditional_Event_StatusCashItemRemoteStoreQ_1402FC060.h" />
    <ClInclude Include="headers\Check_GrosssalesCashItemRemoteStoreQEAAXKZ_1402FB6D0.h" />
    <ClInclude Include="headers\check_loaded_cde_statusCashItemRemoteStoreQEAAXXZ_1402F5F30.h" />
    <ClInclude Include="headers\Check_Loaded_Event_StatusCashItemRemoteStoreQEAAXE_1402F8140.h" />
    <ClInclude Include="headers\Check_Total_SellingCashItemRemoteStoreQEAAXXZ_1402FB640.h" />
    <ClInclude Include="headers\CleanUpBattleCNormalGuildBattleGuildGUILD_BATTLEQE_1403E1E50.h" />
    <ClInclude Include="headers\CleanUpBattleCNormalGuildBattleGuildMemberGUILD_BA_1403E00D0.h" />
    <ClInclude Include="headers\CleanUpBattleCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E6FE0.h" />
    <ClInclude Include="headers\CleanUpCCurrentGuildBattleInfoManagerGUILD_BATTLEI_1403CE510.h" />
    <ClInclude Include="headers\CleanUpCGuildBattleControllerIEAAXXZ_1403D7900.h" />
    <ClInclude Include="headers\CleanUpCGuildBattleRankManagerGUILD_BATTLEIEAAXXZ_1403CB3F0.h" />
    <ClInclude Include="headers\CleanUpDanglingReservedScheduleCGuildBattleReserve_1403DB570.h" />
    <ClInclude Include="headers\CleanUpDanglingReservedScheduleCGuildBattleReserve_1403DC5F0.h" />
    <ClInclude Include="headers\CleanUpDanglingReservedScheduleCGuildBattleSchedul_1403DD1C0.h" />
    <ClInclude Include="headers\ClearAllCGuildBattleSchedulePoolGUILD_BATTLEQEAAXX_1403DA890.h" />
    <ClInclude Include="headers\ClearBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_N_1403ED140.h" />
    <ClInclude Include="headers\ClearByDayIDCGuildBattleSchedulePoolGUILD_BATTLEQE_1403DA930.h" />
    <ClInclude Include="headers\ClearCCurrentGuildBattleInfoManagerGUILD_BATTLEQEA_1403CE220.h" />
    <ClInclude Include="headers\ClearCGuildBattleControllerQEAAXXZ_1403D62F0.h" />
    <ClInclude Include="headers\ClearCGuildBattleRankManagerGUILD_BATTLEIEAAXEZ_1403CB530.h" />
    <ClInclude Include="headers\ClearCGuildBattleRankManagerGUILD_BATTLEQEAAXXZ_1403CB4B0.h" />
    <ClInclude Include="headers\ClearCGuildBattleReservedScheduleGUILD_BATTLEQEAAX_1403DAE20.h" />
    <ClInclude Include="headers\ClearCGuildBattleReservedScheduleGUILD_BATTLEQEAA__1403DB420.h" />
    <ClInclude Include="headers\ClearCGuildBattleReservedScheduleListManagerGUILD__1403CD970.h" />
    <ClInclude Include="headers\ClearCGuildBattleReservedScheduleMapGroupGUILD_BAT_1403DC020.h" />
    <ClInclude Include="headers\ClearCGuildBattleReservedScheduleMapGroupGUILD_BAT_1403DC230.h" />
    <ClInclude Include="headers\ClearCGuildBattleScheduleGUILD_BATTLEQEAAXXZ_1403D9E90.h" />
    <ClInclude Include="headers\ClearCGuildBattleScheduleManagerGUILD_BATTLEAEAAXX_1403DD480.h" />
    <ClInclude Include="headers\ClearCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403DF030.h" />
    <ClInclude Include="headers\ClearCNormalGuildBattleGuildGUILD_BATTLEQEAAXXZ_1403E0600.h" />
    <ClInclude Include="headers\ClearCNormalGuildBattleGuildMemberGUILD_BATTLEQEAA_1403DF9B0.h" />
    <ClInclude Include="headers\ClearCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E3660.h" />
    <ClInclude Include="headers\ClearCNormalGuildBattleManagerGUILD_BATTLEIEAAXPEA_1403D5400.h" />
    <ClInclude Include="headers\ClearCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D4280.h" />
    <ClInclude Include="headers\ClearCNormalGuildBattleStateListPoolGUILD_BATTLEQE_1403F2640.h" />
    <ClInclude Include="headers\ClearCPossibleBattleGuildListManagerGUILD_BATTLEQE_1403D98E0.h" />
    <ClInclude Include="headers\ClearCReservedGuildScheduleDayGroupGUILD_BATTLEQEA_1403CCE80.h" />
    <ClInclude Include="headers\ClearCReservedGuildScheduleMapGroupGUILD_BATTLEQEA_1403CC4C0.h" />
    <ClInclude Include="headers\ClearCReservedGuildSchedulePageGUILD_BATTLEQEAA_NX_1403CC220.h" />
    <ClInclude Include="headers\ClearDBCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403D9F10.h" />
    <ClInclude Include="headers\ClearDBRecordCNormalGuildBattleGUILD_BATTLEQEAA_NX_1403E3740.h" />
    <ClInclude Include="headers\ClearElapsedScheduleCGuildBattleReservedScheduleGU_1403DB950.h" />
    <ClInclude Include="headers\ClearGuildBattleCGuildQEAAXXZ_140258290.h" />
    <ClInclude Include="headers\ClearInBattleStateCNormalGuildBattleGuildGUILD_BAT_1403E1680.h" />
    <ClInclude Include="headers\ClearRegenCNormalGuildBattleFieldGUILD_BATTLEQEAA__1403ED190.h" />
    <ClInclude Include="headers\ClearTommorowScheduleByIDCGuildBattleScheduleManag_1403DD0D0.h" />
    <ClInclude Include="headers\Clear_guild_battle_suggest_matterQEAAXXZ_1402075A0.h" />
    <ClInclude Include="headers\CompleteClearGuildBattleRankCGuildBattleController_1403D7030.h" />
    <ClInclude Include="headers\CompleteCreateGuildBattleRankTableCGuildBattleCont_1403D6FB0.h" />
    <ClInclude Include="headers\CompleteLoadGuildBattleTotalRecordCMainThreadAEAAX_1401EDBD0.h" />
    <ClInclude Include="headers\CompleteOutGuildbattleCostCGuildQEAAXKKKKZ_140257B00.h" />
    <ClInclude Include="headers\CompleteUpdateRankCGuildBattleControllerQEAAXEEPEA_1403D6EC0.h" />
    <ClInclude Include="headers\CompleteUpdateReservedScheduleCGuildBattleControll_1403D6F60.h" />
    <ClInclude Include="headers\constructallocatorVCGuildBattleRewardItemGUILD_BAT_1403D2F40.h" />
    <ClInclude Include="headers\CopyUseTimeFieldCGuildBattleReservedScheduleGUILD__1403DAD20.h" />
    <ClInclude Include="headers\CopyUseTimeFieldCGuildBattleReservedScheduleMapGro_1403DC4F0.h" />
    <ClInclude Include="headers\CreateFieldObjectCNormalGuildBattleFieldGUILD_BATT_1403EC630.h" />
    <ClInclude Include="headers\CreateGuildBattleRankTableCRFWorldDatabaseQEAA_NPE_1404A3810.h" />
    <ClInclude Include="headers\CreateLogFileCGuildBattleLoggerGUILD_BATTLEQEAAXPE_1403CE9E0.h" />
    <ClInclude Include="headers\CreateLogFileCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E7040.h" />
    <ClInclude Include="headers\CreateLogFileCNormalGuildBattleLoggerGUILD_BATTLEQ_1403CED50.h" />
    <ClInclude Include="headers\CreateLoggerCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9020.h" />
    <ClInclude Include="headers\ct_StopBattleCHolyStoneSystemQEAA_NXZ_1402815B0.h" />
    <ClInclude Include="headers\deallocateallocatorVCGuildBattleRewardItemGUILD_BA_1403D1920.h" />
    <ClInclude Include="headers\DecideColorInxCNormalGuildBattleGUILD_BATTLEQEAAXX_1403E3E20.h" />
    <ClInclude Include="headers\DecideWinCNormalGuildBattleGUILD_BATTLEIEAAEXZ_1403E76F0.h" />
    <ClInclude Include="headers\DecPvpPointCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403E1860.h" />
    <ClInclude Include="headers\DecPvpPointCNormalGuildBattleGuildMemberGUILD_BATT_1403DFE10.h" />
    <ClInclude Include="headers\DeleteGuildBattleInfoCRFWorldDatabaseQEAA_NXZ_1404A2410.h" />
    <ClInclude Include="headers\DeleteGuildBattleScheduleInfoCRFWorldDatabaseQEAA__1404A0FD0.h" />
    <ClInclude Include="headers\DestGuildIsAvailableBattleRequestStateCGuildQEAAEX_140257960.h" />
    <ClInclude Include="headers\destroyallocatorVCGuildBattleRewardItemGUILD_BATTL_1403D2FA0.h" />
    <ClInclude Include="headers\DestroyCCurrentGuildBattleInfoManagerGUILD_BATTLES_1403CDF80.h" />
    <ClInclude Include="headers\DestroyCGuildBattleControllerSAXXZ_1403D57A0.h" />
    <ClInclude Include="headers\DestroyCGuildBattleLoggerGUILD_BATTLESAXXZ_1403CE850.h" />
    <ClInclude Include="headers\DestroyCGuildBattleRankManagerGUILD_BATTLESAXXZ_1403CA430.h" />
    <ClInclude Include="headers\DestroyCGuildBattleReservedScheduleListManagerGUIL_1403CD420.h" />
    <ClInclude Include="headers\DestroyCGuildBattleScheduleManagerGUILD_BATTLESAXX_1403DCAD0.h" />
    <ClInclude Include="headers\DestroyCGuildBattleSchedulePoolGUILD_BATTLESAXXZ_1403DA640.h" />
    <ClInclude Include="headers\DestroyCGuildBattleSchedulerGUILD_BATTLESAXXZ_1403DD7C0.h" />
    <ClInclude Include="headers\DestroyCNormalGuildBattleFieldGUILD_BATTLEAEAAXXZ_1403EDED0.h" />
    <ClInclude Include="headers\DestroyCNormalGuildBattleFieldListGUILD_BATTLESAXX_1403EE420.h" />
    <ClInclude Include="headers\DestroyCNormalGuildBattleManagerGUILD_BATTLESAXXZ_1403D35F0.h" />
    <ClInclude Include="headers\DestroyCNormalGuildBattleStateListPoolGUILD_BATTLE_1403F2430.h" />
    <ClInclude Include="headers\DestroyCPossibleBattleGuildListManagerGUILD_BATTLE_1403C9710.h" />
    <ClInclude Include="headers\DestroyFieldObjectCNormalGuildBattleFieldGUILD_BAT_1403EC810.h" />
    <ClInclude Include="headers\dhRExtractSubStringCRFCashItemDatabaseQEAAXPEAD0HZ_140483420.h" />
    <ClInclude Include="headers\DividePvpPointCNormalGuildBattleGUILD_BATTLEQEAAXX_1403E6490.h" />
    <ClInclude Include="headers\DoDayChangedWorkCNormalGuildBattleManagerGUILD_BAT_1403D4220.h" />
    <ClInclude Include="headers\DoDayChangedWorkCPossibleBattleGuildListManagerGUI_1403C9AE0.h" />
    <ClInclude Include="headers\DropGravityStoneCNormalGuildBattleGUILD_BATTLEQEAA_1403E5830.h" />
    <ClInclude Include="headers\DropGravityStoneCNormalGuildBattleManagerGUILD_BAT_1403D4A10.h" />
    <ClInclude Include="headers\dtor00OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C00.h" />
    <ClInclude Include="headers\dtor10OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C20.h" />
    <ClInclude Include="headers\dtor20OnToolHitTestCMFCToolBarMEBA_JVCPointPEAUtag_140646C40.h" />
    <ClInclude Include="headers\endvectorVCGuildBattleRewardItemGUILD_BATTLEValloc_1403D10A0.h" />
    <ClInclude Include="headers\EnterCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildB_14007F760.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateCountDownGUILD_BATTLEM_1403F0870.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVC_14007FBC0.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVC_1403F0380.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateInBattleGUILD_BATTLEME_1403F0A00.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateNotifyGUILD_BATTLEMEAA_1403F0700.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateReadyGUILD_BATTLEMEAAH_1403F07C0.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateRoundGUILD_BATTLEMEAAH_1403F3180.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateRoundGUILD_BATTLEUEAAH_1403F1020.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateRoundProcessGUILD_BATT_1403F1970.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateRoundReturnStartPosGUI_1403F1D10.h" />
    <ClInclude Include="headers\EnterCNormalGuildBattleStateRoundStartGUILD_BATTLE_1403F1550.h" />
    <ClInclude Include="headers\erasevectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1110.h" />
    <ClInclude Include="headers\fillPEAVCGuildBattleRewardItemGUILD_BATTLEV12stdYA_1403D27F0.h" />
    <ClInclude Include="headers\fill_eff_list_ATTACK_DELAY_CHECKERQEAA_NXZ_14008EB90.h" />
    <ClInclude Include="headers\fill_mas_list_ATTACK_DELAY_CHECKERQEAA_NXZ_14008EBC0.h" />
    <ClInclude Include="headers\FinCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildBat_14007F7A0.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateDivideGUILD_BATTLEMEAAHP_1403F0D70.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateFinGUILD_BATTLEMEAAHPEAV_1403F0F70.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVCNo_14007FC00.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVCGu_1403F0460.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateInBattleGUILD_BATTLEMEAA_1403F0B50.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateReturnGUILD_BATTLEMEAAHP_1403F0E60.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateRoundGUILD_BATTLEMEAAHPE_1403F31C0.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateRoundGUILD_BATTLEUEAAHPE_1403F1100.h" />
    <ClInclude Include="headers\FinCNormalGuildBattleStateRoundStartGUILD_BATTLEME_1403F1660.h" />
    <ClInclude Include="headers\FindCashRecCashItemRemoteStoreSAPEBU_CashShop_fldH_1402F48F0.h" />
    <ClInclude Include="headers\FindCGuildBattleRankManagerGUILD_BATTLEIEAA_NEKAEA_1403CB6F0.h" />
    <ClInclude Include="headers\FindCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCFA0.h" />
    <ClInclude Include="headers\FindCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCA30.h" />
    <ClInclude Include="headers\FindCReservedGuildSchedulePageGUILD_BATTLEQEAA_NKZ_1403CBD80.h" />
    <ClInclude Include="headers\FlashDamageProcCAttackIEAAXHHHH_NZ_14016B6F0.h" />
    <ClInclude Include="headers\FlipCGuildBattleControllerQEAAXXZ_1403D6260.h" />
    <ClInclude Include="headers\FlipCGuildBattleReservedScheduleGUILD_BATTLEQEAAXX_1403DB4B0.h" />
    <ClInclude Include="headers\FlipCGuildBattleReservedScheduleListManagerGUILD_B_1403CD8F0.h" />
    <ClInclude Include="headers\FlipCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DC1A0.h" />
    <ClInclude Include="headers\FlipCGuildBattleScheduleManagerGUILD_BATTLEAEAAXXZ_1403DD3B0.h" />
    <ClInclude Include="headers\FlipCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D41C0.h" />
    <ClInclude Include="headers\FlipCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CD1C0.h" />
    <ClInclude Include="headers\FlipCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCB50.h" />
    <ClInclude Include="headers\FlipCReservedGuildSchedulePageGUILD_BATTLEQEAAXXZ_1403CC090.h" />
    <ClInclude Include="headers\ForceNextCGuildBattleStateListGUILD_BATTLEIEAAXXZ_1403DF6A0.h" />
    <ClInclude Include="headers\force_endup_cash_discount_eventCashItemRemoteStore_1402F7850.h" />
    <ClInclude Include="headers\Get1PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNormal_1403D9360.h" />
    <ClInclude Include="headers\Get2PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNormal_1403D9380.h" />
    <ClInclude Include="headers\GetAccessibilityHitTestCWndQEAAJJJPEAUtagVARIANTZ__1404DC1DA.h" />
    <ClInclude Include="headers\GetAmountCGuildBattleRewardItemGUILD_BATTLEQEBAEXZ_1403EAE80.h" />
    <ClInclude Include="headers\GetANSIGuildNameCNormalGuildBattleGuildGUILD_BATTL_1403E0770.h" />
    <ClInclude Include="headers\GetAttackDelay_WEAPON_PARAMQEAAKHHZ_14008E730.h" />
    <ClInclude Include="headers\GetAttackDPCAnimusUEAAHXZ_14012CDF0.h" />
    <ClInclude Include="headers\GetAttackDPCGameObjectUEAAHXZ_14012E360.h" />
    <ClInclude Include="headers\GetAttackDPCGuardTowerUEAAHXZ_1401328C0.h" />
    <ClInclude Include="headers\GetAttackDPCHolyKeeperUEAAHXZ_140136B30.h" />
    <ClInclude Include="headers\GetAttackDPCHolyStoneUEAAHXZ_140138EB0.h" />
    <ClInclude Include="headers\GetAttackDPCMonsterUEAAHXZ_14014BB10.h" />
    <ClInclude Include="headers\GetAttackDPCTrapUEAAHXZ_1401412B0.h" />
    <ClInclude Include="headers\GetAttackPartCAnimusQEAAHXZ_1401264E0.h" />
    <ClInclude Include="headers\GetAttackPartCMonsterQEAAHXZ_14014DDC0.h" />
    <ClInclude Include="headers\GetAttackPivotCHolyKeeperQEAAPEAMXZ_140133E40.h" />
    <ClInclude Include="headers\GetAttackRangeCAnimusUEAAMXZ_1401295C0.h" />
    <ClInclude Include="headers\GetAttackRangeCGameObjectUEAAMXZ_14012E300.h" />
    <ClInclude Include="headers\GetAttackRangeCGuardTowerUEAAMXZ_140132670.h" />
    <ClInclude Include="headers\GetAttackRangeCHolyKeeperUEAAMXZ_140136880.h" />
    <ClInclude Include="headers\GetAttackRangeCMonsterUEAAMXZ_140146660.h" />
    <ClInclude Include="headers\GetAttackRangeCTrapUEAAMXZ_140141070.h" />
    <ClInclude Include="headers\GetAttackToolType_WEAPON_PARAMQEAAHXZ_1400349B0.h" />
    <ClInclude Include="headers\GetBattleByGuildSerialCNormalGuildBattleManagerGUI_1403D5580.h" />
    <ClInclude Include="headers\GetBattleCNormalGuildBattleManagerGUILD_BATTLEIEAA_1403D5520.h" />
    <ClInclude Include="headers\GetBattleModeTimeCMonsterAIQEAAKXZ_140155890.h" />
    <ClInclude Include="headers\GetBattleTimeCGuildBattleScheduleGUILD_BATTLEQEAAA_1403D9120.h" />
    <ClInclude Include="headers\GetBattleTurmCGuildBattleScheduleGUILD_BATTLEQEAAJ_1403D9440.h" />
    <ClInclude Include="headers\GetBlueCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_1403F30E0.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataBRUEAAHPEAU_Cash_14022F020.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataCNUEAAHPEAU_Cash_140230920.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataESUEAAHPEAU_Cash_1402318F0.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataGBUEAAHPEAU_Cash_14022C0D0.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataIDUEAAHPEAU_Cash_14022CA60.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataJPUEAAHPEAU_Cash_14022D3E0.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataKRUEAAHPEAU_Cash_14022B4A0.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataNULLUEAAHPEAU_Ca_1402130B0.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataPHUEAAHPEAU_Cash_14022DE90.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataRUUEAAHPEAU_Cash_14022E820.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataTHUEAAHPEAU_Cash_140232270.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataTWUEAAHPEAU_Cash_1402300F0.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataUEAAHPEAU_CashSh_140212910.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingDataUSUEAAHPEAU_Cash_1402313F0.h" />
    <ClInclude Include="headers\GetCashItemPriceCNationSettingManagerQEAAHPEAU_Cas_140304810.h" />
    <ClInclude Include="headers\GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVCGu_1403DAA30.h" />
    <ClInclude Include="headers\GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVCGu_1403DEA50.h" />
    <ClInclude Include="headers\GetCircleZoneCGuildBattleControllerQEAAPEAVCGameOb_1403D6860.h" />
    <ClInclude Include="headers\GetCircleZoneCNormalGuildBattleFieldGUILD_BATTLEQE_1403ECA10.h" />
    <ClInclude Include="headers\GetCircleZoneCNormalGuildBattleFieldListGUILD_BATT_1403EED90.h" />
    <ClInclude Include="headers\GetCNormalGuildBattleStateListPoolGUILD_BATTLEQEAA_1403F26D0.h" />
    <ClInclude Include="headers\GetColorInxCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403EAFC0.h" />
    <ClInclude Include="headers\GetColorNameCNormalGuildBattleGuildGUILD_BATTLEQEA_1403EB320.h" />
    <ClInclude Include="headers\GetCombatStateCMonsterQEAAEXZ_140143870.h" />
    <ClInclude Include="headers\GetCurScheduleIDCGuildBattleReservedScheduleGUILD__1403DB640.h" />
    <ClInclude Include="headers\GetCurScheduleIDCGuildBattleReservedScheduleMapGro_1403DC710.h" />
    <ClInclude Include="headers\GetCurScheduleIDCGuildBattleScheduleManagerGUILD_B_1403DD2D0.h" />
    <ClInclude Include="headers\GetDamagedObjNumCNuclearBombQEAAHXZ_14013E3A0.h" />
    <ClInclude Include="headers\GetDayIDCGuildBattleReservedScheduleMapGroupGUILD__1403D9A90.h" />
    <ClInclude Include="headers\GetEmptyMemberCNormalGuildBattleGuildGUILD_BATTLEI_1403E29E0.h" />
    <ClInclude Include="headers\GetEvnetTimeCashItemRemoteStoreQEAAXPEAU_cash_even_1402FBA00.h" />
    <ClInclude Include="headers\GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EE870.h" />
    <ClInclude Include="headers\GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EE950.h" />
    <ClInclude Include="headers\GetFieldCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_1400A6A80.h" />
    <ClInclude Include="headers\GetFirstMapFieldByRaceCNormalGuildBattleFieldListG_1403EEB10.h" />
    <ClInclude Include="headers\GetFirstMapInxByRaceCNormalGuildBattleFieldListGUI_1403EEA80.h" />
    <ClInclude Include="headers\GetGoalCntCNormalGuildBattleGuildGUILD_BATTLEQEAAK_1403EB150.h" />
    <ClInclude Include="headers\GetGoalCountCNormalGuildBattleGuildMemberGUILD_BAT_1403EB380.h" />
    <ClInclude Include="headers\GetGravityStoneCNormalGuildBattleGUILD_BATTLEQEAAE_1403E4B80.h" />
    <ClInclude Include="headers\GetGuildBattleNumberCNormalGuildBattleGUILD_BATTLE_1403D93A0.h" />
    <ClInclude Include="headers\GetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAPEA_1403EB130.h" />
    <ClInclude Include="headers\GetGuildCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_1403E3AB0.h" />
    <ClInclude Include="headers\GetGuildNameCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0710.h" />
    <ClInclude Include="headers\GetGuildRaceCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0830.h" />
    <ClInclude Include="headers\GetGuildSerialCNormalGuildBattleGuildGUILD_BATTLEQ_1403E07D0.h" />
    <ClInclude Include="headers\GetIDCGuildBattleReservedScheduleGUILD_BATTLEQEAAI_1403DEC40.h" />
    <ClInclude Include="headers\GetIDCNormalGuildBattleGUILD_BATTLEQEAAKXZ_1403D9340.h" />
    <ClInclude Include="headers\GetIndexCNormalGuildBattleGuildMemberGUILD_BATTLEQ_1403E0300.h" />
    <ClInclude Include="headers\GetInfoCNormalGuildBattleGUILD_BATTLEQEAA_NAEAU_gu_1403E39A0.h" />
    <ClInclude Include="headers\GetItemCodeCGuildBattleRewardItemGUILD_BATTLEQEBAP_1403C9270.h" />
    <ClInclude Include="headers\GetJoinMemberCntCNormalGuildBattleGuildGUILD_BATTL_1403E2B20.h" />
    <ClInclude Include="headers\GetKillCountCNormalGuildBattleGuildMemberGUILD_BAT_1403EB360.h" />
    <ClInclude Include="headers\GetKillCountSumCNormalGuildBattleGuildGUILD_BATTLE_1403EB3F0.h" />
    <ClInclude Include="headers\GetLeftTimeCCurrentGuildBattleInfoManagerGUILD_BAT_1403CE5C0.h" />
    <ClInclude Include="headers\GetLeftTimeCGuildBattleScheduleGUILD_BATTLEQEAA_NA_1403DA220.h" />
    <ClInclude Include="headers\GetLimDiscoutCashItemRemoteStoreQEAAEXZ_1402FD930.h" />
    <ClInclude Include="headers\GetLoggerCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNo_1403F3100.h" />
    <ClInclude Include="headers\GetMapCNormalGuildBattleFieldGUILD_BATTLEQEAAPEAVC_1400A6A60.h" />
    <ClInclude Include="headers\GetMapCntCNormalGuildBattleFieldListGUILD_BATTLEQE_1403D0B10.h" />
    <ClInclude Include="headers\GetMapCodeCNormalGuildBattleFieldGUILD_BATTLEQEAAH_1403EC950.h" />
    <ClInclude Include="headers\GetMapIDCNormalGuildBattleFieldGUILD_BATTLEQEAAKXZ_1403EB0F0.h" />
    <ClInclude Include="headers\GetMapInxCNormalGuildBattleFieldListGUILD_BATTLEQE_1403EE990.h" />
    <ClInclude Include="headers\GetMapInxListCNormalGuildBattleFieldListGUILD_BATT_1403EEB70.h" />
    <ClInclude Include="headers\GetMapStrCodeCNormalGuildBattleFieldGUILD_BATTLEQE_1403EC910.h" />
    <ClInclude Include="headers\GetMaxJoinMemberCountCNormalGuildBattleGuildGUILD__1403EB410.h" />
    <ClInclude Include="headers\GetMaxPageCReservedGuildScheduleMapGroupGUILD_BATT_1403D0A80.h" />
    <ClInclude Include="headers\GetMemberCNormalGuildBattleGuildGUILD_BATTLEIEAAHK_1403E2A60.h" />
    <ClInclude Include="headers\GetMemberPtrCNormalGuildBattleGuildGUILD_BATTLEQEA_1403E0A00.h" />
    <ClInclude Include="headers\GetObjTypeCGuildBattleGUILD_BATTLEUEAAHXZ_1403EB060.h" />
    <ClInclude Include="headers\GetObjTypeCNormalGuildBattleGUILD_BATTLEUEAAHXZ_1403EB090.h" />
    <ClInclude Include="headers\GetPortalIndexInfoCNormalGuildBattleFieldGUILD_BAT_1403ECAF0.h" />
    <ClInclude Include="headers\GetRealStartTimeCGuildBattleScheduleGUILD_BATTLEQE_1403D93F0.h" />
    <ClInclude Include="headers\GetRedCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorma_1403F30C0.h" />
    <ClInclude Include="headers\GetRefCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAV_1403DAB00.h" />
    <ClInclude Include="headers\GetRegenerCGuildBattleControllerQEAAPEAVCGameObjec_1403D6810.h" />
    <ClInclude Include="headers\GetRegenerCNormalGuildBattleFieldGUILD_BATTLEQEAAP_1403EC980.h" />
    <ClInclude Include="headers\GetRegenerCNormalGuildBattleFieldListGUILD_BATTLEQ_1403EECF0.h" />
    <ClInclude Include="headers\GetRemainNumOfGoodCashItemRemoteStoreQEAAHGZ_1402F5CD0.h" />
    <ClInclude Include="headers\GetRemainNumOfGoodCashItemRemoteStoreQEAAHQEADZ_1402F5D60.h" />
    <ClInclude Include="headers\GetScoreCNormalGuildBattleGuildGUILD_BATTLEQEAAKXZ_1403EB170.h" />
    <ClInclude Include="headers\GetSerialCNormalGuildBattleGuildMemberGUILD_BATTLE_1403EAFA0.h" />
    <ClInclude Include="headers\GetSetDiscoutCashItemRemoteStoreQEAAEEZ_1402FB0A0.h" />
    <ClInclude Include="headers\GetSIDCGuildBattleScheduleGUILD_BATTLEQEAAKXZ_1403D9100.h" />
    <ClInclude Include="headers\GetSIDCGuildBattleSchedulePoolGUILD_BATTLEQEAAKIKZ_1403DEC80.h" />
    <ClInclude Include="headers\GetSLIDCGuildBattleReservedScheduleMapGroupGUILD_B_1403DC680.h" />
    <ClInclude Include="headers\GetStartBattleTickTimeCHolyStoneSystemQEAAKXZ_14027B5C0.h" />
    <ClInclude Include="headers\GetStateCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1403D93D0.h" />
    <ClInclude Include="headers\GetStoneCGuildBattleControllerQEAAPEAVCGameObjectH_1403D67C0.h" />
    <ClInclude Include="headers\GetStoneCNormalGuildBattleFieldGUILD_BATTLEQEAAPEA_1403F0360.h" />
    <ClInclude Include="headers\GetStoneCNormalGuildBattleFieldListGUILD_BATTLEQEA_1403EEC50.h" />
    <ClInclude Include="headers\GetTermCGuildBattleStateGUILD_BATTLEUEAAAVCTimeSpa_14007F7C0.h" />
    <ClInclude Include="headers\GetTermCGuildBattleStateListGUILD_BATTLEQEAAAVCTim_1403DF470.h" />
    <ClInclude Include="headers\GetTermCNormalGuildBattleStateCountDownGUILD_BATTL_14007FDC0.h" />
    <ClInclude Include="headers\GetTermCNormalGuildBattleStateDivideGUILD_BATTLEUE_140080160.h" />
    <ClInclude Include="headers\GetTermCNormalGuildBattleStateFinGUILD_BATTLEUEAAA_1400802E0.h" />
    <ClInclude Include="headers\GetTermCNormalGuildBattleStateInBattleGUILD_BATTLE_1400800D0.h" />
    <ClInclude Include="headers\GetTermCNormalGuildBattleStateNotifyGUILD_BATTLEUE_14007FC20.h" />
    <ClInclude Include="headers\GetTermCNormalGuildBattleStateReadyGUILD_BATTLEUEA_14007FCF0.h" />
    <ClInclude Include="headers\GetTermCNormalGuildBattleStateReturnGUILD_BATTLEUE_140080220.h" />
    <ClInclude Include="headers\GetTimeCGuildBattleScheduleGUILD_BATTLEQEAAAVCTime_1403DEAC0.h" />
    <ClInclude Include="headers\GetTodayDayIDCGuildBattleScheduleManagerGUILD_BATT_1403D9A40.h" />
    <ClInclude Include="headers\GetTodaySLIDByMapCGuildBattleScheduleManagerGUILD__1403DD230.h" />
    <ClInclude Include="headers\GetToleranceProbMonsterSFContDamageToleracneQEAAMX_14014CAF0.h" />
    <ClInclude Include="headers\GetTomorrowDayIDCGuildBattleScheduleManagerGUILD_B_1403D9AB0.h" />
    <ClInclude Include="headers\GetTomorrowSLIDByMapCGuildBattleScheduleManagerGUI_1403DD280.h" />
    <ClInclude Include="headers\GetTopGoalMemberCNormalGuildBattleGuildGUILD_BATTL_1403E0B00.h" />
    <ClInclude Include="headers\GetTopKillMemberCNormalGuildBattleGuildGUILD_BATTL_1403E0A70.h" />
    <ClInclude Include="headers\GetWinnerGradeCBattleTournamentInfoQEAAEKPEADZ_1403FEC30.h" />
    <ClInclude Include="headers\Get_CashEvent_StatusCashItemRemoteStoreQEAAEEZ_1402FAC70.h" />
    <ClInclude Include="headers\get_cde_statusCashItemRemoteStoreQEAAEXZ_1402F7380.h" />
    <ClInclude Include="headers\Get_Conditional_Event_NameCashItemRemoteStoreQEAAX_1402FC8A0.h" />
    <ClInclude Include="headers\Get_Conditional_Event_StatusCashItemRemoteStoreQEA_1402FC460.h" />
    <ClInclude Include="headers\GoalCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEAVCN_1403E1600.h" />
    <ClInclude Include="headers\GoalCNormalGuildBattleGUILD_BATTLEQEAAEKHZ_1403E4CA0.h" />
    <ClInclude Include="headers\GoalCNormalGuildBattleStateGUILD_BATTLEUEAAXXZ_14007FBB0.h" />
    <ClInclude Include="headers\GoodsListBuyByCashCashItemRemoteStoreAEAA_NGPEADZ_1403009C0.h" />
    <ClInclude Include="headers\GoodsListBuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_140300C60.h" />
    <ClInclude Include="headers\GoodsListCashItemRemoteStoreQEAA_NGPEADZ_1402F5220.h" />
    <ClInclude Include="headers\GotoCGuildBattleStateListGUILD_BATTLEQEAAHXZ_1403DF2C0.h" />
    <ClInclude Include="headers\GotoStateCGuildBattleStateListGUILD_BATTLEQEAA_NHZ_1403F3370.h" />
    <ClInclude Include="headers\GuildBattleBlockReportCNetworkEXAEAA_NHPEADZ_1401D7F40.h" />
    <ClInclude Include="headers\GuildBattleCurrentBattleInfoRequestCNetworkEXAEAA__1401C8770.h" />
    <ClInclude Include="headers\GuildBattleGetGravityStoneRequestCNetworkEXAEAA_NH_1401C8940.h" />
    <ClInclude Include="headers\GuildBattleGoalRequestCNetworkEXAEAA_NHPEADZ_1401C89E0.h" />
    <ClInclude Include="headers\GuildBattleJoinGuildBattleRequestCNetworkEXAEAA_NH_1401C8820.h" />
    <ClInclude Include="headers\GuildBattlePossibleGuildBattleListCNetworkEXAEAA_N_1401C84A0.h" />
    <ClInclude Include="headers\GuildBattleRankListRequestCNetworkEXAEAA_NHPEADZ_1401C8560.h" />
    <ClInclude Include="headers\GuildBattleReservedScheduleRequestCNetworkEXAEAA_N_1401C8670.h" />
    <ClInclude Include="headers\GuildBattleResultLogCNormalGuildBattleGUILD_BATTLE_1403E6E10.h" />
    <ClInclude Include="headers\GuildBattleResultLogNotifyWebCNormalGuildBattleGUI_1403E83E0.h" />
    <ClInclude Include="headers\GuildBattleResultLogPushDBLogCNormalGuildBattleGUI_1403E7D30.h" />
    <ClInclude Include="headers\GuildBattleSuggestRequestToDestGuildCGuildQEAAEKKK_140257C30.h" />
    <ClInclude Include="headers\GuildBattleTakeGravityStoneRequestCNetworkEXAEAA_N_1401C88B0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E7FF0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E82F0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E83B0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8780.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8880.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8A50.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8B90.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E8DE0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9040.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9240.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9300.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E93C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9480.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E9500.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__GUILD__1406E95C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8070.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8370.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8430.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8800.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8900.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8AD0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8C10.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E8E60.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E90C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E92C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9380.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9440.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9580.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__NORMAL_1406E9640.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8030.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8330.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E83F0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E87C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E88C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8A90.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8BD0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E8E20.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9080.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9280.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9340.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9400.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E94C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9540.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_atexit_destructor_for__STATE__1406E9600.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DA120.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DA8F0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DAA70.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DD460.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DDFB0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DF520.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406DF7C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E0540.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E1270.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E3F80.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4140.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E42C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4440.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4570.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__GUILD_BATTLE_1406E4770.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DA1D0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DA9A0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DAB20.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DD510.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DE060.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DF5D0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406DF870.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E05F0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E1320.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4030.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E41F0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4370.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4620.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__NORMAL_GUILD_1406E4820.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DA170.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DA940.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DAAC0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DD4B0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DE000.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DF570.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406DF810.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E0590.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E12C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E3FD0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4190.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4310.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E4490.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E45C0.h" />
    <ClInclude Include="headers\GUILD_BATTLE_dynamic_initializer_for__STATE_LIST_N_1406E47C0.h" />
    <ClInclude Include="headers\IncPvpPointCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403E1730.h" />
    <ClInclude Include="headers\IncPvpPointCNormalGuildBattleGuildMemberGUILD_BATT_1403DFCE0.h" />
    <ClInclude Include="headers\IncVerCReservedGuildSchedulePageGUILD_BATTLEQEAAXX_1403CBD40.h" />
    <ClInclude Include="headers\inform_cashdiscount_eventCashItemRemoteStoreQEAAXG_1402F6F60.h" />
    <ClInclude Include="headers\inform_cashdiscount_status_allCashItemRemoteStoreQ_1402F6FC0.h" />
    <ClInclude Include="headers\Inform_CashEventCashItemRemoteStoreQEAAXGZ_1402FAFC0.h" />
    <ClInclude Include="headers\Inform_CashEvent_Status_AllCashItemRemoteStoreQEAA_1402FADC0.h" />
    <ClInclude Include="headers\Inform_ConditionalEventCashItemRemoteStoreQEAAXGZ_1402FC970.h" />
    <ClInclude Include="headers\Inform_ConditionalEvent_Status_AllCashItemRemoteSt_1402FC640.h" />
    <ClInclude Include="headers\InGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_SYN_D_1401F46D0.h" />
    <ClInclude Include="headers\InGuildbattleRewardMoneyCMainThreadQEAAXPEAU_DB_QR_1401F4AD0.h" />
    <ClInclude Include="headers\InitCBattleTournamentInfoQEAAXXZ_1403FEAC0.h" />
    <ClInclude Include="headers\InitCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA_1403CE000.h" />
    <ClInclude Include="headers\InitCGuildBattleControllerQEAA_NXZ_1403D5820.h" />
    <ClInclude Include="headers\InitCGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_1403CE8D0.h" />
    <ClInclude Include="headers\InitCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_1403CA4B0.h" />
    <ClInclude Include="headers\InitCGuildBattleReservedScheduleListManagerGUILD_B_1403CD4A0.h" />
    <ClInclude Include="headers\InitCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DBB90.h" />
    <ClInclude Include="headers\InitCGuildBattleRewardItemGUILD_BATTLEQEAA_NGZ_1403C8F30.h" />
    <ClInclude Include="headers\InitCGuildBattleRewardItemManagerGUILD_BATTLEQEAA__1403C9420.h" />
    <ClInclude Include="headers\InitCGuildBattleScheduleManagerGUILD_BATTLEQEAA_NI_1403DCB50.h" />
    <ClInclude Include="headers\InitCGuildBattleSchedulePoolGUILD_BATTLEQEAA_NIZ_1403DA6C0.h" />
    <ClInclude Include="headers\InitCGuildBattleSchedulerGUILD_BATTLEQEAA_NXZ_1403DD840.h" />
    <ClInclude Include="headers\InitCNormalGuildBattleFieldGUILD_BATTLEQEAA_NIZ_1403EB870.h" />
    <ClInclude Include="headers\InitCNormalGuildBattleFieldListGUILD_BATTLEQEAA_NX_1403EE4A0.h" />
    <ClInclude Include="headers\InitCNormalGuildBattleGUILD_BATTLEQEAAXPEAVCGuild0_1403E30D0.h" />
    <ClInclude Include="headers\InitCNormalGuildBattleGUILD_BATTLEQEAA_N_NIKKKKEZ_1403E3180.h" />
    <ClInclude Include="headers\InitCNormalGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_1403CEC70.h" />
    <ClInclude Include="headers\InitCNormalGuildBattleManagerGUILD_BATTLEQEAA_NXZ_1403D3670.h" />
    <ClInclude Include="headers\InitCNormalGuildBattleStateListPoolGUILD_BATTLEQEA_1403F24B0.h" />
    <ClInclude Include="headers\InitCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403C9790.h" />
    <ClInclude Include="headers\InitCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCC90.h" />
    <ClInclude Include="headers\InitCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CC420.h" />
    <ClInclude Include="headers\InitCReservedGuildSchedulePageGUILD_BATTLEQEAA_NEZ_1403CC170.h" />
    <ClInclude Include="headers\InitializeCashItemRemoteStoreQEAA_NXZ_1402F4EF0.h" />
    <ClInclude Include="headers\InitMonsterSFContDamageToleracneQEAAXMZ_140157EF0.h" />
    <ClInclude Include="headers\InitUseFieldCNormalGuildBattleFieldListGUILD_BATTL_1403EEE30.h" />
    <ClInclude Include="headers\Init_ATTACK_DELAY_CHECKERQEAAXXZ_140072E60.h" />
    <ClInclude Include="headers\init_eff_list_ATTACK_DELAY_CHECKERQEAAXXZ_140072F70.h" />
    <ClInclude Include="headers\init_mas_list_ATTACK_DELAY_CHECKERQEAAXXZ_140072F90.h" />
    <ClInclude Include="headers\InsertGuildBattleDefaultRecordCRFWorldDatabaseQEAA_1404A24B0.h" />
    <ClInclude Include="headers\InsertGuildBattleRankRecordCRFWorldDatabaseQEAA_NK_1404A3760.h" />
    <ClInclude Include="headers\InsertGuildBattleScheduleDefaultRecordCRFWorldData_1404A1070.h" />
    <ClInclude Include="headers\insertvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D16C0.h" />
    <ClInclude Include="headers\Insert_PatrirchItemChargeRefundCRFWorldDatabaseQEA_1404BE8B0.h" />
    <ClInclude Include="headers\Insert_PatrirchItemChargeRefundPatriarchElectProce_1402BC040.h" />
    <ClInclude Include="headers\Insert_RaceBattleLogCRFWorldDatabaseQEAA_NPEAU_rac_1404C1670.h" />
    <ClInclude Include="headers\InstanceCashItemRemoteStoreSAPEAV1XZ_140079810.h" />
    <ClInclude Include="headers\InstanceCCurrentGuildBattleInfoManagerGUILD_BATTLE_1403CDEC0.h" />
    <ClInclude Include="headers\InstanceCGuildBattleControllerSAPEAV1XZ_1403D56E0.h" />
    <ClInclude Include="headers\InstanceCGuildBattleLoggerGUILD_BATTLESAPEAV12XZ_1403CE790.h" />
    <ClInclude Include="headers\InstanceCGuildBattleRankManagerGUILD_BATTLESAPEAV1_1403CA370.h" />
    <ClInclude Include="headers\InstanceCGuildBattleReservedScheduleListManagerGUI_1403CD360.h" />
    <ClInclude Include="headers\InstanceCGuildBattleRewardItemManagerGUILD_BATTLES_1403D9790.h" />
    <ClInclude Include="headers\InstanceCGuildBattleScheduleManagerGUILD_BATTLESAP_1403DCA10.h" />
    <ClInclude Include="headers\InstanceCGuildBattleSchedulePoolGUILD_BATTLESAPEAV_1403DA580.h" />
    <ClInclude Include="headers\InstanceCGuildBattleSchedulerGUILD_BATTLESAPEAV12X_1403DD700.h" />
    <ClInclude Include="headers\InstanceCNormalGuildBattleFieldListGUILD_BATTLESAP_1403EE360.h" />
    <ClInclude Include="headers\InstanceCNormalGuildBattleManagerGUILD_BATTLESAPEA_1403D3530.h" />
    <ClInclude Include="headers\InstanceCNormalGuildBattleStateListPoolGUILD_BATTL_1403F2370.h" />
    <ClInclude Include="headers\InstanceCPossibleBattleGuildListManagerGUILD_BATTL_1403C9650.h" />
    <ClInclude Include="headers\IsAttackableInTownCGameObjectUEAA_NXZ_140072B70.h" />
    <ClInclude Include="headers\IsAttackableInTownCMonsterUEAA_NXZ_14014BB30.h" />
    <ClInclude Include="headers\IsAvailableSuggestCGuildBattleControllerQEAAEPEAVC_1403D5BD0.h" />
    <ClInclude Include="headers\IsBattleModeUseCRecallRequestQEAA_NXZ_14024FD00.h" />
    <ClInclude Include="headers\IsBeAttackedAbleAutominePersonalUEAA_N_NZ_1402DDD00.h" />
    <ClInclude Include="headers\IsBeAttackedAbleCAnimusUEAA_N_NZ_14012CE10.h" />
    <ClInclude Include="headers\IsBeAttackedAbleCGameObjectUEAA_N_NZ_14012E390.h" />
    <ClInclude Include="headers\IsBeAttackedAbleCGuardTowerUEAA_N_NZ_1401328F0.h" />
    <ClInclude Include="headers\IsBeAttackedAbleCHolyKeeperUEAA_N_NZ_140136B00.h" />
    <ClInclude Include="headers\IsBeAttackedAbleCHolyStoneUEAA_N_NZ_140138ED0.h" />
    <ClInclude Include="headers\IsBeAttackedAbleCMonsterUEAA_N_NZ_1401468F0.h" />
    <ClInclude Include="headers\IsBeAttackedAbleCTrapUEAA_N_NZ_1401412E0.h" />
    <ClInclude Include="headers\IsBuyCashItemByGoldCashItemRemoteStoreQEAA_NXZ_1400F0860.h" />
    <ClInclude Include="headers\IsCashItemYAHEKZ_14003F600.h" />
    <ClInclude Include="headers\IsCharInSectorCAttackSAHQEAM00MMZ_14016D920.h" />
    <ClInclude Include="headers\IsCommitteeMemberCNormalGuildBattleGuildMemberGUIL_1403E0220.h" />
    <ClInclude Include="headers\IsCompleteBattle_guild_battle_suggest_matterQEAA_N_1403D0800.h" />
    <ClInclude Include="headers\isConEventTimeCashItemRemoteStoreQEAA_NXZ_1402FBB80.h" />
    <ClInclude Include="headers\IsDayChangedCGuildBattleScheduleManagerGUILD_BATTL_1403DD530.h" />
    <ClInclude Include="headers\IsDelay_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008EC60.h" />
    <ClInclude Include="headers\IsDoneCGuildBattleReservedScheduleGUILD_BATTLEQEAA_1403DEC60.h" />
    <ClInclude Include="headers\IsDoneCGuildBattleReservedScheduleMapGroupGUILD_BA_1403DED60.h" />
    <ClInclude Include="headers\IsDoneCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DE9F0.h" />
    <ClInclude Include="headers\IsEmptyCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DE990.h" />
    <ClInclude Include="headers\IsEmptyCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1403DF8E0.h" />
    <ClInclude Include="headers\IsEmptyCNormalGuildBattleGuildMemberGUILD_BATTLEQE_1403EAD50.h" />
    <ClInclude Include="headers\IsEmptyCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9630.h" />
    <ClInclude Include="headers\IsEmptyTimeCGuildBattleReservedScheduleGUILD_BATTL_1403DABC0.h" />
    <ClInclude Include="headers\IsEmptyTimeCGuildBattleReservedScheduleMapGroupGUI_1403DC3C0.h" />
    <ClInclude Include="headers\IsEmptyTimeCGuildBattleScheduleManagerGUILD_BATTLE_1403D9870.h" />
    <ClInclude Include="headers\IsEnableStartCNormalGuildBattleGuildMemberGUILD_BA_1403E01B0.h" />
    <ClInclude Include="headers\IsEventTimeCashItemRemoteStoreQEAA_NEZ_1402FACA0.h" />
    <ClInclude Include="headers\IsExistCNormalGuildBattleGuildMemberGUILD_BATTLEQE_1403E0130.h" />
    <ClInclude Include="headers\IsInBattleCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14007C0A0.h" />
    <ClInclude Include="headers\IsInBattleCNormalGuildBattleStateListGUILD_BATTLEQ_14007C0F0.h" />
    <ClInclude Include="headers\IsInBattleRegenStateCNormalGuildBattleGUILD_BATTLE_1403D94B0.h" />
    <ClInclude Include="headers\IsInBattleRegenStateCNormalGuildBattleStateInBattl_1403D9570.h" />
    <ClInclude Include="headers\IsInBattleRegenStateCNormalGuildBattleStateListGUI_1403D9500.h" />
    <ClInclude Include="headers\IsInBattleRegenStateCNormalGuildBattleStateRoundLi_1403D95C0.h" />
    <ClInclude Include="headers\IsJoinMemberCNormalGuildBattleGuildGUILD_BATTLEIEA_1403E2BC0.h" />
    <ClInclude Include="headers\IsMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA_NK_1403EB1B0.h" />
    <ClInclude Include="headers\IsMemberGuildCNormalGuildBattleGUILD_BATTLEQEAA_NK_1403D9690.h" />
    <ClInclude Include="headers\IsNullCGuildBattleRewardItemGUILD_BATTLEQEBA_NXZ_1403EAEA0.h" />
    <ClInclude Include="headers\IsPreAttackAbleMonCMonsterQEAAHXZ_140155460.h" />
    <ClInclude Include="headers\IsProcCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DEAF0.h" />
    <ClInclude Include="headers\IsProcCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1403D9250.h" />
    <ClInclude Include="headers\IsProcCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403D9200.h" />
    <ClInclude Include="headers\IsReadyOrCountStateCNormalGuildBattleGUILD_BATTLEQ_14007BFD0.h" />
    <ClInclude Include="headers\IsReadyOrCountStateCNormalGuildBattleStateListGUIL_14007C020.h" />
    <ClInclude Include="headers\IsRegistedMapInxCNormalGuildBattleFieldListGUILD_B_1403EF0C0.h" />
    <ClInclude Include="headers\IsReStartCNormalGuildBattleGuildGUILD_BATTLEQEAAHK_1403E0970.h" />
    <ClInclude Include="headers\IsReStartCNormalGuildBattleGuildMemberGUILD_BATTLE_1403EADD0.h" />
    <ClInclude Include="headers\IsReStartCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E41A0.h" />
    <ClInclude Include="headers\IsSFContDamageMonsterSFContDamageToleracneQEAA_NXZ_140157F90.h" />
    <ClInclude Include="headers\IsStorageCodeWithItemKindYAHHHZ_14003BD10.h" />
    <ClInclude Include="headers\IsWaitCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_1403DEB50.h" />
    <ClInclude Include="headers\IsWhiteSpaceCondensedTiXmlBaseSA_NXZ_140530810.h" />
    <ClInclude Include="headers\IsWhiteSpaceTiXmlBaseKA_NDZ_140530820.h" />
    <ClInclude Include="headers\Is_Battle_ModeCGameObjectUEAA_NXZ_14012CBC0.h" />
    <ClInclude Include="headers\is_cde_timeCashItemRemoteStoreQEAA_NXZ_1402F7070.h" />
    <ClInclude Include="headers\JoinCNormalGuildBattleGuildGUILD_BATTLEQEAAEKEAEAH_1403E0B90.h" />
    <ClInclude Include="headers\JoinCNormalGuildBattleGuildMemberGUILD_BATTLEQEAAX_1403DFA30.h" />
    <ClInclude Include="headers\JoinCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E3B90.h" />
    <ClInclude Include="headers\JoinCNormalGuildBattleManagerGUILD_BATTLEQEAAXHKKZ_1403D4420.h" />
    <ClInclude Include="headers\JoinGuildCGuildBattleControllerQEAAXHKKZ_1400AD450.h" />
    <ClInclude Include="headers\JoinGuildCNormalGuildBattleManagerGUILD_BATTLEQEAA_1403D4BB0.h" />
    <ClInclude Include="headers\JudgeBattleCNormalGuildBattleGUILD_BATTLEQEAAEXZ_1403E6400.h" />
    <ClInclude Include="headers\j_0allocatorVCGuildBattleRewardItemGUILD_BATTLEstd_1400029C8.h" />
    <ClInclude Include="headers\j_0allocatorVCGuildBattleRewardItemGUILD_BATTLEstd_14000CC5C.h" />
    <ClInclude Include="headers\j_0CashItemRemoteStoreQEAAXZ_140013930.h" />
    <ClInclude Include="headers\j_0CBattleTournamentInfoQEAAXZ_140008C01.h" />
    <ClInclude Include="headers\j_0CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAX_1400017BC.h" />
    <ClInclude Include="headers\j_0CGuildBattleControllerIEAAXZ_140002748.h" />
    <ClInclude Include="headers\j_0CGuildBattleGUILD_BATTLEQEAAXZ_140011EE1.h" />
    <ClInclude Include="headers\j_0CGuildBattleLoggerGUILD_BATTLEIEAAXZ_140001870.h" />
    <ClInclude Include="headers\j_0CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_1400064FB.h" />
    <ClInclude Include="headers\j_0CGuildBattleReservedScheduleGUILD_BATTLEQEAAIZ_14000ED36.h" />
    <ClInclude Include="headers\j_0CGuildBattleReservedScheduleListManagerGUILD_BA_14000559C.h" />
    <ClInclude Include="headers\j_0CGuildBattleReservedScheduleMapGroupGUILD_BATTL_14000AD85.h" />
    <ClInclude Include="headers\j_0CGuildBattleRewardItemGUILD_BATTLEQEAAXZ_14000764E.h" />
    <ClInclude Include="headers\j_0CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_140010DCA.h" />
    <ClInclude Include="headers\j_0CGuildBattleScheduleGUILD_BATTLEQEAAKZ_140013C32.h" />
    <ClInclude Include="headers\j_0CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_140004E4E.h" />
    <ClInclude Include="headers\j_0CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_14000697E.h" />
    <ClInclude Include="headers\j_0CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_140005D30.h" />
    <ClInclude Include="headers\j_0CGuildBattleStateGUILD_BATTLEQEAAXZ_140011E50.h" />
    <ClInclude Include="headers\j_0CGuildBattleStateListGUILD_BATTLEQEAAHHIZ_140008C9C.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_1400114D2.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_1400080D5.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleGuildGUILD_BATTLEQEAAEZ_14000B596.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_1400058E4.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleGUILD_BATTLEQEAAKZ_140011E4B.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_140011388.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_140005673.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateCountDownGUILD_BATTLEQEA_14000BC80.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_14000C1C6.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140010FDC.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14000CC11.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateInBattleGUILD_BATTLEQEAA_14000253B.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_1400127B0.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateListPoolGUILD_BATTLEIEAA_14000323D.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_140005BB4.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14000A943.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_14000FCB3.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_1400020C7.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateRoundListGUILD_BATTLEQEA_140002F04.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateRoundProcessGUILD_BATTLE_140003ABC.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateRoundReturnStartPosGUILD_1400118DD.h" />
    <ClInclude Include="headers\j_0CNormalGuildBattleStateRoundStartGUILD_BATTLEQE_1400085DF.h" />
    <ClInclude Include="headers\j_0CPossibleBattleGuildListManagerGUILD_BATTLEIEAA_14000CCF7.h" />
    <ClInclude Include="headers\j_0CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAX_14000DBC5.h" />
    <ClInclude Include="headers\j_0CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAX_140006799.h" />
    <ClInclude Include="headers\j_0CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_140012198.h" />
    <ClInclude Include="headers\j_0CRFCashItemDatabaseQEAAXZ_140013EA8.h" />
    <ClInclude Include="headers\j_0MonsterSFContDamageToleracneQEAAXZ_140011072.h" />
    <ClInclude Include="headers\j_0vectorVCGuildBattleRewardItemGUILD_BATTLEValloc_140011A18.h" />
    <ClInclude Include="headers\j_0_attack_count_result_zoclQEAAXZ_14000E692.h" />
    <ClInclude Include="headers\j_0_ATTACK_DELAY_CHECKERQEAAXZ_14000C0E0.h" />
    <ClInclude Include="headers\j_0_attack_force_result_zoclQEAAXZ_1400039FE.h" />
    <ClInclude Include="headers\j_0_attack_gen_result_zoclQEAAXZ_14000C086.h" />
    <ClInclude Include="headers\j_0_attack_keeper_inform_zoclQEAAXZ_1400013E8.h" />
    <ClInclude Include="headers\j_0_attack_paramQEAAXZ_14000AE4D.h" />
    <ClInclude Include="headers\j_0_attack_selfdestruction_result_zoclQEAAXZ_140012C56.h" />
    <ClInclude Include="headers\j_0_attack_siege_result_zoclQEAAXZ_140010F8C.h" />
    <ClInclude Include="headers\j_0_attack_trap_inform_zoclQEAAXZ_1400033F5.h" />
    <ClInclude Include="headers\j_0_attack_unit_result_zoclQEAAXZ_14000B6FE.h" />
    <ClInclude Include="headers\j_0_be_damaged_charQEAAXZ_14000A335.h" />
    <ClInclude Include="headers\j_0_eff_list_ATTACK_DELAY_CHECKERQEAAXZ_14000A560.h" />
    <ClInclude Include="headers\j_0_guild_battle_suggest_matterQEAAXZ_140004B56.h" />
    <ClInclude Include="headers\j_0_mas_list_ATTACK_DELAY_CHECKERQEAAXZ_14000A82B.h" />
    <ClInclude Include="headers\j_0_param_cashitem_dblogQEAAKZ_14000CE0F.h" />
    <ClInclude Include="headers\j_0_personal_automine_attacked_zoclQEAAXZ_14000471E.h" />
    <ClInclude Include="headers\j_0_possible_battle_guild_list_result_zoclQEAAXZ_14000DC92.h" />
    <ClInclude Include="headers\j_0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_140001DF2.h" />
    <ClInclude Include="headers\j_0_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_1400085AD.h" />
    <ClInclude Include="headers\j_0_remain_num_of_goodCashItemRemoteStoreQEAAXZ_14000560F.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorVCGuildBattleRewardItemGU_140001393.h" />
    <ClInclude Include="headers\j_0_Vector_const_iteratorVCGuildBattleRewardItemGU_140006127.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_140002E2D.h" />
    <ClInclude Include="headers\j_0_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_14000F79F.h" />
    <ClInclude Include="headers\j_0_Vector_valVCGuildBattleRewardItemGUILD_BATTLEV_140007DC4.h" />
    <ClInclude Include="headers\j_1CashItemRemoteStoreQEAAXZ_140011815.h" />
    <ClInclude Include="headers\j_1CBattleTournamentInfoQEAAXZ_14000B604.h" />
    <ClInclude Include="headers\j_1CCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAX_14000AD17.h" />
    <ClInclude Include="headers\j_1CGuildBattleControllerIEAAXZ_14000C4FA.h" />
    <ClInclude Include="headers\j_1CGuildBattleGUILD_BATTLEQEAAXZ_14000B41A.h" />
    <ClInclude Include="headers\j_1CGuildBattleLoggerGUILD_BATTLEIEAAXZ_14000FE0C.h" />
    <ClInclude Include="headers\j_1CGuildBattleRankManagerGUILD_BATTLEIEAAXZ_140007243.h" />
    <ClInclude Include="headers\j_1CGuildBattleReservedScheduleGUILD_BATTLEQEAAXZ_14000C540.h" />
    <ClInclude Include="headers\j_1CGuildBattleReservedScheduleListManagerGUILD_BA_14000C4B4.h" />
    <ClInclude Include="headers\j_1CGuildBattleReservedScheduleMapGroupGUILD_BATTL_140009075.h" />
    <ClInclude Include="headers\j_1CGuildBattleRewardItemManagerGUILD_BATTLEIEAAXZ_140007DE2.h" />
    <ClInclude Include="headers\j_1CGuildBattleScheduleGUILD_BATTLEQEAAXZ_1400037A1.h" />
    <ClInclude Include="headers\j_1CGuildBattleScheduleManagerGUILD_BATTLEIEAAXZ_140013327.h" />
    <ClInclude Include="headers\j_1CGuildBattleSchedulePoolGUILD_BATTLEIEAAXZ_1400074C3.h" />
    <ClInclude Include="headers\j_1CGuildBattleSchedulerGUILD_BATTLEIEAAXZ_14000BF82.h" />
    <ClInclude Include="headers\j_1CGuildBattleStateGUILD_BATTLEQEAAXZ_140010217.h" />
    <ClInclude Include="headers\j_1CGuildBattleStateListGUILD_BATTLEQEAAXZ_14000CEA5.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleFieldGUILD_BATTLEQEAAXZ_140010145.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleFieldListGUILD_BATTLEIEAAXZ_14000E2FF.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleGuildGUILD_BATTLEQEAAXZ_140013A1B.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleGuildMemberGUILD_BATTLEQEAAXZ_14001133D.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleGUILD_BATTLEQEAAXZ_14000FF38.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleLoggerGUILD_BATTLEQEAAXZ_14000A533.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleManagerGUILD_BATTLEIEAAXZ_1400066B3.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateCountDownGUILD_BATTLEQEA_140009CF0.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateDivideGUILD_BATTLEQEAAXZ_140002185.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateFinGUILD_BATTLEQEAAXZ_140005C1D.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateGUILD_BATTLEQEAAXZ_14000D648.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateInBattleGUILD_BATTLEQEAA_140009831.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateListGUILD_BATTLEQEAAXZ_140004D45.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateListPoolGUILD_BATTLEIEAA_14000D463.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateNotifyGUILD_BATTLEQEAAXZ_140007A90.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateReadyGUILD_BATTLEQEAAXZ_14000448F.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateReturnGUILD_BATTLEQEAAXZ_14000312F.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateRoundGUILD_BATTLEQEAAXZ_14000358F.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateRoundListGUILD_BATTLEQEA_14000696F.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateRoundProcessGUILD_BATTLE_14000EF7F.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateRoundReturnStartPosGUILD_14000A349.h" />
    <ClInclude Include="headers\j_1CNormalGuildBattleStateRoundStartGUILD_BATTLEQE_1400012BC.h" />
    <ClInclude Include="headers\j_1CPossibleBattleGuildListManagerGUILD_BATTLEIEAA_1400069E7.h" />
    <ClInclude Include="headers\j_1CReservedGuildScheduleDayGroupGUILD_BATTLEQEAAX_140006E6A.h" />
    <ClInclude Include="headers\j_1CReservedGuildScheduleMapGroupGUILD_BATTLEQEAAX_14000ABE1.h" />
    <ClInclude Include="headers\j_1CReservedGuildSchedulePageGUILD_BATTLEQEAAXZ_14000DD32.h" />
    <ClInclude Include="headers\j_1CRFCashItemDatabaseUEAAXZ_14000F1AF.h" />
    <ClInclude Include="headers\j_1vectorVCGuildBattleRewardItemGUILD_BATTLEValloc_14000CA36.h" />
    <ClInclude Include="headers\j_1_param_cashitem_dblogQEAAXZ_14000ACE0.h" />
    <ClInclude Include="headers\j_1_RanitVCGuildBattleRewardItemGUILD_BATTLE_JPEBV_140013AE3.h" />
    <ClInclude Include="headers\j_1_Vector_const_iteratorVCGuildBattleRewardItemGU_1400069C9.h" />
    <ClInclude Include="headers\j_1_Vector_iteratorVCGuildBattleRewardItemGUILD_BA_14000C54A.h" />
    <ClInclude Include="headers\j_4CGuildBattleReservedScheduleGUILD_BATTLEQEAAAEB_14000ECE6.h" />
    <ClInclude Include="headers\j_4CGuildBattleReservedScheduleMapGroupGUILD_BATTL_14000983B.h" />
    <ClInclude Include="headers\j_4CGuildBattleScheduleGUILD_BATTLEQEAAAEBV01AEBV0_14000E138.h" />
    <ClInclude Include="headers\j_8_Vector_const_iteratorVCGuildBattleRewardItemGU_1400104F1.h" />
    <ClInclude Include="headers\j_9_Vector_const_iteratorVCGuildBattleRewardItemGU_14000C7DE.h" />
    <ClInclude Include="headers\j_accHitTestCFormViewUEAAJJJPEAUtagVARIANTZ_140006DC5.h" />
    <ClInclude Include="headers\j_Action_Attack_OnLoopDfAIMgrSAXPEAVUs_HFSMKPEAXZ_140008D23.h" />
    <ClInclude Include="headers\j_AddCGuildBattleControllerQEAAEPEAVCGuild0KEKZ_14000ECFF.h" />
    <ClInclude Include="headers\j_AddCGuildBattleControllerQEAAEPEAVCGuild0KKEKZ_1400092CD.h" />
    <ClInclude Include="headers\j_AddCGuildBattleReservedScheduleGUILD_BATTLEQEAAE_140012021.h" />
    <ClInclude Include="headers\j_AddCGuildBattleReservedScheduleMapGroupGUILD_BAT_14000BC5D.h" />
    <ClInclude Include="headers\j_AddCGuildBattleScheduleManagerGUILD_BATTLEQEAAEI_14000774D.h" />
    <ClInclude Include="headers\j_AddCNormalGuildBattleManagerGUILD_BATTLEQEAAEPEA_1400042EB.h" />
    <ClInclude Include="headers\j_AddCompleteCGuildBattleControllerQEAAXEIKKZ_14000FB91.h" />
    <ClInclude Include="headers\j_AddCompleteCNormalGuildBattleGUILD_BATTLEQEAAXEZ_14001316F.h" />
    <ClInclude Include="headers\j_AddCompleteCNormalGuildBattleManagerGUILD_BATTLE_140003477.h" />
    <ClInclude Include="headers\j_AddDefaultDBRecordCNormalGuildBattleManagerGUILD_140013B5B.h" />
    <ClInclude Include="headers\j_AddDefaultDBTableCGuildBattleScheduleManagerGUIL_1400086F2.h" />
    <ClInclude Include="headers\j_AddGoldCntCNormalGuildBattleGuildMemberGUILD_BAT_140001898.h" />
    <ClInclude Include="headers\j_AddGuildBattleSchduleCMainThreadQEAAXPEAU_DB_QRY_140009BF6.h" />
    <ClInclude Include="headers\j_AddKillCntCNormalGuildBattleGuildMemberGUILD_BAT_14000B249.h" />
    <ClInclude Include="headers\j_AddScheduleCGuildBattleControllerQEAAEPEADZ_140007D47.h" />
    <ClInclude Include="headers\j_AdvanceCGuildBattleStateListGUILD_BATTLEIEAAXHZ_140009665.h" />
    <ClInclude Include="headers\j_AdvanceRegenStateCNormalGuildBattleStateInBattle_1400026E4.h" />
    <ClInclude Include="headers\j_AdvanceRegenStateCNormalGuildBattleStateListGUIL_14000F628.h" />
    <ClInclude Include="headers\j_allocateallocatorVCGuildBattleRewardItemGUILD_BA_140009ECB.h" />
    <ClInclude Include="headers\j_AreaDamageProcCAttackQEAAXHHPEAMH_NZ_140006B09.h" />
    <ClInclude Include="headers\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEIEAAXH_14000ED54.h" />
    <ClInclude Include="headers\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXH_14001347B.h" />
    <ClInclude Include="headers\j_AskJoinCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_1400119C3.h" />
    <ClInclude Include="headers\j_AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXHKKZ_14000EA02.h" />
    <ClInclude Include="headers\j_AskJoinCNormalGuildBattleGUILD_BATTLEQEAAXXZ_14000FB6E.h" />
    <ClInclude Include="headers\j_assignvectorVCGuildBattleRewardItemGUILD_BATTLEV_14000E3DB.h" />
    <ClInclude Include="headers\j_AttackableHeightCAnimusUEAAHXZ_140010000.h" />
    <ClInclude Include="headers\j_AttackableHeightCGameObjectUEAAHXZ_14000B3E3.h" />
    <ClInclude Include="headers\j_AttackableHeightCGuardTowerUEAAHXZ_14000AF4C.h" />
    <ClInclude Include="headers\j_AttackableHeightCMonsterUEAAHXZ_1400048E0.h" />
    <ClInclude Include="headers\j_AttackableHeightCTrapUEAAHXZ_14000C5B3.h" />
    <ClInclude Include="headers\j_AttackCAnimusQEAA_NKZ_140001C94.h" />
    <ClInclude Include="headers\j_AttackCNuclearBombQEAAXHHZ_14000F88F.h" />
    <ClInclude Include="headers\j_AttackForceCAttackQEAAXPEAU_attack_param_NZ_14001098D.h" />
    <ClInclude Include="headers\j_AttackForceRequestCNetworkEXAEAA_NHPEADZ_1400067B7.h" />
    <ClInclude Include="headers\j_AttackGenCAttackQEAAXPEAU_attack_param_N1Z_140013714.h" />
    <ClInclude Include="headers\j_AttackMonsterForceCMonsterAttackQEAAXPEAU_attack_14000AF65.h" />
    <ClInclude Include="headers\j_AttackMonsterGenCMonsterAttackQEAAXPEAU_attack_p_14000999E.h" />
    <ClInclude Include="headers\j_AttackObjectCMonsterQEAAHHPEAVCGameObjectZ_140008F58.h" />
    <ClInclude Include="headers\j_AttackPersonalRequestCNetworkEXAEAA_NHPEADZ_14000EEA3.h" />
    <ClInclude Include="headers\j_AttackSiegeRequestCNetworkEXAEAA_NHPEADZ_14000510F.h" />
    <ClInclude Include="headers\j_AttackTestRequestCNetworkEXAEAA_NHPEADZ_1400111E4.h" />
    <ClInclude Include="headers\j_AttackUnitRequestCNetworkEXAEAA_NHPEADZ_14000B609.h" />
    <ClInclude Include="headers\j_AvectorVCGuildBattleRewardItemGUILD_BATTLEValloc_14000E36D.h" />
    <ClInclude Include="headers\j_beginvectorVCGuildBattleRewardItemGUILD_BATTLEVa_140013AC0.h" />
    <ClInclude Include="headers\j_BuyByCashCashItemRemoteStoreAEAA_NGPEADZ_140013D81.h" />
    <ClInclude Include="headers\j_BuyByGoldCashItemRemoteStoreAEAA_NGPEADZ_140001078.h" />
    <ClInclude Include="headers\j_BuyCashItemRemoteStoreQEAA_NGPEADZ_140006EE2.h" />
    <ClInclude Include="headers\j_BuyLimSaleCashItemRemoteStoreQEAAGEKZ_140013737.h" />
    <ClInclude Include="headers\j_buy_to_inven_cashitemCMgrAvatorItemHistoryQEAAXE_140011126.h" />
    <ClInclude Include="headers\j_CalcAvgDamageCAttackQEAAXXZ_1400123A0.h" />
    <ClInclude Include="headers\j_CallProc_InsertCashItemLogCRFCashItemDatabaseQEA_140007E0A.h" />
    <ClInclude Include="headers\j_CallProc_RFOnlineAvg_EventCRFCashItemDatabaseQEA_140013FD9.h" />
    <ClInclude Include="headers\j_CallProc_RFOnlineUseCRFCashItemDatabaseQEAAHAEAU_140008DAA.h" />
    <ClInclude Include="headers\j_CallProc_RFOnlineUse_JapCRFCashItemDatabaseQEAAH_140013FB1.h" />
    <ClInclude Include="headers\j_CallProc_RFONLINE_CancelCRFCashItemDatabaseQEAAH_140004A61.h" />
    <ClInclude Include="headers\j_CallProc_RFONLINE_Cancel_JapCRFCashItemDatabaseQ_140008067.h" />
    <ClInclude Include="headers\j_CancelSuggestedMatter_guild_battle_suggest_matte_14001192D.h" />
    <ClInclude Include="headers\j_capacityvectorVCGuildBattleRewardItemGUILD_BATTL_140010884.h" />
    <ClInclude Include="headers\j_cashitem_del_from_invenCMgrAvatorItemHistoryQEAA_14000DF35.h" />
    <ClInclude Include="headers\j_ChangeDiscountEventTimeCashItemRemoteStoreQEAA_N_1400104CE.h" />
    <ClInclude Include="headers\j_ChangeEventTimeCashItemRemoteStoreQEAA_NEZ_1400049DA.h" />
    <ClInclude Include="headers\j_Change_Conditional_Event_StatusCashItemRemoteSto_14000F8E9.h" />
    <ClInclude Include="headers\j_CheatBuyCashItemRemoteStoreQEAA_NGPEBDHZ_140008FD5.h" />
    <ClInclude Include="headers\j_CheatDestroyStoneCNormalGuildBattleFieldGUILD_BA_14000BFB4.h" />
    <ClInclude Include="headers\j_CheatLoadCashAmountCashItemRemoteStoreQEAA_NGHZ_1400047AA.h" />
    <ClInclude Include="headers\j_CheatRegenStoneCNormalGuildBattleFieldGUILD_BATT_140002FE0.h" />
    <ClInclude Include="headers\j_CheckAttackCHolyKeeperQEAA_NXZ_140004FAC.h" />
    <ClInclude Include="headers\j_CheckBallTakeLimitTimeCNormalGuildBattleFieldGUI_14000B672.h" />
    <ClInclude Include="headers\j_CheckCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_14000A1E6.h" />
    <ClInclude Include="headers\j_CheckGetGravityStoneCNormalGuildBattleManagerGUI_14000BF50.h" />
    <ClInclude Include="headers\j_CheckGoalCNormalGuildBattleManagerGUILD_BATTLEQE_14000DBC0.h" />
    <ClInclude Include="headers\j_CheckGuildBattleLimitCAttackIEAA_NPEAVCGameObjec_140005E20.h" />
    <ClInclude Include="headers\j_CheckGuildBattleSuggestRequestToDestGuildCGuildQ_14000927D.h" />
    <ClInclude Include="headers\j_CheckIsInTownCNormalGuildBattleFieldGUILD_BATTLE_1400060A0.h" />
    <ClInclude Include="headers\j_CheckLoopCGuildBattleStateListGUILD_BATTLEIEAAHX_140008DB9.h" />
    <ClInclude Include="headers\j_CheckNextEventCGuildBattleReservedScheduleGUILD__14000AA0B.h" />
    <ClInclude Include="headers\j_CheckRecordCGuildBattleRankManagerGUILD_BATTLEIE_140001712.h" />
    <ClInclude Include="headers\j_CheckTakeGravityStoneCNormalGuildBattleManagerGU_14000B1A4.h" />
    <ClInclude Include="headers\j_Check_CashEvent_INICashItemRemoteStoreQEAA_NEZ_1400016FE.h" />
    <ClInclude Include="headers\j_Check_CashEvent_StatusCashItemRemoteStoreQEAAXEZ_140005F9C.h" />
    <ClInclude Include="headers\j_check_cash_discount_iniCashItemRemoteStoreQEAAXX_14000C234.h" />
    <ClInclude Include="headers\j_check_cash_discount_statusCashItemRemoteStoreQEA_1400110B3.h" />
    <ClInclude Include="headers\j_Check_Conditional_Event_INICashItemRemoteStoreQE_1400088DC.h" />
    <ClInclude Include="headers\j_Check_Conditional_Event_StatusCashItemRemoteStor_14000B4FB.h" />
    <ClInclude Include="headers\j_Check_GrosssalesCashItemRemoteStoreQEAAXKZ_140002B7B.h" />
    <ClInclude Include="headers\j_check_loaded_cde_statusCashItemRemoteStoreQEAAXX_14000F6AF.h" />
    <ClInclude Include="headers\j_Check_Loaded_Event_StatusCashItemRemoteStoreQEAA_14000F1D2.h" />
    <ClInclude Include="headers\j_Check_Total_SellingCashItemRemoteStoreQEAAXXZ_14000AB50.h" />
    <ClInclude Include="headers\j_CleanUpBattleCNormalGuildBattleGuildGUILD_BATTLE_140011671.h" />
    <ClInclude Include="headers\j_CleanUpBattleCNormalGuildBattleGuildMemberGUILD__140013CA0.h" />
    <ClInclude Include="headers\j_CleanUpBattleCNormalGuildBattleGUILD_BATTLEQEAAX_1400080BC.h" />
    <ClInclude Include="headers\j_CleanUpCCurrentGuildBattleInfoManagerGUILD_BATTL_140003571.h" />
    <ClInclude Include="headers\j_CleanUpCGuildBattleControllerIEAAXXZ_14000FDF3.h" />
    <ClInclude Include="headers\j_CleanUpCGuildBattleRankManagerGUILD_BATTLEIEAAXX_14000A835.h" />
    <ClInclude Include="headers\j_CleanUpDanglingReservedScheduleCGuildBattleReser_140002F86.h" />
    <ClInclude Include="headers\j_CleanUpDanglingReservedScheduleCGuildBattleReser_1400039AE.h" />
    <ClInclude Include="headers\j_CleanUpDanglingReservedScheduleCGuildBattleSched_140013D68.h" />
    <ClInclude Include="headers\j_ClearAllCGuildBattleSchedulePoolGUILD_BATTLEQEAA_14000B343.h" />
    <ClInclude Include="headers\j_ClearBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_14000F6AA.h" />
    <ClInclude Include="headers\j_ClearByDayIDCGuildBattleSchedulePoolGUILD_BATTLE_14000DA9E.h" />
    <ClInclude Include="headers\j_ClearCCurrentGuildBattleInfoManagerGUILD_BATTLEQ_140004C23.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleControllerQEAAXXZ_14000ACE5.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleRankManagerGUILD_BATTLEIEAAXEZ_140012440.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleRankManagerGUILD_BATTLEQEAAXXZ_14000C67B.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleReservedScheduleGUILD_BATTLEQEA_140008A35.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleReservedScheduleGUILD_BATTLEQEA_140012742.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleReservedScheduleListManagerGUIL_140010CE9.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleReservedScheduleMapGroupGUILD_B_140007036.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleReservedScheduleMapGroupGUILD_B_1400128A5.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleScheduleGUILD_BATTLEQEAAXXZ_140006573.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleScheduleManagerGUILD_BATTLEAEAA_140012B02.h" />
    <ClInclude Include="headers\j_ClearCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1400016F9.h" />
    <ClInclude Include="headers\j_ClearCNormalGuildBattleGuildGUILD_BATTLEQEAAXXZ_14000289C.h" />
    <ClInclude Include="headers\j_ClearCNormalGuildBattleGuildMemberGUILD_BATTLEQE_14000AA4C.h" />
    <ClInclude Include="headers\j_ClearCNormalGuildBattleGUILD_BATTLEQEAAXXZ_14000DC1F.h" />
    <ClInclude Include="headers\j_ClearCNormalGuildBattleManagerGUILD_BATTLEIEAAXP_14000BB1D.h" />
    <ClInclude Include="headers\j_ClearCNormalGuildBattleManagerGUILD_BATTLEQEAAXX_14000D76F.h" />
    <ClInclude Include="headers\j_ClearCNormalGuildBattleStateListPoolGUILD_BATTLE_140011216.h" />
    <ClInclude Include="headers\j_ClearCPossibleBattleGuildListManagerGUILD_BATTLE_140007464.h" />
    <ClInclude Include="headers\j_ClearCReservedGuildScheduleDayGroupGUILD_BATTLEQ_1400090A2.h" />
    <ClInclude Include="headers\j_ClearCReservedGuildScheduleMapGroupGUILD_BATTLEQ_14000D062.h" />
    <ClInclude Include="headers\j_ClearCReservedGuildSchedulePageGUILD_BATTLEQEAA__140003C92.h" />
    <ClInclude Include="headers\j_ClearDBCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_140003BC5.h" />
    <ClInclude Include="headers\j_ClearDBRecordCNormalGuildBattleGUILD_BATTLEQEAA__140008ABC.h" />
    <ClInclude Include="headers\j_ClearElapsedScheduleCGuildBattleReservedSchedule_140009E71.h" />
    <ClInclude Include="headers\j_ClearGuildBattleCGuildQEAAXXZ_140008D87.h" />
    <ClInclude Include="headers\j_ClearInBattleStateCNormalGuildBattleGuildGUILD_B_14000693D.h" />
    <ClInclude Include="headers\j_ClearRegenCNormalGuildBattleFieldGUILD_BATTLEQEA_140002E96.h" />
    <ClInclude Include="headers\j_ClearTommorowScheduleByIDCGuildBattleScheduleMan_14000427D.h" />
    <ClInclude Include="headers\j_Clear_guild_battle_suggest_matterQEAAXXZ_14000AFDD.h" />
    <ClInclude Include="headers\j_CompleteClearGuildBattleRankCGuildBattleControll_1400139D0.h" />
    <ClInclude Include="headers\j_CompleteCreateGuildBattleRankTableCGuildBattleCo_140012355.h" />
    <ClInclude Include="headers\j_CompleteLoadGuildBattleTotalRecordCMainThreadAEA_140011158.h" />
    <ClInclude Include="headers\j_CompleteOutGuildbattleCostCGuildQEAAXKKKKZ_14000A59C.h" />
    <ClInclude Include="headers\j_CompleteUpdateRankCGuildBattleControllerQEAAXEEP_140006A0A.h" />
    <ClInclude Include="headers\j_CompleteUpdateReservedScheduleCGuildBattleContro_14001106D.h" />
    <ClInclude Include="headers\j_constructallocatorVCGuildBattleRewardItemGUILD_B_140011EA0.h" />
    <ClInclude Include="headers\j_CopyUseTimeFieldCGuildBattleReservedScheduleGUIL_140001140.h" />
    <ClInclude Include="headers\j_CopyUseTimeFieldCGuildBattleReservedScheduleMapG_1400047E6.h" />
    <ClInclude Include="headers\j_CreateFieldObjectCNormalGuildBattleFieldGUILD_BA_140004EEE.h" />
    <ClInclude Include="headers\j_CreateGuildBattleRankTableCRFWorldDatabaseQEAA_N_140012512.h" />
    <ClInclude Include="headers\j_CreateLogFileCGuildBattleLoggerGUILD_BATTLEQEAAX_1400118D3.h" />
    <ClInclude Include="headers\j_CreateLogFileCNormalGuildBattleGUILD_BATTLEQEAAX_140011C7F.h" />
    <ClInclude Include="headers\j_CreateLogFileCNormalGuildBattleLoggerGUILD_BATTL_14000E237.h" />
    <ClInclude Include="headers\j_CreateLoggerCNormalGuildBattleGUILD_BATTLEQEAA_N_14000D29C.h" />
    <ClInclude Include="headers\j_ct_StopBattleCHolyStoneSystemQEAA_NXZ_14000812F.h" />
    <ClInclude Include="headers\j_deallocateallocatorVCGuildBattleRewardItemGUILD__14000BC21.h" />
    <ClInclude Include="headers\j_DecideColorInxCNormalGuildBattleGUILD_BATTLEQEAA_14000DACB.h" />
    <ClInclude Include="headers\j_DecideWinCNormalGuildBattleGUILD_BATTLEIEAAEXZ_140003148.h" />
    <ClInclude Include="headers\j_DecPvpPointCNormalGuildBattleGuildGUILD_BATTLEQE_140009D3B.h" />
    <ClInclude Include="headers\j_DecPvpPointCNormalGuildBattleGuildMemberGUILD_BA_140010375.h" />
    <ClInclude Include="headers\j_DeleteGuildBattleInfoCRFWorldDatabaseQEAA_NXZ_14000EBCE.h" />
    <ClInclude Include="headers\j_DeleteGuildBattleScheduleInfoCRFWorldDatabaseQEA_140009B74.h" />
    <ClInclude Include="headers\j_DestGuildIsAvailableBattleRequestStateCGuildQEAA_140012472.h" />
    <ClInclude Include="headers\j_destroyallocatorVCGuildBattleRewardItemGUILD_BAT_140013174.h" />
    <ClInclude Include="headers\j_DestroyCCurrentGuildBattleInfoManagerGUILD_BATTL_140008F35.h" />
    <ClInclude Include="headers\j_DestroyCGuildBattleControllerSAXXZ_140003EEF.h" />
    <ClInclude Include="headers\j_DestroyCGuildBattleLoggerGUILD_BATTLESAXXZ_140005EC0.h" />
    <ClInclude Include="headers\j_DestroyCGuildBattleRankManagerGUILD_BATTLESAXXZ_14000B668.h" />
    <ClInclude Include="headers\j_DestroyCGuildBattleReservedScheduleListManagerGU_14000DC65.h" />
    <ClInclude Include="headers\j_DestroyCGuildBattleScheduleManagerGUILD_BATTLESA_14000BBA4.h" />
    <ClInclude Include="headers\j_DestroyCGuildBattleSchedulePoolGUILD_BATTLESAXXZ_140010CB7.h" />
    <ClInclude Include="headers\j_DestroyCGuildBattleSchedulerGUILD_BATTLESAXXZ_140011905.h" />
    <ClInclude Include="headers\j_DestroyCNormalGuildBattleFieldGUILD_BATTLEAEAAXX_140008F62.h" />
    <ClInclude Include="headers\j_DestroyCNormalGuildBattleFieldListGUILD_BATTLESA_1400083F5.h" />
    <ClInclude Include="headers\j_DestroyCNormalGuildBattleManagerGUILD_BATTLESAXX_14000361B.h" />
    <ClInclude Include="headers\j_DestroyCNormalGuildBattleStateListPoolGUILD_BATT_1400010FA.h" />
    <ClInclude Include="headers\j_DestroyCPossibleBattleGuildListManagerGUILD_BATT_14000D41D.h" />
    <ClInclude Include="headers\j_DestroyFieldObjectCNormalGuildBattleFieldGUILD_B_140011BBC.h" />
    <ClInclude Include="headers\j_dhRExtractSubStringCRFCashItemDatabaseQEAAXPEAD0_1400107C1.h" />
    <ClInclude Include="headers\j_DividePvpPointCNormalGuildBattleGUILD_BATTLEQEAA_14000305D.h" />
    <ClInclude Include="headers\j_DoDayChangedWorkCNormalGuildBattleManagerGUILD_B_140008CBA.h" />
    <ClInclude Include="headers\j_DoDayChangedWorkCPossibleBattleGuildListManagerG_140006302.h" />
    <ClInclude Include="headers\j_DropGravityStoneCNormalGuildBattleGUILD_BATTLEQE_140006BF9.h" />
    <ClInclude Include="headers\j_DropGravityStoneCNormalGuildBattleManagerGUILD_B_140011090.h" />
    <ClInclude Include="headers\j_endvectorVCGuildBattleRewardItemGUILD_BATTLEVall_140004642.h" />
    <ClInclude Include="headers\j_EnterCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuil_1400117C5.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateCountDownGUILD_BATTL_140010BF4.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateGUILD_BATTLEMEAAHPEA_140005FC9.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateGUILD_BATTLEUEAAHPEA_14000DC1A.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateInBattleGUILD_BATTLE_140008869.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateNotifyGUILD_BATTLEME_140012B34.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateReadyGUILD_BATTLEMEA_140005A10.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateRoundGUILD_BATTLEMEA_14000F25E.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateRoundGUILD_BATTLEUEA_140009043.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateRoundProcessGUILD_BA_14000E7FF.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateRoundReturnStartPosG_14000FD0D.h" />
    <ClInclude Include="headers\j_EnterCNormalGuildBattleStateRoundStartGUILD_BATT_14001307F.h" />
    <ClInclude Include="headers\j_erasevectorVCGuildBattleRewardItemGUILD_BATTLEVa_14000BD70.h" />
    <ClInclude Include="headers\j_fillPEAVCGuildBattleRewardItemGUILD_BATTLEV12std_140009642.h" />
    <ClInclude Include="headers\j_fill_eff_list_ATTACK_DELAY_CHECKERQEAA_NXZ_140013B60.h" />
    <ClInclude Include="headers\j_fill_mas_list_ATTACK_DELAY_CHECKERQEAA_NXZ_1400088D7.h" />
    <ClInclude Include="headers\j_FinCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildB_1400062A8.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateDivideGUILD_BATTLEMEAA_140009E26.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateFinGUILD_BATTLEMEAAHPE_140010CF3.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVC_14000B32F.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVC_1400084E0.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateInBattleGUILD_BATTLEME_14000F4E3.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateReturnGUILD_BATTLEMEAA_1400056CD.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateRoundGUILD_BATTLEMEAAH_1400031D9.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateRoundGUILD_BATTLEUEAAH_140011CA2.h" />
    <ClInclude Include="headers\j_FinCNormalGuildBattleStateRoundStartGUILD_BATTLE_1400139E9.h" />
    <ClInclude Include="headers\j_FindCashRecCashItemRemoteStoreSAPEBU_CashShop_fl_1400070D1.h" />
    <ClInclude Include="headers\j_FindCGuildBattleRankManagerGUILD_BATTLEIEAA_NEKA_140005326.h" />
    <ClInclude Include="headers\j_FindCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000CBB2.h" />
    <ClInclude Include="headers\j_FindCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000E4DF.h" />
    <ClInclude Include="headers\j_FindCReservedGuildSchedulePageGUILD_BATTLEQEAA_N_14000C662.h" />
    <ClInclude Include="headers\j_FlashDamageProcCAttackIEAAXHHHH_NZ_14000B186.h" />
    <ClInclude Include="headers\j_FlipCGuildBattleControllerQEAAXXZ_140007798.h" />
    <ClInclude Include="headers\j_FlipCGuildBattleReservedScheduleGUILD_BATTLEQEAA_14000249B.h" />
    <ClInclude Include="headers\j_FlipCGuildBattleReservedScheduleListManagerGUILD_140002432.h" />
    <ClInclude Include="headers\j_FlipCGuildBattleReservedScheduleMapGroupGUILD_BA_14000B069.h" />
    <ClInclude Include="headers\j_FlipCGuildBattleScheduleManagerGUILD_BATTLEAEAAX_140010546.h" />
    <ClInclude Include="headers\j_FlipCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_140003094.h" />
    <ClInclude Include="headers\j_FlipCReservedGuildScheduleDayGroupGUILD_BATTLEQE_140008AA8.h" />
    <ClInclude Include="headers\j_FlipCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000FFDD.h" />
    <ClInclude Include="headers\j_FlipCReservedGuildSchedulePageGUILD_BATTLEQEAAXX_140005DE9.h" />
    <ClInclude Include="headers\j_ForceNextCGuildBattleStateListGUILD_BATTLEIEAAXX_14001065E.h" />
    <ClInclude Include="headers\j_force_endup_cash_discount_eventCashItemRemoteSto_1400070D6.h" />
    <ClInclude Include="headers\j_Get1PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_140006037.h" />
    <ClInclude Include="headers\j_Get2PCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNorm_1400134B2.h" />
    <ClInclude Include="headers\j_GetAmountCGuildBattleRewardItemGUILD_BATTLEQEBAE_140007B8F.h" />
    <ClInclude Include="headers\j_GetANSIGuildNameCNormalGuildBattleGuildGUILD_BAT_14000A303.h" />
    <ClInclude Include="headers\j_GetAttackDelay_WEAPON_PARAMQEAAKHHZ_140008F3A.h" />
    <ClInclude Include="headers\j_GetAttackDPCAnimusUEAAHXZ_14000E417.h" />
    <ClInclude Include="headers\j_GetAttackDPCGameObjectUEAAHXZ_1400026C6.h" />
    <ClInclude Include="headers\j_GetAttackDPCGuardTowerUEAAHXZ_14000A8F3.h" />
    <ClInclude Include="headers\j_GetAttackDPCHolyKeeperUEAAHXZ_140009F20.h" />
    <ClInclude Include="headers\j_GetAttackDPCHolyStoneUEAAHXZ_140007C11.h" />
    <ClInclude Include="headers\j_GetAttackDPCMonsterUEAAHXZ_140011725.h" />
    <ClInclude Include="headers\j_GetAttackDPCTrapUEAAHXZ_1400110F9.h" />
    <ClInclude Include="headers\j_GetAttackPartCAnimusQEAAHXZ_140006D57.h" />
    <ClInclude Include="headers\j_GetAttackPartCMonsterQEAAHXZ_140005BD2.h" />
    <ClInclude Include="headers\j_GetAttackPivotCHolyKeeperQEAAPEAMXZ_14000B63B.h" />
    <ClInclude Include="headers\j_GetAttackRangeCAnimusUEAAMXZ_1400091E2.h" />
    <ClInclude Include="headers\j_GetAttackRangeCGameObjectUEAAMXZ_140010334.h" />
    <ClInclude Include="headers\j_GetAttackRangeCGuardTowerUEAAMXZ_140013642.h" />
    <ClInclude Include="headers\j_GetAttackRangeCHolyKeeperUEAAMXZ_14000E34F.h" />
    <ClInclude Include="headers\j_GetAttackRangeCMonsterUEAAMXZ_140008BF7.h" />
    <ClInclude Include="headers\j_GetAttackRangeCTrapUEAAMXZ_140004462.h" />
    <ClInclude Include="headers\j_GetAttackToolType_WEAPON_PARAMQEAAHXZ_14000F047.h" />
    <ClInclude Include="headers\j_GetBattleByGuildSerialCNormalGuildBattleManagerG_140013101.h" />
    <ClInclude Include="headers\j_GetBattleCNormalGuildBattleManagerGUILD_BATTLEIE_14000A4E8.h" />
    <ClInclude Include="headers\j_GetBattleModeTimeCMonsterAIQEAAKXZ_140002801.h" />
    <ClInclude Include="headers\j_GetBattleTimeCGuildBattleScheduleGUILD_BATTLEQEA_14000DB7F.h" />
    <ClInclude Include="headers\j_GetBattleTurmCGuildBattleScheduleGUILD_BATTLEQEA_140010A3C.h" />
    <ClInclude Include="headers\j_GetBlueCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNo_1400129EF.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataBRUEAAHPEAU_Ca_14000F547.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataCNUEAAHPEAU_Ca_1400135E8.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataESUEAAHPEAU_Ca_1400070A4.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataGBUEAAHPEAU_Ca_140009B51.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataIDUEAAHPEAU_Ca_1400099DA.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataJPUEAAHPEAU_Ca_14000A358.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataKRUEAAHPEAU_Ca_140009F8E.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataNULLUEAAHPEAU__140005FB0.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataPHUEAAHPEAU_Ca_1400076AD.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataRUUEAAHPEAU_Ca_140009E44.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataTHUEAAHPEAU_Ca_140010A05.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataTWUEAAHPEAU_Ca_1400062CB.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataUEAAHPEAU_Cash_14000D92C.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingDataUSUEAAHPEAU_Ca_140001352.h" />
    <ClInclude Include="headers\j_GetCashItemPriceCNationSettingManagerQEAAHPEAU_C_1400088B9.h" />
    <ClInclude Include="headers\j_GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVC_1400077B6.h" />
    <ClInclude Include="headers\j_GetCGuildBattleSchedulePoolGUILD_BATTLEQEAAPEAVC_140012AD0.h" />
    <ClInclude Include="headers\j_GetCircleZoneCGuildBattleControllerQEAAPEAVCGame_1400020B3.h" />
    <ClInclude Include="headers\j_GetCircleZoneCNormalGuildBattleFieldGUILD_BATTLE_140009AE3.h" />
    <ClInclude Include="headers\j_GetCircleZoneCNormalGuildBattleFieldListGUILD_BA_140010B72.h" />
    <ClInclude Include="headers\j_GetCNormalGuildBattleStateListPoolGUILD_BATTLEQE_14001139C.h" />
    <ClInclude Include="headers\j_GetColorInxCNormalGuildBattleGuildGUILD_BATTLEQE_140006EAB.h" />
    <ClInclude Include="headers\j_GetColorNameCNormalGuildBattleGuildGUILD_BATTLEQ_1400011A4.h" />
    <ClInclude Include="headers\j_GetCombatStateCMonsterQEAAEXZ_140008C4C.h" />
    <ClInclude Include="headers\j_GetCurScheduleIDCGuildBattleReservedScheduleGUIL_14000675D.h" />
    <ClInclude Include="headers\j_GetCurScheduleIDCGuildBattleReservedScheduleMapG_140006EB0.h" />
    <ClInclude Include="headers\j_GetCurScheduleIDCGuildBattleScheduleManagerGUILD_14000C6BC.h" />
    <ClInclude Include="headers\j_GetDamagedObjNumCNuclearBombQEAAHXZ_14000ABB9.h" />
    <ClInclude Include="headers\j_GetDayIDCGuildBattleReservedScheduleMapGroupGUIL_140001B6D.h" />
    <ClInclude Include="headers\j_GetEmptyMemberCNormalGuildBattleGuildGUILD_BATTL_140009D90.h" />
    <ClInclude Include="headers\j_GetEvnetTimeCashItemRemoteStoreQEAAXPEAU_cash_ev_14000C423.h" />
    <ClInclude Include="headers\j_GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQ_1400038F5.h" />
    <ClInclude Include="headers\j_GetFieldCNormalGuildBattleFieldListGUILD_BATTLEQ_14000A42A.h" />
    <ClInclude Include="headers\j_GetFieldCNormalGuildBattleGUILD_BATTLEQEAAPEAVCN_14000F5DD.h" />
    <ClInclude Include="headers\j_GetFirstMapFieldByRaceCNormalGuildBattleFieldLis_14000D2BF.h" />
    <ClInclude Include="headers\j_GetFirstMapInxByRaceCNormalGuildBattleFieldListG_14000B2E9.h" />
    <ClInclude Include="headers\j_GetGoalCntCNormalGuildBattleGuildGUILD_BATTLEQEA_140011CED.h" />
    <ClInclude Include="headers\j_GetGoalCountCNormalGuildBattleGuildMemberGUILD_B_140008D8C.h" />
    <ClInclude Include="headers\j_GetGravityStoneCNormalGuildBattleGUILD_BATTLEQEA_140013C0F.h" />
    <ClInclude Include="headers\j_GetGuildBattleNumberCNormalGuildBattleGUILD_BATT_14000BAC3.h" />
    <ClInclude Include="headers\j_GetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAP_1400048A4.h" />
    <ClInclude Include="headers\j_GetGuildCNormalGuildBattleGUILD_BATTLEQEAAPEAVCN_140003120.h" />
    <ClInclude Include="headers\j_GetGuildNameCNormalGuildBattleGuildGUILD_BATTLEQ_14000BD1B.h" />
    <ClInclude Include="headers\j_GetGuildRaceCNormalGuildBattleGuildGUILD_BATTLEQ_14000C937.h" />
    <ClInclude Include="headers\j_GetGuildSerialCNormalGuildBattleGuildGUILD_BATTL_140005560.h" />
    <ClInclude Include="headers\j_GetIDCGuildBattleReservedScheduleGUILD_BATTLEQEA_1400069F6.h" />
    <ClInclude Include="headers\j_GetIDCNormalGuildBattleGUILD_BATTLEQEAAKXZ_140008175.h" />
    <ClInclude Include="headers\j_GetIndexCNormalGuildBattleGuildMemberGUILD_BATTL_14000DCE2.h" />
    <ClInclude Include="headers\j_GetInfoCNormalGuildBattleGUILD_BATTLEQEAA_NAEAU__14000ECC3.h" />
    <ClInclude Include="headers\j_GetItemCodeCGuildBattleRewardItemGUILD_BATTLEQEB_1400038AF.h" />
    <ClInclude Include="headers\j_GetJoinMemberCntCNormalGuildBattleGuildGUILD_BAT_1400037BA.h" />
    <ClInclude Include="headers\j_GetKillCountCNormalGuildBattleGuildMemberGUILD_B_14000F0D3.h" />
    <ClInclude Include="headers\j_GetKillCountSumCNormalGuildBattleGuildGUILD_BATT_14000D260.h" />
    <ClInclude Include="headers\j_GetLeftTimeCCurrentGuildBattleInfoManagerGUILD_B_140009E8F.h" />
    <ClInclude Include="headers\j_GetLeftTimeCGuildBattleScheduleGUILD_BATTLEQEAA__14000B843.h" />
    <ClInclude Include="headers\j_GetLimDiscoutCashItemRemoteStoreQEAAEXZ_140005D8F.h" />
    <ClInclude Include="headers\j_GetLoggerCNormalGuildBattleGUILD_BATTLEQEAAPEAVC_140004ACF.h" />
    <ClInclude Include="headers\j_GetMapCNormalGuildBattleFieldGUILD_BATTLEQEAAPEA_14000D972.h" />
    <ClInclude Include="headers\j_GetMapCntCNormalGuildBattleFieldListGUILD_BATTLE_140011027.h" />
    <ClInclude Include="headers\j_GetMapCodeCNormalGuildBattleFieldGUILD_BATTLEQEA_140007135.h" />
    <ClInclude Include="headers\j_GetMapIDCNormalGuildBattleFieldGUILD_BATTLEQEAAK_14000877E.h" />
    <ClInclude Include="headers\j_GetMapInxCNormalGuildBattleFieldListGUILD_BATTLE_14000FA2E.h" />
    <ClInclude Include="headers\j_GetMapInxListCNormalGuildBattleFieldListGUILD_BA_14001089D.h" />
    <ClInclude Include="headers\j_GetMapStrCodeCNormalGuildBattleFieldGUILD_BATTLE_140006005.h" />
    <ClInclude Include="headers\j_GetMaxJoinMemberCountCNormalGuildBattleGuildGUIL_140006136.h" />
    <ClInclude Include="headers\j_GetMaxPageCReservedGuildScheduleMapGroupGUILD_BA_14000E3F9.h" />
    <ClInclude Include="headers\j_GetMemberCNormalGuildBattleGuildGUILD_BATTLEIEAA_1400057D1.h" />
    <ClInclude Include="headers\j_GetMemberPtrCNormalGuildBattleGuildGUILD_BATTLEQ_14000B681.h" />
    <ClInclude Include="headers\j_GetObjTypeCGuildBattleGUILD_BATTLEUEAAHXZ_1400086CF.h" />
    <ClInclude Include="headers\j_GetObjTypeCNormalGuildBattleGUILD_BATTLEUEAAHXZ_140002608.h" />
    <ClInclude Include="headers\j_GetPortalIndexInfoCNormalGuildBattleFieldGUILD_B_140009061.h" />
    <ClInclude Include="headers\j_GetRealStartTimeCGuildBattleScheduleGUILD_BATTLE_1400070F4.h" />
    <ClInclude Include="headers\j_GetRedCNormalGuildBattleGUILD_BATTLEQEAAPEAVCNor_14000BADC.h" />
    <ClInclude Include="headers\j_GetRefCGuildBattleSchedulePoolGUILD_BATTLEQEAAPE_14000E89F.h" />
    <ClInclude Include="headers\j_GetRegenerCGuildBattleControllerQEAAPEAVCGameObj_14000D6F7.h" />
    <ClInclude Include="headers\j_GetRegenerCNormalGuildBattleFieldGUILD_BATTLEQEA_14000EDE0.h" />
    <ClInclude Include="headers\j_GetRegenerCNormalGuildBattleFieldListGUILD_BATTL_14000787E.h" />
    <ClInclude Include="headers\j_GetRemainNumOfGoodCashItemRemoteStoreQEAAHGZ_140009228.h" />
    <ClInclude Include="headers\j_GetRemainNumOfGoodCashItemRemoteStoreQEAAHQEADZ_1400100AF.h" />
    <ClInclude Include="headers\j_GetScoreCNormalGuildBattleGuildGUILD_BATTLEQEAAK_140002883.h" />
    <ClInclude Include="headers\j_GetSerialCNormalGuildBattleGuildMemberGUILD_BATT_14000FE98.h" />
    <ClInclude Include="headers\j_GetSetDiscoutCashItemRemoteStoreQEAAEEZ_14000B54B.h" />
    <ClInclude Include="headers\j_GetSIDCGuildBattleScheduleGUILD_BATTLEQEAAKXZ_14000522C.h" />
    <ClInclude Include="headers\j_GetSIDCGuildBattleSchedulePoolGUILD_BATTLEQEAAKI_14000FAB0.h" />
    <ClInclude Include="headers\j_GetSLIDCGuildBattleReservedScheduleMapGroupGUILD_1400064A6.h" />
    <ClInclude Include="headers\j_GetStartBattleTickTimeCHolyStoneSystemQEAAKXZ_14000DC5B.h" />
    <ClInclude Include="headers\j_GetStateCGuildBattleScheduleGUILD_BATTLEQEAAHXZ_1400057B3.h" />
    <ClInclude Include="headers\j_GetStoneCGuildBattleControllerQEAAPEAVCGameObjec_140004DEF.h" />
    <ClInclude Include="headers\j_GetStoneCNormalGuildBattleFieldGUILD_BATTLEQEAAP_140009D04.h" />
    <ClInclude Include="headers\j_GetStoneCNormalGuildBattleFieldListGUILD_BATTLEQ_140006F1E.h" />
    <ClInclude Include="headers\j_GetTermCGuildBattleStateGUILD_BATTLEUEAAAVCTimeS_140007C0C.h" />
    <ClInclude Include="headers\j_GetTermCGuildBattleStateListGUILD_BATTLEQEAAAVCT_14000B0A5.h" />
    <ClInclude Include="headers\j_GetTermCNormalGuildBattleStateCountDownGUILD_BAT_140003404.h" />
    <ClInclude Include="headers\j_GetTermCNormalGuildBattleStateDivideGUILD_BATTLE_140012DFA.h" />
    <ClInclude Include="headers\j_GetTermCNormalGuildBattleStateFinGUILD_BATTLEUEA_1400089AE.h" />
    <ClInclude Include="headers\j_GetTermCNormalGuildBattleStateInBattleGUILD_BATT_14000EA2F.h" />
    <ClInclude Include="headers\j_GetTermCNormalGuildBattleStateNotifyGUILD_BATTLE_14000EC8C.h" />
    <ClInclude Include="headers\j_GetTermCNormalGuildBattleStateReadyGUILD_BATTLEU_140008BB1.h" />
    <ClInclude Include="headers\j_GetTermCNormalGuildBattleStateReturnGUILD_BATTLE_1400025B8.h" />
    <ClInclude Include="headers\j_GetTimeCGuildBattleScheduleGUILD_BATTLEQEAAAVCTi_140001FF5.h" />
    <ClInclude Include="headers\j_GetTodayDayIDCGuildBattleScheduleManagerGUILD_BA_140011748.h" />
    <ClInclude Include="headers\j_GetTodaySLIDByMapCGuildBattleScheduleManagerGUIL_14000294B.h" />
    <ClInclude Include="headers\j_GetToleranceProbMonsterSFContDamageToleracneQEAA_140012E3B.h" />
    <ClInclude Include="headers\j_GetTomorrowDayIDCGuildBattleScheduleManagerGUILD_14000FBF5.h" />
    <ClInclude Include="headers\j_GetTomorrowSLIDByMapCGuildBattleScheduleManagerG_140006E15.h" />
    <ClInclude Include="headers\j_GetTopGoalMemberCNormalGuildBattleGuildGUILD_BAT_1400118C9.h" />
    <ClInclude Include="headers\j_GetTopKillMemberCNormalGuildBattleGuildGUILD_BAT_1400132B9.h" />
    <ClInclude Include="headers\j_GetWinnerGradeCBattleTournamentInfoQEAAEKPEADZ_140012A49.h" />
    <ClInclude Include="headers\j_Get_CashEvent_StatusCashItemRemoteStoreQEAAEEZ_14000346D.h" />
    <ClInclude Include="headers\j_get_cde_statusCashItemRemoteStoreQEAAEXZ_14001028F.h" />
    <ClInclude Include="headers\j_Get_Conditional_Event_NameCashItemRemoteStoreQEA_140008C7E.h" />
    <ClInclude Include="headers\j_Get_Conditional_Event_StatusCashItemRemoteStoreQ_140004DC2.h" />
    <ClInclude Include="headers\j_GoalCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEAV_14000BAA5.h" />
    <ClInclude Include="headers\j_GoalCNormalGuildBattleGUILD_BATTLEQEAAEKHZ_14000214E.h" />
    <ClInclude Include="headers\j_GoalCNormalGuildBattleStateGUILD_BATTLEUEAAXXZ_14000C117.h" />
    <ClInclude Include="headers\j_GoodsListBuyByCashCashItemRemoteStoreAEAA_NGPEAD_140011252.h" />
    <ClInclude Include="headers\j_GoodsListBuyByGoldCashItemRemoteStoreAEAA_NGPEAD_140010DE3.h" />
    <ClInclude Include="headers\j_GoodsListCashItemRemoteStoreQEAA_NGPEADZ_140001A82.h" />
    <ClInclude Include="headers\j_GotoCGuildBattleStateListGUILD_BATTLEQEAAHXZ_14000C63F.h" />
    <ClInclude Include="headers\j_GotoStateCGuildBattleStateListGUILD_BATTLEQEAA_N_140013246.h" />
    <ClInclude Include="headers\j_GuildBattleBlockReportCNetworkEXAEAA_NHPEADZ_140011856.h" />
    <ClInclude Include="headers\j_GuildBattleCurrentBattleInfoRequestCNetworkEXAEA_14000C46E.h" />
    <ClInclude Include="headers\j_GuildBattleGetGravityStoneRequestCNetworkEXAEAA__14000F989.h" />
    <ClInclude Include="headers\j_GuildBattleGoalRequestCNetworkEXAEAA_NHPEADZ_140012FA8.h" />
    <ClInclude Include="headers\j_GuildBattleJoinGuildBattleRequestCNetworkEXAEAA__140005867.h" />
    <ClInclude Include="headers\j_GuildBattlePossibleGuildBattleListCNetworkEXAEAA_140005A06.h" />
    <ClInclude Include="headers\j_GuildBattleRankListRequestCNetworkEXAEAA_NHPEADZ_140013BAB.h" />
    <ClInclude Include="headers\j_GuildBattleReservedScheduleRequestCNetworkEXAEAA_14000E377.h" />
    <ClInclude Include="headers\j_GuildBattleResultLogCNormalGuildBattleGUILD_BATT_14000E804.h" />
    <ClInclude Include="headers\j_GuildBattleResultLogNotifyWebCNormalGuildBattleG_14000B83E.h" />
    <ClInclude Include="headers\j_GuildBattleResultLogPushDBLogCNormalGuildBattleG_14000C80B.h" />
    <ClInclude Include="headers\j_GuildBattleSuggestRequestToDestGuildCGuildQEAAEK_140011F59.h" />
    <ClInclude Include="headers\j_GuildBattleTakeGravityStoneRequestCNetworkEXAEAA_140007F18.h" />
    <ClInclude Include="headers\j_IncPvpPointCNormalGuildBattleGuildGUILD_BATTLEQE_14000D206.h" />
    <ClInclude Include="headers\j_IncPvpPointCNormalGuildBattleGuildMemberGUILD_BA_14000E020.h" />
    <ClInclude Include="headers\j_IncVerCReservedGuildSchedulePageGUILD_BATTLEQEAA_14000770C.h" />
    <ClInclude Include="headers\j_inform_cashdiscount_eventCashItemRemoteStoreQEAA_140012B3E.h" />
    <ClInclude Include="headers\j_inform_cashdiscount_status_allCashItemRemoteStor_14000B59B.h" />
    <ClInclude Include="headers\j_Inform_CashEventCashItemRemoteStoreQEAAXGZ_140012A30.h" />
    <ClInclude Include="headers\j_Inform_CashEvent_Status_AllCashItemRemoteStoreQE_14001369C.h" />
    <ClInclude Include="headers\j_Inform_ConditionalEventCashItemRemoteStoreQEAAXG_14000FDA3.h" />
    <ClInclude Include="headers\j_Inform_ConditionalEvent_Status_AllCashItemRemote_14000EC19.h" />
    <ClInclude Include="headers\j_InGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_SYN_14000EE3A.h" />
    <ClInclude Include="headers\j_InGuildbattleRewardMoneyCMainThreadQEAAXPEAU_DB__1400060CD.h" />
    <ClInclude Include="headers\j_InitCBattleTournamentInfoQEAAXXZ_140005EE3.h" />
    <ClInclude Include="headers\j_InitCCurrentGuildBattleInfoManagerGUILD_BATTLEQE_14000C4A0.h" />
    <ClInclude Include="headers\j_InitCGuildBattleControllerQEAA_NXZ_14000FD4E.h" />
    <ClInclude Include="headers\j_InitCGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_140008341.h" />
    <ClInclude Include="headers\j_InitCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_14000A466.h" />
    <ClInclude Include="headers\j_InitCGuildBattleReservedScheduleListManagerGUILD_14000FEAC.h" />
    <ClInclude Include="headers\j_InitCGuildBattleReservedScheduleMapGroupGUILD_BA_140001186.h" />
    <ClInclude Include="headers\j_InitCGuildBattleRewardItemGUILD_BATTLEQEAA_NGZ_140010AE1.h" />
    <ClInclude Include="headers\j_InitCGuildBattleRewardItemManagerGUILD_BATTLEQEA_140012FBC.h" />
    <ClInclude Include="headers\j_InitCGuildBattleScheduleManagerGUILD_BATTLEQEAA__140003D73.h" />
    <ClInclude Include="headers\j_InitCGuildBattleSchedulePoolGUILD_BATTLEQEAA_NIZ_140012ACB.h" />
    <ClInclude Include="headers\j_InitCGuildBattleSchedulerGUILD_BATTLEQEAA_NXZ_1400125EE.h" />
    <ClInclude Include="headers\j_InitCNormalGuildBattleFieldGUILD_BATTLEQEAA_NIZ_140003D46.h" />
    <ClInclude Include="headers\j_InitCNormalGuildBattleFieldListGUILD_BATTLEQEAA__140001A14.h" />
    <ClInclude Include="headers\j_InitCNormalGuildBattleGUILD_BATTLEQEAAXPEAVCGuil_140010A37.h" />
    <ClInclude Include="headers\j_InitCNormalGuildBattleGUILD_BATTLEQEAA_N_NIKKKKE_14000F100.h" />
    <ClInclude Include="headers\j_InitCNormalGuildBattleLoggerGUILD_BATTLEQEAA_NXZ_140004C55.h" />
    <ClInclude Include="headers\j_InitCNormalGuildBattleManagerGUILD_BATTLEQEAA_NX_14000351C.h" />
    <ClInclude Include="headers\j_InitCNormalGuildBattleStateListPoolGUILD_BATTLEQ_14000CB71.h" />
    <ClInclude Include="headers\j_InitCPossibleBattleGuildListManagerGUILD_BATTLEQ_140008355.h" />
    <ClInclude Include="headers\j_InitCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000B97E.h" />
    <ClInclude Include="headers\j_InitCReservedGuildScheduleMapGroupGUILD_BATTLEQE_140003599.h" />
    <ClInclude Include="headers\j_InitCReservedGuildSchedulePageGUILD_BATTLEQEAA_N_140008125.h" />
    <ClInclude Include="headers\j_InitializeCashItemRemoteStoreQEAA_NXZ_14000D742.h" />
    <ClInclude Include="headers\j_InitMonsterSFContDamageToleracneQEAAXMZ_1400035C6.h" />
    <ClInclude Include="headers\j_InitUseFieldCNormalGuildBattleFieldListGUILD_BAT_14000AD80.h" />
    <ClInclude Include="headers\j_Init_ATTACK_DELAY_CHECKERQEAAXXZ_140013DB3.h" />
    <ClInclude Include="headers\j_init_eff_list_ATTACK_DELAY_CHECKERQEAAXXZ_14000FF29.h" />
    <ClInclude Include="headers\j_init_mas_list_ATTACK_DELAY_CHECKERQEAAXXZ_140001E60.h" />
    <ClInclude Include="headers\j_InsertGuildBattleDefaultRecordCRFWorldDatabaseQE_1400118B5.h" />
    <ClInclude Include="headers\j_InsertGuildBattleRankRecordCRFWorldDatabaseQEAA__140010B9F.h" />
    <ClInclude Include="headers\j_InsertGuildBattleScheduleDefaultRecordCRFWorldDa_140003012.h" />
    <ClInclude Include="headers\j_insertvectorVCGuildBattleRewardItemGUILD_BATTLEV_1400126E3.h" />
    <ClInclude Include="headers\j_Insert_PatrirchItemChargeRefundCRFWorldDatabaseQ_1400099F8.h" />
    <ClInclude Include="headers\j_Insert_PatrirchItemChargeRefundPatriarchElectPro_14000DDD2.h" />
    <ClInclude Include="headers\j_Insert_RaceBattleLogCRFWorldDatabaseQEAA_NPEAU_r_14001004B.h" />
    <ClInclude Include="headers\j_InstanceCashItemRemoteStoreSAPEAV1XZ_140010320.h" />
    <ClInclude Include="headers\j_InstanceCCurrentGuildBattleInfoManagerGUILD_BATT_1400028B5.h" />
    <ClInclude Include="headers\j_InstanceCGuildBattleControllerSAPEAV1XZ_140013458.h" />
    <ClInclude Include="headers\j_InstanceCGuildBattleLoggerGUILD_BATTLESAPEAV12XZ_14000E5C0.h" />
    <ClInclude Include="headers\j_InstanceCGuildBattleRankManagerGUILD_BATTLESAPEA_140001E5B.h" />
    <ClInclude Include="headers\j_InstanceCGuildBattleReservedScheduleListManagerG_140004660.h" />
    <ClInclude Include="headers\j_InstanceCGuildBattleRewardItemManagerGUILD_BATTL_140008747.h" />
    <ClInclude Include="headers\j_InstanceCGuildBattleScheduleManagerGUILD_BATTLES_140002DE2.h" />
    <ClInclude Include="headers\j_InstanceCGuildBattleSchedulePoolGUILD_BATTLESAPE_1400075F4.h" />
    <ClInclude Include="headers\j_InstanceCGuildBattleSchedulerGUILD_BATTLESAPEAV1_14000C2BB.h" />
    <ClInclude Include="headers\j_InstanceCNormalGuildBattleFieldListGUILD_BATTLES_14000D4D6.h" />
    <ClInclude Include="headers\j_InstanceCNormalGuildBattleManagerGUILD_BATTLESAP_140003A76.h" />
    <ClInclude Include="headers\j_InstanceCNormalGuildBattleStateListPoolGUILD_BAT_14000C5C2.h" />
    <ClInclude Include="headers\j_InstanceCPossibleBattleGuildListManagerGUILD_BAT_140007E96.h" />
    <ClInclude Include="headers\j_IsAttackableInTownCGameObjectUEAA_NXZ_14000BC03.h" />
    <ClInclude Include="headers\j_IsAttackableInTownCMonsterUEAA_NXZ_140013151.h" />
    <ClInclude Include="headers\j_IsAvailableSuggestCGuildBattleControllerQEAAEPEA_14000898B.h" />
    <ClInclude Include="headers\j_IsBattleModeUseCRecallRequestQEAA_NXZ_14000D02B.h" />
    <ClInclude Include="headers\j_IsBeAttackedAbleAutominePersonalUEAA_N_NZ_1400021B2.h" />
    <ClInclude Include="headers\j_IsBeAttackedAbleCAnimusUEAA_N_NZ_140002C98.h" />
    <ClInclude Include="headers\j_IsBeAttackedAbleCGameObjectUEAA_N_NZ_14000CDA6.h" />
    <ClInclude Include="headers\j_IsBeAttackedAbleCGuardTowerUEAA_N_NZ_14000250E.h" />
    <ClInclude Include="headers\j_IsBeAttackedAbleCHolyKeeperUEAA_N_NZ_1400075AE.h" />
    <ClInclude Include="headers\j_IsBeAttackedAbleCHolyStoneUEAA_N_NZ_14001366F.h" />
    <ClInclude Include="headers\j_IsBeAttackedAbleCMonsterUEAA_N_NZ_140006C8F.h" />
    <ClInclude Include="headers\j_IsBeAttackedAbleCTrapUEAA_N_NZ_14000CDCE.h" />
    <ClInclude Include="headers\j_IsBuyCashItemByGoldCashItemRemoteStoreQEAA_NXZ_14000CFB3.h" />
    <ClInclude Include="headers\j_IsCashItemYAHEKZ_1400022A2.h" />
    <ClInclude Include="headers\j_IsCharInSectorCAttackSAHQEAM00MMZ_140006EBF.h" />
    <ClInclude Include="headers\j_IsCommitteeMemberCNormalGuildBattleGuildMemberGU_1400103FC.h" />
    <ClInclude Include="headers\j_IsCompleteBattle_guild_battle_suggest_matterQEAA_140012E4A.h" />
    <ClInclude Include="headers\j_isConEventTimeCashItemRemoteStoreQEAA_NXZ_140009BA6.h" />
    <ClInclude Include="headers\j_IsDayChangedCGuildBattleScheduleManagerGUILD_BAT_140010267.h" />
    <ClInclude Include="headers\j_IsDelay_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14000FD26.h" />
    <ClInclude Include="headers\j_IsDoneCGuildBattleReservedScheduleGUILD_BATTLEQE_14000280B.h" />
    <ClInclude Include="headers\j_IsDoneCGuildBattleReservedScheduleMapGroupGUILD__1400087FB.h" />
    <ClInclude Include="headers\j_IsDoneCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000818E.h" />
    <ClInclude Include="headers\j_IsEmptyCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000F2CC.h" />
    <ClInclude Include="headers\j_IsEmptyCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_1400111CB.h" />
    <ClInclude Include="headers\j_IsEmptyCNormalGuildBattleGuildMemberGUILD_BATTLE_140004B06.h" />
    <ClInclude Include="headers\j_IsEmptyCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000763A.h" />
    <ClInclude Include="headers\j_IsEmptyTimeCGuildBattleReservedScheduleGUILD_BAT_140009787.h" />
    <ClInclude Include="headers\j_IsEmptyTimeCGuildBattleReservedScheduleMapGroupG_14000A0A6.h" />
    <ClInclude Include="headers\j_IsEmptyTimeCGuildBattleScheduleManagerGUILD_BATT_140010933.h" />
    <ClInclude Include="headers\j_IsEnableStartCNormalGuildBattleGuildMemberGUILD__1400067DA.h" />
    <ClInclude Include="headers\j_IsEventTimeCashItemRemoteStoreQEAA_NEZ_140005D85.h" />
    <ClInclude Include="headers\j_IsExistCNormalGuildBattleGuildMemberGUILD_BATTLE_140006159.h" />
    <ClInclude Include="headers\j_IsInBattleCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_140011FCC.h" />
    <ClInclude Include="headers\j_IsInBattleCNormalGuildBattleStateListGUILD_BATTL_1400103A7.h" />
    <ClInclude Include="headers\j_IsInBattleRegenStateCNormalGuildBattleGUILD_BATT_14000AC5E.h" />
    <ClInclude Include="headers\j_IsInBattleRegenStateCNormalGuildBattleStateInBat_14000BEE7.h" />
    <ClInclude Include="headers\j_IsInBattleRegenStateCNormalGuildBattleStateListG_140001861.h" />
    <ClInclude Include="headers\j_IsInBattleRegenStateCNormalGuildBattleStateRound_1400119F0.h" />
    <ClInclude Include="headers\j_IsJoinMemberCNormalGuildBattleGuildGUILD_BATTLEI_1400096CE.h" />
    <ClInclude Include="headers\j_IsMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA__14000D995.h" />
    <ClInclude Include="headers\j_IsMemberGuildCNormalGuildBattleGUILD_BATTLEQEAA__14000C92D.h" />
    <ClInclude Include="headers\j_IsNullCGuildBattleRewardItemGUILD_BATTLEQEBA_NXZ_14000923C.h" />
    <ClInclude Include="headers\j_IsPreAttackAbleMonCMonsterQEAAHXZ_140002982.h" />
    <ClInclude Include="headers\j_IsProcCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000ACB8.h" />
    <ClInclude Include="headers\j_IsProcCGuildBattleStateListGUILD_BATTLEQEAA_NXZ_14000E223.h" />
    <ClInclude Include="headers\j_IsProcCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000E03E.h" />
    <ClInclude Include="headers\j_IsReadyOrCountStateCNormalGuildBattleGUILD_BATTL_140001A7D.h" />
    <ClInclude Include="headers\j_IsReadyOrCountStateCNormalGuildBattleStateListGU_1400075EF.h" />
    <ClInclude Include="headers\j_IsRegistedMapInxCNormalGuildBattleFieldListGUILD_14000AAB5.h" />
    <ClInclude Include="headers\j_IsReStartCNormalGuildBattleGuildGUILD_BATTLEQEAA_140010FB9.h" />
    <ClInclude Include="headers\j_IsReStartCNormalGuildBattleGuildMemberGUILD_BATT_14000533A.h" />
    <ClInclude Include="headers\j_IsReStartCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_14000CEDC.h" />
    <ClInclude Include="headers\j_IsSFContDamageMonsterSFContDamageToleracneQEAA_N_1400018C0.h" />
    <ClInclude Include="headers\j_IsStorageCodeWithItemKindYAHHHZ_140003724.h" />
    <ClInclude Include="headers\j_IsWaitCGuildBattleScheduleGUILD_BATTLEQEAA_NXZ_14000BA96.h" />
    <ClInclude Include="headers\j_Is_Battle_ModeCGameObjectUEAA_NXZ_14000DE27.h" />
    <ClInclude Include="headers\j_is_cde_timeCashItemRemoteStoreQEAA_NXZ_140010866.h" />
    <ClInclude Include="headers\j_JoinCNormalGuildBattleGuildGUILD_BATTLEQEAAEKEAE_14000534E.h" />
    <ClInclude Include="headers\j_JoinCNormalGuildBattleGuildMemberGUILD_BATTLEQEA_14000FA83.h" />
    <ClInclude Include="headers\j_JoinCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_140003D87.h" />
    <ClInclude Include="headers\j_JoinCNormalGuildBattleManagerGUILD_BATTLEQEAAXHK_140012DB9.h" />
    <ClInclude Include="headers\j_JoinGuildCGuildBattleControllerQEAAXHKKZ_1400082CE.h" />
    <ClInclude Include="headers\j_JoinGuildCNormalGuildBattleManagerGUILD_BATTLEQE_14000F3EE.h" />
    <ClInclude Include="headers\j_JudgeBattleCNormalGuildBattleGUILD_BATTLEQEAAEXZ_1400108E8.h" />
    <ClInclude Include="headers\j_KillCNormalGuildBattleGuildGUILD_BATTLEQEAAHPEAV_14000D148.h" />
    <ClInclude Include="headers\j_KillCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_140010BB3.h" />
    <ClInclude Include="headers\j_KillCNormalGuildBattleManagerGUILD_BATTLEQEAAHKK_140003FCB.h" />
    <ClInclude Include="headers\j_LeaveGuildCNormalGuildBattleGuildGUILD_BATTLEQEA_1400035E4.h" />
    <ClInclude Include="headers\j_LimitedSale_check_countCashItemRemoteStoreQEAA_N_140005C27.h" />
    <ClInclude Include="headers\j_LoadBuyCashModeCashItemRemoteStoreAEAA_NXZ_140002ADB.h" />
    <ClInclude Include="headers\j_LoadCGuildBattleControllerQEAA_NXZ_140010C53.h" />
    <ClInclude Include="headers\j_LoadCGuildBattleRankManagerGUILD_BATTLEIEAA_NEZ_140012044.h" />
    <ClInclude Include="headers\j_LoadCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_14000521D.h" />
    <ClInclude Include="headers\j_LoadCGuildBattleReservedScheduleGUILD_BATTLEQEAA_14000F08D.h" />
    <ClInclude Include="headers\j_LoadCGuildBattleReservedScheduleListManagerGUILD_140002B2B.h" />
    <ClInclude Include="headers\j_LoadCGuildBattleReservedScheduleMapGroupGUILD_BA_140007739.h" />
    <ClInclude Include="headers\j_LoadCGuildBattleScheduleGUILD_BATTLEQEAA_N_NKE_J_14000A713.h" />
    <ClInclude Include="headers\j_LoadCGuildBattleScheduleManagerGUILD_BATTLEQEAA__1400014A6.h" />
    <ClInclude Include="headers\j_LoadCNormalGuildBattleManagerGUILD_BATTLEIEAA_N__140005FCE.h" />
    <ClInclude Include="headers\j_LoadCNormalGuildBattleManagerGUILD_BATTLEQEAA_NH_1400044A3.h" />
    <ClInclude Include="headers\j_LoadCPossibleBattleGuildListManagerGUILD_BATTLEQ_14000A286.h" />
    <ClInclude Include="headers\j_LoadCReservedGuildScheduleDayGroupGUILD_BATTLEQE_140005CA4.h" />
    <ClInclude Include="headers\j_LoadCReservedGuildScheduleMapGroupGUILD_BATTLEQE_14000A678.h" />
    <ClInclude Include="headers\j_LoadDBGuildBattleInfoCNormalGuildBattleManagerGU_14000889B.h" />
    <ClInclude Include="headers\j_LoadDummysCNormalGuildBattleFieldGUILD_BATTLEAEA_140005D17.h" />
    <ClInclude Include="headers\j_LoadGuildBattleInfoCRFWorldDatabaseQEAA_NKKPEAU__140010037.h" />
    <ClInclude Include="headers\j_LoadGuildBattleScheduleInfoCRFWorldDatabaseQEAAE_1400080F3.h" />
    <ClInclude Include="headers\j_LoadINICGuildBattleControllerAEAA_NAEAIAEAH111Z_140008AB2.h" />
    <ClInclude Include="headers\j_LoadNationalPriceCashItemRemoteStoreAEAA_NAEAVCR_14000C0C2.h" />
    <ClInclude Include="headers\j_LoadTodayScheduleCGuildBattleReservedScheduleLis_1400056EB.h" />
    <ClInclude Include="headers\j_LoadTomorrowScheduleCGuildBattleReservedSchedule_14000F0B0.h" />
    <ClInclude Include="headers\j_load_cash_discount_eventCashItemRemoteStoreQEAAX_140010F4B.h" />
    <ClInclude Include="headers\j_Load_Cash_EventCashItemRemoteStoreQEAAXXZ_14001208A.h" />
    <ClInclude Include="headers\j_load_cde_iniCashItemRemoteStoreQEAAXPEAU_cash_di_140006A1E.h" />
    <ClInclude Include="headers\j_Load_Conditional_EventCashItemRemoteStoreQEAAXXZ_140001505.h" />
    <ClInclude Include="headers\j_load_con_event_iniCashItemRemoteStoreQEAAXPEAU_c_1400125C6.h" />
    <ClInclude Include="headers\j_Load_Event_INICashItemRemoteStoreQEAAXPEAU_cash__140001C30.h" />
    <ClInclude Include="headers\j_Load_LimitedSale_Event_INICashItemRemoteStoreQEA_140004ED5.h" />
    <ClInclude Include="headers\j_LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_140010802.h" />
    <ClInclude Include="headers\j_LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZZ_1400030E4.h" />
    <ClInclude Include="headers\j_LogCGuildBattleStateGUILD_BATTLEIEAAXPEADZ_140001D2A.h" />
    <ClInclude Include="headers\j_LogCGuildBattleStateListGUILD_BATTLEIEAAXPEADZ_140006523.h" />
    <ClInclude Include="headers\j_LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEAD_14000726B.h" />
    <ClInclude Include="headers\j_LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEA__140004AF7.h" />
    <ClInclude Include="headers\j_LogCNormalGuildBattleStateGUILD_BATTLEIEAAXPEAVC_140005B87.h" />
    <ClInclude Include="headers\j_LogCNormalGuildBattleStateRoundGUILD_BATTLEIEAAX_14000F4B1.h" />
    <ClInclude Include="headers\j_log_about_cash_eventCashItemRemoteStoreQEAAXPEAD_1400060FA.h" />
    <ClInclude Include="headers\j_LoopCGuildBattleControllerQEAAXXZ_140005DDA.h" />
    <ClInclude Include="headers\j_LoopCGuildBattleReservedScheduleGUILD_BATTLEQEAA_1400074FA.h" />
    <ClInclude Include="headers\j_LoopCGuildBattleReservedScheduleMapGroupGUILD_BA_14000175D.h" />
    <ClInclude Include="headers\j_LoopCGuildBattleScheduleManagerGUILD_BATTLEQEAAX_14000ABAA.h" />
    <ClInclude Include="headers\j_LoopCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuild_1400111FD.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_140002059.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAV_140010FFA.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAV_1400065BE.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleStateInBattleGUILD_BATTLEU_14000FD08.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleStateRoundGUILD_BATTLEMEAA_14001372D.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleStateRoundGUILD_BATTLEUEAA_140009755.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleStateRoundProcessGUILD_BAT_140009291.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleStateRoundReturnStartPosGU_1400124D1.h" />
    <ClInclude Include="headers\j_LoopCNormalGuildBattleStateRoundStartGUILD_BATTL_140002F9A.h" />
    <ClInclude Include="headers\j_loop_cash_discount_eventCashItemRemoteStoreQEAAX_1400092FA.h" />
    <ClInclude Include="headers\j_Loop_Cash_EventCashItemRemoteStoreQEAAXXZ_140012E81.h" />
    <ClInclude Include="headers\j_Loop_Check_Total_SellingCashItemRemoteStoreQEAAX_140009D27.h" />
    <ClInclude Include="headers\j_Loop_ContEventCashItemRemoteStoreQEAAXXZ_14000B398.h" />
    <ClInclude Include="headers\j_Loop_TatalCashEventCashItemRemoteStoreQEAAXXZ_14000BA4B.h" />
    <ClInclude Include="headers\j_MakePageCPossibleBattleGuildListManagerGUILD_BAT_14000F9CF.h" />
    <ClInclude Include="headers\j_ManageAcceptORRefuseGuildBattleCGuildQEAAE_NZ_14001272E.h" />
    <ClInclude Include="headers\j_ManageProposeGuildBattleCGuildQEAAEKKKKZ_140010DD4.h" />
    <ClInclude Include="headers\j_max_elementPEAVCNormalGuildBattleGuildMemberGUIL_14000382D.h" />
    <ClInclude Include="headers\j_max_elementPEAVCNormalGuildBattleGuildMemberGUIL_140011DE7.h" />
    <ClInclude Include="headers\j_max_sizeallocatorVCGuildBattleRewardItemGUILD_BA_14000D544.h" />
    <ClInclude Include="headers\j_max_sizevectorVCGuildBattleRewardItemGUILD_BATTL_140007F1D.h" />
    <ClInclude Include="headers\j_ModifyMonsterAttFcCMonsterAttackIEAAMMZ_140003FDF.h" />
    <ClInclude Include="headers\j_MoveMapCNormalGuildBattleGuildGUILD_BATTLEQEAAXI_140004FC0.h" />
    <ClInclude Include="headers\j_MoveMemberCNormalGuildBattleGuildGUILD_BATTLEQEA_14000B717.h" />
    <ClInclude Include="headers\j_NetCloseCNormalGuildBattleGuildGUILD_BATTLEQEAA__140002FE5.h" />
    <ClInclude Include="headers\j_NetCloseCNormalGuildBattleGuildMemberGUILD_BATTL_1400138BD.h" />
    <ClInclude Include="headers\j_NextCGuildBattleReservedScheduleGUILD_BATTLEAEAA_14000CA63.h" />
    <ClInclude Include="headers\j_NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_140011B3F.h" />
    <ClInclude Include="headers\j_NotifyBallPositionCNormalGuildBattleGUILD_BATTLE_14000C56D.h" />
    <ClInclude Include="headers\j_NotifyBattleResultCNormalGuildBattleGUILD_BATTLE_140003C6A.h" />
    <ClInclude Include="headers\j_NotifyBeforeStartCNormalGuildBattleGUILD_BATTLEQ_140005E8E.h" />
    <ClInclude Include="headers\j_NotifyCommitteeMemberPositionCNormalGuildBattleG_14000C2FC.h" />
    <ClInclude Include="headers\j_NotifyDestoryBallCNormalGuildBattleGUILD_BATTLEQ_140004827.h" />
    <ClInclude Include="headers\j_NotifyLeftMinuteBeforeStartCNormalGuildBattleGui_14000A9BB.h" />
    <ClInclude Include="headers\j_NotifyPassGravityStoneLimitTimeCNormalGuildBattl_14000E2CD.h" />
    <ClInclude Include="headers\j_OnlyOnceInitMonsterSFContDamageToleracneQEAAXPEA_140009A84.h" />
    <ClInclude Include="headers\j_On_HS_SCENE_BATTLE_END_WAIT_TIMECHolyStoneSystem_140006497.h" />
    <ClInclude Include="headers\j_On_HS_SCENE_BATTLE_TIMECHolyStoneSystemIEAAXXZ_14000E1DD.h" />
    <ClInclude Include="headers\j_On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSyst_14000B523.h" />
    <ClInclude Include="headers\j_On_HS_SCENE_KEEPER_DEATTACKABLE_TIMECHolyStoneSy_14000A2D6.h" />
    <ClInclude Include="headers\j_OutDestGuildbattleCostCMainThreadQEAAXPEAU_DB_QR_140007B17.h" />
    <ClInclude Include="headers\j_OutSrcGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_14000565F.h" />
    <ClInclude Include="headers\j_ProcCheckGetGravityStoneCNormalGuildBattleManage_14000B7CB.h" />
    <ClInclude Include="headers\j_ProcCheckGoalCNormalGuildBattleManagerGUILD_BATT_140013908.h" />
    <ClInclude Include="headers\j_ProcCheckTakeGravityStoneCNormalGuildBattleManag_14000C1E4.h" />
    <ClInclude Include="headers\j_ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_140008F3F.h" />
    <ClInclude Include="headers\j_ProcessCGuildBattleStateListGUILD_BATTLEQEAAXPEA_140009B9C.h" />
    <ClInclude Include="headers\j_ProcessCNormalGuildBattleGUILD_BATTLEQEAAXXZ_140008422.h" />
    <ClInclude Include="headers\j_ProcJoinCNormalGuildBattleManagerGUILD_BATTLEIEA_140001AA0.h" />
    <ClInclude Include="headers\j_PushClearGuildBattleRankCGuildBattleControllerQE_140005637.h" />
    <ClInclude Include="headers\j_PushClearGuildBattleRankCGuildBattleRankManagerG_1400108FC.h" />
    <ClInclude Include="headers\j_PushCreateGuildBattleRankTableCGuildBattleContro_140002978.h" />
    <ClInclude Include="headers\j_PushCreateGuildBattleRankTableCGuildBattleRankMa_1400065EB.h" />
    <ClInclude Include="headers\j_PushDQSCGuildBattleReservedScheduleListManagerGU_14000740F.h" />
    <ClInclude Include="headers\j_PushDQSClearCGuildBattleReservedScheduleMapGroup_140009D4F.h" />
    <ClInclude Include="headers\j_PushDQSDataCNormalGuildBattleManagerGUILD_BATTLE_14000C5F9.h" />
    <ClInclude Include="headers\j_PushDQSDestGuildOutputGuildBattleCostCGuildQEAAX_14000141A.h" />
    <ClInclude Include="headers\j_PushDQSDrawRankCNormalGuildBattleGUILD_BATTLEIEA_140005470.h" />
    <ClInclude Include="headers\j_PushDQSInGuildBattleCostCGuildQEAAXXZ_140011D06.h" />
    <ClInclude Include="headers\j_PushDQSInGuildBattleRewardMoneyCGuildQEAAXXZ_1400124BD.h" />
    <ClInclude Include="headers\j_PushDQSPvpPointCNormalGuildBattleGuildMemberGUIL_140008995.h" />
    <ClInclude Include="headers\j_PushDQSSourceGuildOutputGuildBattleCostCGuildQEA_14000527C.h" />
    <ClInclude Include="headers\j_PushDQSWinLoseRankCNormalGuildBattleGUILD_BATTLE_140003017.h" />
    <ClInclude Include="headers\j_PushItemCObjectListQEAA_NPEAU_object_list_pointZ_140001C49.h" />
    <ClInclude Include="headers\j_PushItem_TRAP_PARAMQEAA_NPEAVCTrapKZ_1400036CF.h" />
    <ClInclude Include="headers\j_RCTopGoalPrediCateCNormalGuildBattleGuildGUILD_B_140001104.h" />
    <ClInclude Include="headers\j_RCTopKillPrediCateCNormalGuildBattleGuildGUILD_B_14000658C.h" />
    <ClInclude Include="headers\j_RegenBallCNormalGuildBattleFieldGUILD_BATTLEQEAA_14000F317.h" />
    <ClInclude Include="headers\j_RequestSubProcSetRaceBattleResultCRaceBuffByHoly_140012F80.h" />
    <ClInclude Include="headers\j_ReturnBindPosAllCNormalGuildBattleGuildGUILD_BAT_14000C75C.h" />
    <ClInclude Include="headers\j_ReturnBindPosCNormalGuildBattleGuildMemberGUILD__14000580D.h" />
    <ClInclude Include="headers\j_ReturnHQPosAllCNormalGuildBattleGuildGUILD_BATTL_140006FA5.h" />
    <ClInclude Include="headers\j_ReturnStartPosAllCNormalGuildBattleGuildGUILD_BA_140005EAC.h" />
    <ClInclude Include="headers\j_ReturnStartPosCNormalGuildBattleGuildMemberGUILD_140004511.h" />
    <ClInclude Include="headers\j_RewardGuildBattleMoneyCNormalGuildBattleGUILD_BA_14000FEB6.h" />
    <ClInclude Include="headers\j_RewardItemCNormalGuildBattleGuildGUILD_BATTLEQEA_14000E1F6.h" />
    <ClInclude Include="headers\j_RewardItemCNormalGuildBattleGUILD_BATTLEQEAAXXZ_140010FA5.h" />
    <ClInclude Include="headers\j_rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD__140013034.h" />
    <ClInclude Include="headers\j_SaveCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_14000F817.h" />
    <ClInclude Include="headers\j_SaveCNormalGuildBattleManagerGUILD_BATTLEQEAA_NK_14000BE38.h" />
    <ClInclude Include="headers\j_SaveINICGuildBattleControllerAEAA_NXZ_140009430.h" />
    <ClInclude Include="headers\j_SearchItemAddSpeedCEquipItemSFAgentIEAAMPEAU_db__14000D45E.h" />
    <ClInclude Include="headers\j_SectorDamageProcCAttackIEAAXHHHHHH_NZ_1400057C7.h" />
    <ClInclude Include="headers\j_SelectGuildBattleRankListCGuildBattleRankManager_140012314.h" />
    <ClInclude Include="headers\j_SelectGuildBattleRankListCRFWorldDatabaseQEAA_NE_1400037E2.h" />
    <ClInclude Include="headers\j_SelectGuildBattleRankRecordCRFWorldDatabaseQEAA__140012C15.h" />
    <ClInclude Include="headers\j_SelectGuildBattleRerservedListCRFWorldDatabaseQE_140003EEA.h" />
    <ClInclude Include="headers\j_SelectGuildBattleScheduleInfoIDCRFWorldDatabaseQ_14001019F.h" />
    <ClInclude Include="headers\j_SelectRowCountGuildBattleInfoCRFWorldDatabaseQEA_140004DF9.h" />
    <ClInclude Include="headers\j_SelectRowCountGuildBattleScheduleInfoCRFWorldDat_140009048.h" />
    <ClInclude Include="headers\j_Select_BattleResultLogLatestCRFWorldDatabaseQEAA_14000CD56.h" />
    <ClInclude Include="headers\j_Select_BattleTournamentInfoCRFWorldDatabaseQEAA__140001910.h" />
    <ClInclude Include="headers\j_Select_FailBattleCountCRFWorldDatabaseQEAAHEKAEA_1400040DE.h" />
    <ClInclude Include="headers\j_Select_GuildBattleRecordCRFWorldDatabaseQEAA_NKP_140012C51.h" />
    <ClInclude Include="headers\j_Select_LoseBattleCountCRFWorldDatabaseQEAAHEKAEA_1400103CF.h" />
    <ClInclude Include="headers\j_Select_WinBattleCountCRFWorldDatabaseQEAAHEKAEAK_140013601.h" />
    <ClInclude Include="headers\j_SellCashItemRemoteStoreQEAA_NGPEADZ_14000EDCC.h" />
    <ClInclude Include="headers\j_SetActiveSuccCAttackQEAAX_NZ_14000AC2C.h" />
    <ClInclude Include="headers\j_SetAttackPartCGameObjectUEAAXHZ_140012738.h" />
    <ClInclude Include="headers\j_SetBattleModeTimeCMonsterAIQEAAXKZ_140008012.h" />
    <ClInclude Include="headers\j_SetBattleStateCNormalGuildBattleGuildMemberGUILD_140005D49.h" />
    <ClInclude Include="headers\j_SetBattleTimeCNormalGuildBattleStateInBattleGUIL_1400122CE.h" />
    <ClInclude Include="headers\j_SetBattleTimeCNormalGuildBattleStateListGUILD_BA_1400066D6.h" />
    <ClInclude Include="headers\j_SetCCurrentGuildBattleInfoManagerGUILD_BATTLEQEA_1400029B9.h" />
    <ClInclude Include="headers\j_SetCGuildBattleScheduleGUILD_BATTLEQEAAEKKZ_1400038EB.h" />
    <ClInclude Include="headers\j_SetColorInxCNormalGuildBattleGuildGUILD_BATTLEQE_14000574F.h" />
    <ClInclude Include="headers\j_SetCombatStateCMonsterQEAAXEZ_14000C9BE.h" />
    <ClInclude Include="headers\j_SetCopmlteGuildBattleSuggestCGuildQEAAXXZ_140013D5E.h" />
    <ClInclude Include="headers\j_SetDamageAbleStateCHolyKeeperQEAAX_NZ_14000D9EA.h" />
    <ClInclude Include="headers\j_SetDelay_ATTACK_DELAY_CHECKERQEAAXKZ_14000E7B4.h" />
    <ClInclude Include="headers\j_SetGotoRegenStartCNormalGuildBattleGUILD_BATTLEQ_14000CB8F.h" />
    <ClInclude Include="headers\j_SetGotoRegenStateCNormalGuildBattleStateInBattle_140008C15.h" />
    <ClInclude Include="headers\j_SetGotoRegenStateCNormalGuildBattleStateListGUIL_14000E8C2.h" />
    <ClInclude Include="headers\j_SetGuildBattleMatterCGuildQEAAXKKKKZ_14000FC86.h" />
    <ClInclude Include="headers\j_SetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAX_140009223.h" />
    <ClInclude Include="headers\j_SetItemCGuildBattleRewardItemGUILD_BATTLEIEAA_NP_140009C37.h" />
    <ClInclude Include="headers\j_SetLoadCBattleTournamentInfoQEAAX_NZ_14000187A.h" />
    <ClInclude Include="headers\j_SetNextDiscountEventTimeCashItemRemoteStoreQEAA__140006BA4.h" />
    <ClInclude Include="headers\j_SetNextEventCNormalGuildBattleManagerGUILD_BATTL_1400057D6.h" />
    <ClInclude Include="headers\j_SetNextEventTimeCashItemRemoteStoreQEAA_NEZ_140002D92.h" />
    <ClInclude Include="headers\j_SetNextEvnetCGuildBattleScheduleManagerGUILD_BAT_140005DE4.h" />
    <ClInclude Include="headers\j_SetNextStateCGuildBattleStateListGUILD_BATTLEMEA_140008B2A.h" />
    <ClInclude Include="headers\j_SetNextStateCNormalGuildBattleStateListGUILD_BAT_14000E9FD.h" />
    <ClInclude Include="headers\j_SetNextStateCNormalGuildBattleStateRoundListGUIL_1400041F1.h" />
    <ClInclude Include="headers\j_SetOrderViewAttackStateCPvpOrderViewQEAAXXZ_140002CC5.h" />
    <ClInclude Include="headers\j_SetOrderViewDamagedStateCPvpOrderViewQEAAXXZ_14000ECE1.h" />
    <ClInclude Include="headers\j_SetPortalInxCNormalGuildBattleFieldGUILD_BATTLEA_14000E87C.h" />
    <ClInclude Include="headers\j_SetProcStateCGuildBattleScheduleGUILD_BATTLEQEAA_14000A308.h" />
    <ClInclude Include="headers\j_SetReadyCGuildBattleStateListGUILD_BATTLEQEAAXXZ_140001780.h" />
    <ClInclude Include="headers\j_SetReadyStateCNormalGuildBattleGUILD_BATTLEQEAAX_140001118.h" />
    <ClInclude Include="headers\j_SetReadyStateCNormalGuildBattleManagerGUILD_BATT_14000CBC6.h" />
    <ClInclude Include="headers\j_SetReStartFlagCNormalGuildBattleGuildGUILD_BATTL_140001893.h" />
    <ClInclude Include="headers\j_SetReStartFlagCNormalGuildBattleGuildMemberGUILD_140001AEB.h" />
    <ClInclude Include="headers\j_SetSFDamageToleracne_VariationMonsterSFContDamag_140003620.h" />
    <ClInclude Include="headers\j_SetStateListCGuildBattleScheduleGUILD_BATTLEQEAA_14000B6BD.h" />
    <ClInclude Include="headers\j_SetStaticMemberCAttackSAXPEAVCRecordDataZ_1400035B2.h" />
    <ClInclude Include="headers\j_SetWaitCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1400049B7.h" />
    <ClInclude Include="headers\j_SetWinnerInfoCBattleTournamentInfoQEAA_NKPEADEZ_140011AE0.h" />
    <ClInclude Include="headers\j_Set_CashEvent_StatusCashItemRemoteStoreQEAAXEEZ_14000DE31.h" />
    <ClInclude Include="headers\j_set_cde_statusCashItemRemoteStoreQEAAXEZ_14000B767.h" />
    <ClInclude Include="headers\j_Set_Conditional_Evnet_StatusCashItemRemoteStoreQ_14000241E.h" />
    <ClInclude Include="headers\j_Set_DB_LimitedSale_EventCashItemRemoteStoreQEAAX_140011469.h" />
    <ClInclude Include="headers\j_Set_FROMDB_LimitedSale_EventCashItemRemoteStoreQ_14000E30E.h" />
    <ClInclude Include="headers\j_Set_LimitedSale_countCashItemRemoteStoreQEAAXEKZ_140002F9F.h" />
    <ClInclude Include="headers\j_Set_LimitedSale_DCKCashItemRemoteStoreQEAAXEEZ_140004C19.h" />
    <ClInclude Include="headers\j_Set_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_140013B7E.h" />
    <ClInclude Include="headers\j_Set_LimitedSale_Event_IniCashItemRemoteStoreQEAA_140012DCD.h" />
    <ClInclude Include="headers\j_sizevectorVCGuildBattleRewardItemGUILD_BATTLEVal_14000AAD8.h" />
    <ClInclude Include="headers\j_size_attack_count_result_zoclQEAAHXZ_140010F19.h" />
    <ClInclude Include="headers\j_size_attack_force_result_zoclQEAAHXZ_140013D63.h" />
    <ClInclude Include="headers\j_size_attack_gen_result_zoclQEAAHXZ_140011FB3.h" />
    <ClInclude Include="headers\j_size_attack_keeper_inform_zoclQEAAHXZ_14001157C.h" />
    <ClInclude Include="headers\j_size_attack_selfdestruction_result_zoclQEAAHXZ_14000FA24.h" />
    <ClInclude Include="headers\j_size_attack_siege_result_zoclQEAAHXZ_140006875.h" />
    <ClInclude Include="headers\j_size_attack_trap_inform_zoclQEAAHXZ_14000F13C.h" />
    <ClInclude Include="headers\j_size_attack_unit_result_zoclQEAAHXZ_1400026B7.h" />
    <ClInclude Include="headers\j_size_guild_battle_get_gravity_stone_result_zoclQ_140006E10.h" />
    <ClInclude Include="headers\j_size_guild_battle_goal_result_zoclQEAAHXZ_14000AF2E.h" />
    <ClInclude Include="headers\j_size_guild_battle_rank_list_result_zoclQEAAHXZ_14000A0D3.h" />
    <ClInclude Include="headers\j_size_guild_battle_reserved_schedule_result_zoclQ_140001055.h" />
    <ClInclude Include="headers\j_size_guild_battle_suggest_request_result_zoclQEA_140010EE7.h" />
    <ClInclude Include="headers\j_size_notify_not_use_premium_cashitem_zoclQEAAHXZ_14001203F.h" />
    <ClInclude Include="headers\j_size_param_cashitem_dblogQEAAHXZ_140001AB9.h" />
    <ClInclude Include="headers\j_size_personal_automine_attacked_zoclQEAAHXZ_14000EAFC.h" />
    <ClInclude Include="headers\j_size_possible_battle_guild_list_result_zoclQEAAH_1400045A2.h" />
    <ClInclude Include="headers\j_size_qry_case_addguildbattlescheduleQEAAHXZ_1400017B7.h" />
    <ClInclude Include="headers\j_size_qry_case_dest_guild_out_guildbattlecostQEAA_14000E700.h" />
    <ClInclude Include="headers\j_size_qry_case_in_guildbattlecostQEAAHXZ_140012B16.h" />
    <ClInclude Include="headers\j_size_qry_case_in_guildbattlerewardmoneyQEAAHXZ_140013E49.h" />
    <ClInclude Include="headers\j_size_qry_case_loadguildbattlerankQEAAHXZ_140002B30.h" />
    <ClInclude Include="headers\j_size_qry_case_load_guildbattle_totalrecordQEAAHX_140007FEA.h" />
    <ClInclude Include="headers\j_size_qry_case_src_guild_out_guildbattlecostQEAAH_14000D837.h" />
    <ClInclude Include="headers\j_size_qry_case_updateclearguildbattleDayInfoQEAAH_1400117ED.h" />
    <ClInclude Include="headers\j_size_qry_case_updatedrawguildbattlerankQEAAHXZ_1400060A5.h" />
    <ClInclude Include="headers\j_size_qry_case_updatewinloseguildbattlerankQEAAHX_14000D5E9.h" />
    <ClInclude Include="headers\j_SrcGuildIsAvailableBattleRequestStateCGuildQEAAE_1400053B2.h" />
    <ClInclude Include="headers\j_start_casheventCashItemRemoteStoreQEAA_NHHHHEZ_1400016F4.h" />
    <ClInclude Include="headers\j_start_cdeCashItemRemoteStoreQEAA_NHHHHZ_140004F7A.h" />
    <ClInclude Include="headers\j_start_coneventCashItemRemoteStoreQEAA_NHHEZ_140004F61.h" />
    <ClInclude Include="headers\j_StockOldInfoCNormalGuildBattleGuildMemberGUILD_B_14000909D.h" />
    <ClInclude Include="headers\j_TakeGravityStoneCNormalGuildBattleGUILD_BATTLEQE_1400067DF.h" />
    <ClInclude Include="headers\j_unchecked_copyPEAVCGuildBattleRewardItemGUILD_BA_14000BF28.h" />
    <ClInclude Include="headers\j_unchecked_uninitialized_copyPEAVCGuildBattleRewa_140010965.h" />
    <ClInclude Include="headers\j_unchecked_uninitialized_fill_nPEAVCGuildBattleRe_14001087F.h" />
    <ClInclude Include="headers\j_UpdateCGuildBattleRankManagerGUILD_BATTLEQEAA_NE_14000F8BC.h" />
    <ClInclude Include="headers\j_UpdateClearGuildBattleDayInfoCNormalGuildBattleM_1400047B9.h" />
    <ClInclude Include="headers\j_UpdateClearGuildBattleInfoCRFWorldDatabaseQEAA_N_140004192.h" />
    <ClInclude Include="headers\j_UpdateClearGuildBattleRankCRFWorldDatabaseQEAA_N_1400108C0.h" />
    <ClInclude Include="headers\j_UpdateClearGuildBattleScheduleDayInfoCGuildBattl_1400013ED.h" />
    <ClInclude Include="headers\j_UpdateClearGuildBattleScheduleInfoCRFWorldDataba_1400014C4.h" />
    <ClInclude Include="headers\j_UpdateClearGuildBattleScheduleInfoCRFWorldDataba_14001141E.h" />
    <ClInclude Include="headers\j_UpdateClearRerservedDayInfoCGuildBattleControlle_1400042A5.h" />
    <ClInclude Include="headers\j_UpdateDayChangedWorkCGuildBattleScheduleManagerG_14000998F.h" />
    <ClInclude Include="headers\j_UpdateDrawCGuildBattleControllerQEAA_NEKEKZ_140008F67.h" />
    <ClInclude Include="headers\j_UpdateDrawCGuildBattleRankManagerGUILD_BATTLEQEA_14000CABD.h" />
    <ClInclude Include="headers\j_UpdateDrawGuildBattleResultCRFWorldDatabaseQEAA__140003594.h" />
    <ClInclude Include="headers\j_UpdateGoalCntCCurrentGuildBattleInfoManagerGUILD_140010BA9.h" />
    <ClInclude Include="headers\j_UpdateGuildBattleDrawRankInfoCMainThreadQEAAXPEA_14000FA88.h" />
    <ClInclude Include="headers\j_UpdateGuildBattleInfoCRFWorldDatabaseQEAA_NKKKKE_140001F1E.h" />
    <ClInclude Include="headers\j_UpdateGuildBattleScheduleInfoCRFWorldDatabaseQEA_14000E46C.h" />
    <ClInclude Include="headers\j_UpdateGuildBattleWinCntCGuildQEAAXKKKZ_14000ED40.h" />
    <ClInclude Include="headers\j_UpdateGuildBattleWinLoseRankInfoCMainThreadQEAAX_14000B578.h" />
    <ClInclude Include="headers\j_UpdateGuildListCPossibleBattleGuildListManagerGU_1400016EF.h" />
    <ClInclude Include="headers\j_UpdateLoadGuildBattleRankCMainThreadQEAAXPEAU_DB_14000EFE8.h" />
    <ClInclude Include="headers\j_UpdateLoseGuildBattleResultCRFWorldDatabaseQEAA__140008AF8.h" />
    <ClInclude Include="headers\j_UpdateMonsterSFContDamageToleracneQEAAXXZ_140009827.h" />
    <ClInclude Include="headers\j_UpdatePossibleBattleGuildListCGuildBattleControl_14000D8B4.h" />
    <ClInclude Include="headers\j_UpdateRankCGuildBattleControllerQEAA_NEPEAEZ_140006FC3.h" />
    <ClInclude Include="headers\j_UpdateReservedGuildBattleScheduleCGuildBattleCon_14000D4EF.h" />
    <ClInclude Include="headers\j_UpdateReservedGuildBattleScheduleCMainThreadQEAA_14000994E.h" />
    <ClInclude Include="headers\j_UpdateReservedSheduleCGuildBattleReservedSchedul_140005182.h" />
    <ClInclude Include="headers\j_UpdateScoreCCurrentGuildBattleInfoManagerGUILD_B_140002022.h" />
    <ClInclude Include="headers\j_UpdateScoreCNormalGuildBattleGuildGUILD_BATTLEIE_14000E610.h" />
    <ClInclude Include="headers\j_UpdateTodayScheduleCGuildBattleReservedScheduleL_14000D274.h" />
    <ClInclude Include="headers\j_UpdateTomorrowCompleteCGuildBattleReservedSchedu_1400074E6.h" />
    <ClInclude Include="headers\j_UpdateTomorrowScheduleCGuildBattleReservedSchedu_140001E06.h" />
    <ClInclude Include="headers\j_UpdateUseFieldCGuildBattleReservedScheduleGUILD__140004020.h" />
    <ClInclude Include="headers\j_UpdateUseFlagCGuildBattleReservedScheduleGUILD_B_140011C84.h" />
    <ClInclude Include="headers\j_UpdateUseFlagCGuildBattleReservedScheduleMapGrou_14000585D.h" />
    <ClInclude Include="headers\j_UpdateUseFlagCGuildBattleScheduleManagerGUILD_BA_140010A2D.h" />
    <ClInclude Include="headers\j_UpdateWinGuildBattleResultCRFWorldDatabaseQEAA_N_14000321F.h" />
    <ClInclude Include="headers\j_UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_140003C0B.h" />
    <ClInclude Include="headers\j_UpdateWinLoseCGuildBattleRankManagerGUILD_BATTLE_14000142E.h" />
    <ClInclude Include="headers\j_Update_BattleResultLogBattleResultAndPvpPointCRF_140011DEC.h" />
    <ClInclude Include="headers\j_Update_CristalBattleCharInfoCRFWorldDatabaseQEAA_140007D06.h" />
    <ClInclude Include="headers\j_update_cristalbattle_dateCRFWorldDatabaseQEAA_NK_140001046.h" />
    <ClInclude Include="headers\j_Update_IncreaseWeeklyGuildGuildBattlePvpPointSum_140004868.h" />
    <ClInclude Include="headers\j_update_iniCashItemRemoteStoreQEAAXPEAU_cash_disc_140008184.h" />
    <ClInclude Include="headers\j_Update_INICashItemRemoteStoreQEAAXPEAU_cash_even_1400026DA.h" />
    <ClInclude Include="headers\j_WriteLogPer10Min_CombatCHolyStoneSystemIEAAXXZ_14000515F.h" />
    <ClInclude Include="headers\j__AllocateVCGuildBattleRewardItemGUILD_BATTLEstdY_14000FF9C.h" />
    <ClInclude Include="headers\j__Assign_nvectorVCGuildBattleRewardItemGUILD_BATT_14000C199.h" />
    <ClInclude Include="headers\j__buybygold_buy_single_item_calc_price_discountCa_140002518.h" />
    <ClInclude Include="headers\j__buybygold_buy_single_item_calc_price_limitsaleC_14000AE48.h" />
    <ClInclude Include="headers\j__buybygold_buy_single_item_calc_price_one_n_oneC_14000FA74.h" />
    <ClInclude Include="headers\j__buybygold_buy_single_item_setbuydblogCashItemRe_14000C64E.h" />
    <ClInclude Include="headers\j__BuyvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1400111EE.h" />
    <ClInclude Include="headers\j__CalcForceAttPntCAttackIEAAH_NZ_1400030C1.h" />
    <ClInclude Include="headers\j__CalcGenAttPntCAttackIEAAH_NZ_1400132FA.h" />
    <ClInclude Include="headers\j__check_buyitemCashItemRemoteStoreAEAAAW4CS_RCODE_1400056FA.h" />
    <ClInclude Include="headers\j__complete_tsk_cashitem_buy_dblogCashDbWorkerIEAA_1400022DE.h" />
    <ClInclude Include="headers\j__ConstructVCGuildBattleRewardItemGUILD_BATTLEV12_14000670D.h" />
    <ClInclude Include="headers\j__Copy_backward_optPEAVCGuildBattleRewardItemGUIL_14000188E.h" />
    <ClInclude Include="headers\j__Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEP_14000C6AD.h" />
    <ClInclude Include="headers\j__db_Load_BattleTournamentInfoCMainThreadAEAAXXZ_140003EAE.h" />
    <ClInclude Include="headers\j__db_load_losebattlecountCMainThreadAEAAEKPEAU_AV_14000ECFA.h" />
    <ClInclude Include="headers\j__delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_1400055CE.h" />
    <ClInclude Include="headers\j__DestroyVCGuildBattleRewardItemGUILD_BATTLEstdYA_140008FA3.h" />
    <ClInclude Include="headers\j__DestroyvectorVCGuildBattleRewardItemGUILD_BATTL_14000AF6F.h" />
    <ClInclude Include="headers\j__Destroy_rangeVCGuildBattleRewardItemGUILD_BATTL_140004D27.h" />
    <ClInclude Include="headers\j__Destroy_rangeVCGuildBattleRewardItemGUILD_BATTL_1400116D0.h" />
    <ClInclude Include="headers\j__ECNormalGuildBattleFieldGUILD_BATTLEQEAAPEAXIZ_140007608.h" />
    <ClInclude Include="headers\j__ECNormalGuildBattleStateListGUILD_BATTLEQEAAPEA_140003846.h" />
    <ClInclude Include="headers\j__ECReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_14000C0E5.h" />
    <ClInclude Include="headers\j__FillPEAVCGuildBattleRewardItemGUILD_BATTLEV12st_14000904D.h" />
    <ClInclude Include="headers\j__GCCurrentGuildBattleInfoManagerGUILD_BATTLEIEAA_140011207.h" />
    <ClInclude Include="headers\j__GCGuildBattleControllerIEAAPEAXIZ_140010406.h" />
    <ClInclude Include="headers\j__GCGuildBattleLoggerGUILD_BATTLEIEAAPEAXIZ_14000BFF5.h" />
    <ClInclude Include="headers\j__GCGuildBattleRankManagerGUILD_BATTLEIEAAPEAXIZ_140002BC6.h" />
    <ClInclude Include="headers\j__GCGuildBattleReservedScheduleGUILD_BATTLEQEAAPE_140012DC8.h" />
    <ClInclude Include="headers\j__GCGuildBattleReservedScheduleListManagerGUILD_B_140009D5E.h" />
    <ClInclude Include="headers\j__GCGuildBattleScheduleGUILD_BATTLEQEAAPEAXIZ_140009FD4.h" />
    <ClInclude Include="headers\j__GCGuildBattleScheduleManagerGUILD_BATTLEIEAAPEA_140007F8B.h" />
    <ClInclude Include="headers\j__GCGuildBattleSchedulePoolGUILD_BATTLEIEAAPEAXIZ_14000EBF1.h" />
    <ClInclude Include="headers\j__GCGuildBattleSchedulerGUILD_BATTLEIEAAPEAXIZ_14000BA7D.h" />
    <ClInclude Include="headers\j__GCNormalGuildBattleFieldListGUILD_BATTLEIEAAPEA_1400116AD.h" />
    <ClInclude Include="headers\j__GCNormalGuildBattleGUILD_BATTLEQEAAPEAXIZ_140009CDC.h" />
    <ClInclude Include="headers\j__GCNormalGuildBattleManagerGUILD_BATTLEIEAAPEAXI_140009764.h" />
    <ClInclude Include="headers\j__GCNormalGuildBattleStateListPoolGUILD_BATTLEIEA_1400123F0.h" />
    <ClInclude Include="headers\j__GCPossibleBattleGuildListManagerGUILD_BATTLEIEA_14001128E.h" />
    <ClInclude Include="headers\j__GCRFCashItemDatabaseUEAAPEAXIZ_0_140010613.h" />
    <ClInclude Include="headers\j__GCRFCashItemDatabaseUEAAPEAXIZ_14000A637.h" />
    <ClInclude Include="headers\j__InitLoggersCashItemRemoteStoreAEAA_NXZ_140013FFC.h" />
    <ClInclude Include="headers\j__Insert_nvectorVCGuildBattleRewardItemGUILD_BATT_14000CD4C.h" />
    <ClInclude Include="headers\j__Iter_randomPEAVCGuildBattleRewardItemGUILD_BATT_140013921.h" />
    <ClInclude Include="headers\j__MakeLinkTableCashItemRemoteStoreAEAA_NPEADHZ_14001249F.h" />
    <ClInclude Include="headers\j__Max_elementPEAVCNormalGuildBattleGuildMemberGUI_140005B32.h" />
    <ClInclude Include="headers\j__Max_elementPEAVCNormalGuildBattleGuildMemberGUI_1400114E1.h" />
    <ClInclude Include="headers\j__Move_backward_optPEAVCGuildBattleRewardItemGUIL_140012BB1.h" />
    <ClInclude Include="headers\j__Move_catPEAVCGuildBattleRewardItemGUILD_BATTLEs_14001233C.h" />
    <ClInclude Include="headers\j__Ptr_catPEAVCGuildBattleRewardItemGUILD_BATTLEPE_140009435.h" />
    <ClInclude Include="headers\j__PushItemCutLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LT_14000C135.h" />
    <ClInclude Include="headers\j__PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_L_14000870B.h" />
    <ClInclude Include="headers\j__ReadGoodsCashItemRemoteStoreAEAA_NXZ_140004606.h" />
    <ClInclude Include="headers\j__TidyvectorVCGuildBattleRewardItemGUILD_BATTLEVa_1400041AB.h" />
    <ClInclude Include="headers\j__UfillvectorVCGuildBattleRewardItemGUILD_BATTLEV_140006C3A.h" />
    <ClInclude Include="headers\j__UmovePEAVCGuildBattleRewardItemGUILD_BATTLEvect_14000B7D0.h" />
    <ClInclude Include="headers\j__Unchecked_move_backwardPEAVCGuildBattleRewardIt_14000239C.h" />
    <ClInclude Include="headers\j__Unchecked_uninitialized_movePEAVCGuildBattleRew_14000F358.h" />
    <ClInclude Include="headers\j__Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATT_140011E82.h" />
    <ClInclude Include="headers\j__Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BA_14000B6C2.h" />
    <ClInclude Include="headers\j__Uninit_movePEAVCGuildBattleRewardItemGUILD_BATT_140012D1E.h" />
    <ClInclude Include="headers\j__XlenvectorVCGuildBattleRewardItemGUILD_BATTLEVa_1400131A1.h" />
    <ClInclude Include="headers\j___CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecord_14000F484.h" />
    <ClInclude Include="headers\KillCNormalGuildBattleGuildGUILD_BATTLEQEAAHPEAVCN_1403E1560.h" />
    <ClInclude Include="headers\KillCNormalGuildBattleGUILD_BATTLEQEAAHKKZ_1403E5C30.h" />
    <ClInclude Include="headers\KillCNormalGuildBattleManagerGUILD_BATTLEQEAAHKKKZ_1403D4B20.h" />
    <ClInclude Include="headers\LeaveGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAX_1403E1460.h" />
    <ClInclude Include="headers\LimitedSale_check_countCashItemRemoteStoreQEAA_NEK_1402FDD90.h" />
    <ClInclude Include="headers\LoadBuyCashModeCashItemRemoteStoreAEAA_NXZ_1402F4D90.h" />
    <ClInclude Include="headers\LoadCGuildBattleControllerQEAA_NXZ_1403D5950.h" />
    <ClInclude Include="headers\LoadCGuildBattleRankManagerGUILD_BATTLEIEAA_NEZ_1403CB820.h" />
    <ClInclude Include="headers\LoadCGuildBattleRankManagerGUILD_BATTLEQEAA_NXZ_1403CA610.h" />
    <ClInclude Include="headers\LoadCGuildBattleReservedScheduleGUILD_BATTLEQEAA_N_1403DAEA0.h" />
    <ClInclude Include="headers\LoadCGuildBattleReservedScheduleListManagerGUILD_B_1403CD540.h" />
    <ClInclude Include="headers\LoadCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DBDC0.h" />
    <ClInclude Include="headers\LoadCGuildBattleScheduleGUILD_BATTLEQEAA_N_NKE_JGZ_1403D9F60.h" />
    <ClInclude Include="headers\LoadCGuildBattleScheduleManagerGUILD_BATTLEQEAA_NH_1403DCDD0.h" />
    <ClInclude Include="headers\LoadCNormalGuildBattleManagerGUILD_BATTLEIEAA_N_NI_1403D4D90.h" />
    <ClInclude Include="headers\LoadCNormalGuildBattleManagerGUILD_BATTLEQEAA_NHIH_1403D38F0.h" />
    <ClInclude Include="headers\LoadCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403C99F0.h" />
    <ClInclude Include="headers\LoadCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CCF20.h" />
    <ClInclude Include="headers\LoadCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CC540.h" />
    <ClInclude Include="headers\LoadDBGuildBattleInfoCNormalGuildBattleManagerGUIL_1403D4F40.h" />
    <ClInclude Include="headers\LoadDummysCNormalGuildBattleFieldGUILD_BATTLEAEAA__1403ED930.h" />
    <ClInclude Include="headers\LoadGuildBattleInfoCRFWorldDatabaseQEAA_NKKPEAU_wo_1404A25D0.h" />
    <ClInclude Include="headers\LoadGuildBattleScheduleInfoCRFWorldDatabaseQEAAEII_1404A15E0.h" />
    <ClInclude Include="headers\LoadINICGuildBattleControllerAEAA_NAEAIAEAH111Z_1403D7BB0.h" />
    <ClInclude Include="headers\LoadNationalPriceCashItemRemoteStoreAEAA_NAEAVCRec_1402F4C90.h" />
    <ClInclude Include="headers\LoadTodayScheduleCGuildBattleReservedScheduleListM_1403CDD00.h" />
    <ClInclude Include="headers\LoadTomorrowScheduleCGuildBattleReservedScheduleLi_1403CDDA0.h" />
    <ClInclude Include="headers\load_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F5E30.h" />
    <ClInclude Include="headers\Load_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7F10.h" />
    <ClInclude Include="headers\load_cde_iniCashItemRemoteStoreQEAAXPEAU_cash_disc_1402F60C0.h" />
    <ClInclude Include="headers\Load_Conditional_EventCashItemRemoteStoreQEAAXXZ_1402FC330.h" />
    <ClInclude Include="headers\load_con_event_iniCashItemRemoteStoreQEAAXPEAU_con_1402FBC10.h" />
    <ClInclude Include="headers\Load_Event_INICashItemRemoteStoreQEAAXPEAU_cash_ev_1402F99B0.h" />
    <ClInclude Include="headers\Load_LimitedSale_Event_INICashItemRemoteStoreQEAAX_1402FD4A0.h" />
    <ClInclude Include="headers\LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_1403CEAC0.h" />
    <ClInclude Include="headers\LogCGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZZ_1403CEB50.h" />
    <ClInclude Include="headers\LogCGuildBattleStateGUILD_BATTLEIEAAXPEADZ_1403DEE60.h" />
    <ClInclude Include="headers\LogCGuildBattleStateListGUILD_BATTLEIEAAXPEADZ_1403DF340.h" />
    <ClInclude Include="headers\LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEADZZ_1403CEE40.h" />
    <ClInclude Include="headers\LogCNormalGuildBattleLoggerGUILD_BATTLEQEAAXPEA_WZ_1403CEED0.h" />
    <ClInclude Include="headers\LogCNormalGuildBattleStateGUILD_BATTLEIEAAXPEAVCNo_1403F04D0.h" />
    <ClInclude Include="headers\LogCNormalGuildBattleStateRoundGUILD_BATTLEIEAAXPE_1403F1170.h" />
    <ClInclude Include="headers\log_about_cash_eventCashItemRemoteStoreQEAAXPEADPE_1402F73A0.h" />
    <ClInclude Include="headers\LoopCGuildBattleControllerQEAAXXZ_1403D6760.h" />
    <ClInclude Include="headers\LoopCGuildBattleReservedScheduleGUILD_BATTLEQEAA_N_1403DAD80.h" />
    <ClInclude Include="headers\LoopCGuildBattleReservedScheduleMapGroupGUILD_BATT_1403DC0D0.h" />
    <ClInclude Include="headers\LoopCGuildBattleScheduleManagerGUILD_BATTLEQEAAXXZ_1403DCD30.h" />
    <ClInclude Include="headers\LoopCGuildBattleStateGUILD_BATTLEUEAAHPEAVCGuildBa_14007F780.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleManagerGUILD_BATTLEQEAAXXZ_1403D4110.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleStateGUILD_BATTLEMEAAHPEAVCN_14007FBE0.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleStateGUILD_BATTLEUEAAHPEAVCG_1403F03F0.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleStateInBattleGUILD_BATTLEUEA_140080070.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleStateRoundGUILD_BATTLEMEAAHP_1403F31A0.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleStateRoundGUILD_BATTLEUEAAHP_1403F1090.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleStateRoundProcessGUILD_BATTL_1403F1A00.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleStateRoundReturnStartPosGUIL_1403F1DA0.h" />
    <ClInclude Include="headers\LoopCNormalGuildBattleStateRoundStartGUILD_BATTLEM_1403F15E0.h" />
    <ClInclude Include="headers\loop_cash_discount_eventCashItemRemoteStoreQEAAXXZ_1402F6920.h" />
    <ClInclude Include="headers\Loop_Cash_EventCashItemRemoteStoreQEAAXXZ_1402F7E80.h" />
    <ClInclude Include="headers\Loop_Check_Total_SellingCashItemRemoteStoreQEAAXXZ_1402FB620.h" />
    <ClInclude Include="headers\Loop_ContEventCashItemRemoteStoreQEAAXXZ_1402FB5D0.h" />
    <ClInclude Include="headers\Loop_TatalCashEventCashItemRemoteStoreQEAAXXZ_1402FB560.h" />
    <ClInclude Include="headers\MakePageCPossibleBattleGuildListManagerGUILD_BATTL_1403C9D70.h" />
    <ClInclude Include="headers\ManageAcceptORRefuseGuildBattleCGuildQEAAE_NZ_140259EF0.h" />
    <ClInclude Include="headers\ManageProposeGuildBattleCGuildQEAAEKKKKZ_1402593D0.h" />
    <ClInclude Include="headers\max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB430.h" />
    <ClInclude Include="headers\max_elementPEAVCNormalGuildBattleGuildMemberGUILD__1403EB4A0.h" />
    <ClInclude Include="headers\max_sizeallocatorVCGuildBattleRewardItemGUILD_BATT_1403D22F0.h" />
    <ClInclude Include="headers\max_sizevectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D1670.h" />
    <ClInclude Include="headers\ModifyMonsterAttFcCMonsterAttackIEAAMMZ_1401615B0.h" />
    <ClInclude Include="headers\MoveMapCNormalGuildBattleGuildGUILD_BATTLEQEAAXIPE_1403E0F60.h" />
    <ClInclude Include="headers\MoveMemberCNormalGuildBattleGuildGUILD_BATTLEQEAA__1403E1140.h" />
    <ClInclude Include="headers\NetCloseCNormalGuildBattleGuildGUILD_BATTLEQEAA_N__1403E13B0.h" />
    <ClInclude Include="headers\NetCloseCNormalGuildBattleGuildMemberGUILD_BATTLEQ_1403DFAE0.h" />
    <ClInclude Include="headers\NextCGuildBattleReservedScheduleGUILD_BATTLEAEAA_N_1403DB7F0.h" />
    <ClInclude Include="headers\NextCGuildBattleStateListGUILD_BATTLEQEAAH_NZ_1403DF220.h" />
    <ClInclude Include="headers\NotifyBallPositionCNormalGuildBattleGUILD_BATTLEQE_1403E53E0.h" />
    <ClInclude Include="headers\NotifyBattleResultCNormalGuildBattleGUILD_BATTLEQE_1403E6A90.h" />
    <ClInclude Include="headers\NotifyBeforeStartCNormalGuildBattleGUILD_BATTLEQEA_1403E50E0.h" />
    <ClInclude Include="headers\NotifyCommitteeMemberPositionCNormalGuildBattleGUI_1403E55B0.h" />
    <ClInclude Include="headers\NotifyDestoryBallCNormalGuildBattleGUILD_BATTLEQEA_1403E5510.h" />
    <ClInclude Include="headers\NotifyLeftMinuteBeforeStartCNormalGuildBattleGuild_1403E2050.h" />
    <ClInclude Include="headers\NotifyPassGravityStoneLimitTimeCNormalGuildBattleG_1403E5700.h" />
    <ClInclude Include="headers\OnlyOnceInitMonsterSFContDamageToleracneQEAAXPEAVC_140157ED0.h" />
    <ClInclude Include="headers\OnToolHitTestCWndUEBA_JVCPointPEAUtagTOOLINFOAZ_0_1404DBC28.h" />
    <ClInclude Include="headers\On_HS_SCENE_BATTLE_END_WAIT_TIMECHolyStoneSystemIE_14027C3E0.h" />
    <ClInclude Include="headers\On_HS_SCENE_BATTLE_TIMECHolyStoneSystemIEAAXXZ_14027C120.h" />
    <ClInclude Include="headers\On_HS_SCENE_KEEPER_ATTACKABLE_TIMECHolyStoneSystem_14027C540.h" />
    <ClInclude Include="headers\On_HS_SCENE_KEEPER_DEATTACKABLE_TIMECHolyStoneSyst_14027C610.h" />
    <ClInclude Include="headers\OutDestGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY__1401F4970.h" />
    <ClInclude Include="headers\OutSrcGuildbattleCostCMainThreadQEAAXPEAU_DB_QRY_S_1401F47E0.h" />
    <ClInclude Include="headers\ProcCheckGetGravityStoneCNormalGuildBattleManagerG_1403D52C0.h" />
    <ClInclude Include="headers\ProcCheckGoalCNormalGuildBattleManagerGUILD_BATTLE_1403D5360.h" />
    <ClInclude Include="headers\ProcCheckTakeGravityStoneCNormalGuildBattleManager_1403D5220.h" />
    <ClInclude Include="headers\ProcessCGuildBattleScheduleGUILD_BATTLEIEAAHXZ_1403DA3B0.h" />
    <ClInclude Include="headers\ProcessCGuildBattleStateListGUILD_BATTLEQEAAXPEAVC_1403DF090.h" />
    <ClInclude Include="headers\ProcessCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403D92F0.h" />
    <ClInclude Include="headers\ProcJoinCNormalGuildBattleManagerGUILD_BATTLEIEAAH_1403D5190.h" />
    <ClInclude Include="headers\PushClearGuildBattleRankCGuildBattleControllerQEAA_1402CFCA0.h" />
    <ClInclude Include="headers\PushClearGuildBattleRankCGuildBattleRankManagerGUI_1403CB380.h" />
    <ClInclude Include="headers\PushCreateGuildBattleRankTableCGuildBattleControll_1402CFC50.h" />
    <ClInclude Include="headers\PushCreateGuildBattleRankTableCGuildBattleRankMana_1403CB250.h" />
    <ClInclude Include="headers\PushDQSCGuildBattleReservedScheduleListManagerGUIL_1403CD690.h" />
    <ClInclude Include="headers\PushDQSClearCGuildBattleReservedScheduleMapGroupGU_1403DC2E0.h" />
    <ClInclude Include="headers\PushDQSDataCNormalGuildBattleManagerGUILD_BATTLEIE_1403D5050.h" />
    <ClInclude Include="headers\PushDQSDestGuildOutputGuildBattleCostCGuildQEAAXXZ_140258020.h" />
    <ClInclude Include="headers\PushDQSDrawRankCNormalGuildBattleGUILD_BATTLEIEAAX_1403E7B70.h" />
    <ClInclude Include="headers\PushDQSInGuildBattleCostCGuildQEAAXXZ_140257DB0.h" />
    <ClInclude Include="headers\PushDQSInGuildBattleRewardMoneyCGuildQEAAXXZ_140258180.h" />
    <ClInclude Include="headers\PushDQSPvpPointCNormalGuildBattleGuildMemberGUILD__1403E03F0.h" />
    <ClInclude Include="headers\PushDQSSourceGuildOutputGuildBattleCostCGuildQEAAX_140257EC0.h" />
    <ClInclude Include="headers\PushDQSWinLoseRankCNormalGuildBattleGUILD_BATTLEIE_1403E7C40.h" />
    <ClInclude Include="headers\PushItemCObjectListQEAA_NPEAU_object_list_pointZ_140189C30.h" />
    <ClInclude Include="headers\PushItem_TRAP_PARAMQEAA_NPEAVCTrapKZ_1400791F0.h" />
    <ClInclude Include="headers\RCTopGoalPrediCateCNormalGuildBattleGuildGUILD_BAT_1403EB720.h" />
    <ClInclude Include="headers\RCTopKillPrediCateCNormalGuildBattleGuildGUILD_BAT_1403EB5D0.h" />
    <ClInclude Include="headers\RegenBallCNormalGuildBattleFieldGUILD_BATTLEQEAAHX_1403ECBE0.h" />
    <ClInclude Include="headers\RequestSubProcSetRaceBattleResultCRaceBuffByHolyQu_1403B66E0.h" />
    <ClInclude Include="headers\ReturnBindPosAllCNormalGuildBattleGuildGUILD_BATTL_1403E1CC0.h" />
    <ClInclude Include="headers\ReturnBindPosCNormalGuildBattleGuildMemberGUILD_BA_1403E0020.h" />
    <ClInclude Include="headers\ReturnHQPosAllCNormalGuildBattleGuildGUILD_BATTLEQ_1403E1B50.h" />
    <ClInclude Include="headers\ReturnStartPosAllCNormalGuildBattleGuildGUILD_BATT_1403E1D50.h" />
    <ClInclude Include="headers\ReturnStartPosCNormalGuildBattleGuildMemberGUILD_B_1403DFFC0.h" />
    <ClInclude Include="headers\RewardGuildBattleMoneyCNormalGuildBattleGUILD_BATT_1403E6840.h" />
    <ClInclude Include="headers\RewardItemCNormalGuildBattleGuildGUILD_BATTLEQEAAX_1403E19B0.h" />
    <ClInclude Include="headers\RewardItemCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E6960.h" />
    <ClInclude Include="headers\rollback_cashitemCMgrAvatorItemHistoryQEAAXPEAD_K0_14023E160.h" />
    <ClInclude Include="headers\SaveCNormalGuildBattleGUILD_BATTLEQEAA_NXZ_1403E37C0.h" />
    <ClInclude Include="headers\SaveCNormalGuildBattleManagerGUILD_BATTLEQEAA_NKZ_1403D3D10.h" />
    <ClInclude Include="headers\SaveINICGuildBattleControllerAEAA_NXZ_1403D7960.h" />
    <ClInclude Include="headers\SearchItemAddSpeedCEquipItemSFAgentIEAAMPEAU_db_co_1401217E0.h" />
    <ClInclude Include="headers\SectorDamageProcCAttackIEAAXHHHHHH_NZ_14016CDD0.h" />
    <ClInclude Include="headers\SelectGuildBattleRankListCGuildBattleRankManagerGU_1403CAF70.h" />
    <ClInclude Include="headers\SelectGuildBattleRankListCRFWorldDatabaseQEAA_NEPE_1404A2B70.h" />
    <ClInclude Include="headers\SelectGuildBattleRankRecordCRFWorldDatabaseQEAA_NK_1404A3460.h" />
    <ClInclude Include="headers\SelectGuildBattleRerservedListCRFWorldDatabaseQEAA_1404A38C0.h" />
    <ClInclude Include="headers\SelectGuildBattleScheduleInfoIDCRFWorldDatabaseQEA_1404A1CF0.h" />
    <ClInclude Include="headers\SelectRowCountGuildBattleInfoCRFWorldDatabaseQEAAH_1404A20D0.h" />
    <ClInclude Include="headers\SelectRowCountGuildBattleScheduleInfoCRFWorldDatab_1404A12B0.h" />
    <ClInclude Include="headers\Select_BattleResultLogLatestCRFWorldDatabaseQEAAEP_1404B0DE0.h" />
    <ClInclude Include="headers\Select_BattleTournamentInfoCRFWorldDatabaseQEAA_NP_1404C5EA0.h" />
    <ClInclude Include="headers\Select_FailBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C12B0.h" />
    <ClInclude Include="headers\Select_GuildBattleRecordCRFWorldDatabaseQEAA_NKPEA_1404AA8F0.h" />
    <ClInclude Include="headers\Select_LoseBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C0B30.h" />
    <ClInclude Include="headers\Select_WinBattleCountCRFWorldDatabaseQEAAHEKAEAKZ_1404C0EF0.h" />
    <ClInclude Include="headers\SellCashItemRemoteStoreQEAA_NGPEADZ_1402F5CB0.h" />
    <ClInclude Include="headers\SetActiveSuccCAttackQEAAX_NZ_14008E710.h" />
    <ClInclude Include="headers\SetAttackPartCGameObjectUEAAXHZ_14012C700.h" />
    <ClInclude Include="headers\SetBattleModeTimeCMonsterAIQEAAXKZ_1401555F0.h" />
    <ClInclude Include="headers\SetBattleStateCNormalGuildBattleGuildMemberGUILD_B_1403DFC80.h" />
    <ClInclude Include="headers\SetBattleTimeCNormalGuildBattleStateInBattleGUILD__1403D90D0.h" />
    <ClInclude Include="headers\SetBattleTimeCNormalGuildBattleStateListGUILD_BATT_1403D9070.h" />
    <ClInclude Include="headers\SetCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA__1403CE160.h" />
    <ClInclude Include="headers\SetCGuildBattleScheduleGUILD_BATTLEQEAAEKKZ_1403D9B90.h" />
    <ClInclude Include="headers\SetColorInxCNormalGuildBattleGuildGUILD_BATTLEQEAA_1403EB190.h" />
    <ClInclude Include="headers\SetCombatStateCMonsterQEAAXEZ_140143830.h" />
    <ClInclude Include="headers\SetCopmlteGuildBattleSuggestCGuildQEAAXXZ_1402582E0.h" />
    <ClInclude Include="headers\SetDamageAbleStateCHolyKeeperQEAAX_NZ_140284770.h" />
    <ClInclude Include="headers\SetDelay_ATTACK_DELAY_CHECKERQEAAXKZ_14008E760.h" />
    <ClInclude Include="headers\SetGotoRegenStartCNormalGuildBattleGUILD_BATTLEQEA_1403F3240.h" />
    <ClInclude Include="headers\SetGotoRegenStateCNormalGuildBattleStateInBattleGU_1403F3300.h" />
    <ClInclude Include="headers\SetGotoRegenStateCNormalGuildBattleStateListGUILD__1403F3290.h" />
    <ClInclude Include="headers\SetGuildBattleMatterCGuildQEAAXKKKKZ_140259E40.h" />
    <ClInclude Include="headers\SetGuildCNormalGuildBattleGuildGUILD_BATTLEQEAAXPE_1403EB0A0.h" />
    <ClInclude Include="headers\SetItemCGuildBattleRewardItemGUILD_BATTLEIEAA_NPEA_1403C92D0.h" />
    <ClInclude Include="headers\SetLoadCBattleTournamentInfoQEAAX_NZ_1403FEB50.h" />
    <ClInclude Include="headers\SetNextDiscountEventTimeCashItemRemoteStoreQEAA_NX_1402F7900.h" />
    <ClInclude Include="headers\SetNextEventCNormalGuildBattleManagerGUILD_BATTLEQ_1403DED80.h" />
    <ClInclude Include="headers\SetNextEventTimeCashItemRemoteStoreQEAA_NEZ_1402FCD30.h" />
    <ClInclude Include="headers\SetNextEvnetCGuildBattleScheduleManagerGUILD_BATTL_1403DD6C0.h" />
    <ClInclude Include="headers\SetNextStateCGuildBattleStateListGUILD_BATTLEMEAAX_14007F830.h" />
    <ClInclude Include="headers\SetNextStateCNormalGuildBattleStateListGUILD_BATTL_140080340.h" />
    <ClInclude Include="headers\SetNextStateCNormalGuildBattleStateRoundListGUILD__140080040.h" />
    <ClInclude Include="headers\SetOrderViewAttackStateCPvpOrderViewQEAAXXZ_1403F7AA0.h" />
    <ClInclude Include="headers\SetOrderViewDamagedStateCPvpOrderViewQEAAXXZ_1403F7AF0.h" />
    <ClInclude Include="headers\SetPortalInxCNormalGuildBattleFieldGUILD_BATTLEAEA_1403EDD60.h" />
    <ClInclude Include="headers\SetProcStateCGuildBattleScheduleGUILD_BATTLEQEAAXX_1403DEBB0.h" />
    <ClInclude Include="headers\SetReadyCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403EB0D0.h" />
    <ClInclude Include="headers\SetReadyStateCNormalGuildBattleGUILD_BATTLEQEAAXXZ_1403E3B30.h" />
    <ClInclude Include="headers\SetReadyStateCNormalGuildBattleManagerGUILD_BATTLE_1403D5480.h" />
    <ClInclude Include="headers\SetReStartFlagCNormalGuildBattleGuildGUILD_BATTLEQ_1403E0900.h" />
    <ClInclude Include="headers\SetReStartFlagCNormalGuildBattleGuildMemberGUILD_B_1403EADB0.h" />
    <ClInclude Include="headers\SetSFDamageToleracne_VariationMonsterSFContDamageT_140158000.h" />
    <ClInclude Include="headers\SetStateListCGuildBattleScheduleGUILD_BATTLEQEAAXP_1403D9150.h" />
    <ClInclude Include="headers\SetStaticMemberCAttackSAXPEAVCRecordDataZ_14016D860.h" />
    <ClInclude Include="headers\SetWaitCGuildBattleStateListGUILD_BATTLEQEAAXXZ_1403EB110.h" />
    <ClInclude Include="headers\SetWinnerInfoCBattleTournamentInfoQEAA_NKPEADEZ_1403FEB70.h" />
    <ClInclude Include="headers\Set_CashEvent_StatusCashItemRemoteStoreQEAAXEEZ_1402FAB90.h" />
    <ClInclude Include="headers\set_cde_statusCashItemRemoteStoreQEAAXEZ_1402F72D0.h" />
    <ClInclude Include="headers\Set_Conditional_Evnet_StatusCashItemRemoteStoreQEA_1402FC3E0.h" />
    <ClInclude Include="headers\Set_DB_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_1402FDAD0.h" />
    <ClInclude Include="headers\Set_FROMDB_LimitedSale_EventCashItemRemoteStoreQEA_1402FDE70.h" />
    <ClInclude Include="headers\Set_LimitedSale_countCashItemRemoteStoreQEAAXEKZ_1402FDA00.h" />
    <ClInclude Include="headers\Set_LimitedSale_DCKCashItemRemoteStoreQEAAXEEZ_1402FD750.h" />
    <ClInclude Include="headers\Set_LimitedSale_EventCashItemRemoteStoreQEAAXXZ_1402FDFD0.h" />
    <ClInclude Include="headers\Set_LimitedSale_Event_IniCashItemRemoteStoreQEAAXP_1402FD780.h" />
    <ClInclude Include="headers\sizevectorVCGuildBattleRewardItemGUILD_BATTLEVallo_1403D2270.h" />
    <ClInclude Include="headers\size_attack_count_result_zoclQEAAHXZ_1400EEEF0.h" />
    <ClInclude Include="headers\size_attack_force_result_zoclQEAAHXZ_1400EEDF0.h" />
    <ClInclude Include="headers\size_attack_gen_result_zoclQEAAHXZ_1400EECF0.h" />
    <ClInclude Include="headers\size_attack_keeper_inform_zoclQEAAHXZ_140136C20.h" />
    <ClInclude Include="headers\size_attack_selfdestruction_result_zoclQEAAHXZ_1400EEF70.h" />
    <ClInclude Include="headers\size_attack_siege_result_zoclQEAAHXZ_1400EEFF0.h" />
    <ClInclude Include="headers\size_attack_trap_inform_zoclQEAAHXZ_140141420.h" />
    <ClInclude Include="headers\size_attack_unit_result_zoclQEAAHXZ_1400EEE70.h" />
    <ClInclude Include="headers\size_guild_battle_get_gravity_stone_result_zoclQEA_1403EAFE0.h" />
    <ClInclude Include="headers\size_guild_battle_goal_result_zoclQEAAHXZ_1403EB3A0.h" />
    <ClInclude Include="headers\size_guild_battle_rank_list_result_zoclQEAAHXZ_1403D0930.h" />
    <ClInclude Include="headers\size_guild_battle_reserved_schedule_result_zoclQEA_1403D0970.h" />
    <ClInclude Include="headers\size_guild_battle_suggest_request_result_zoclQEAAH_14025D5F0.h" />
    <ClInclude Include="headers\size_notify_not_use_premium_cashitem_zoclQEAAHXZ_1400F0850.h" />
    <ClInclude Include="headers\size_param_cashitem_dblogQEAAHXZ_140304D90.h" />
    <ClInclude Include="headers\size_personal_automine_attacked_zoclQEAAHXZ_1402DE340.h" />
    <ClInclude Include="headers\size_possible_battle_guild_list_result_zoclQEAAHXZ_1403D0860.h" />
    <ClInclude Include="headers\size_qry_case_addguildbattlescheduleQEAAHXZ_1403D93C0.h" />
    <ClInclude Include="headers\size_qry_case_dest_guild_out_guildbattlecostQEAAHX_14025D5D0.h" />
    <ClInclude Include="headers\size_qry_case_in_guildbattlecostQEAAHXZ_14025D5B0.h" />
    <ClInclude Include="headers\size_qry_case_in_guildbattlerewardmoneyQEAAHXZ_14025D5E0.h" />
    <ClInclude Include="headers\size_qry_case_loadguildbattlerankQEAAHXZ_140207580.h" />
    <ClInclude Include="headers\size_qry_case_load_guildbattle_totalrecordQEAAHXZ_140207590.h" />
    <ClInclude Include="headers\size_qry_case_src_guild_out_guildbattlecostQEAAHXZ_14025D5C0.h" />
    <ClInclude Include="headers\size_qry_case_updateclearguildbattleDayInfoQEAAHXZ_1403DECE0.h" />
    <ClInclude Include="headers\size_qry_case_updatedrawguildbattlerankQEAAHXZ_1403EB3D0.h" />
    <ClInclude Include="headers\size_qry_case_updatewinloseguildbattlerankQEAAHXZ_1403EB3E0.h" />
    <ClInclude Include="headers\SkipWhiteSpaceTiXmlBaseKAPEBDPEBDW4TiXmlEncodingZ_140530B60.h" />
    <ClInclude Include="headers\SrcGuildIsAvailableBattleRequestStateCGuildQEAAEXZ_1402578D0.h" />
    <ClInclude Include="headers\start_casheventCashItemRemoteStoreQEAA_NHHHHEZ_1402FB0D0.h" />
    <ClInclude Include="headers\start_cdeCashItemRemoteStoreQEAA_NHHHHZ_1402F7590.h" />
    <ClInclude Include="headers\start_coneventCashItemRemoteStoreQEAA_NHHEZ_1402FCAE0.h" />
    <ClInclude Include="headers\StockOldInfoCNormalGuildBattleGuildMemberGUILD_BAT_1403DFB40.h" />
    <ClInclude Include="headers\TakeGravityStoneCNormalGuildBattleGUILD_BATTLEQEAA_1403E4A60.h" />
    <ClInclude Include="headers\unchecked_copyPEAVCGuildBattleRewardItemGUILD_BATT_1403D2590.h" />
    <ClInclude Include="headers\unchecked_uninitialized_copyPEAVCGuildBattleReward_1403D31F0.h" />
    <ClInclude Include="headers\unchecked_uninitialized_fill_nPEAVCGuildBattleRewa_1403D2910.h" />
    <ClInclude Include="headers\UpdateCGuildBattleRankManagerGUILD_BATTLEQEAA_NEPE_1403CA680.h" />
    <ClInclude Include="headers\UpdateClearGuildBattleDayInfoCNormalGuildBattleMan_1403D42E0.h" />
    <ClInclude Include="headers\UpdateClearGuildBattleInfoCRFWorldDatabaseQEAA_NKK_1404A2AC0.h" />
    <ClInclude Include="headers\UpdateClearGuildBattleRankCRFWorldDatabaseQEAA_NXZ_1404A31B0.h" />
    <ClInclude Include="headers\UpdateClearGuildBattleScheduleDayInfoCGuildBattleS_1403DD8B0.h" />
    <ClInclude Include="headers\UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A1C40.h" />
    <ClInclude Include="headers\UpdateClearGuildBattleScheduleInfoCRFWorldDatabase_1404A2020.h" />
    <ClInclude Include="headers\UpdateClearRerservedDayInfoCGuildBattleControllerQ_1403D6CF0.h" />
    <ClInclude Include="headers\UpdateDayChangedWorkCGuildBattleScheduleManagerGUI_1403DD650.h" />
    <ClInclude Include="headers\UpdateDrawCGuildBattleControllerQEAA_NEKEKZ_1403D6C80.h" />
    <ClInclude Include="headers\UpdateDrawCGuildBattleRankManagerGUILD_BATTLEQEAA__1403CB120.h" />
    <ClInclude Include="headers\UpdateDrawGuildBattleResultCRFWorldDatabaseQEAA_NK_1404A33B0.h" />
    <ClInclude Include="headers\UpdateGoalCntCCurrentGuildBattleInfoManagerGUILD_B_1403CE340.h" />
    <ClInclude Include="headers\UpdateGuildBattleDrawRankInfoCMainThreadQEAAXPEAU__1401F4470.h" />
    <ClInclude Include="headers\UpdateGuildBattleInfoCRFWorldDatabaseQEAA_NKKKKEZ_1404A29E0.h" />
    <ClInclude Include="headers\UpdateGuildBattleScheduleInfoCRFWorldDatabaseQEAA__1404A1AA0.h" />
    <ClInclude Include="headers\UpdateGuildBattleWinCntCGuildQEAAXKKKZ_140253190.h" />
    <ClInclude Include="headers\UpdateGuildBattleWinLoseRankInfoCMainThreadQEAAXPE_1401F4290.h" />
    <ClInclude Include="headers\UpdateGuildListCPossibleBattleGuildListManagerGUIL_1403C9B90.h" />
    <ClInclude Include="headers\UpdateLoadGuildBattleRankCMainThreadQEAAXPEAU_DB_Q_1401F4650.h" />
    <ClInclude Include="headers\UpdateLoseGuildBattleResultCRFWorldDatabaseQEAA_NK_1404A3300.h" />
    <ClInclude Include="headers\UpdateMonsterSFContDamageToleracneQEAAXXZ_140158080.h" />
    <ClInclude Include="headers\UpdatePossibleBattleGuildListCGuildBattleControlle_1403D6370.h" />
    <ClInclude Include="headers\UpdateRankCGuildBattleControllerQEAA_NEPEAEZ_1403D6D80.h" />
    <ClInclude Include="headers\UpdateReservedGuildBattleScheduleCGuildBattleContr_1403D6DD0.h" />
    <ClInclude Include="headers\UpdateReservedGuildBattleScheduleCMainThreadQEAAXP_1401F4BE0.h" />
    <ClInclude Include="headers\UpdateReservedSheduleCGuildBattleReservedScheduleL_1403CD720.h" />
    <ClInclude Include="headers\UpdateScoreCCurrentGuildBattleInfoManagerGUILD_BAT_1403CE2B0.h" />
    <ClInclude Include="headers\UpdateScoreCNormalGuildBattleGuildGUILD_BATTLEIEAA_1403EAE20.h" />
    <ClInclude Include="headers\UpdateTodayScheduleCGuildBattleReservedScheduleLis_1403CD9C0.h" />
    <ClInclude Include="headers\UpdateTomorrowCompleteCGuildBattleReservedSchedule_1403CD7A0.h" />
    <ClInclude Include="headers\UpdateTomorrowScheduleCGuildBattleReservedSchedule_1403CDB60.h" />
    <ClInclude Include="headers\UpdateUseFieldCGuildBattleReservedScheduleGUILD_BA_1403DB8D0.h" />
    <ClInclude Include="headers\UpdateUseFlagCGuildBattleReservedScheduleGUILD_BAT_1403DB4F0.h" />
    <ClInclude Include="headers\UpdateUseFlagCGuildBattleReservedScheduleMapGroupG_1403DC560.h" />
    <ClInclude Include="headers\UpdateUseFlagCGuildBattleScheduleManagerGUILD_BATT_1403DD120.h" />
    <ClInclude Include="headers\UpdateWinGuildBattleResultCRFWorldDatabaseQEAA_NKK_1404A3250.h" />
    <ClInclude Include="headers\UpdateWinLoseCGuildBattleControllerQEAA_NEKEKZ_1403D6C10.h" />
    <ClInclude Include="headers\UpdateWinLoseCGuildBattleRankManagerGUILD_BATTLEQE_1403CAFF0.h" />
    <ClInclude Include="headers\Update_BattleResultLogBattleResultAndPvpPointCRFWo_1404B1100.h" />
    <ClInclude Include="headers\Update_CristalBattleCharInfoCRFWorldDatabaseQEAA_N_1404A9A90.h" />
    <ClInclude Include="headers\update_cristalbattle_dateCRFWorldDatabaseQEAA_NKEZ_1404C5160.h" />
    <ClInclude Include="headers\Update_IncreaseWeeklyGuildGuildBattlePvpPointSumCR_1404A70F0.h" />
    <ClInclude Include="headers\update_iniCashItemRemoteStoreQEAAXPEAU_cash_discou_1402F7160.h" />
    <ClInclude Include="headers\Update_INICashItemRemoteStoreQEAAXPEAU_cash_event__1402F8C40.h" />
    <ClInclude Include="headers\WriteLogPer10Min_CombatCHolyStoneSystemIEAAXXZ_1402804E0.h" />
    <ClInclude Include="headers\_AllocateVCGuildBattleRewardItemGUILD_BATTLEstdYAP_1403D26D0.h" />
    <ClInclude Include="headers\_Assign_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D0D70.h" />
    <ClInclude Include="headers\_buybygold_buy_single_item_calc_price_discountCash_1402FFCE0.h" />
    <ClInclude Include="headers\_buybygold_buy_single_item_calc_price_limitsaleCas_1402FFE60.h" />
    <ClInclude Include="headers\_buybygold_buy_single_item_calc_price_one_n_oneCas_1402FFDB0.h" />
    <ClInclude Include="headers\_buybygold_buy_single_item_setbuydblogCashItemRemo_140300760.h" />
    <ClInclude Include="headers\_BuyvectorVCGuildBattleRewardItemGUILD_BATTLEVallo_1403D12E0.h" />
    <ClInclude Include="headers\_CalcForceAttPntCAttackIEAAH_NZ_14016AF70.h" />
    <ClInclude Include="headers\_CalcGenAttPntCAttackIEAAH_NZ_14016AA00.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreBuyByCash__1_dtor0_1402FEC90.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreBuyByGold__1_dtor0_1402FEF70.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreCashItemRemoteStore__1_dtor0_1402F3920.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreCashItemRemoteStore__1_dtor1_1402F3960.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreCashItemRemoteStore__1_dtor2_1402F3990.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreCashItemRemoteStore__1_dtor3_1402F39C0.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreCashItemRemoteStore__1_dtor4_1402F39F0.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreCashItemRemoteStore__1_dtor5_1402F3A30.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreCashItemRemoteStore__1_dtor6_1402F3A60.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreCheatLoadCashAmount__1_dtor0_1402F5B20.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreFindCashRec__1_dtor0_1402F4A30.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreFindCashRec__1_dtor1_1402F4A60.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreGoodsListBuyByCash__1_dtor0_140300C30.h" />
    <ClInclude Include="headers\_CashItemRemoteStoreInstance__1_dtor0_1400798A0.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_CashItemRemoteStore__1_dtor0_1402F3B80.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_CashItemRemoteStore__1_dtor1_1402F3BC0.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_CashItemRemoteStore__1_dtor2_1402F3BF0.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_CashItemRemoteStore__1_dtor3_1402F3C20.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_CashItemRemoteStore__1_dtor4_1402F3C50.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_CashItemRemoteStore__1_dtor5_1402F3C90.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_CashItemRemoteStore__1_dtor6_1402F3CC0.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_MakeLinkTable__1_dtor0_1402F48C0.h" />
    <ClInclude Include="headers\_CashItemRemoteStore_ReadGoods__1_dtor0_1402F4C60.h" />
    <ClInclude Include="headers\_CashItemRemoteStore__CheckGoods__1_dtor0_1402F4620.h" />
    <ClInclude Include="headers\_CGuildBattleControllerInstance__1_dtor0_1403D5770.h" />
    <ClInclude Include="headers\_check_buyitemCashItemRemoteStoreAEAAAW4CS_RCODEEP_1402F4FA0.h" />
    <ClInclude Include="headers\_complete_tsk_cashitem_buy_dblogCashDbWorkerIEAAXP_1402F0210.h" />
    <ClInclude Include="headers\_ConstructVCGuildBattleRewardItemGUILD_BATTLEV12st_1403D3120.h" />
    <ClInclude Include="headers\_Copy_backward_optPEAVCGuildBattleRewardItemGUILD__1403D3060.h" />
    <ClInclude Include="headers\_Copy_optPEAVCGuildBattleRewardItemGUILD_BATTLEPEA_1403D2A80.h" />
    <ClInclude Include="headers\_db_Load_BattleTournamentInfoCMainThreadAEAAXXZ_1401B7210.h" />
    <ClInclude Include="headers\_db_load_losebattlecountCMainThreadAEAAEKPEAU_AVAT_1401A99F0.h" />
    <ClInclude Include="headers\_delay_check_ATTACK_DELAY_CHECKERQEAA_NEGEZ_14008ED10.h" />
    <ClInclude Include="headers\_DestroyVCGuildBattleRewardItemGUILD_BATTLEstdYAXP_1403D31E0.h" />
    <ClInclude Include="headers\_DestroyvectorVCGuildBattleRewardItemGUILD_BATTLEV_1403D17A0.h" />
    <ClInclude Include="headers\_Destroy_rangeVCGuildBattleRewardItemGUILD_BATTLEV_1403D2650.h" />
    <ClInclude Include="headers\_Destroy_rangeVCGuildBattleRewardItemGUILD_BATTLEV_1403D2B40.h" />
    <ClInclude Include="headers\_dynamic_initializer_for__CAttacks_DefParam___1406DC660.h" />
    <ClInclude Include="headers\_dynamic_initializer_for__GUILD_BATTLECGuildBattle_1406E4080.h" />
    <ClInclude Include="headers\_ECNormalGuildBattleFieldGUILD_BATTLEQEAAPEAXIZ_1403F0220.h" />
    <ClInclude Include="headers\_ECNormalGuildBattleStateListGUILD_BATTLEQEAAPEAXI_1403F33B0.h" />
    <ClInclude Include="headers\_ECReservedGuildScheduleMapGroupGUILD_BATTLEQEAAPE_1403D09B0.h" />
    <ClInclude Include="headers\_FillPEAVCGuildBattleRewardItemGUILD_BATTLEV12stdY_1403D2C70.h" />
    <ClInclude Include="headers\_GCCurrentGuildBattleInfoManagerGUILD_BATTLEIEAAPE_1403D0B40.h" />
    <ClInclude Include="headers\_GCGuildBattleControllerIEAAPEAXIZ_1403D9720.h" />
    <ClInclude Include="headers\_GCGuildBattleLoggerGUILD_BATTLEIEAAPEAXIZ_1403D0BB0.h" />
    <ClInclude Include="headers\_GCGuildBattleRankManagerGUILD_BATTLEIEAAPEAXIZ_1403D08C0.h" />
    <ClInclude Include="headers\_GCGuildBattleReservedScheduleGUILD_BATTLEQEAAPEAX_1403DEBD0.h" />
    <ClInclude Include="headers\_GCGuildBattleReservedScheduleListManagerGUILD_BAT_1403D0AA0.h" />
    <ClInclude Include="headers\_GCGuildBattleScheduleGUILD_BATTLEQEAAPEAXIZ_1403DE8B0.h" />
    <ClInclude Include="headers\_GCGuildBattleScheduleManagerGUILD_BATTLEIEAAPEAXI_1403DECF0.h" />
    <ClInclude Include="headers\_GCGuildBattleSchedulePoolGUILD_BATTLEIEAAPEAXIZ_1403DE920.h" />
    <ClInclude Include="headers\_GCGuildBattleSchedulerGUILD_BATTLEIEAAPEAXIZ_1403DEDB0.h" />
    <ClInclude Include="headers\_GCNormalGuildBattleFieldListGUILD_BATTLEIEAAPEAXI_1403F02F0.h" />
    <ClInclude Include="headers\_GCNormalGuildBattleGUILD_BATTLEQEAAPEAXIZ_1403D8F40.h" />
    <ClInclude Include="headers\_GCNormalGuildBattleManagerGUILD_BATTLEIEAAPEAXIZ_1403D8FB0.h" />
    <ClInclude Include="headers\_GCNormalGuildBattleStateListPoolGUILD_BATTLEIEAAP_1403F3480.h" />
    <ClInclude Include="headers\_GCPossibleBattleGuildListManagerGUILD_BATTLEIEAAP_1403D0770.h" />
    <ClInclude Include="headers\_GCRFCashItemDatabaseUEAAPEAXIZ_1402F2B40.h" />
    <ClInclude Include="headers\_GUILD_BATTLECCurrentGuildBattleInfoManagerInstanc_1403CDF50.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleLoggerInit__1_dtor0_1403CE9B0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleLoggerInstance__1_dtor0_1403CE820.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleRankManagerInstance__1_dt_1403CA400.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleReservedScheduleListManag_1403CD3F0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleReservedScheduleMapGroupI_1403DBD90.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleRewardItemManagerInstance_1403D9820.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleScheduleManagerInit__1_dt_1403DCD00.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleScheduleManagerInstance___1403DCAA0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleScheduleManager_CGuildBat_1403DC9D0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleSchedulePoolInit__1_dtor0_1403DA860.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleSchedulePoolInstance__1_d_1403DA610.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleSchedulerInstance__1_dtor_1403DD790.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleStateListLog__1_dtor0_1403DF440.h" />
    <ClInclude Include="headers\_GUILD_BATTLECGuildBattleStateLog__1_dtor0_1403DEF60.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2EF0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F20.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F50.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleCNormalGuildBattle__1403E2F80.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor0_1403EC510.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor1_1403EC540.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor2_1403EC570.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor3_1403EC5A0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor4_1403EC5D0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldInit__1_dtor5_1403EC600.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldListInit__1_dt_1403EE840.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldListInstance___1403EE3F0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleFieldLoadDummys__1__1403EDD30.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleGuildCNormalGuildBa_1403E0550.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleLoggerInit__1_dtor0_1403CED20.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleManagerInit__1_dtor_1403D38C0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleManagerInstance__1__1403D35C0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateInBattleCNorma_1403F09D0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateInBattle_CNorm_14007FE90.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2000.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2030.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2060.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2090.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F20C0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F20F0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListCNormalGui_1403F2120.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListPoolInit___1403F2610.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateListPoolInstan_1403F2400.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F920.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F950.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F980.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F9B0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007F9E0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007FA10.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateList_CNormalGu_14007FA40.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2220.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2250.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundListCNorm_1403F2280.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FF70.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FFA0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundList_CNor_14007FFD0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundProcessCN_1403F1830.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundProcessCN_1403F1860.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundProcess_C_1403F1940.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1BD0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1C00.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundReturnSta_1403F1CE0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundStartCNor_1403F1410.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundStartCNor_1403F1440.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattleStateRoundStart_CNo_1403F1520.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E3040.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E3070.h" />
    <ClInclude Include="headers\_GUILD_BATTLECNormalGuildBattle_CNormalGuildBattle_1403E30A0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECPossibleBattleGuildListManagerInit___1403C99C0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECPossibleBattleGuildListManagerInstan_1403C96E0.h" />
    <ClInclude Include="headers\_GUILD_BATTLECReservedGuildScheduleDayGroupInit__1_1403CCE50.h" />
    <ClInclude Include="headers\_InitLoggersCashItemRemoteStoreAEAA_NXZ_1402F3CF0.h" />
    <ClInclude Include="headers\_Insert_nvectorVCGuildBattleRewardItemGUILD_BATTLE_1403D1A90.h" />
    <ClInclude Include="headers\_Iter_randomPEAVCGuildBattleRewardItemGUILD_BATTLE_1403D29C0.h" />
    <ClInclude Include="headers\_MakeLinkTableCashItemRemoteStoreAEAA_NPEADHZ_1402F4650.h" />
    <ClInclude Include="headers\_Max_elementPEAVCNormalGuildBattleGuildMemberGUILD_1403EB510.h" />
    <ClInclude Include="headers\_Max_elementPEAVCNormalGuildBattleGuildMemberGUILD_1403EB660.h" />
    <ClInclude Include="headers\_Move_backward_optPEAVCGuildBattleRewardItemGUILD__1403D2D70.h" />
    <ClInclude Include="headers\_Move_catPEAVCGuildBattleRewardItemGUILD_BATTLEstd_1403D2D10.h" />
    <ClInclude Include="headers\_Ptr_catPEAVCGuildBattleRewardItemGUILD_BATTLEPEAV_1403D2A20.h" />
    <ClInclude Include="headers\_PushItemCutLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTDZ_14024AEB0.h" />
    <ClInclude Include="headers\_PushItemMoveLtdWriterAEAAXEPEAU_LTD_PARAMPEAU_LTD_14024B300.h" />
    <ClInclude Include="headers\_ReadGoodsCashItemRemoteStoreAEAA_NXZ_1402F4A90.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D0EE0.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D11F0.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1220.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1250.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1750.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D18A0.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D1FE0.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D2010.h" />
    <ClInclude Include="headers\_stdvector_GUILD_BATTLECGuildBattleRewardItem_stda_1403D2070.h" />
    <ClInclude Include="headers\_std_Uninit_copy_GUILD_BATTLECGuildBattleRewardIte_1403D3330.h" />
    <ClInclude Include="headers\_std_Uninit_fill_n_GUILD_BATTLECGuildBattleRewardI_1403D2EB0.h" />
    <ClInclude Include="headers\_TidyvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D13E0.h" />
    <ClInclude Include="headers\_UfillvectorVCGuildBattleRewardItemGUILD_BATTLEVal_1403D2500.h" />
    <ClInclude Include="headers\_UmovePEAVCGuildBattleRewardItemGUILD_BATTLEvector_1403D2780.h" />
    <ClInclude Include="headers\_Unchecked_move_backwardPEAVCGuildBattleRewardItem_1403D2850.h" />
    <ClInclude Include="headers\_Unchecked_uninitialized_movePEAVCGuildBattleRewar_1403D2BC0.h" />
    <ClInclude Include="headers\_Uninit_copyPEAVCGuildBattleRewardItemGUILD_BATTLE_1403D32A0.h" />
    <ClInclude Include="headers\_Uninit_fill_nPEAVCGuildBattleRewardItemGUILD_BATT_1403D2E20.h" />
    <ClInclude Include="headers\_Uninit_movePEAVCGuildBattleRewardItemGUILD_BATTLE_1403D2FF0.h" />
    <ClInclude Include="headers\_XlenvectorVCGuildBattleRewardItemGUILD_BATTLEVall_1403D1810.h" />
    <ClInclude Include="headers\__CheckGoodsCashItemRemoteStoreAEAA_NAEAVCRecordDa_1402F4150.h" />
  </ItemGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>