#pragma once
#ifndef _DECCONST_ITERATOR_TREEV_TMAP_TRAITSVBASIC_STRINGD_140191F90_H
#define _DECCONST_ITERATOR_TREEV_TMAP_TRAITSVBASIC_STRINGD_140191F90_H

// Auto-generated header for _Decconst_iterator_TreeV_Tmap_traitsVbasic_stringD_140191F90.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
const_iterator::_Dec(std::_Tree<std::_Tmap_traits<std::basic_string<char,std::char_traits<char>,std::allocator<char> >,AreaList,std::less<std::basic_string<char,std::char_traits<char>,std::allocator<char> > >,std::allocator<std::pair<std::basic_string<char,std::char_traits<char>,std::allocator<char> > const ,AreaList> >,0> >::const_iterator *this)
;

#endif // _DECCONST_ITERATOR_TREEV_TMAP_TRAITSVBASIC_STRINGD_140191F90_H
