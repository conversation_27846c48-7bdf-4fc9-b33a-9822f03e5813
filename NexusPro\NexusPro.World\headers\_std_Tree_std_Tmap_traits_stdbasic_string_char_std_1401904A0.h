#pragma once
#ifndef _STD_TREE_STD_TMAP_TRAITS_STDBASIC_STRING_CHAR_STD_1401904A0_H
#define _STD_TREE_STD_TMAP_TRAITS_STDBASIC_STRING_CHAR_STD_1401904A0_H

// Auto-generated header for _std_Tree_std_Tmap_traits_stdbasic_string_char_std_1401904A0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_3_0(__int64 a1, __int64 a2)
;

#endif // _STD_TREE_STD_TMAP_TRAITS_STDBASIC_STRING_CHAR_STD_1401904A0_H
