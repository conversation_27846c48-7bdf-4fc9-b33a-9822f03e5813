/*
 * Function: ?pc_UpgradeItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@00E0@Z
 * Address: 0x1400B13E0
 */

void __fastcall CPlayer::pc_UpgradeItem(CPlayer *this, _STORAGE_POS_INDIV *pposTalik, _STORAGE_POS_INDIV *pposToolItem, _STORAGE_POS_INDIV *pposUpgItem, char by<PERSON><PERSON><PERSON><PERSON><PERSON>, _STORAGE_POS_INDIV *pposUpgJewel)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char v8; // al@16
  __int64 v9; // rax@64
  float v10; // xmm0_4@68
  int v11; // ecx@108
  __int64 v12; // [sp+0h] [bp-718h]@1
  int *pnTalikLim; // [sp+20h] [bp-6F8h]@56
  bool bSend[8]; // [sp+28h] [bp-6F0h]@62
  unsigned int dwAfterLv; // [sp+38h] [bp-6E0h]@108
  char *pszFileName; // [sp+40h] [bp-6D8h]@108
  char v17; // [sp+50h] [bp-6C8h]@4
  int j; // [sp+54h] [bp-6C4h]@34
  _STORAGE_LIST *v19; // [sp+58h] [bp-6C0h]@4
  void *v20; // [sp+60h] [bp-6B8h]@4
  _STORAGE_LIST::_db_con *v21; // [sp+68h] [bp-6B0h]@4
  void *Src; // [sp+70h] [bp-6A8h]@4
  void *v23; // [sp+88h] [bp-690h]@36
  _ItemUpgrade_fld *v24; // [sp+B8h] [bp-660h]@4
  __int64 Dst[5]; // [sp+C8h] [bp-650h]@4
  unsigned __int8 v26; // [sp+F4h] [bp-624h]@4
  int k; // [sp+F8h] [bp-620h]@43
  _STORAGE_LIST::_db_con pItem; // [sp+108h] [bp-610h]@59
  _STORAGE_LIST::_db_con pTalik; // [sp+158h] [bp-5C0h]@59
  char __t[212]; // [sp+1B0h] [bp-568h]@59
  float v31; // [sp+284h] [bp-494h]@65
  char v32; // [sp+288h] [bp-490h]@71
  CGameStatistics::_DAY *v33; // [sp+290h] [bp-488h]@75
  int v34; // [sp+2B0h] [bp-468h]@76
  int v35; // [sp+2B4h] [bp-464h]@76
  int v36; // [sp+2B8h] [bp-460h]@76
  int v37; // [sp+2BCh] [bp-45Ch]@76
  int v38; // [sp+2C0h] [bp-458h]@76
  int v39; // [sp+2C4h] [bp-454h]@76
  int v40; // [sp+2C8h] [bp-450h]@76
  int v41; // [sp+2CCh] [bp-44Ch]@76
  int v42; // [sp+2D0h] [bp-448h]@76
  int v43; // [sp+2D4h] [bp-444h]@76
  int v44; // [sp+2D8h] [bp-440h]@76
  int v45; // [sp+2DCh] [bp-43Ch]@76
  int v46; // [sp+2E0h] [bp-438h]@76
  int v47; // [sp+2E4h] [bp-434h]@76
  int v48; // [sp+2E8h] [bp-430h]@76
  int v49; // [sp+2ECh] [bp-42Ch]@76
  int v50; // [sp+2F0h] [bp-428h]@76
  int v51; // [sp+2F4h] [bp-424h]@76
  int v52; // [sp+2F8h] [bp-420h]@76
  int v53; // [sp+2FCh] [bp-41Ch]@76
  int v54; // [sp+300h] [bp-418h]@76
  int v55; // [sp+304h] [bp-414h]@76
  int v56; // [sp+308h] [bp-410h]@76
  int v57; // [sp+30Ch] [bp-40Ch]@76
  int v58; // [sp+310h] [bp-408h]@76
  int v59; // [sp+314h] [bp-404h]@76
  int v60; // [sp+318h] [bp-400h]@76
  int v61; // [sp+31Ch] [bp-3FCh]@76
  int v62; // [sp+320h] [bp-3F8h]@76
  int v63; // [sp+324h] [bp-3F4h]@76
  int v64; // [sp+328h] [bp-3F0h]@76
  int v65; // [sp+32Ch] [bp-3ECh]@76
  int v66; // [sp+330h] [bp-3E8h]@76
  int v67; // [sp+334h] [bp-3E4h]@76
  int v68; // [sp+338h] [bp-3E0h]@76
  int v69; // [sp+33Ch] [bp-3DCh]@76
  int v70; // [sp+340h] [bp-3D8h]@76
  int v71; // [sp+344h] [bp-3D4h]@76
  int v72; // [sp+348h] [bp-3D0h]@76
  int v73; // [sp+34Ch] [bp-3CCh]@76
  int v74; // [sp+350h] [bp-3C8h]@76
  int v75; // [sp+354h] [bp-3C4h]@76
  int v76; // [sp+358h] [bp-3C0h]@76
  int v77; // [sp+35Ch] [bp-3BCh]@76
  int v78; // [sp+360h] [bp-3B8h]@76
  int v79; // [sp+364h] [bp-3B4h]@76
  int v80; // [sp+368h] [bp-3B0h]@76
  int v81; // [sp+36Ch] [bp-3ACh]@76
  int v82; // [sp+370h] [bp-3A8h]@76
  int v83; // [sp+374h] [bp-3A4h]@76
  int v84; // [sp+378h] [bp-3A0h]@76
  int v85; // [sp+37Ch] [bp-39Ch]@76
  int v86; // [sp+380h] [bp-398h]@76
  int v87; // [sp+384h] [bp-394h]@76
  int v88; // [sp+388h] [bp-390h]@76
  int v89; // [sp+38Ch] [bp-38Ch]@76
  int v90; // [sp+390h] [bp-388h]@76
  int v91; // [sp+394h] [bp-384h]@76
  int v92; // [sp+398h] [bp-380h]@76
  int v93; // [sp+39Ch] [bp-37Ch]@76
  int v94; // [sp+3A0h] [bp-378h]@76
  int v95; // [sp+3A4h] [bp-374h]@76
  int v96; // [sp+3A8h] [bp-370h]@76
  int v97; // [sp+3ACh] [bp-36Ch]@76
  int v98; // [sp+3B0h] [bp-368h]@76
  int v99; // [sp+3B4h] [bp-364h]@76
  int v100; // [sp+3B8h] [bp-360h]@76
  int v101; // [sp+3BCh] [bp-35Ch]@76
  int v102; // [sp+3C0h] [bp-358h]@76
  int v103; // [sp+3C4h] [bp-354h]@76
  int v104; // [sp+3C8h] [bp-350h]@76
  int v105; // [sp+3CCh] [bp-34Ch]@76
  int v106; // [sp+3D0h] [bp-348h]@76
  int v107; // [sp+3D4h] [bp-344h]@76
  int v108; // [sp+3D8h] [bp-340h]@76
  int v109; // [sp+3DCh] [bp-33Ch]@76
  int v110; // [sp+3E0h] [bp-338h]@76
  char v111; // [sp+3F4h] [bp-324h]@76
  unsigned __int8 v112; // [sp+3F5h] [bp-323h]@76
  unsigned int v113; // [sp+3F8h] [bp-320h]@76
  unsigned __int8 v114; // [sp+3FCh] [bp-31Ch]@76
  int v115; // [sp+400h] [bp-318h]@78
  unsigned int v116; // [sp+404h] [bp-314h]@78
  int v117; // [sp+420h] [bp-2F8h]@80
  int v118; // [sp+424h] [bp-2F4h]@80
  int v119; // [sp+428h] [bp-2F0h]@80
  int v120; // [sp+42Ch] [bp-2ECh]@80
  int v121; // [sp+430h] [bp-2E8h]@80
  int v122; // [sp+434h] [bp-2E4h]@80
  int v123; // [sp+438h] [bp-2E0h]@80
  int v124; // [sp+43Ch] [bp-2DCh]@80
  int v125; // [sp+440h] [bp-2D8h]@80
  int v126; // [sp+444h] [bp-2D4h]@80
  int v127; // [sp+448h] [bp-2D0h]@80
  int v128; // [sp+44Ch] [bp-2CCh]@80
  int v129; // [sp+450h] [bp-2C8h]@80
  int v130; // [sp+454h] [bp-2C4h]@80
  int v131; // [sp+458h] [bp-2C0h]@80
  int v132; // [sp+45Ch] [bp-2BCh]@80
  int v133; // [sp+460h] [bp-2B8h]@80
  int v134; // [sp+464h] [bp-2B4h]@80
  int v135; // [sp+468h] [bp-2B0h]@80
  int v136; // [sp+46Ch] [bp-2ACh]@80
  int v137; // [sp+470h] [bp-2A8h]@80
  int v138; // [sp+474h] [bp-2A4h]@80
  int v139; // [sp+478h] [bp-2A0h]@80
  int v140; // [sp+47Ch] [bp-29Ch]@80
  int v141; // [sp+480h] [bp-298h]@80
  int v142; // [sp+484h] [bp-294h]@80
  int v143; // [sp+488h] [bp-290h]@80
  int v144; // [sp+48Ch] [bp-28Ch]@80
  int v145; // [sp+490h] [bp-288h]@80
  int v146; // [sp+494h] [bp-284h]@80
  int v147; // [sp+498h] [bp-280h]@80
  int v148; // [sp+49Ch] [bp-27Ch]@80
  int v149; // [sp+4A0h] [bp-278h]@80
  int v150; // [sp+4A4h] [bp-274h]@80
  int v151; // [sp+4A8h] [bp-270h]@80
  int v152; // [sp+4ACh] [bp-26Ch]@80
  int v153; // [sp+4B0h] [bp-268h]@80
  int v154; // [sp+4B4h] [bp-264h]@80
  int v155; // [sp+4B8h] [bp-260h]@80
  int v156; // [sp+4BCh] [bp-25Ch]@80
  int v157; // [sp+4C0h] [bp-258h]@80
  int v158; // [sp+4C4h] [bp-254h]@80
  int v159; // [sp+4C8h] [bp-250h]@80
  int v160; // [sp+4CCh] [bp-24Ch]@80
  int v161; // [sp+4D0h] [bp-248h]@80
  int v162; // [sp+4D4h] [bp-244h]@80
  int v163; // [sp+4D8h] [bp-240h]@80
  int v164; // [sp+4DCh] [bp-23Ch]@80
  int v165; // [sp+4E0h] [bp-238h]@80
  int v166; // [sp+4E4h] [bp-234h]@80
  int v167; // [sp+4E8h] [bp-230h]@80
  int v168; // [sp+4ECh] [bp-22Ch]@80
  int v169; // [sp+4F0h] [bp-228h]@80
  int v170; // [sp+4F4h] [bp-224h]@80
  int v171; // [sp+4F8h] [bp-220h]@80
  int v172; // [sp+4FCh] [bp-21Ch]@80
  int v173; // [sp+500h] [bp-218h]@80
  int v174; // [sp+504h] [bp-214h]@80
  int v175; // [sp+508h] [bp-210h]@80
  int v176; // [sp+50Ch] [bp-20Ch]@80
  int v177; // [sp+510h] [bp-208h]@80
  int v178; // [sp+514h] [bp-204h]@80
  int v179; // [sp+518h] [bp-200h]@80
  int v180; // [sp+51Ch] [bp-1FCh]@80
  int v181; // [sp+520h] [bp-1F8h]@80
  int v182; // [sp+524h] [bp-1F4h]@80
  int v183; // [sp+528h] [bp-1F0h]@80
  int v184; // [sp+52Ch] [bp-1ECh]@80
  int v185; // [sp+530h] [bp-1E8h]@80
  int v186; // [sp+534h] [bp-1E4h]@80
  int v187; // [sp+538h] [bp-1E0h]@80
  int v188; // [sp+53Ch] [bp-1DCh]@80
  int v189; // [sp+540h] [bp-1D8h]@80
  int v190; // [sp+544h] [bp-1D4h]@80
  int v191; // [sp+548h] [bp-1D0h]@80
  int v192; // [sp+54Ch] [bp-1CCh]@80
  int v193; // [sp+550h] [bp-1C8h]@80
  int v194; // [sp+580h] [bp-198h]@80
  int v195; // [sp+584h] [bp-194h]@80
  int v196; // [sp+588h] [bp-190h]@80
  int v197; // [sp+58Ch] [bp-18Ch]@80
  int v198; // [sp+590h] [bp-188h]@80
  int v199; // [sp+594h] [bp-184h]@80
  int v200; // [sp+598h] [bp-180h]@80
  int v201; // [sp+59Ch] [bp-17Ch]@80
  int v202; // [sp+5A0h] [bp-178h]@80
  int v203; // [sp+5A4h] [bp-174h]@80
  int v204; // [sp+5A8h] [bp-170h]@80
  int v205; // [sp+5ACh] [bp-16Ch]@80
  int v206; // [sp+5B0h] [bp-168h]@80
  int v207; // [sp+5B4h] [bp-164h]@80
  int v208; // [sp+5B8h] [bp-160h]@80
  int v209; // [sp+5BCh] [bp-15Ch]@80
  int v210; // [sp+5C0h] [bp-158h]@80
  int v211; // [sp+5C4h] [bp-154h]@80
  int v212; // [sp+5C8h] [bp-150h]@80
  int v213; // [sp+5CCh] [bp-14Ch]@80
  int v214; // [sp+5D0h] [bp-148h]@80
  int v215; // [sp+5D4h] [bp-144h]@80
  int v216; // [sp+5D8h] [bp-140h]@80
  int v217; // [sp+5DCh] [bp-13Ch]@80
  int v218; // [sp+5E0h] [bp-138h]@80
  int v219; // [sp+5E4h] [bp-134h]@80
  int v220; // [sp+5E8h] [bp-130h]@80
  int v221; // [sp+5ECh] [bp-12Ch]@80
  int v222; // [sp+5F0h] [bp-128h]@80
  int v223; // [sp+5F4h] [bp-124h]@80
  int v224; // [sp+5F8h] [bp-120h]@80
  int v225; // [sp+5FCh] [bp-11Ch]@80
  int v226; // [sp+600h] [bp-118h]@80
  int v227; // [sp+604h] [bp-114h]@80
  int v228; // [sp+608h] [bp-110h]@80
  int v229; // [sp+60Ch] [bp-10Ch]@80
  int v230; // [sp+610h] [bp-108h]@80
  int v231; // [sp+614h] [bp-104h]@80
  int v232; // [sp+618h] [bp-100h]@80
  int v233; // [sp+61Ch] [bp-FCh]@80
  int v234; // [sp+620h] [bp-F8h]@80
  int v235; // [sp+624h] [bp-F4h]@80
  int v236; // [sp+628h] [bp-F0h]@80
  int v237; // [sp+62Ch] [bp-ECh]@80
  int v238; // [sp+630h] [bp-E8h]@80
  int v239; // [sp+634h] [bp-E4h]@80
  int v240; // [sp+638h] [bp-E0h]@80
  int v241; // [sp+63Ch] [bp-DCh]@80
  int v242; // [sp+640h] [bp-D8h]@80
  int v243; // [sp+644h] [bp-D4h]@80
  int v244; // [sp+648h] [bp-D0h]@80
  int v245; // [sp+64Ch] [bp-CCh]@80
  int v246; // [sp+650h] [bp-C8h]@80
  int v247; // [sp+654h] [bp-C4h]@80
  int v248; // [sp+658h] [bp-C0h]@80
  int v249; // [sp+65Ch] [bp-BCh]@80
  int v250; // [sp+660h] [bp-B8h]@80
  int v251; // [sp+664h] [bp-B4h]@80
  int v252; // [sp+668h] [bp-B0h]@80
  int v253; // [sp+66Ch] [bp-ACh]@80
  int v254; // [sp+670h] [bp-A8h]@80
  int v255; // [sp+674h] [bp-A4h]@80
  int v256; // [sp+678h] [bp-A0h]@80
  int v257; // [sp+67Ch] [bp-9Ch]@80
  int v258; // [sp+680h] [bp-98h]@80
  int v259; // [sp+684h] [bp-94h]@80
  int v260; // [sp+688h] [bp-90h]@80
  int v261; // [sp+68Ch] [bp-8Ch]@80
  int v262; // [sp+690h] [bp-88h]@80
  int v263; // [sp+694h] [bp-84h]@80
  int v264; // [sp+698h] [bp-80h]@80
  int v265; // [sp+69Ch] [bp-7Ch]@80
  int v266; // [sp+6A0h] [bp-78h]@80
  int v267; // [sp+6A4h] [bp-74h]@80
  int v268; // [sp+6A8h] [bp-70h]@80
  int v269; // [sp+6ACh] [bp-6Ch]@80
  int v270; // [sp+6B0h] [bp-68h]@80
  unsigned int dwGradeInfo; // [sp+6C4h] [bp-54h]@86
  unsigned int v272; // [sp+6C8h] [bp-50h]@89
  _base_fld *v273; // [sp+6D0h] [bp-48h]@89
  char v274; // [sp+6D8h] [bp-40h]@89
  CGameStatistics::_DAY *v275; // [sp+6E0h] [bp-38h]@93
  int v276; // [sp+6E8h] [bp-30h]@78
  int *v277; // [sp+6F0h] [bp-28h]@81
  __int64 v278; // [sp+6F8h] [bp-20h]@81
  int *v279; // [sp+700h] [bp-18h]@85
  __int64 v280; // [sp+708h] [bp-10h]@85
  CPlayer *v281; // [sp+720h] [bp+8h]@1
  _STORAGE_POS_INDIV *v282; // [sp+728h] [bp+10h]@1
  _STORAGE_POS_INDIV *v283; // [sp+730h] [bp+18h]@1
  _STORAGE_POS_INDIV *v284; // [sp+738h] [bp+20h]@1

  v284 = pposUpgItem;
  v283 = pposToolItem;
  v282 = pposTalik;
  v281 = this;
  v6 = &v12;
  for ( i = 452i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v17 = 0;
  v19 = v281->m_Param.m_pStoragePtr[pposUpgItem->byStorageCode];
  v20 = 0i64;
  v21 = 0i64;
  Src = 0i64;
  v24 = 0i64;
  memset_0(Dst, 0, 0x20ui64);
  v26 = 0;
  if ( _effect_parameter::GetEff_State(&v281->m_EP, 20) )
  {
    v17 = 8;
  }
  else if ( _effect_parameter::GetEff_State(&v281->m_EP, 28) )
  {
    v17 = 8;
  }
  else
  {
    Src = _STORAGE_LIST::GetPtrFromSerial(v19, v284->wItemSerial);
    if ( Src )
    {
      if ( *((_BYTE *)Src + 19) )
      {
        v17 = 13;
      }
      else if ( GetItemKindCode(*((_BYTE *)Src + 1)) )
      {
        v17 = 9;
      }
      else if ( GetDefItemUpgSocketNum(*((_BYTE *)Src + 1), *(_WORD *)((char *)Src + 3)) )
      {
        v26 = GetItemUpgLimSocket(*(_DWORD *)((char *)Src + 13));
        v8 = GetItemUpgedLv(*(_DWORD *)((char *)Src + 13));
        if ( (unsigned __int8)v8 < (signed int)v26 )
        {
          v20 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v281->m_Param.m_dbInven.m_nListNum, v282->wItemSerial);
          if ( v20 )
          {
            if ( *((_BYTE *)v20 + 1) == 18 )
            {
              if ( *((_BYTE *)v20 + 19) )
              {
                v17 = 13;
              }
              else
              {
                v24 = CItemUpgradeTable::GetRecordFromRes(&stru_1799C69D8, *(_WORD *)((char *)v20 + 3));
                if ( v24 )
                {
                  if ( v24->m_dwIndex < 0xD )
                  {
                    v21 = _STORAGE_LIST::GetPtrFromSerial(
                            (_STORAGE_LIST *)&v281->m_Param.m_dbInven.m_nListNum,
                            v283->wItemSerial);
                    if ( v21 )
                    {
                      if ( v21->m_byTableCode == 11 )
                      {
                        if ( v21->m_bLock )
                        {
                          v17 = 13;
                        }
                        else
                        {
                          for ( j = 0; j < (unsigned __int8)byJewelNum; ++j )
                          {
                            *(&v23 + j) = _STORAGE_LIST::GetPtrFromSerial(
                                            (_STORAGE_LIST *)&v281->m_Param.m_dbInven.m_nListNum,
                                            pposUpgJewel[j].wItemSerial);
                            if ( !*(&v23 + j) )
                            {
                              CPlayer::SendMsg_AdjustAmountInform(v281, 0, pposUpgJewel[j].wItemSerial, 0);
                              v17 = 6;
                              goto $RESULT_40;
                            }
                            if ( *((_BYTE *)*(&v23 + j) + 1) != 18 )
                            {
                              v17 = 7;
                              goto $RESULT_40;
                            }
                            if ( *((_BYTE *)*(&v23 + j) + 19) )
                            {
                              v17 = 13;
                              goto $RESULT_40;
                            }
                            Dst[j] = (__int64)CItemUpgradeTable::GetRecordFromRes(
                                                &stru_1799C69D8,
                                                *(_WORD *)((char *)*(&v23 + j) + 3));
                            if ( !Dst[j] )
                              return;
                            for ( k = 0; k < j; ++k )
                            {
                              if ( *(&v23 + k) == *(&v23 + j) )
                              {
                                v17 = 7;
                                goto $RESULT_40;
                              }
                            }
                            for ( k = 0; k < j; ++k )
                            {
                              if ( Dst[k] == Dst[j] )
                              {
                                v17 = 7;
                                goto $RESULT_40;
                              }
                            }
                            if ( v24 == (_ItemUpgrade_fld *)Dst[j] )
                            {
                              v17 = 7;
                              goto $RESULT_40;
                            }
                          }
                          pnTalikLim = &v24->m_nUpperUp;
                          if ( !IsAddAbleTalikToItem(
                                  *((_BYTE *)Src + 1),
                                  *(_WORD *)((char *)Src + 3),
                                  *(_DWORD *)((char *)Src + 13),
                                  v24->m_dwIndex,
                                  &v24->m_nUpperUp) )
                            v17 = 11;
                        }
                      }
                      else
                      {
                        v17 = 4;
                      }
                    }
                    else
                    {
                      CPlayer::SendMsg_AdjustAmountInform(v281, 0, v283->wItemSerial, 0);
                      v17 = 3;
                    }
                  }
                  else
                  {
                    v17 = 2;
                  }
                }
                else
                {
                  v17 = 2;
                }
              }
            }
            else
            {
              v17 = 2;
            }
          }
          else
          {
            CPlayer::SendMsg_AdjustAmountInform(v281, 0, v282->wItemSerial, 0);
            v17 = 1;
          }
        }
        else
        {
          v17 = 10;
        }
      }
      else
      {
        v17 = 9;
      }
    }
    else
    {
      v17 = 5;
    }
  }
$RESULT_40:
  if ( !v17 )
  {
    _STORAGE_LIST::_db_con::_db_con(&pItem);
    _STORAGE_LIST::_db_con::_db_con(&pTalik);
    `vector constructor iterator'(__t, 0x32ui64, 4, (void *(__cdecl *)(void *))_STORAGE_LIST::_db_con::_db_con);
    memcpy_0(&pItem, Src, 0x32ui64);
    memcpy_0(&pTalik, v20, 0x32ui64);
    for ( j = 0; j < (unsigned __int8)byJewelNum; ++j )
      memcpy_0(&__t[50 * j], *(&v23 + j), 0x32ui64);
    bSend[0] = 0;
    LOBYTE(pnTalikLim) = 0;
    CPlayer::Emb_AlterDurPoint(v281, 0, *((_BYTE *)v20 + 49), -1, 0, 0);
    for ( j = 0; j < (unsigned __int8)byJewelNum; ++j )
    {
      v9 = (__int64)*(&v23 + j);
      bSend[0] = 0;
      LOBYTE(pnTalikLim) = 0;
      CPlayer::Emb_AlterDurPoint(v281, 0, *(_BYTE *)(v9 + 49), -1, 0, 0);
    }
    v31 = 0.0;
    for ( j = 0; j < 4; ++j )
    {
      if ( Dst[j] )
      {
        v31 = v31 + *(float *)(Dst[j] + 136);
      }
      else
      {
        v10 = v31 + 0.125;
        v31 = v10;
      }
    }
    v32 = 1;
    if ( CMainThread::IsReleaseServiceMode(&g_Main) && v281->m_byUserDgr )
      v32 = 0;
    if ( v32 )
    {
      v33 = CGameStatistics::CurWriteData(&g_GameStatistics);
      v33->dwDaePokUse_Evt += (signed int)ffloor(v31);
    }
    v34 = 1000;
    v35 = 750;
    v36 = 500;
    v37 = 250;
    v38 = 100;
    v39 = 50;
    v40 = 0;
    v41 = 1000;
    v42 = 750;
    v43 = 500;
    v44 = 250;
    v45 = 100;
    v46 = 50;
    v47 = 0;
    v48 = 1000;
    v49 = 750;
    v50 = 500;
    v51 = 250;
    v52 = 100;
    v53 = 50;
    v54 = 0;
    v55 = 1000;
    v56 = 750;
    v57 = 500;
    v58 = 250;
    v59 = 100;
    v60 = 50;
    v61 = 0;
    v62 = 328;
    v63 = 246;
    v64 = 164;
    v65 = 82;
    v66 = 49;
    v67 = 16;
    v68 = 0;
    v69 = 640;
    v70 = 480;
    v71 = 320;
    v72 = 160;
    v73 = 96;
    v74 = 32;
    v75 = 0;
    v76 = 512;
    v77 = 384;
    v78 = 256;
    v79 = 128;
    v80 = 77;
    v81 = 26;
    v82 = 0;
    v83 = 410;
    v84 = 307;
    v85 = 205;
    v86 = 102;
    v87 = 61;
    v88 = 20;
    v89 = 0;
    v90 = 800;
    v91 = 600;
    v92 = 400;
    v93 = 200;
    v94 = 120;
    v95 = 40;
    v96 = 0;
    v97 = 262;
    v98 = 197;
    v99 = 131;
    v100 = 66;
    v101 = 39;
    v102 = 13;
    v103 = 0;
    v104 = 210;
    v105 = 157;
    v106 = 105;
    v107 = 52;
    v108 = 31;
    v109 = 10;
    v110 = 0;
    v111 = GetItemGrade(*((_BYTE *)Src + 1), *(_WORD *)((char *)Src + 3));
    v112 = GetItemUpgedLv(*(_DWORD *)((char *)Src + 13));
    v113 = (signed int)ffloor((float)((float)((float)*(&v34 + 7 * (unsigned __int8)v111 + v112) * v31) / 4.0) * 100.0);
    v114 = GetItemEquipLevel(*((_BYTE *)Src + 1), *(_WORD *)((char *)Src + 3));
    if ( (signed int)v114 > 0 )
      v113 = (signed int)ffloor((float)(signed int)v113 * (float)(30.0 / (float)v114));
    v115 = rand();
    v276 = v115 << 16;
    v116 = rand() + v276;
    if ( v281->m_bCheat_100SuccMake )
      v113 = -1;
    v117 = 0;
    v118 = 0;
    v119 = 0;
    v120 = 0;
    v121 = 1600;
    v122 = 3200;
    v123 = 6400;
    v124 = 0;
    v125 = 0;
    v126 = 1200;
    v127 = 1600;
    v128 = 3000;
    v129 = 4400;
    v130 = 6800;
    v131 = 0;
    v132 = 0;
    v133 = 0;
    v134 = 1000;
    v135 = 2250;
    v136 = 3500;
    v137 = 5750;
    v138 = 0;
    v139 = 750;
    v140 = 1125;
    v141 = 1500;
    v142 = 2375;
    v143 = 3250;
    v144 = 4625;
    v145 = 2400;
    v146 = 3940;
    v147 = 6170;
    v148 = 6790;
    v149 = 7470;
    v150 = 8220;
    v151 = 9040;
    v152 = 7000;
    v153 = 7700;
    v154 = 8470;
    v155 = 9320;
    v156 = 10000;
    v157 = 10000;
    v158 = 10000;
    v159 = 4900;
    v160 = 6160;
    v161 = 7620;
    v162 = 8390;
    v163 = 9220;
    v164 = 10000;
    v165 = 10000;
    v166 = 3430;
    v167 = 4930;
    v168 = 6860;
    v169 = 7550;
    v170 = 8300;
    v171 = 9130;
    v172 = 10000;
    v173 = 10000;
    v174 = 10000;
    v175 = 10000;
    v176 = 10000;
    v177 = 10000;
    v178 = 10000;
    v179 = 10000;
    v180 = 1680;
    v181 = 3150;
    v182 = 5560;
    v183 = 6110;
    v184 = 6720;
    v185 = 7400;
    v186 = 8140;
    v187 = 1180;
    v188 = 2520;
    v189 = 5000;
    v190 = 5500;
    v191 = 6050;
    v192 = 6660;
    v193 = 7320;
    v194 = 0;
    v195 = 0;
    v196 = 0;
    v197 = 0;
    v198 = 2000;
    v199 = 4000;
    v200 = 8000;
    v201 = 0;
    v202 = 0;
    v203 = 2400;
    v204 = 3200;
    v205 = 6000;
    v206 = 8800;
    v207 = 9500;
    v208 = 0;
    v209 = 0;
    v210 = 0;
    v211 = 2000;
    v212 = 4500;
    v213 = 7000;
    v214 = 9500;
    v215 = 0;
    v216 = 3000;
    v217 = 4500;
    v218 = 6000;
    v219 = 9500;
    v220 = 9500;
    v221 = 9500;
    v222 = 0;
    v223 = 7300;
    v224 = 9500;
    v225 = 9500;
    v226 = 9500;
    v227 = 9500;
    v228 = 9500;
    v229 = 0;
    v230 = 4300;
    v231 = 6400;
    v232 = 8600;
    v233 = 9500;
    v234 = 9500;
    v235 = 9500;
    v236 = 0;
    v237 = 5100;
    v238 = 7600;
    v239 = 9500;
    v240 = 9500;
    v241 = 9500;
    v242 = 9500;
    v243 = 0;
    v244 = 6100;
    v245 = 9100;
    v246 = 9500;
    v247 = 9500;
    v248 = 9500;
    v249 = 9500;
    v250 = 0;
    v251 = 3600;
    v252 = 5400;
    v253 = 7200;
    v254 = 9500;
    v255 = 9500;
    v256 = 9500;
    v257 = 0;
    v258 = 8700;
    v259 = 9500;
    v260 = 9500;
    v261 = 9500;
    v262 = 9500;
    v263 = 9500;
    v264 = 0;
    v265 = 9500;
    v266 = 9500;
    v267 = 9500;
    v268 = 9500;
    v269 = 9500;
    v270 = 9500;
    if ( v113 > v116 % 0x186A0 )
    {
      v272 = GetBitAfterUpgrade(*(_DWORD *)((char *)Src + 13), v24->m_dwIndex, v112);
      LODWORD(pnTalikLim) = v272;
      CPlayer::Emb_ItemUpgrade(v281, 0, v19->m_nListCode, *((_BYTE *)Src + 49), v272);
      CPlayer::SendMsg_FanfareItem(v281, 0, (_STORAGE_LIST::_db_con *)Src, 0i64);
      v273 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + *((_BYTE *)Src + 1), *(_WORD *)((char *)Src + 3));
      CPlayer::Emb_CheckActForQuest(v281, 10, v273->m_strCode, 1u, 0);
      v274 = 1;
      if ( CMainThread::IsReleaseServiceMode(&g_Main) && v281->m_byUserDgr )
        v274 = 0;
      if ( v274 )
      {
        v275 = CGameStatistics::CurWriteData(&g_GameStatistics);
        if ( v112 == 3 )
        {
          if ( v24->m_dwIndex )
          {
            if ( v24->m_dwIndex == 5 )
            {
              ++v275->dw4EunUpgradeSucc_Evt;
            }
            else if ( v24->m_dwIndex == 12 )
            {
              ++v275->dw4JaUpgradeSucc_Evt;
            }
          }
          else
          {
            ++v275->dw4MuUpgradeSucc_Evt;
          }
        }
        else if ( v112 == 4 )
        {
          if ( v24->m_dwIndex )
          {
            if ( v24->m_dwIndex == 5 )
            {
              ++v275->dw5EunUpgradeSucc_Evt;
            }
            else if ( v24->m_dwIndex == 12 )
            {
              ++v275->dw5JaUpgradeSucc_Evt;
            }
          }
          else
          {
            ++v275->dw5MuUpgradeSucc_Evt;
          }
        }
      }
    }
    else
    {
      v277 = &v117 + 7 * (unsigned __int8)v111;
      v278 = v112;
      if ( v277[v278] <= (unsigned int)(rand() % 10000) )
      {
        v279 = &v194 + 7 * (unsigned __int8)v111;
        v280 = v112;
        if ( v279[v280] <= (unsigned int)(rand() % 10000) )
        {
          v17 = 100;
        }
        else
        {
          dwGradeInfo = GetBitAfterSetLimSocket(v26);
          LODWORD(pnTalikLim) = dwGradeInfo;
          CPlayer::Emb_ItemUpgrade(v281, 2, v284->byStorageCode, *((_BYTE *)Src + 49), dwGradeInfo);
          v17 = 101;
        }
      }
      else
      {
        *(_QWORD *)bSend = "CPlayer::pc_UpgradeItem()";
        LOBYTE(pnTalikLim) = 1;
        if ( !CPlayer::Emb_DelStorage(v281, v19->m_nListCode, *((_BYTE *)Src + 49), 0, 1, "CPlayer::pc_UpgradeItem()") )
        {
          CPlayer::SendMsg_ItemUpgrade(v281, -1);
          return;
        }
        v17 = 102;
      }
    }
    v11 = v281->m_ObjID.m_wIndex;
    pszFileName = v281->m_szItemHistoryFileName;
    dwAfterLv = *(_DWORD *)((char *)Src + 13);
    CMgrAvatorItemHistory::grade_up_item(
      &CPlayer::s_MgrItemHistory,
      v11,
      &pItem,
      &pTalik,
      (_STORAGE_LIST::_db_con *)__t,
      byJewelNum,
      v17,
      dwAfterLv,
      v281->m_szItemHistoryFileName);
  }
  CPlayer::SendMsg_ItemUpgrade(v281, v17);
}
