/*
 * Function: ?_buybygold_buy_single_item_proc_price@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@PEAVCPlayer@@PEAU_request_csi_buy_clzo@@PEAU__item@4@PEAU_param_cashitem_dblog@@PEAU_CashShop_fld@@PEA_NAEAU_result_csi_buy_zocl@@AEAK7@Z
 * Address: 0x1402FF7C0
 */

signed __int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_proc_price(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, _request_csi_buy_clzo::__item *pSrc, _param_cashitem_dblog *pSheet, _CashShop_fld *pCsFld, bool *bCouponUseCheck, _result_csi_buy_zocl *Send, unsigned int *dwPrice, unsigned int *dwDiscountRate)
{
  __int64 *v10; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@5
  __int64 v13; // [sp+0h] [bp-58h]@1
  CashItemRemoteStore *v14; // [sp+60h] [bp+8h]@1
  CPlayer *pOnea; // [sp+68h] [bp+10h]@1

  pOnea = pOne;
  v14 = this;
  v10 = &v13;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v10 = -858993460;
    v10 = (__int64 *)((char *)v10 + 4);
  }
  *dwPrice = CashItemRemoteStore::_buybygold_buy_single_item_calc_price(
               v14,
               pOne,
               pRecv,
               pSrc,
               pSheet,
               pCsFld,
               bCouponUseCheck,
               Send,
               dwDiscountRate);
  if ( CPlayerDB::GetGold(&pOnea->m_Param) >= *dwPrice )
  {
    CPlayer::SubGold(pOnea, *dwPrice);
    result = 0i64;
  }
  else
  {
    result = 13i64;
  }
  return result;
}
