# NexusPro (Nexus Protection) - RF Online Decompiled Code
RF Online Game Guard - Preserving Original Decompiled Structure with Organized Solution Explorer

## Project Structure

This solution preserves the original RF Online decompiled code while organizing it into a modern Visual Studio 2022 structure with organized Solution Explorer:

```
NexusPro/
├── NexusPro.sln                    # Main solution file
├── NexusPro.Authentication/        # Authentication module
│   ├── headers/                    # Generated header files (.h)
│   ├── source/                     # Original RF Online code (.cpp)
│   ├── NexusPro.Authentication.vcxproj
│   └── NexusPro.Authentication.vcxproj.filters  # Solution Explorer organization
├── NexusPro.Combat/               # Combat module
│   ├── headers/                    # Generated header files (.h)
│   ├── source/                     # Original RF Online code (.cpp)
│   ├── NexusPro.Combat.vcxproj
│   └── NexusPro.Combat.vcxproj.filters
├── ... (other modules)
├── NexusPro.Core/                 # Core module
│   ├── headers/                    # Original RF Online headers
│   ├── NexusPro.Core.vcxproj
│   └── NexusPro.Core.vcxproj.filters
├── bin/                           # Build output
└── obj/                           # Intermediate files
```

### Solution Explorer Organization
Each project in Visual Studio will show:
- **Header Files** folder containing all .h files
- **Source Files** folder containing all .cpp files
- Subfolders organized by system/component within each category

### Core Modules (Based on RF Online Structure)
- **NexusPro.Authentication** - Original RF Online authentication code
- **NexusPro.Combat** - Original RF Online combat system code
- **NexusPro.Database** - Original RF Online database operations
- **NexusPro.Economy** - Original RF Online economic system
- **NexusPro.Items** - Original RF Online item management
- **NexusPro.Network** - Original RF Online network protocols
- **NexusPro.Player** - Original RF Online player management
- **NexusPro.Security** - Original RF Online security systems
- **NexusPro.System** - Original RF Online core system
- **NexusPro.World** - Original RF Online world management
- **NexusPro.Core** - Original RF Online common headers

## Important Notes

### Code Preservation
- **Original Content**: All .cpp files contain the exact original RF Online decompiled code
- **File Extensions**: .c files have been renamed to .cpp for Visual Studio compatibility
- **No Modifications**: The actual code logic remains completely unchanged
- **IDA Pro Structure**: Maintains the original IDA Pro decompiled file organization

### Solution Explorer Features
- **Organized Folders**: Headers and sources are separated in Solution Explorer
- **System Organization**: Files are grouped by their system/component
- **Easy Navigation**: Quick access to related files through folder structure
- **Professional Layout**: Follows Visual Studio conventions for large projects

## Building

1. Open `NexusPro.sln` in Visual Studio 2022
2. Select Debug or Release configuration
3. Build Solution (Ctrl+Shift+B)

## Requirements

- Visual Studio 2022 with C++ development tools
- Windows 10 SDK
- Platform Toolset v143

## Development Notes

### Working with Original Code
- The source files contain the original RF Online decompiled code
- Header files need manual completion based on the corresponding .cpp files
- Preprocessor definition `RF_ONLINE_DECOMPILED` is set for all projects

### Solution Explorer Navigation
- Use the organized folder structure to quickly find related files
- Headers and sources are clearly separated for better organization
- Each module maintains its own folder hierarchy

This structure allows you to work with the original RF Online decompiled code in a modern development environment while maintaining complete fidelity to the source material and providing excellent organization in Visual Studio.
