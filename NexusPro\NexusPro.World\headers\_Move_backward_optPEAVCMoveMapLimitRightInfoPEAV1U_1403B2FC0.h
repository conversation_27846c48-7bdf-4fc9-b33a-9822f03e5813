#pragma once
#ifndef _MOVE_BACKWARD_OPTPEAVCMOVEMAPLIMITRIGHTINFOPEAV1U_1403B2FC0_H
#define _MOVE_BACKWARD_OPTPEAVCMOVEMAPLIMITRIGHTINFOPEAV1U_1403B2FC0_H

// Auto-generated header for _Move_backward_optPEAVCMoveMapLimitRightInfoPEAV1U_1403B2FC0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

#endif // _MOVE_BACKWARD_OPTPEAVCMOVEMAPLIMITRIGHTINFOPEAV1U_1403B2FC0_H
