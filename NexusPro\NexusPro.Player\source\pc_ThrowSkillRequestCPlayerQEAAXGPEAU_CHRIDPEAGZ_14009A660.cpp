/*
 * Function: ?pc_ThrowSkillRequest@CPlayer@@QEAAXGPEAU_CHRID@@PEAG@Z
 * Address: 0x14009A660
 */

void __usercall CPlayer::pc_ThrowSkillRequest(CPlayer *this@<rcx>, unsigned __int16 wBulletSerial@<dx>, _CHRID *pidDst@<r8>, unsigned __int16 *pConsumeSerial@<r9>, float a5@<xmm0>)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CGuildRoomSystem *v7; // rax@54
  int v8; // eax@71
  __int64 v9; // [sp+0h] [bp-188h]@1
  int *pnConsume; // [sp+20h] [bp-168h]@68
  bool *pbOverLap; // [sp+28h] [bp-160h]@68
  char pbyErrorCode; // [sp+44h] [bp-144h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+58h] [bp-130h]@4
  _base_fld *v14; // [sp+60h] [bp-128h]@4
  _skill_fld *pSkillFld; // [sp+68h] [bp-120h]@4
  CCharacter *pDst; // [sp+70h] [bp-118h]@4
  __int16 v17; // [sp+84h] [bp-104h]@4
  char v18; // [sp+86h] [bp-102h]@4
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+A8h] [bp-E0h]@56
  char v20; // [sp+B0h] [bp-D8h]@56
  int v21; // [sp+D8h] [bp-B0h]@56
  char v22; // [sp+DCh] [bp-ACh]@56
  bool v23; // [sp+104h] [bp-84h]@56
  char v24; // [sp+105h] [bp-83h]@56
  char v25; // [sp+114h] [bp-74h]@66
  bool v26; // [sp+115h] [bp-73h]@67
  bool v27; // [sp+124h] [bp-64h]@67
  unsigned __int16 v28; // [sp+134h] [bp-54h]@68
  unsigned int delay; // [sp+138h] [bp-50h]@71
  CUserDB *v30; // [sp+148h] [bp-40h]@54
  int n; // [sp+150h] [bp-38h]@54
  CGuild *v32; // [sp+158h] [bp-30h]@54
  int nAddDelay; // [sp+160h] [bp-28h]@71
  CGameObjectVtbl *v34; // [sp+168h] [bp-20h]@71
  unsigned __int64 v35; // [sp+170h] [bp-18h]@4
  CPlayer *v36; // [sp+190h] [bp+8h]@1
  unsigned __int16 v37; // [sp+198h] [bp+10h]@1
  _CHRID *pidDsta; // [sp+1A0h] [bp+18h]@1
  unsigned __int16 *pItemSerials; // [sp+1A8h] [bp+20h]@1

  pItemSerials = pConsumeSerial;
  pidDsta = pidDst;
  v37 = wBulletSerial;
  v36 = this;
  v5 = &v9;
  for ( i = 96i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v35 = (unsigned __int64)&v9 ^ _security_cookie;
  pbyErrorCode = 0;
  pItem = 0i64;
  v14 = 0i64;
  pSkillFld = 0i64;
  pDst = 0i64;
  v17 = 0;
  memset(&v18, 0, 4ui64);
  if ( CPlayer::IsRidingUnit(v36) )
  {
    pbyErrorCode = 14;
  }
  else if ( v36->m_byMoveType == 2 )
  {
    pbyErrorCode = 28;
  }
  else if ( _effect_parameter::GetEff_State(&v36->m_EP, 20) )
  {
    pbyErrorCode = 24;
  }
  else if ( _effect_parameter::GetEff_State(&v36->m_EP, 28) )
  {
    pbyErrorCode = 24;
  }
  else if ( _effect_parameter::GetEff_State(&v36->m_EP, 21) )
  {
    pbyErrorCode = 25;
  }
  else if ( v36->m_pmWpn.byWpType == 11 || v36->m_pmWpn.byWpType == 6 )
  {
    if ( v36->m_pmWpn.byWpType == 6
      && (!v36->m_pmWpn.pFixWp || v36->m_pmWpn.pFixWp->m_byTableCode != 6 || v36->m_pmWpn.pFixWp->m_wItemIndex != 8518) )
    {
      pbyErrorCode = 22;
    }
    pItem = CPlayer::IsBulletValidity(v36, v37);
    if ( pItem )
    {
      if ( pItem->m_bLock )
      {
        pbyErrorCode = 20;
      }
      else
      {
        v14 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 10, pItem->m_wItemIndex);
        if ( v14 )
        {
          if ( !strncmp((const char *)&v14[7], "-1", 2ui64) )
          {
            pbyErrorCode = 19;
          }
          else
          {
            pSkillFld = (_skill_fld *)CRecordData::GetRecord(&stru_1799C8410 + 3, (const char *)&v14[7]);
            if ( pSkillFld )
            {
              if ( v36->m_bSFDelayNotCheck || _ATTACK_DELAY_CHECKER::IsDelay(&v36->m_AttDelayChker, -1, 0xFFu, -1) )
              {
                pDst = (CCharacter *)CMainThread::GetObjectA(&g_Main, 0, pidDsta->byID, pidDsta->wIndex);
                if ( pDst )
                {
                  if ( pDst->m_bLive && pDst->m_pCurMap == v36->m_pCurMap )
                  {
                    if ( CCharacter::IsEffectableDst((CCharacter *)&v36->vfptr, pSkillFld->m_strActableDst, pDst) )
                    {
                      if ( pSkillFld->m_nTempEffectType != -1 || pSkillFld->m_nContEffectType != -1 )
                      {
                        if ( pSkillFld->m_nContEffectType != -1 )
                        {
                          if ( !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsRecvableContEffect)(pDst) )
                          {
                            pbyErrorCode = 13;
                            goto $RESULT_11;
                          }
                          if ( !pSkillFld->m_nContEffectType
                            && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v36->vfptr->IsAttackableInTown)(v36)
                            && !(unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsAttackableInTown)(pDst) )
                          {
                            if ( (unsigned __int8)((int (__fastcall *)(CPlayer *))v36->vfptr->IsInTown)(v36)
                              || (unsigned __int8)((int (__fastcall *)(CCharacter *))pDst->vfptr->IsInTown)(pDst)
                              || v36->m_Param.m_pGuild
                              && (v30 = v36->m_pUserDB,
                                  n = v36->m_ObjID.m_wIndex,
                                  v32 = v36->m_Param.m_pGuild,
                                  v7 = CGuildRoomSystem::GetInstance(),
                                  CGuildRoomSystem::IsGuildRoomMemberIn(v7, v32->m_dwSerial, n, v30->m_dwSerial)) )
                            {
                              pbyErrorCode = 18;
                              goto $RESULT_11;
                            }
                          }
                        }
                        ppConsumeItems = 0i64;
                        memset(&v20, 0, 0x10ui64);
                        v21 = 0;
                        memset(&v22, 0, 8ui64);
                        v23 = 0;
                        memset(&v24, 0, 2ui64);
                        if ( CPlayer::GetUseConsumeItem(
                               v36,
                               pSkillFld->m_ConsumeItemList,
                               pItemSerials,
                               &ppConsumeItems,
                               &v21,
                               &v23) )
                        {
                          if ( pSkillFld->m_nContEffectType != -1 )
                          {
                            if ( pSkillFld->m_nContEffectType == 1 )
                            {
                              if ( CPlayer::_pre_check_in_guild_battle_race(v36, pDst, 0) )
                                pbyErrorCode = 13;
                            }
                            else if ( CPlayer::_pre_check_in_guild_battle_race(v36, pDst, 1)
                                   && !CCharacter::IsEffectableDst(
                                         (CCharacter *)&v36->vfptr,
                                         pSkillFld->m_strActableDst,
                                         pDst) )
                            {
                              pbyErrorCode = 13;
                            }
                          }
                        }
                        else
                        {
                          pbyErrorCode = 32;
                        }
                      }
                      else
                      {
                        pbyErrorCode = 8;
                      }
                    }
                    else
                    {
                      pbyErrorCode = 5;
                    }
                  }
                  else
                  {
                    pbyErrorCode = 2;
                  }
                }
                else
                {
                  pbyErrorCode = 2;
                }
              }
              else
              {
                pbyErrorCode = 9;
              }
            }
            else
            {
              pbyErrorCode = 8;
            }
          }
        }
        else
        {
          pbyErrorCode = 19;
        }
      }
    }
    else
    {
      pbyErrorCode = 19;
    }
  }
  else
  {
    pbyErrorCode = 22;
  }
$RESULT_11:
  v25 = -1;
  g_tmpEffectedNum = 0;
  if ( !pbyErrorCode )
  {
    v25 = pSkillFld->m_dwIndex;
    v26 = CCharacter::GetStealth((CCharacter *)&v36->vfptr, 1);
    v27 = 0;
    if ( CCharacter::AssistSkill((CCharacter *)&v36->vfptr, pDst, 3, pSkillFld, 1, &pbyErrorCode, &v27) )
    {
      LOBYTE(pbOverLap) = 1;
      LOBYTE(pnConsume) = 0;
      v28 = CPlayer::Emb_AlterDurPoint(v36, 2, pItem->m_byStorageIndex, -1, 0, 1);
      if ( v28 )
        CPlayer::SendMsg_AlterWeaponBulletInform(v36, pItem->m_wSerial, v28);
      else
        CMgrAvatorItemHistory::consume_del_item(
          &CPlayer::s_MgrItemHistory,
          v36->m_ObjID.m_wIndex,
          pItem,
          v36->m_szItemHistoryFileName);
      CPlayer::DeleteUseConsumeItem(v36, &ppConsumeItems, &v21, &v23);
      nAddDelay = CPlayer::CalcEquipAttackDelay(v36);
      v34 = v36->vfptr;
      v8 = ((int (__fastcall *)(CPlayer *))v34->GetLevel)(v36);
      delay = _WEAPON_PARAM::GetAttackDelay(&v36->m_pmWpn, v8, nAddDelay);
      if ( v36->m_pmWpn.byWpType != 11 && !v36->m_pmWpn.byWpClass )
      {
        _effect_parameter::GetEff_Plus(&v36->m_EP, 9);
        a5 = (float)(signed int)delay + a5;
        delay = (signed int)ffloor(a5);
      }
      if ( v36->m_pmWpn.byWpType == 7 || v36->m_pmWpn.byWpType == 11 )
      {
        _effect_parameter::GetEff_Plus(&v36->m_EP, 11);
        delay = (signed int)ffloor((float)(signed int)delay + a5);
      }
      _ATTACK_DELAY_CHECKER::SetDelay(&v36->m_AttDelayChker, delay);
      if ( v26 )
        CCharacter::BreakStealth((CCharacter *)&v36->vfptr);
    }
  }
  CPlayer::SendMsg_ThrowSkillResult(v36, pbyErrorCode, pidDsta, v25);
}
