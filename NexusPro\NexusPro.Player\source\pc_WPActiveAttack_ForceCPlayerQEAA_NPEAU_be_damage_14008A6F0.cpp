/*
 * Function: ?pc_WPActiveAttack_Force@CPlayer@@QEAA_NPEAU_be_damaged_char@@PEAHPEAU_force_fld@@@Z
 * Address: 0x14008A6F0
 */

char __fastcall CPlayer::pc_WPActiveAttack_Force(CPlayer *this, _be_damaged_char *pDamList, int *nDamagedObjNum, _force_fld *pForceFld)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  __int64 v7; // [sp+0h] [bp-428h]@1
  _attack_param *pAP; // [sp+20h] [bp-408h]@6
  char Dst; // [sp+38h] [bp-3F0h]@4
  _attack_param pParam; // [sp+70h] [bp-3B8h]@6
  CPlayerAttack v11; // [sp+110h] [bp-318h]@6
  char v12; // [sp+414h] [bp-14h]@10
  int j; // [sp+418h] [bp-10h]@7
  int k; // [sp+41Ch] [bp-Ch]@10
  CPlayer *v15; // [sp+430h] [bp+8h]@1
  _be_damaged_char *v16; // [sp+438h] [bp+10h]@1
  int *v17; // [sp+440h] [bp+18h]@1
  _force_fld *pForceFlda; // [sp+448h] [bp+20h]@1

  pForceFlda = pForceFld;
  v17 = nDamagedObjNum;
  v16 = pDamList;
  v15 = this;
  v4 = &v7;
  for ( i = 264i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  memcpy_0(&Dst, pDamList->m_pChar->m_fCurPos, 0xCui64);
  if ( CPlayer::_pre_check_wpactive_force_attack(v15) )
  {
    _attack_param::_attack_param(&pParam);
    pAP = &pParam;
    CPlayer::make_wpactive_force_attack_param(v15, v16->m_pChar, pForceFlda, (float *)&Dst, &pParam);
    CPlayerAttack::CPlayerAttack(&v11, (CCharacter *)&v15->vfptr);
    CPlayerAttack::WPActiveAttackForce(&v11, &pParam);
    if ( v11.m_nDamagedObjNum > 0 )
    {
      for ( j = 0; j < v11.m_nDamagedObjNum; ++j )
      {
        v12 = 0;
        for ( k = 0; k < *v17; ++k )
        {
          if ( v16[k].m_pChar == v11.m_DamList[j].m_pChar )
          {
            v12 = 1;
            v16[k].m_bActiveSucc = v11.m_DamList[j].m_nDamage > 0;
            v16[k].m_nActiveDamage = v11.m_DamList[j].m_nDamage;
            break;
          }
        }
        if ( !v12 && *v17 < 30 )
        {
          v16[*v17].m_pChar = v11.m_DamList[j].m_pChar;
          v16[*v17].m_nDamage = 0;
          v16[*v17].m_bActiveSucc = v11.m_DamList[j].m_nDamage > 0;
          v16[(*v17)++].m_nActiveDamage = v11.m_DamList[j].m_nDamage;
        }
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
