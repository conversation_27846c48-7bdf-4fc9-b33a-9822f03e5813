#pragma once
#ifndef SET_RESPAWN_MONSTER_ACT_DH_MISSION_MGRQEAAXPEAU__R_14026F0F0_H
#define SET_RESPAWN_MONSTER_ACT_DH_MISSION_MGRQEAAXPEAU__R_14026F0F0_H

// Auto-generated header for set_respawn_monster_act_dh_mission_mgrQEAAXPEAU__r_14026F0F0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_respawn_monster_act::set(_dh_mission_mgr::_respawn_monster_act *this, __respawn_monster *data)
;

#endif // SET_RESPAWN_MONSTER_ACT_DH_MISSION_MGRQEAAXPEAU__R_14026F0F0_H
