/*
 * Function: ?pc_ReleaseGroupTargetObjectRequest@CPlayer@@QEAAXE@Z
 * Address: 0x1400FE3D0
 */

void __fastcall CPlayer::pc_ReleaseGroupTargetObjectRequest(CPlayer *this, char byGroupType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@21
  CPvpUserAndGuildRankingSystem *v5; // rax@31
  unsigned int v6; // eax@31
  int v7; // eax@36
  __int64 v8; // [sp+0h] [bp-68h]@1
  int j; // [sp+20h] [bp-48h]@12
  CPlayer *v10; // [sp+28h] [bp-40h]@4
  CPartyPlayer **v11; // [sp+30h] [bp-38h]@4
  _guild_member_info *v12; // [sp+38h] [bp-30h]@4
  char v13; // [sp+40h] [bp-28h]@5
  unsigned int v14; // [sp+44h] [bp-24h]@21
  int v15; // [sp+48h] [bp-20h]@31
  unsigned int v16; // [sp+4Ch] [bp-1Ch]@31
  int v17; // [sp+50h] [bp-18h]@36
  CPlayer *v18; // [sp+70h] [bp+8h]@1
  char v19; // [sp+78h] [bp+10h]@1

  v19 = byGroupType;
  v18 = this;
  v2 = &v8;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v10 = 0i64;
  v11 = 0i64;
  v12 = 0i64;
  if ( v18->m_GroupTargetObject[(unsigned __int8)byGroupType].pObject )
  {
    v13 = byGroupType;
    if ( byGroupType )
    {
      if ( v13 == 1 )
      {
        if ( v18->m_Param.m_pGuild )
        {
          v14 = CGuild::GetGuildMasterSerial(v18->m_Param.m_pGuild);
          v4 = CPlayerDB::GetCharSerial(&v18->m_Param);
          if ( v14 == v4 )
          {
            for ( j = 0; j < 50; ++j )
            {
              v12 = &v18->m_Param.m_pGuild->m_MemberData[j];
              if ( _guild_member_info::IsFill(v12) )
              {
                v10 = v12->pPlayer;
                if ( v10 )
                {
                  if ( v10->m_bOper && v10->m_GroupTargetObject[(unsigned __int8)v19].pObject )
                  {
                    CPlayer::__target::init(&v10->m_GroupTargetObject[(unsigned __int8)v19]);
                    CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v10, v19);
                  }
                }
              }
            }
          }
        }
      }
      else if ( v13 == 2 )
      {
        v15 = CPlayerDB::GetRaceCode(&v18->m_Param);
        v5 = CPvpUserAndGuildRankingSystem::Instance();
        v16 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, v15, 0);
        v6 = CPlayerDB::GetCharSerial(&v18->m_Param);
        if ( v16 == v6 )
        {
          for ( j = 0; j < 2532; ++j )
          {
            v10 = &g_Player + j;
            if ( v10->m_bOper )
            {
              v17 = CPlayerDB::GetRaceCode(&v10->m_Param);
              v7 = CPlayerDB::GetRaceCode(&v18->m_Param);
              if ( v17 == v7 )
              {
                if ( v10->m_GroupTargetObject[(unsigned __int8)v19].pObject )
                {
                  CPlayer::__target::init(&v10->m_GroupTargetObject[(unsigned __int8)v19]);
                  CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v10, v19);
                }
              }
            }
          }
        }
      }
    }
    else if ( CPartyPlayer::IsPartyMode(v18->m_pPartyMgr) && CPartyPlayer::IsPartyBoss(v18->m_pPartyMgr) )
    {
      v11 = CPartyPlayer::GetPtrPartyMember(v18->m_pPartyMgr);
      if ( v11 )
      {
        for ( j = 0; j < 8; ++j )
        {
          if ( v11[j] )
          {
            v10 = &g_Player + v11[j]->m_id.wIndex;
            if ( v10->m_bOper )
            {
              if ( v10->m_GroupTargetObject[0].pObject )
              {
                CPlayer::__target::init(v10->m_GroupTargetObject);
                CPlayer::SendMsg_ReleaseGroupTargetObjectResult(v10, 0);
              }
            }
          }
        }
      }
    }
  }
}
