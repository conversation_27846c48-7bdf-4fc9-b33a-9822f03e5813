/*
 * Function: ?_buybygold_buy_single_item_additional_process@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@PEAVCPlayer@@PEAU__item@_request_csi_buy_clzo@@PEAU_param_cashitem_dblog@@AEAU_result_csi_buy_zocl@@@Z
 * Address: 0x140300110
 */

signed __int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_additional_process(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo::__item *pSrc, _param_cashitem_dblog *pSheet, _result_csi_buy_zocl *Send)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  CAsyncLogger *v8; // rax@12
  __int64 v9; // [sp+0h] [bp-D8h]@1
  bool bAdd[4]; // [sp+20h] [bp-B8h]@12
  char *v11; // [sp+28h] [bp-B0h]@12
  char *v12; // [sp+30h] [bp-A8h]@12
  unsigned __int64 v13; // [sp+38h] [bp-A0h]@12
  int v14; // [sp+40h] [bp-98h]@12
  _STORAGE_LIST::_db_con v15; // [sp+58h] [bp-80h]@6
  int v16; // [sp+94h] [bp-44h]@6
  _STORAGE_LIST::_db_con *v17; // [sp+98h] [bp-40h]@9
  _base_fld *v18; // [sp+A0h] [bp-38h]@11
  int v19; // [sp+A8h] [bp-30h]@12
  int nTableCode; // [sp+ACh] [bp-2Ch]@12
  char *v21; // [sp+B0h] [bp-28h]@12
  char *v22; // [sp+B8h] [bp-20h]@12
  CUserDB *v23; // [sp+C0h] [bp-18h]@12
  char *v24; // [sp+C8h] [bp-10h]@12
  CPlayer *v25; // [sp+E8h] [bp+10h]@1
  _request_csi_buy_clzo::__item *v26; // [sp+F0h] [bp+18h]@1

  v26 = pSrc;
  v25 = pOne;
  v5 = &v9;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  if ( pSheet->in_bOneN_One && pSrc->byEventType == 3 )
  {
    _STORAGE_LIST::_db_con::_db_con(&v15);
    v16 = 0;
    v15.m_byTableCode = v26->byTblCode;
    v15.m_wItemIndex = v26->wItemIdx;
    if ( IsOverLapItem((unsigned __int8)v15.m_byTableCode) )
      v15.m_dwDur = v26->byOverlapNum;
    else
      v15.m_dwDur = GetItemDurPoint(v26->byTblCode, v26->wItemIdx);
    v15.m_dwLv = (unsigned __int8)GetDefItemUpgSocketNum(v26->byTblCode, v26->wItemIdx);
    v15.m_wSerial = CPlayerDB::GetNewItemSerial(&v25->m_Param);
    v17 = CPlayer::Emb_AddStorage(v25, 0, (_STORAGE_LIST::_storage_con *)&v15.m_bLoad, 0, 1);
    if ( !v17 )
      return 15i64;
    Send->item[Send->nNum].byTblCode = v15.m_byTableCode;
    Send->item[Send->nNum].wItemIdx = v15.m_wItemIndex;
    Send->item[Send->nNum].dwDur = v15.m_dwDur;
    Send->item[Send->nNum].dwUp = v15.m_dwLv;
    Send->item[Send->nNum].dwItemSerial = v15.m_wSerial;
    Send->item[Send->nNum].byCsMethod = v15.m_byCsMethod;
    Send->item[Send->nNum++].dwT = v15.m_dwT;
    v18 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + (unsigned __int8)v15.m_byTableCode, v15.m_wItemIndex);
    if ( v18 )
    {
      v19 = v26->byEventType;
      nTableCode = (unsigned __int8)v15.m_byTableCode;
      v21 = GetItemKorName((unsigned __int8)v15.m_byTableCode, v15.m_wItemIndex);
      v22 = v18->m_strCode;
      v23 = v25->m_pUserDB;
      v24 = CPlayerDB::GetCharNameA(&v25->m_Param);
      v8 = CAsyncLogger::Instance();
      v14 = v19;
      v13 = v15.m_dwDur;
      v12 = v21;
      v11 = v22;
      *(_DWORD *)bAdd = v23->m_dwAccountSerial;
      CAsyncLogger::FormatLog(
        v8,
        5,
        "[Name: %s AccountID: %d] [Insert 1+1 Event CASTITEM] : %s(%s) [UID: %I64u] [Num:%d Event: %d]",
        v24);
    }
  }
  return 0i64;
}
