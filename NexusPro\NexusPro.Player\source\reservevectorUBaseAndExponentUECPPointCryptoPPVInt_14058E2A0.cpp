/*
 * Function: ?reserve@?$vector@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@V?$allocator@U?$BaseAndExponent@UECPPoint@CryptoPP@@VInteger@2@@CryptoPP@@@std@@@std@@QEAAX_K@Z
 * Address: 0x14058E2A0
 */

int __fastcall std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::reserve(__int64 a1, unsigned __int64 a2)
{
  unsigned __int64 v2; // rax@1
  unsigned __int64 v3; // rax@3
  __int64 v4; // rax@4
  __int64 v5; // rax@4
  __int64 v6; // rax@4
  __int64 v7; // rax@4
  __int64 v9; // [sp+20h] [bp-78h]@4
  __int64 v10; // [sp+28h] [bp-70h]@4
  char v11; // [sp+30h] [bp-68h]@4
  char *v12; // [sp+48h] [bp-50h]@4
  char v13; // [sp+50h] [bp-48h]@4
  char *v14; // [sp+68h] [bp-30h]@4
  __int64 v15; // [sp+70h] [bp-28h]@1
  __int64 v16; // [sp+78h] [bp-20h]@4
  __int64 v17; // [sp+80h] [bp-18h]@4
  __int64 v18; // [sp+88h] [bp-10h]@4
  __int64 v19; // [sp+A0h] [bp+8h]@1
  unsigned __int64 v20; // [sp+A8h] [bp+10h]@1

  v20 = a2;
  v19 = a1;
  v15 = -2i64;
  LODWORD(v2) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::max_size();
  if ( v2 < v20 )
    std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Xlen();
  LODWORD(v3) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::capacity(v19);
  if ( v3 < v20 )
  {
    LODWORD(v4) = std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::allocate(
                    v19 + 8,
                    v20);
    v9 = v4;
    v12 = &v11;
    v14 = &v13;
    LODWORD(v5) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::end(
                    v19,
                    &v11);
    v16 = v5;
    v17 = v5;
    LODWORD(v6) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::begin(
                    v19,
                    v14);
    v18 = v6;
    std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Umove<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>>(
      v19,
      v6,
      v17,
      v9);
    LODWORD(v7) = std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::size(v19);
    v10 = v7;
    if ( *(_QWORD *)(v19 + 16) )
    {
      std::vector<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>>::_Destroy(
        v19,
        *(_QWORD *)(v19 + 16),
        *(_QWORD *)(v19 + 24));
      std::allocator<CryptoPP::BaseAndExponent<CryptoPP::ECPPoint,CryptoPP::Integer>>::deallocate(
        v19 + 8,
        *(_QWORD *)(v19 + 16),
        (*(_QWORD *)(v19 + 32) - *(_QWORD *)(v19 + 16)) >> 7);
    }
    *(_QWORD *)(v19 + 32) = (v20 << 7) + v9;
    *(_QWORD *)(v19 + 24) = (v10 << 7) + v9;
    LODWORD(v3) = v19;
    *(_QWORD *)(v19 + 16) = v9;
  }
  return v3;
}
