/*
 * Function: ?_pre_check_in_guild_battle@CPlayer@@QEAAHPEAVCCharacter@@@Z
 * Address: 0x140087670
 */

signed __int64 __fastcall CPlayer::_pre_check_in_guild_battle(CPlayer *this, CCharacter *pDst)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@34
  __int64 v6; // [sp+0h] [bp-68h]@1
  _BYTE *v7; // [sp+20h] [bp-48h]@5
  CCharacter *v8; // [sp+28h] [bp-40h]@18
  __int64 v9; // [sp+30h] [bp-38h]@22
  CPlayer *v10; // [sp+38h] [bp-30h]@28
  CPlayer *v11; // [sp+40h] [bp-28h]@32
  int v12; // [sp+48h] [bp-20h]@34
  CGameObjectVtbl *v13; // [sp+50h] [bp-18h]@34
  CPlayer *v14; // [sp+70h] [bp+8h]@1
  CCharacter *v15; // [sp+78h] [bp+10h]@1

  v15 = pDst;
  v14 = this;
  v2 = &v6;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( v14->m_bInGuildBattle )
  {
    v7 = 0i64;
    if ( pDst->m_ObjID.m_byID )
    {
      if ( pDst->m_ObjID.m_byID == 3 )
      {
        v7 = *(_BYTE **)&pDst[1].m_bLive;
      }
      else if ( pDst->m_ObjID.m_byID == 4 )
      {
        v7 = pDst[1].m_pRecordSet;
      }
    }
    else
    {
      v7 = pDst;
    }
    if ( !v7 )
      return 4294967290i64;
    if ( !v7[1922] || v14->m_byGuildBattleColorInx == v7[1924] )
      return 4294967290i64;
  }
  else
  {
    if ( pDst->m_ObjID.m_byID )
    {
      if ( pDst->m_ObjID.m_byID == 3 )
      {
        v9 = *(_QWORD *)&pDst[1].m_bLive;
        if ( v9 )
        {
          if ( *(_BYTE *)(v9 + 1922) )
            return 4294967290i64;
        }
      }
    }
    else
    {
      v8 = pDst;
      if ( BYTE2(pDst[1].m_fCurPos[2]) )
        return 4294967290i64;
    }
    if ( v14->m_nChaosMode )
      return 0i64;
    if ( pDst->m_ObjID.m_byID )
    {
      if ( pDst->m_ObjID.m_byID == 3 )
      {
        v11 = *(CPlayer **)&pDst[1].m_bLive;
        if ( CPlayer::IsPunished(v11, 1, 0) )
          return 0i64;
      }
    }
    else
    {
      v10 = (CPlayer *)pDst;
      if ( CPlayer::IsPunished((CPlayer *)pDst, 1, 0) )
        return 0i64;
    }
    v12 = ((int (__fastcall *)(CPlayer *))v14->vfptr->GetObjRace)(v14);
    v13 = v15->vfptr;
    v5 = ((int (__fastcall *)(CCharacter *))v13->GetObjRace)(v15);
    if ( v12 == v5 )
      return 4294967290i64;
  }
  return 0i64;
}
