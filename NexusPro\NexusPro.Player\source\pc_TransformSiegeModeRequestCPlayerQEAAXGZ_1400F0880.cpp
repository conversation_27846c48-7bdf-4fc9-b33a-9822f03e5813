/*
 * Function: ?pc_TransformSiegeModeRequest@CPlayer@@QEAAXG@Z
 * Address: 0x1400F0880
 */

void __fastcall CPlayer::pc_TransformSiegeModeRequest(CPlayer *this, unsigned __int16 wItemSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  char v4; // al@19
  int v5; // ecx@19
  __int64 v6; // [sp+0h] [bp-58h]@1
  char v7; // [sp+20h] [bp-38h]@4
  _STORAGE_LIST::_storage_con *pCon; // [sp+28h] [bp-30h]@4
  char *v9; // [sp+30h] [bp-28h]@4
  _base_fld *v10; // [sp+38h] [bp-20h]@4
  _base_fld *v11; // [sp+40h] [bp-18h]@4
  int nTableCode; // [sp+48h] [bp-10h]@19
  CPlayer *v13; // [sp+60h] [bp+8h]@1

  v13 = this;
  v2 = &v6;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v7 = 0;
  pCon = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                          (_STORAGE_LIST *)&v13->m_Param.m_dbInven.m_nListNum,
                                          wItemSerial);
  v9 = &v13->m_Param.m_dbEquip.m_pStorageList[6].m_bLoad;
  v10 = 0i64;
  v11 = 0i64;
  if ( pCon )
  {
    if ( pCon->m_byTableCode == 27 )
    {
      v10 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, pCon->m_wItemIndex);
      if ( CPlayer::IsSiegeMode(v13) )
      {
        v7 = 3;
      }
      else if ( pCon->m_dwDur )
      {
        if ( *v9 )
        {
          v11 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 6, *(_WORD *)(v9 + 3));
          if ( v13->m_pmWpn.byWpType == *(_DWORD *)&v10[3].m_strCode[4]
            && *(_DWORD *)&v11[6].m_strCode[12] == *(_DWORD *)&v10[3].m_strCode[8] )
          {
            if ( *(_DWORD *)&v11[6].m_strCode[12] == *(_DWORD *)&v10[3].m_strCode[8] )
            {
              v4 = CPlayerDB::GetRaceSexCode(&v13->m_Param);
              v5 = pCon->m_wItemIndex;
              nTableCode = pCon->m_byTableCode;
              if ( IsItemEquipCivil(nTableCode, v5, v4) )
              {
                if ( CPlayer::IsEffectableEquip(v13, pCon) )
                {
                  if ( CPlayer::IsRidingUnit(v13) )
                  {
                    v7 = 9;
                  }
                  else if ( pCon->m_bLock )
                  {
                    v7 = 10;
                  }
                }
                else
                {
                  v7 = 8;
                }
              }
              else
              {
                v7 = 7;
              }
            }
            else
            {
              v7 = 6;
            }
          }
          else
          {
            v7 = 6;
          }
        }
        else
        {
          v7 = 5;
        }
      }
      else
      {
        v7 = 4;
      }
    }
    else
    {
      v7 = 1;
    }
  }
  else
  {
    v7 = 1;
  }
  if ( v7 )
  {
    CPlayer::SendMsg_TransformSiegeModeResult(v13, v7);
  }
  else
  {
    v13->m_bIsSiegeActing = 1;
    CMyTimer::BeginTimer(&v13->m_tmrSiegeTime, 0x7D0u);
    CPlayer::SetSiege(v13, (_STORAGE_LIST::_db_con *)pCon);
  }
}
