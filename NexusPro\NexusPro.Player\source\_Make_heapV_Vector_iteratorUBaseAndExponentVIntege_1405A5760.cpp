/*
 * Function: ??$_Make_heap@V?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@std@@_JU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@YAXV?$_Vector_iterator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@V?$allocator@U?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@std@@@0@0PEA_JPEAU?$BaseAndExponent@VInteger@CryptoPP@@V12@@CryptoPP@@@Z
 * Address: 0x1405A5760
 */

int __fastcall std::_Make_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(__int64 a1)
{
  signed __int64 v1; // rax@1
  const struct CryptoPP::Integer *v2; // rax@3
  signed __int64 v4; // [sp+20h] [bp-D8h]@1
  __int64 v5; // [sp+28h] [bp-D0h]@1
  char v6; // [sp+30h] [bp-C8h]@3
  CryptoPP::Integer *v7; // [sp+80h] [bp-78h]@3
  char v8; // [sp+88h] [bp-70h]@3
  char v9; // [sp+A0h] [bp-58h]@3
  char *v10; // [sp+B8h] [bp-40h]@3
  __int64 v11; // [sp+C0h] [bp-38h]@1
  __int64 v12; // [sp+C8h] [bp-30h]@3
  __int64 v13; // [sp+D0h] [bp-28h]@3
  CryptoPP::Integer *v14; // [sp+D8h] [bp-20h]@3
  CryptoPP::Integer *v15; // [sp+E0h] [bp-18h]@3
  __int64 v16; // [sp+E8h] [bp-10h]@3
  __int64 v17; // [sp+100h] [bp+8h]@1

  v17 = a1;
  v11 = -2i64;
  LODWORD(v1) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator-();
  v4 = v1;
  v5 = v1 / 2;
  while ( v5 > 0 )
  {
    --v5;
    v7 = (CryptoPP::Integer *)&v6;
    v10 = &v9;
    v12 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator+(
            v17,
            (__int64)&v8,
            v5);
    v13 = v12;
    LODWORD(v2) = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::operator*();
    v14 = CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>(
            v7,
            v2);
    v15 = v14;
    v16 = std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>((__int64)v10);
    std::_Adjust_heap<std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>,__int64,CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>(
      v16,
      v5,
      v4,
      v15);
    std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  }
  std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
  return std::_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>::~_Vector_iterator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>,std::allocator<CryptoPP::BaseAndExponent<CryptoPP::Integer,CryptoPP::Integer>>>();
}
