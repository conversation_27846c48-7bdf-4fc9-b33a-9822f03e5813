/*
 * Function: ?pc_OffPart@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@@Z
 * Address: 0x1400AE4D0
 */

void __fastcall CPlayer::pc_OffPart(CPlayer *this, _STORAGE_POS_INDIV *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  _STORAGE_LIST::_db_con *v4; // rax@10
  __int64 v5; // [sp+0h] [bp-A8h]@1
  bool bDelete; // [sp+20h] [bp-88h]@19
  char *strErrorCodePos; // [sp+28h] [bp-80h]@19
  char v8; // [sp+30h] [bp-78h]@4
  _STORAGE_LIST *v9; // [sp+38h] [bp-70h]@4
  _STORAGE_LIST *v10; // [sp+40h] [bp-68h]@4
  void *Src; // [sp+48h] [bp-60h]@4
  _STORAGE_LIST::_db_con Dst; // [sp+58h] [bp-50h]@19
  CPlayer *v13; // [sp+B0h] [bp+8h]@1
  _STORAGE_POS_INDIV *v14; // [sp+B8h] [bp+10h]@1

  v14 = pItem;
  v13 = this;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v8 = 0;
  v9 = 0i64;
  v10 = (_STORAGE_LIST *)&v13->m_Param.m_dbInven.m_nListNum;
  Src = 0i64;
  if ( _effect_parameter::GetEff_State(&v13->m_EP, 20) )
  {
    v8 = 8;
  }
  else if ( _effect_parameter::GetEff_State(&v13->m_EP, 28) )
  {
    v8 = 8;
  }
  else if ( v14->byStorageCode != 1 && v14->byStorageCode != 2 )
  {
    v8 = 2;
  }
  else
  {
    v9 = v13->m_Param.m_pStoragePtr[v14->byStorageCode];
    v4 = _STORAGE_LIST::GetPtrFromSerial(v9, v14->wItemSerial);
    Src = v4;
    if ( v4 )
    {
      if ( *((_BYTE *)Src + 19) )
      {
        v8 = 11;
      }
      else if ( _STORAGE_LIST::GetIndexEmptyCon(v10) == 255 )
      {
        v8 = 3;
      }
    }
    else
    {
      v8 = 2;
    }
  }
  if ( !v8 )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    memcpy_0(&Dst, Src, 0x32ui64);
    strErrorCodePos = "CPlayer::pc_OffPart()";
    bDelete = 0;
    if ( !CPlayer::Emb_DelStorage(v13, v9->m_nListCode, *((_BYTE *)Src + 49), 0, 0, "CPlayer::pc_OffPart()") )
    {
      CPlayer::SendMsg_OffPartResult(v13, -1);
      return;
    }
    bDelete = 0;
    if ( !CPlayer::Emb_AddStorage(v13, v10->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0) )
    {
      bDelete = 0;
      CPlayer::Emb_AddStorage(v13, v9->m_nListCode, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 1, 0);
      v8 = 11;
      CPlayer::SendMsg_OffPartResult(v13, 11);
    }
    CPlayer::Emb_EquipLink(v13);
  }
  CPlayer::SendMsg_OffPartResult(v13, v8);
}
