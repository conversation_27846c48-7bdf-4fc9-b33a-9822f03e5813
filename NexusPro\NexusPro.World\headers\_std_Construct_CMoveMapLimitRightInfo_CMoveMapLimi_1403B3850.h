#pragma once
#ifndef _STD_CONSTRUCT_CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMI_1403B3850_H
#define _STD_CONSTRUCT_CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMI_1403B3850_H

// Auto-generated header for _std_Construct_CMoveMapLimitRightInfo_CMoveMapLimi_1403B3850.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _STD_CONSTRUCT_CMOVEMAPLIMITRIGHTINFO_CMOVEMAPLIMI_1403B3850_H
