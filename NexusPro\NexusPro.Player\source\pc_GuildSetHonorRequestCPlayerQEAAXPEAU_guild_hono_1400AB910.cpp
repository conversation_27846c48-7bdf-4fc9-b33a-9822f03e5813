/*
 * Function: ?pc_GuildSetHonorRequest@CPlayer@@QEAAXPEAU_guild_honor_set_request_clzo@@@Z
 * Address: 0x1400AB910
 */

void __fastcall CPlayer::pc_GuildSetHonorRequest(CPlayer *this, _guild_honor_set_request_clzo *pData)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@4
  unsigned int v5; // eax@4
  CHonorGuild *v6; // rax@6
  __int64 v7; // [sp+0h] [bp-38h]@1
  char v8; // [sp+20h] [bp-18h]@6
  unsigned int v9; // [sp+24h] [bp-14h]@4
  int v10; // [sp+28h] [bp-10h]@4
  int v11; // [sp+2Ch] [bp-Ch]@6
  CPlayer *v12; // [sp+40h] [bp+8h]@1
  _guild_honor_set_request_clzo *pRecv; // [sp+48h] [bp+10h]@1

  pRecv = pData;
  v12 = this;
  v2 = &v7;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v9 = CPlayerDB::GetCharSerial(&v12->m_Param);
  v10 = CPlayerDB::GetRaceCode(&v12->m_Param);
  v4 = CPvpUserAndGuildRankingSystem::Instance();
  v5 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v4, v10, 0);
  if ( v9 == v5 )
  {
    v11 = CPlayerDB::GetRaceCode(&v12->m_Param);
    v6 = CHonorGuild::Instance();
    v8 = CHonorGuild::SetNextHonorGuild(v6, v11, pRecv);
    CPlayer::SendMsg_GuildSetHonorResult(v12, v8);
  }
  else
  {
    CPlayer::SendMsg_GuildSetHonorResult(v12, 1);
  }
}
