#pragma once
#ifndef _DYNAMIC_ATEXIT_DESTRUCTOR_FOR__CMONSTERS_LOGTRACE_1406E85C0_H
#define _DYNAMIC_ATEXIT_DESTRUCTOR_FOR__CMONSTERS_LOGTRACE_1406E85C0_H

// Auto-generated header for _dynamic_atexit_destructor_for__CMonsters_logTrace_1406E85C0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
dynamic_atexit_destructor_for__CMonster::s_logTrace_Boss_BirthAndDeath__()
;

#endif // _DYNAMIC_ATEXIT_DESTRUCTOR_FOR__CMONSTERS_LOGTRACE_1406E85C0_H
