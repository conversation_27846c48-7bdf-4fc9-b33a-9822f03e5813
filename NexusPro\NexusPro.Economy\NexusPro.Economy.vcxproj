<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{72AC1220-58C8-4A84-BC21-E5D0B6CAF475}</ProjectGuid>
    <RootNamespace>NexusProEconomy</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusPro.Economy</ProjectName>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  
  <ImportGroup Label="Shared">
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <PropertyGroup Label="UserMacros" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Debug\</OutDir>
    <IntDir>$(SolutionDir)obj\Debug\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Release\</OutDir>
    <IntDir>$(SolutionDir)obj\Release\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemGroup>
    <ClCompile Include="source\0CMoneySupplyMgrQEAAXZ_14042B630.cpp" />
    <ClCompile Include="source\0_economy_history_dataQEAAXZ_140205870.cpp" />
    <ClCompile Include="source\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.cpp" />
    <ClCompile Include="source\1CMoneySupplyMgrUEAAXZ_14042B660.cpp" />
    <ClCompile Include="source\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.cpp" />
    <ClCompile Include="source\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.cpp" />
    <ClCompile Include="source\CanAddMoneyForMaxLimGoldYA_N_K0Z_14003F190.cpp" />
    <ClCompile Include="source\CanAddMoneyForMaxLimMoneyYA_N_K0Z_14003F110.cpp" />
    <ClCompile Include="source\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.cpp" />
    <ClCompile Include="source\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.cpp" />
    <ClCompile Include="source\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.cpp" />
    <ClCompile Include="source\ContinueModalCWndUEAAHXZ_0_1404DBC46.cpp" />
    <ClCompile Include="source\DoModalCDialogUEAA_JXZ_0_1404DBD66.cpp" />
    <ClCompile Include="source\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.cpp" />
    <ClCompile Include="source\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.cpp" />
    <ClCompile Include="source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.cpp" />
    <ClCompile Include="source\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.cpp" />
    <ClCompile Include="source\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.cpp" />
    <ClCompile Include="source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.cpp" />
    <ClCompile Include="source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.cpp" />
    <ClCompile Include="source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.cpp" />
    <ClCompile Include="source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.cpp" />
    <ClCompile Include="source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.cpp" />
    <ClCompile Include="source\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.cpp" />
    <ClCompile Include="source\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.cpp" />
    <ClCompile Include="source\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.cpp" />
    <ClCompile Include="source\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.cpp" />
    <ClCompile Include="source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.cpp" />
    <ClCompile Include="source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.cpp" />
    <ClCompile Include="source\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.cpp" />
    <ClCompile Include="source\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.cpp" />
    <ClCompile Include="source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.cpp" />
    <ClCompile Include="source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.cpp" />
    <ClCompile Include="source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.cpp" />
    <ClCompile Include="source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.cpp" />
    <ClCompile Include="source\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.cpp" />
    <ClCompile Include="source\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.cpp" />
    <ClCompile Include="source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.cpp" />
    <ClCompile Include="source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.cpp" />
    <ClCompile Include="source\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406502F0.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0CB0.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0FF0.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A11E0.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1520.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A16E0.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1A20.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A5D40.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.cpp" />
    <ClCompile Include="source\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.cpp" />
    <ClCompile Include="source\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.cpp" />
    <ClCompile Include="source\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.cpp" />
    <ClCompile Include="source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.cpp" />
    <ClCompile Include="source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.cpp" />
    <ClCompile Include="source\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.cpp" />
    <ClCompile Include="source\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.cpp" />
    <ClCompile Include="source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.cpp" />
    <ClCompile Include="source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.cpp" />
    <ClCompile Include="source\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.cpp" />
    <ClCompile Include="source\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.cpp" />
    <ClCompile Include="source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.cpp" />
    <ClCompile Include="source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.cpp" />
    <ClCompile Include="source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.cpp" />
    <ClCompile Include="source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.cpp" />
    <ClCompile Include="source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.cpp" />
    <ClCompile Include="source\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.cpp" />
    <ClCompile Include="source\eAddDalantYAXHHZ_1402A41B0.cpp" />
    <ClCompile Include="source\eGetDalantYANHZ_1402A4390.cpp" />
    <ClCompile Include="source\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.cpp" />
    <ClCompile Include="source\eGetOldDalantYANHZ_1402A47A0.cpp" />
    <ClCompile Include="source\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.cpp" />
    <ClCompile Include="source\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.cpp" />
    <ClCompile Include="source\EndModalStateCWndUEAAXXZ_0_1404DBCFA.cpp" />
    <ClCompile Include="source\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.cpp" />
    <ClCompile Include="source\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.cpp" />
    <ClCompile Include="source\FindAllFileYAHPEADPEAPEADHZ_140480410.cpp" />
    <ClCompile Include="source\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.cpp" />
    <ClCompile Include="source\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.cpp" />
    <ClCompile Include="source\GetTotalDalantCGuildQEAANXZ_1400AD2A0.cpp" />
    <ClCompile Include="source\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.cpp" />
    <ClCompile Include="source\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.cpp" />
    <ClCompile Include="source\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.cpp" />
    <ClCompile Include="source\j_0CMoneySupplyMgrQEAAXZ_140004895.cpp" />
    <ClCompile Include="source\j_0_economy_history_dataQEAAXZ_140009719.cpp" />
    <ClCompile Include="source\j_0_guild_money_io_download_zoclQEAAXZ_140009318.cpp" />
    <ClCompile Include="source\j_1CMoneySupplyMgrUEAAXZ_140010FEB.cpp" />
    <ClCompile Include="source\j_CanAddMoneyForMaxLimGoldYA_N_K0Z_140003C15.cpp" />
    <ClCompile Include="source\j_CanAddMoneyForMaxLimMoneyYA_N_K0Z_140011FE5.cpp" />
    <ClCompile Include="source\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.cpp" />
    <ClCompile Include="source\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.cpp" />
    <ClCompile Include="source\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.cpp" />
    <ClCompile Include="source\j_eAddDalantYAXHHZ_14000650A.cpp" />
    <ClCompile Include="source\j_eGetDalantYANHZ_140012CB5.cpp" />
    <ClCompile Include="source\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.cpp" />
    <ClCompile Include="source\j_eGetOldDalantYANHZ_14001212F.cpp" />
    <ClCompile Include="source\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.cpp" />
    <ClCompile Include="source\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.cpp" />
    <ClCompile Include="source\j_FindAllFileYAHPEADPEAPEADHZ_14000E787.cpp" />
    <ClCompile Include="source\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.cpp" />
    <ClCompile Include="source\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.cpp" />
    <ClCompile Include="source\j_GetTotalDalantCGuildQEAANXZ_1400133CC.cpp" />
    <ClCompile Include="source\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.cpp" />
    <ClCompile Include="source\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.cpp" />
    <ClCompile Include="source\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.cpp" />
    <ClCompile Include="source\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.cpp" />
    <ClCompile Include="source\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.cpp" />
    <ClCompile Include="source\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.cpp" />
    <ClCompile Include="source\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.cpp" />
    <ClCompile Include="source\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.cpp" />
    <ClCompile Include="source\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.cpp" />
    <ClCompile Include="source\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.cpp" />
    <ClCompile Include="source\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.cpp" />
    <ClCompile Include="source\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.cpp" />
    <ClCompile Include="source\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.cpp" />
    <ClCompile Include="source\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.cpp" />
    <ClCompile Include="source\j_size_log_sheet_economyQEAAHXZ_140011DBF.cpp" />
    <ClCompile Include="source\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.cpp" />
    <ClCompile Include="source\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.cpp" />
    <ClCompile Include="source\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.cpp" />
    <ClCompile Include="source\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.cpp" />
    <ClCompile Include="source\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.cpp" />
    <ClCompile Include="source\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.cpp" />
    <ClCompile Include="source\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.cpp" />
    <ClCompile Include="source\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.cpp" />
    <ClCompile Include="source\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.cpp" />
    <ClCompile Include="source\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.cpp" />
    <ClCompile Include="source\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.cpp" />
    <ClCompile Include="source\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.cpp" />
    <ClCompile Include="source\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.cpp" />
    <ClCompile Include="source\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.cpp" />
    <ClCompile Include="source\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.cpp" />
    <ClCompile Include="source\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.cpp" />
    <ClCompile Include="source\j__ReadEconomyIniFileYA_NXZ_140007F31.cpp" />
    <ClCompile Include="source\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.cpp" />
    <ClCompile Include="source\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.cpp" />
    <ClCompile Include="source\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.cpp" />
    <ClCompile Include="source\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.cpp" />
    <ClCompile Include="source\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.cpp" />
    <ClCompile Include="source\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.cpp" />
    <ClCompile Include="source\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.cpp" />
    <ClCompile Include="source\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.cpp" />
    <ClCompile Include="source\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.cpp" />
    <ClCompile Include="source\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.cpp" />
    <ClCompile Include="source\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.cpp" />
    <ClCompile Include="source\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.cpp" />
    <ClCompile Include="source\size_guild_money_io_download_zoclQEAAHXZ_14025D430.cpp" />
    <ClCompile Include="source\size_log_sheet_economyQEAAHXZ_1402A5CE0.cpp" />
    <ClCompile Include="source\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.cpp" />
    <ClCompile Include="source\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.cpp" />
    <ClCompile Include="source\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.cpp" />
    <ClCompile Include="source\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.cpp" />
    <ClCompile Include="source\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.cpp" />
    <ClCompile Include="source\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.cpp" />
    <ClCompile Include="source\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.cpp" />
    <ClCompile Include="source\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.cpp" />
    <ClCompile Include="source\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.cpp" />
    <ClCompile Include="source\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.cpp" />
    <ClCompile Include="source\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.cpp" />
    <ClCompile Include="source\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.cpp" />
    <ClCompile Include="source\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.cpp" />
    <ClCompile Include="source\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.cpp" />
    <ClCompile Include="source\_CMainThreadcheck_min_max_guild_money__1_dtor0_1401B16B0.cpp" />
    <ClCompile Include="source\_CMoneySupplyMgrInstance__1_dtor0_140095100.cpp" />
    <ClCompile Include="source\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.cpp" />
    <ClCompile Include="source\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.cpp" />
    <ClCompile Include="source\_ReadEconomyIniFileYA_NXZ_1402A5040.cpp" />
    <ClCompile Include="source\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.cpp" />
    <ClCompile Include="source\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.cpp" />
    <ClCompile Include="source\__HrLoadAllImportsForDll_14067693C.cpp" />
  </ItemGroup>
  
  <ItemGroup>
    <ClInclude Include="headers\0CMoneySupplyMgrQEAAXZ_14042B630.h" />
    <ClInclude Include="headers\0_economy_history_dataQEAAXZ_140205870.h" />
    <ClInclude Include="headers\0_guild_money_io_download_zoclQEAAXZ_14025CDF0.h" />
    <ClInclude Include="headers\1CMoneySupplyMgrUEAAXZ_14042B660.h" />
    <ClInclude Include="headers\BeginModalStateCFrameWndUEAAXXZ_0_1404DBDF6.h" />
    <ClInclude Include="headers\BeginModalStateCWndUEAAXXZ_0_1404DBCF4.h" />
    <ClInclude Include="headers\CanAddMoneyForMaxLimGoldYA_N_K0Z_14003F190.h" />
    <ClInclude Include="headers\CanAddMoneyForMaxLimMoneyYA_N_K0Z_14003F110.h" />
    <ClInclude Include="headers\CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_14033B300.h" />
    <ClInclude Include="headers\check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1401B1340.h" />
    <ClInclude Include="headers\ContinueModalCPropertySheetUEAAHXZ_0_1404DC2B2.h" />
    <ClInclude Include="headers\ContinueModalCWndUEAAHXZ_0_1404DBC46.h" />
    <ClInclude Include="headers\DoModalCDialogUEAA_JXZ_0_1404DBD66.h" />
    <ClInclude Include="headers\DoModalCPropertySheetUEAA_JXZ_0_1404DC2CA.h" />
    <ClInclude Include="headers\DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14025F0C0.h" />
    <ClInclude Include="headers\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14057A6B0.h" />
    <ClInclude Include="headers\dtor000UMSSchedulerProxydetailsConcurrencyQEAAPEAU_14062D110.h" />
    <ClInclude Include="headers\dtor001SchedulingRingdetailsConcurrencyQEAAXZ4HA_140654370.h" />
    <ClInclude Include="headers\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550C80.h" />
    <ClInclude Include="headers\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140550D10.h" />
    <ClInclude Include="headers\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140555BF0.h" />
    <ClInclude Include="headers\dtor001ThreadProxyFactoryManagerdetailsConcurrency_14055CD30.h" />
    <ClInclude Include="headers\dtor001ThreadProxyFactoryManagerdetailsConcurrency_1405EF210.h" />
    <ClInclude Include="headers\dtor001ThreadProxyFactoryManagerdetailsConcurrency_140651DE0.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A0E00.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1330.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405A1830.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_1405FA460.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406041F0.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_1406060C0.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_140616110.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_140658580.h" />
    <ClInclude Include="headers\dtor00AddRunnableContextScheduleGroupSegmentBasede_140659BE0.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_140597C40.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A58D0.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6240.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6880.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A69C0.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_1405A6E00.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_1405AA1C0.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_1406030B0.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_1406143E0.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_14065A830.h" />
    <ClInclude Include="headers\dtor00AddToRunnablesInternalContextBasedetailsConc_14065B9A0.h" />
    <ClInclude Include="headers\dtor00AllocateScheduleGroupSchedulingRingdetailsCo_140588CB0.h" />
    <ClInclude Include="headers\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_140599460.h" />
    <ClInclude Include="headers\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A040.h" />
    <ClInclude Include="headers\dtor00do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A910.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405501C0.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0C90.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A0FD0.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A11C0.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1500.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A16C0.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A1A00.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405A9800.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_1405AA220.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_14060D1F0.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_140613960.h" />
    <ClInclude Include="headers\dtor00FoundAvailableVirtualProcessorSchedulerBased_14064F020.h" />
    <ClInclude Include="headers\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14054F220.h" />
    <ClInclude Include="headers\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14060D510.h" />
    <ClInclude Include="headers\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_14064E810.h" />
    <ClInclude Include="headers\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140658560.h" />
    <ClInclude Include="headers\dtor00MoveCompletionListToRunnablesUMSThreadSchedu_140659BC0.h" />
    <ClInclude Include="headers\dtor00wait_for_multipleeventConcurrencySA_KPEAPEAV_140634F80.h" />
    <ClInclude Include="headers\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F60.h" />
    <ClInclude Include="headers\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406290F0.h" />
    <ClInclude Include="headers\dtor00wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_1406502F0.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0CB0.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A0FF0.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A11E0.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1520.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A16E0.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A1A20.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A5D40.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A6C30.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_1405A7050.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140605F10.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140617CC0.h" />
    <ClInclude Include="headers\dtor00_CancelVshared_ptrU_ExceptionHolderdetailsCo_140656560.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532D0.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D00.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0C0.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA520.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA630.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B30.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E60.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_1406434E0.h" />
    <ClInclude Include="headers\dtor00_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647150.h" />
    <ClInclude Include="headers\dtor10AllocateScheduleGroupSchedulingRingdetailsCo_140588CD0.h" />
    <ClInclude Include="headers\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_140599480.h" />
    <ClInclude Include="headers\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A060.h" />
    <ClInclude Include="headers\dtor10do_getmoney_getDVistreambuf_iteratorDUchar_t_14059A930.h" />
    <ClInclude Include="headers\dtor10wait_for_multipleeventConcurrencySA_KPEAPEAV_140634FA0.h" />
    <ClInclude Include="headers\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140617F80.h" />
    <ClInclude Include="headers\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140629110.h" />
    <ClInclude Include="headers\dtor10wait_for_oneagentConcurrencySAX_KPEAPEAV12AE_140650310.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405532F0.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140579D20.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA0E0.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA540.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA650.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B50.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633E80.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643500.h" />
    <ClInclude Include="headers\dtor10_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647170.h" />
    <ClInclude Include="headers\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA560.h" />
    <ClInclude Include="headers\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_1405EA670.h" />
    <ClInclude Include="headers\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140631B70.h" />
    <ClInclude Include="headers\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140633EA0.h" />
    <ClInclude Include="headers\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140643520.h" />
    <ClInclude Include="headers\dtor20_Reset_Greedy_nodeW4agent_statusConcurrencyC_140647190.h" />
    <ClInclude Include="headers\eAddDalantYAXHHZ_1402A41B0.h" />
    <ClInclude Include="headers\eGetDalantYANHZ_1402A4390.h" />
    <ClInclude Include="headers\eGetGuideHistoryYAPEAU_economy_history_dataXZ_1402A48C0.h" />
    <ClInclude Include="headers\eGetOldDalantYANHZ_1402A47A0.h" />
    <ClInclude Include="headers\EndModalLoopCWndUEAAXHZ_0_1404DBC4C.h" />
    <ClInclude Include="headers\EndModalStateCFrameWndUEAAXXZ_0_1404DBDFC.h" />
    <ClInclude Include="headers\EndModalStateCWndUEAAXXZ_0_1404DBCFA.h" />
    <ClInclude Include="headers\ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEADZ_1401D2D10.h" />
    <ClInclude Include="headers\ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEADZ_1401D2DC0.h" />
    <ClInclude Include="headers\FindAllFileYAHPEADPEAPEADHZ_140480410.h" />
    <ClInclude Include="headers\GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14025D640.h" />
    <ClInclude Include="headers\GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_1400AD130.h" />
    <ClInclude Include="headers\GetTotalDalantCGuildQEAANXZ_1400AD2A0.h" />
    <ClInclude Include="headers\GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_1401C8410.h" />
    <ClInclude Include="headers\InstanceCMoneySupplyMgrSAPEAV1XZ_140095070.h" />
    <ClInclude Include="headers\IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140253230.h" />
    <ClInclude Include="headers\j_0CMoneySupplyMgrQEAAXZ_140004895.h" />
    <ClInclude Include="headers\j_0_economy_history_dataQEAAXZ_140009719.h" />
    <ClInclude Include="headers\j_0_guild_money_io_download_zoclQEAAXZ_140009318.h" />
    <ClInclude Include="headers\j_1CMoneySupplyMgrUEAAXZ_140010FEB.h" />
    <ClInclude Include="headers\j_CanAddMoneyForMaxLimGoldYA_N_K0Z_140003C15.h" />
    <ClInclude Include="headers\j_CanAddMoneyForMaxLimMoneyYA_N_K0Z_140011FE5.h" />
    <ClInclude Include="headers\j_CheckMaxGuildMoneyCGuildRankingAEAAXKPEADPEAN1Z_140004BFB.h" />
    <ClInclude Include="headers\j_check_min_max_guild_moneyCMainThreadQEAAEKPEAN0Z_1400048B3.h" />
    <ClInclude Include="headers\j_DQSCompleteInAtradTaxMoneyCHonorGuildQEAAXPEADZ_14000EA52.h" />
    <ClInclude Include="headers\j_eAddDalantYAXHHZ_14000650A.h" />
    <ClInclude Include="headers\j_eGetDalantYANHZ_140012CB5.h" />
    <ClInclude Include="headers\j_eGetGuideHistoryYAPEAU_economy_history_dataXZ_14000F0C9.h" />
    <ClInclude Include="headers\j_eGetOldDalantYANHZ_14001212F.h" />
    <ClInclude Include="headers\j_ExchangeDalantForGoldRequestCNetworkEXAEAA_NHPEA_14000A1FF.h" />
    <ClInclude Include="headers\j_ExchangeGoldForDalantRequestCNetworkEXAEAA_NHPEA_14000D5F8.h" />
    <ClInclude Include="headers\j_FindAllFileYAHPEADPEAPEADHZ_14000E787.h" />
    <ClInclude Include="headers\j_GetEmblemDalantGuildCreateEventInfoQEAAKXZ_14000703B.h" />
    <ClInclude Include="headers\j_GetEstConsumeDalantGuildCreateEventInfoQEAAKXZ_14000F6E6.h" />
    <ClInclude Include="headers\j_GetTotalDalantCGuildQEAANXZ_1400133CC.h" />
    <ClInclude Include="headers\j_GuildPushMoneyRequestCNetworkEXAEAA_NHPEADZ_140003805.h" />
    <ClInclude Include="headers\j_InstanceCMoneySupplyMgrSAPEAV1XZ_14000EF11.h" />
    <ClInclude Include="headers\j_IOMoneyCGuildQEAAXPEADKNNNNPEAE_NZ_140010483.h" />
    <ClInclude Include="headers\j_LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io__14000EC96.h" />
    <ClInclude Include="headers\j_LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_140011306.h" />
    <ClInclude Include="headers\j_ManagePopGuildMoneyCGuildQEAAEKKKZ_14000C0FE.h" />
    <ClInclude Include="headers\j_pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1400027C0.h" />
    <ClInclude Include="headers\j_PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140013EEE.h" />
    <ClInclude Include="headers\j_push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_14000E1AB.h" />
    <ClInclude Include="headers\j_qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQues_140005407.h" />
    <ClInclude Include="headers\j_SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_140002351.h" />
    <ClInclude Include="headers\j_SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member__14000C707.h" />
    <ClInclude Include="headers\j_SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_140001203.h" />
    <ClInclude Include="headers\j_size_guild_money_io_download_zoclQEAAHXZ_140006046.h" />
    <ClInclude Include="headers\j_size_log_sheet_economyQEAAHXZ_140011DBF.h" />
    <ClInclude Include="headers\j_size_MONEY_SUPPLY_DATAQEAAHXZ_14000D620.h" />
    <ClInclude Include="headers\j_size_qry_case_inputgmoneyQEAAHXZ_140007DD3.h" />
    <ClInclude Include="headers\j_size_qry_case_outputgmoneyQEAAHXZ_140011171.h" />
    <ClInclude Include="headers\j_SubChargeCostAutoMineMachineQEAAXEPEADZ_1400062C6.h" />
    <ClInclude Include="headers\j_TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEA_14000BDB6.h" />
    <ClInclude Include="headers\j_TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_140010BC2.h" />
    <ClInclude Include="headers\j_UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14000CBE9.h" />
    <ClInclude Include="headers\j_UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_140013665.h" />
    <ClInclude Include="headers\j_UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_140001CF3.h" />
    <ClInclude Include="headers\j_UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHP_140013DAE.h" />
    <ClInclude Include="headers\j_UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEK_1400076D0.h" />
    <ClInclude Include="headers\j_UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEH_1400032BF.h" />
    <ClInclude Include="headers\j_UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_1400030B2.h" />
    <ClInclude Include="headers\j_UpdateUnitRepairingChargesDataCMoneySupplyMgrQEA_140004903.h" />
    <ClInclude Include="headers\j__ECMoneySupplyMgrUEAAPEAXIZ_0_1400110A9.h" />
    <ClInclude Include="headers\j__ECMoneySupplyMgrUEAAPEAXIZ_14000A745.h" />
    <ClInclude Include="headers\j__ReadEconomyIniFileYA_NXZ_140007F31.h" />
    <ClInclude Include="headers\j__UpdateNewEconomyYAXPEAU_economy_calc_dataZ_14000D0D5.h" />
    <ClInclude Include="headers\j___CheckCond_DalantCQuestMgrQEAA_NEHZ_14000F6D7.h" />
    <ClInclude Include="headers\LoadGuildMoneyIOInfoCGuildRankingAEAA_NKPEAU_io_mo_14033B0A0.h" />
    <ClInclude Include="headers\LoopMoneySupplyCMoneySupplyMgrQEAAXXZ_14042B6E0.h" />
    <ClInclude Include="headers\ManagePopGuildMoneyCGuildQEAAEKKKZ_140258EE0.h" />
    <ClInclude Include="headers\pop_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_1402495E0.h" />
    <ClInclude Include="headers\PushHistory_IOMoneyCGuildQEAAX_NPEADKNNNNPEAEZ_140254330.h" />
    <ClInclude Include="headers\push_moneyCMgrGuildHistoryQEAAXPEADKHHNN0Z_140249510.h" />
    <ClInclude Include="headers\qc_DalantYA_NPEAUstrFILEPEAVCDarkHoleDungeonQuestS_140274740.h" />
    <ClInclude Include="headers\SetConsumeDalantFreeGuildCreateEventInfoIEAAX_NZ_14025A6E0.h" />
    <ClInclude Include="headers\SetGuildCGuildQEAAXKEEPEAD0KKHPEAU_guild_member_in_140251E40.h" />
    <ClInclude Include="headers\SetGuildMaintainMoneyCHonorGuildQEAAXEKKZ_14025EEA0.h" />
    <ClInclude Include="headers\size_guild_money_io_download_zoclQEAAHXZ_14025D430.h" />
    <ClInclude Include="headers\size_log_sheet_economyQEAAHXZ_1402A5CE0.h" />
    <ClInclude Include="headers\size_MONEY_SUPPLY_DATAQEAAHXZ_140430790.h" />
    <ClInclude Include="headers\size_qry_case_inputgmoneyQEAAHXZ_1400AD290.h" />
    <ClInclude Include="headers\size_qry_case_outputgmoneyQEAAHXZ_1400AD4C0.h" />
    <ClInclude Include="headers\SubChargeCostAutoMineMachineQEAAXEPEADZ_1402D25E0.h" />
    <ClInclude Include="headers\TrunkCreateCostIsFreeRequestCNetworkEXAEAA_NHPEADZ_1401D63E0.h" />
    <ClInclude Include="headers\TrunkIoMoneyRequestCNetworkEXAEAA_NHPEADZ_1401D6140.h" />
    <ClInclude Include="headers\UpdateBuyDataCMoneySupplyMgrQEAAXEHPEADKZ_14042C4C0.h" />
    <ClInclude Include="headers\UpdateBuyUnitDataCMoneySupplyMgrQEAAXHKZ_14042F470.h" />
    <ClInclude Include="headers\UpdateFeeMoneyDataCMoneySupplyMgrQEAAXEHKZ_14042F1B0.h" />
    <ClInclude Include="headers\UpdateGateRewardMoneyDataCMoneySupplyMgrQEAAXEHPEA_14042E520.h" />
    <ClInclude Include="headers\UpdateHonorGuildMoneyDataCMoneySupplyMgrQEAAXEEKZ_14042F2D0.h" />
    <ClInclude Include="headers\UpdateQuestRewardMoneyDataCMoneySupplyMgrQEAAXEHPE_14042D890.h" />
    <ClInclude Include="headers\UpdateSellDataCMoneySupplyMgrQEAAXEHPEADKZ_14042B7D0.h" />
    <ClInclude Include="headers\UpdateUnitRepairingChargesDataCMoneySupplyMgrQEAAX_14042F530.h" />
    <ClInclude Include="headers\_CMainThreadcheck_min_max_guild_money__1_dtor0_1401B16B0.h" />
    <ClInclude Include="headers\_CMoneySupplyMgrInstance__1_dtor0_140095100.h" />
    <ClInclude Include="headers\_ECMoneySupplyMgrUEAAPEAXIZ_1404306D0.h" />
    <ClInclude Include="headers\_FromImplcancellation_tokenConcurrencySAAV12PEAV_C_14057B7F0.h" />
    <ClInclude Include="headers\_ReadEconomyIniFileYA_NXZ_1402A5040.h" />
    <ClInclude Include="headers\_UpdateNewEconomyYAXPEAU_economy_calc_dataZ_1402A48E0.h" />
    <ClInclude Include="headers\__CheckCond_DalantCQuestMgrQEAA_NEHZ_140288770.h" />
    <ClInclude Include="headers\__HrLoadAllImportsForDll_14067693C.h" />
  </ItemGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>