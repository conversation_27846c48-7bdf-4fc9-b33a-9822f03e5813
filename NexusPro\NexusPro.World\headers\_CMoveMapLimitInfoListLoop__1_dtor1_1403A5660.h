#pragma once
#ifndef _CMOVEMAPLIMITINFOLISTLOOP__1_DTOR1_1403A5660_H
#define _CMOVEMAPLIMITINFOLISTLOOP__1_DTOR1_1403A5660_H

// Auto-generated header for _CMoveMapLimitInfoListLoop__1_dtor1_1403A5660.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_1(__int64 a1, __int64 a2)
;

#endif // _CMOVEMAPLIMITINFOLISTLOOP__1_DTOR1_1403A5660_H
