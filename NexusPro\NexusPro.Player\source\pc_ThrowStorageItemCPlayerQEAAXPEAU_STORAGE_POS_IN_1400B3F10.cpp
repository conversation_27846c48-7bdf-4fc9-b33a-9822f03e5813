/*
 * Function: ?pc_ThrowStorageItem@CPlayer@@QEAAXPEAU_STORAGE_POS_INDIV@@@Z
 * Address: 0x1400B3F10
 */

void __fastcall CPlayer::pc_ThrowStorageItem(CPlayer *this, _STORAGE_POS_INDIV *pItem)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CNationSettingManager *v4; // rax@13
  int v5; // eax@27
  _map_fld *v6; // rdx@30
  __int64 v7; // [sp+0h] [bp-D8h]@1
  CCharacter *pThrower; // [sp+20h] [bp-B8h]@27
  char byCreateCode[8]; // [sp+28h] [bp-B0h]@27
  CMapData *pMap; // [sp+30h] [bp-A8h]@25
  unsigned __int16 wLayerIndex; // [sp+38h] [bp-A0h]@25
  float *pStdPos; // [sp+40h] [bp-98h]@25
  bool bHide; // [sp+48h] [bp-90h]@25
  char v14; // [sp+50h] [bp-88h]@4
  _STORAGE_LIST *v15; // [sp+58h] [bp-80h]@4
  void *Src; // [sp+60h] [bp-78h]@4
  _STORAGE_LIST::_db_con Dst; // [sp+78h] [bp-60h]@23
  CItemBox *v18; // [sp+B8h] [bp-20h]@25
  __int16 *pDest1; // [sp+C0h] [bp-18h]@13
  int n; // [sp+C8h] [bp-10h]@30
  CPlayer *v21; // [sp+E0h] [bp+8h]@1
  _STORAGE_POS_INDIV *v22; // [sp+E8h] [bp+10h]@1

  v22 = pItem;
  v21 = this;
  v2 = &v7;
  for ( i = 52i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v14 = 0;
  v15 = v21->m_Param.m_pStoragePtr[pItem->byStorageCode];
  Src = 0i64;
  if ( v21->m_pUserDB )
  {
    if ( _effect_parameter::GetEff_State(&v21->m_EP, 20) )
    {
      v14 = 5;
    }
    else if ( _effect_parameter::GetEff_State(&v21->m_EP, 28) )
    {
      v14 = 5;
    }
    else
    {
      Src = _STORAGE_LIST::GetPtrFromSerial(v15, v22->wItemSerial);
      if ( Src )
      {
        if ( *((_BYTE *)Src + 19) )
        {
          v14 = 9;
        }
        else
        {
          pDest1 = &v21->m_pUserDB->m_BillingInfo.iType;
          v4 = CTSingleton<CNationSettingManager>::Instance();
          if ( CNationSettingManager::IsPersonalFreeFixedAmountBillingType(v4, pDest1, 0i64) )
          {
            v14 = 9;
          }
          else if ( IsOverLapItem(*((_BYTE *)Src + 1)) && (unsigned __int64)v22->byNum > *(_QWORD *)((char *)Src + 5) )
          {
            CPlayer::SendMsg_AdjustAmountInform(v21, v22->byStorageCode, v22->wItemSerial, *(_DWORD *)((char *)Src + 5));
            v14 = 3;
          }
          else if ( *((_BYTE *)Src + 1) == 19 )
          {
            v14 = 4;
          }
          else if ( !IsGroundableItem(*((_BYTE *)Src + 1), *(_WORD *)((char *)Src + 3)) )
          {
            v14 = 4;
          }
        }
      }
      else
      {
        v14 = 2;
      }
    }
    if ( !v14 )
    {
      _STORAGE_LIST::_db_con::_db_con(&Dst);
      memcpy_0(&Dst, Src, 0x32ui64);
      if ( IsOverLapItem(*((_BYTE *)Src + 1)) )
        Dst.m_dwDur = v22->byNum;
      v18 = 0i64;
      bHide = 0;
      pStdPos = v21->m_fCurPos;
      wLayerIndex = v21->m_wMapLayerIndex;
      pMap = v21->m_pCurMap;
      v18 = CreateItemBox(&Dst, 0i64, 0xFFFFFFFF, 0, (CCharacter *)&v21->vfptr, 1, pMap, wLayerIndex, v21->m_fCurPos, 0);
      if ( v18 )
      {
        if ( IsOverLapItem(*((_BYTE *)Src + 1)) )
        {
          v5 = -v22->byNum;
          byCreateCode[0] = 0;
          LOBYTE(pThrower) = 0;
          CPlayer::Emb_AlterDurPoint(v21, v15->m_nListCode, *((_BYTE *)Src + 49), v5, 0, 0);
        }
        else
        {
          *(_QWORD *)byCreateCode = "CPlayer::pc_ThrowStorageItem()";
          LOBYTE(pThrower) = 1;
          if ( !CPlayer::Emb_DelStorage(
                  v21,
                  v15->m_nListCode,
                  *((_BYTE *)Src + 49),
                  0,
                  1,
                  "CPlayer::pc_ThrowStorageItem()") )
          {
            CItemBox::Destroy(v18);
            CPlayer::SendMsg_ThrowStorageResult(v21, -1);
            return;
          }
        }
        v6 = v21->m_pCurMap->m_pMapSet;
        n = v21->m_ObjID.m_wIndex;
        CMgrAvatorItemHistory::throw_ground_item(
          &CPlayer::s_MgrItemHistory,
          n,
          &Dst,
          v6->m_strCode,
          v21->m_fCurPos,
          v21->m_szItemHistoryFileName);
      }
      else
      {
        v14 = 4;
      }
    }
    CPlayer::SendMsg_ThrowStorageResult(v21, v14);
  }
}
