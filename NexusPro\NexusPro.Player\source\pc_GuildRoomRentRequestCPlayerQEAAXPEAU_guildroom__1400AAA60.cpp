/*
 * Function: ?pc_GuildRoomRentRequest@CPlayer@@QEAAXPEAU_guildroom_rent_request_clzo@@@Z
 * Address: 0x1400AAA60
 */

void __usercall CPlayer::pc_GuildRoomRentRequest(CPlayer *this@<rcx>, _guildroom_rent_request_clzo *pProtocol@<rdx>, double a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char v5; // al@17
  CGuildRoomSystem *v6; // rax@19
  CGuildRoomSystem *v7; // rax@21
  int v8; // eax@23
  CGuildRoomSystem *v9; // rax@23
  CItemStoreManager *v10; // rax@23
  CItemStoreManager *v11; // rax@23
  __int64 v12; // [sp+0h] [bp-128h]@1
  unsigned int dwGuildSerial[2]; // [sp+20h] [bp-108h]@21
  tagTIMESTAMP_STRUCT *ts; // [sp+28h] [bp-100h]@21
  bool bRestore; // [sp+30h] [bp-F8h]@21
  char v16; // [sp+40h] [bp-E8h]@4
  char v17; // [sp+41h] [bp-E7h]@4
  unsigned __int8 v18; // [sp+42h] [bp-E6h]@4
  CGuild *v19; // [sp+48h] [bp-E0h]@10
  __int64 v20; // [sp+50h] [bp-D8h]@23
  __int64 v21; // [sp+58h] [bp-D0h]@23
  char v22; // [sp+64h] [bp-C4h]@23
  char v23; // [sp+65h] [bp-C3h]@23
  char v24; // [sp+66h] [bp-C2h]@23
  char v25; // [sp+67h] [bp-C1h]@23
  _qry_case_outputgmoney v26; // [sp+90h] [bp-98h]@23
  CMapData *v27; // [sp+E8h] [bp-40h]@23
  CMapItemStoreList *pDest; // [sp+F0h] [bp-38h]@23
  CMapItemStoreList *v29; // [sp+F8h] [bp-30h]@23
  int v30; // [sp+108h] [bp-20h]@21
  int v31; // [sp+10Ch] [bp-1Ch]@23
  unsigned __int64 v32; // [sp+110h] [bp-18h]@4
  CPlayer *v33; // [sp+130h] [bp+8h]@1
  _guildroom_rent_request_clzo *v34; // [sp+138h] [bp+10h]@1

  v34 = pProtocol;
  v33 = this;
  v3 = &v12;
  for ( i = 72i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v32 = (unsigned __int64)&v12 ^ _security_cookie;
  v16 = 0;
  v17 = 0;
  v18 = pProtocol->byRoomType;
  if ( (signed int)v18 < 2 )
  {
    if ( v33->m_Param.m_pGuild )
    {
      if ( pProtocol->dwGuildSerial == v33->m_Param.m_pGuild->m_dwSerial )
      {
        v19 = v33->m_Param.m_pGuild;
        if ( v33->m_Param.m_byClassInGuild == 1 || v33->m_Param.m_byClassInGuild == 2 )
        {
          if ( v19->m_bIOWait )
          {
            CPlayer::SendMsg_GuildRoomRentResult(v33, 11, v17, v18);
          }
          else
          {
            CGuild::GetTotalDalant(v19);
            if ( (double)SLODWORD((&CGuildRoomInfo::sm_RoomInfo)[v18]) <= a3 )
            {
              v5 = CGuild::GetGrade(v19);
              if ( (unsigned __int8)v5 >= (signed int)*((_BYTE *)&CGuildRoomInfo::sm_RoomInfo + 8 * v18 + 8) )
              {
                v6 = CGuildRoomSystem::GetInstance();
                if ( CGuildRoomSystem::IsRoomRented(v6, v34->dwGuildSerial) )
                {
                  v16 = 3;
                  CPlayer::SendMsg_GuildRoomRentResult(v33, 3, v17, v18);
                }
                else
                {
                  v30 = v33->m_pUserDB->m_AvatorData.dbAvator.m_byRaceSexCode >> 1;
                  v7 = CGuildRoomSystem::GetInstance();
                  bRestore = 0;
                  ts = 0i64;
                  dwGuildSerial[0] = v19->m_dwSerial;
                  v17 = CGuildRoomSystem::RentRoom(v7, v30, v34->byRoomType, v19->m_nIndex, dwGuildSerial[0], 0i64, 0);
                  if ( v17 )
                  {
                    v16 = 5;
                    CPlayer::SendMsg_GuildRoomRentResult(v33, 5, v17, v18);
                  }
                  else
                  {
                    v16 = 10;
                    v20 = 0i64;
                    v21 = 0i64;
                    CGuild::GetTotalDalant(v19);
                    v20 = 0i64;
                    CGuild::GetTotalGold(v19);
                    v21 = 0i64;
                    v22 = GetCurrentMonth();
                    v23 = GetCurrentDay();
                    v24 = GetCurrentHour();
                    v25 = GetCurrentMin();
                    v26.byProcRet = 0;
                    v26.in_poperserial = 0;
                    v26.tmp_guildindex = v19->m_nIndex;
                    v26.in_guildserial = v19->m_dwSerial;
                    v26.dwSubDalant = (unsigned int)(&CGuildRoomInfo::sm_RoomInfo)[v18];
                    v26.dwSubGold = 0;
                    *(_QWORD *)&v26.out_totalgold = 0i64;
                    *(_QWORD *)&v26.out_totaldalant = 0i64;
                    v26.in_date[0] = v22;
                    v26.in_date[1] = v23;
                    v26.in_date[2] = v24;
                    v26.in_date[3] = v25;
                    strcpy_s(v26.in_w_popername, 0x11ui64, "GuildRoom Rent");
                    v8 = _qry_case_outputgmoney::size(&v26);
                    CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, 20, (char *)&v26, v8);
                    v31 = CPlayerDB::GetRaceCode(&v33->m_Param);
                    v9 = CGuildRoomSystem::GetInstance();
                    v27 = CGuildRoomSystem::GetMapData(v9, v31, v18);
                    v10 = CItemStoreManager::Instance();
                    pDest = CItemStoreManager::GetMapItemStoreListBySerial(v10, v27->m_nMapIndex);
                    v11 = CItemStoreManager::Instance();
                    v29 = CItemStoreManager::GetEmptyInstanceItemStore(v11);
                    if ( pDest && v29 && CMapItemStoreList::CopyItemStoreData(v29, pDest) )
                      CMapItemStoreList::SetTypeNSerial(v29, 1, v19->m_dwSerial);
                    CPlayer::SendMsg_GuildRoomRentResult(v33, v16, v17, v18);
                    CGuild::SendMsg_GuildRoomRented(v19, v18);
                  }
                }
              }
              else
              {
                v16 = 9;
                CPlayer::SendMsg_GuildRoomRentResult(v33, 9, v17, v18);
              }
            }
            else
            {
              v16 = 8;
              CPlayer::SendMsg_GuildRoomRentResult(v33, 8, v17, v18);
            }
          }
        }
        else
        {
          v16 = 2;
          CPlayer::SendMsg_GuildRoomRentResult(v33, 2, v17, v18);
        }
      }
      else
      {
        v16 = 6;
        CPlayer::SendMsg_GuildRoomRentResult(v33, 6, v17, v18);
      }
    }
    else
    {
      v16 = 6;
      CPlayer::SendMsg_GuildRoomRentResult(v33, 6, v17, v18);
    }
  }
  else
  {
    v16 = 7;
    CPlayer::SendMsg_GuildRoomRentResult(v33, 7, v17, v18);
  }
}
