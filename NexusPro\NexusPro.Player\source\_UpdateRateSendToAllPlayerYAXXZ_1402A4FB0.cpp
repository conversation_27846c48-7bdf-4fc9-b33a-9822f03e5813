/*
 * Function: ?_UpdateRateSendToAllPlayer@@YAXXZ
 * Address: 0x1402A4FB0
 */

void _UpdateRateSendToAllPlayer(void)
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-38h]@1
  int j; // [sp+20h] [bp-18h]@4

  v0 = &v2;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  for ( j = 0; j < 2532; ++j )
  {
    if ( *(&g_Player.m_bLive + 50856 * j) )
      CPlayer::SendMsg_EconomyRateInform(&g_Player + j, 0);
  }
}
