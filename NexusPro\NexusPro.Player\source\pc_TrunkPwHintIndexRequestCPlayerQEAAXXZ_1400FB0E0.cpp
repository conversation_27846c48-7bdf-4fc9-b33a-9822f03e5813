/*
 * Function: ?pc_TrunkPwHintIndexRequest@CPlayer@@QEAAXXZ
 * Address: 0x1400FB0E0
 */

void __fastcall CPlayer::pc_TrunkPwHintIndexRequest(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v3; // [sp+0h] [bp-38h]@1
  char v4; // [sp+20h] [bp-18h]@4
  CPlayer *p; // [sp+40h] [bp+8h]@1

  p = this;
  v1 = &v3;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v4 = 0;
  if ( IsBeNearStore(p, 10) )
  {
    if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) <= 0 )
      v4 = 2;
  }
  else
  {
    v4 = 13;
  }
  CPlayer::SendMsg_TrunkPwHintIndexResult(p, v4, p->m_Param.m_byTrunkHintIndex);
}
