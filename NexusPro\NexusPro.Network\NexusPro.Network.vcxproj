<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  
  <PropertyGroup Label="Globals">
    <VCProjectVersion>17.0</VCProjectVersion>
    <Keyword>Win32Proj</Keyword>
    <ProjectGuid>{4B51AD86-CA6B-4F0F-A5F1-7597054C4F26}</ProjectGuid>
    <RootNamespace>NexusProNetwork</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
    <ProjectName>NexusPro.Network</ProjectName>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  
  <ImportGroup Label="Shared">
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  
  <PropertyGroup Label="UserMacros" />
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Debug\</OutDir>
    <IntDir>$(SolutionDir)obj\Debug\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\Release\</OutDir>
    <IntDir>$(SolutionDir)obj\Release\$(ProjectName)\</IntDir>
    <IncludePath>$(ProjectDir)headers;$(SolutionDir)NexusPro.Core\headers;$(IncludePath)</IncludePath>
  </PropertyGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>_DEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level3</WarningLevel>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <SDLCheck>true</SDLCheck>
      <PreprocessorDefinitions>NDEBUG;_LIB;WIN32_LEAN_AND_MEAN;NOMINMAX;_CRT_SECURE_NO_WARNINGS;RF_ONLINE_DECOMPILED;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <ConformanceMode>true</ConformanceMode>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <CompileAs>CompileAsCpp</CompileAs>
    </ClCompile>
    <Link>
      <SubSystem>
      </SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  
  <ItemGroup>
    <ClCompile Include="source\0AlgorithmImplVDL_SignerBaseUEC2NPointCryptoPPCryp_140569C60.cpp" />
    <ClCompile Include="source\0AlgorithmImplVDL_SignerBaseUECPPointCryptoPPCrypt_140569B30.cpp" />
    <ClCompile Include="source\0AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635DD0.cpp" />
    <ClCompile Include="source\0AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140637170.cpp" />
    <ClCompile Include="source\0AlgorithmImplVDL_VerifierBaseUEC2NPointCryptoPPCr_140569E20.cpp" />
    <ClCompile Include="source\0AlgorithmImplVDL_VerifierBaseUECPPointCryptoPPCry_140569C40.cpp" />
    <ClCompile Include="source\0AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140636800.cpp" />
    <ClCompile Include="source\0AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140637190.cpp" />
    <ClCompile Include="source\0allocatorURECV_DATAstdQEAAAEBV01Z_14031FB40.cpp" />
    <ClCompile Include="source\0allocatorURECV_DATAstdQEAAXZ_14031FAC0.cpp" />
    <ClCompile Include="source\0auto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEAA_14056C640.cpp" />
    <ClCompile Include="source\0auto_ptrVPK_MessageAccumulatorCryptoPPstdQEAAPEAV_1405F78F0.cpp" />
    <ClCompile Include="source\0CNetSocketQEAAXZ_14047DB60.cpp" />
    <ClCompile Include="source\0dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14031F980.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_SignerBaseUEC2NPointCryptoPP_140568290.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_SignerBaseUECPPointCryptoPPC_1405681D0.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140568070.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_1406327E0.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140635B90.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140635C50.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_VerifierBaseUEC2NPointCrypto_1405682F0.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_VerifierBaseUECPPointCryptoP_140568230.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140568160.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140635BF0.cpp" />
    <ClCompile Include="source\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140635CB0.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_SignerBaseUEC2NPointCryptoPPCryp_140567FF0.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_SignerBaseUECPPointCryptoPPCrypt_140567F30.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140567D10.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632670.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635990.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635A50.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_VerifierBaseUEC2NPointCryptoPPCr_140568050.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_VerifierBaseUECPPointCryptoPPCry_140567F90.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140567DD0.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_1406359F0.cpp" />
    <ClCompile Include="source\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140635AB0.cpp" />
    <ClCompile Include="source\0DL_SignatureMessageEncodingMethod_DSACryptoPPQEAA_14058F7E0.cpp" />
    <ClCompile Include="source\0DL_SignatureMessageEncodingMethod_NRCryptoPPQEAAX_14063C950.cpp" />
    <ClCompile Include="source\0DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_140561ED0.cpp" />
    <ClCompile Include="source\0DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_140632550.cpp" />
    <ClCompile Include="source\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140564370.cpp" />
    <ClCompile Include="source\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140566010.cpp" />
    <ClCompile Include="source\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140634060.cpp" />
    <ClCompile Include="source\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140634530.cpp" />
    <ClCompile Include="source\0DL_VerifierImplUDL_SignatureSchemeOptionsUDSACryp_1405632F0.cpp" />
    <ClCompile Include="source\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1405650B0.cpp" />
    <ClCompile Include="source\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140566D50.cpp" />
    <ClCompile Include="source\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140634300.cpp" />
    <ClCompile Include="source\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1406347D0.cpp" />
    <ClCompile Include="source\0member_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600220.cpp" />
    <ClCompile Include="source\0MessageQueueCryptoPPQEAAIZ_1406542A0.cpp" />
    <ClCompile Include="source\0pairV_Deque_iteratorUMessageRangeMeterFilterCrypt_140603B50.cpp" />
    <ClCompile Include="source\0PK_DeterministicSignatureMessageEncodingMethodCry_14058FDF0.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_1405617E0.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140561BF0.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140561D50.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632460.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140633F90.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140633FD0.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561880.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561C90.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561DF0.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140633FB0.cpp" />
    <ClCompile Include="source\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140633FF0.cpp" />
    <ClCompile Include="source\0PK_MessageAccumulatorBaseCryptoPPQEAAXZ_140562F60.cpp" />
    <ClCompile Include="source\0PK_MessageAccumulatorCryptoPPQEAAXZ_140563160.cpp" />
    <ClCompile Include="source\0PK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPQEA_140562D60.cpp" />
    <ClCompile Include="source\0PK_SignatureMessageEncodingMethodCryptoPPQEAAXZ_14058FF00.cpp" />
    <ClCompile Include="source\0RECV_DATAQEAAXZ_14031A490.cpp" />
    <ClCompile Include="source\0simple_ptrVDL_SignatureMessageEncodingMethod_DSAC_14058EB60.cpp" />
    <ClCompile Include="source\0simple_ptrVDL_SignatureMessageEncodingMethod_NRCr_14063C280.cpp" />
    <ClCompile Include="source\0SingletonVDL_SignatureMessageEncodingMethod_DSACr_14056C710.cpp" />
    <ClCompile Include="source\0SingletonVDL_SignatureMessageEncodingMethod_NRCry_1406395A0.cpp" />
    <ClCompile Include="source\0URECV_DATAallocatorPEAURECV_DATAstdQEAAAEBValloca_14031FB60.cpp" />
    <ClCompile Include="source\0VRandomNumberGeneratorCryptoPPHPK_FinalTemplateVD_140639D90.cpp" />
    <ClCompile Include="source\0_announ_message_receipt_udpQEAAXZ_140094FE0.cpp" />
    <ClCompile Include="source\0_apex_send_ipQEAAXZ_140410C00.cpp" />
    <ClCompile Include="source\0_chat_message_receipt_udpQEAAXZ_140094E90.cpp" />
    <ClCompile Include="source\0_chat_steal_message_gm_zoclQEAAXZ_1403F8D30.cpp" />
    <ClCompile Include="source\0_Deque_const_iteratorUMessageRangeMeterFilterCryp_140600CE0.cpp" />
    <ClCompile Include="source\0_Deque_const_iteratorUMessageRangeMeterFilterCryp_140601830.cpp" />
    <ClCompile Include="source\0_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031DA90.cpp" />
    <ClCompile Include="source\0_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EA30.cpp" />
    <ClCompile Include="source\0_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140600A50.cpp" />
    <ClCompile Include="source\0_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406017D0.cpp" />
    <ClCompile Include="source\0_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D540.cpp" />
    <ClCompile Include="source\0_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E370.cpp" />
    <ClCompile Include="source\0_Deque_mapURECV_DATAVallocatorURECV_DATAstdstdIEA_14031FAD0.cpp" />
    <ClCompile Include="source\0_Deque_valURECV_DATAVallocatorURECV_DATAstdstdIEA_14031FA30.cpp" />
    <ClCompile Include="source\0_messageQEAAXZ_140438750.cpp" />
    <ClCompile Include="source\0_qry_case_post_sendQEAAXZ_1403282A0.cpp" />
    <ClCompile Include="source\0_qry_case_update_data_for_post_sendQEAAXZ_1400CA7E0.cpp" />
    <ClCompile Include="source\0_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_140600DB0.cpp" />
    <ClCompile Include="source\0_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_14031DBF0.cpp" />
    <ClCompile Include="source\0_socketQEAAXZ_14047F8B0.cpp" />
    <ClCompile Include="source\0_talik_recvr_listQEAAXZ_1403F6ED0.cpp" />
    <ClCompile Include="source\0__list_qry_case_post_sendQEAAXZ_1403285E0.cpp" />
    <ClCompile Include="source\1AlgorithmImplVDL_SignerBaseUEC2NPointCryptoPPCryp_14055F6A0.cpp" />
    <ClCompile Include="source\1AlgorithmImplVDL_SignerBaseUECPPointCryptoPPCrypt_14055F630.cpp" />
    <ClCompile Include="source\1AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_1406329E0.cpp" />
    <ClCompile Include="source\1AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632BE0.cpp" />
    <ClCompile Include="source\1AlgorithmImplVDL_VerifierBaseUEC2NPointCryptoPPCr_14055F6F0.cpp" />
    <ClCompile Include="source\1AlgorithmImplVDL_VerifierBaseUECPPointCryptoPPCry_14055F680.cpp" />
    <ClCompile Include="source\1AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632A30.cpp" />
    <ClCompile Include="source\1AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632C00.cpp" />
    <ClCompile Include="source\1auto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEAA_14056C660.cpp" />
    <ClCompile Include="source\1auto_ptrVPK_MessageAccumulatorCryptoPPstdQEAAXZ_1405F7910.cpp" />
    <ClCompile Include="source\1CNetSocketUEAAXZ_14047DCC0.cpp" />
    <ClCompile Include="source\1dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14031FB80.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_SignerBaseUEC2NPointCryptoPP_14055ED20.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_SignerBaseUECPPointCryptoPPC_14055EC60.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_14055E750.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140632720.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140632860.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_VerifierBaseUEC2NPointCrypto_14055ED80.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_VerifierBaseUECPPointCryptoP_14055ECC0.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_14055E7B0.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140632780.cpp" />
    <ClCompile Include="source\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_1406328C0.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_SignerBaseUEC2NPointCryptoPPCryp_14055E5B0.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_SignerBaseUECPPointCryptoPPCrypt_14055E570.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_14055E1B0.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632630.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_1406326A0.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_VerifierBaseUEC2NPointCryptoPPCr_14055E5D0.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_VerifierBaseUECPPointCryptoPPCry_14055E590.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_14055E1D0.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632650.cpp" />
    <ClCompile Include="source\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_1406326C0.cpp" />
    <ClCompile Include="source\1DL_SignatureMessageEncodingMethod_DSACryptoPPUEAA_14058FEB0.cpp" />
    <ClCompile Include="source\1DL_SignatureMessageEncodingMethod_NRCryptoPPUEAAX_14063D350.cpp" />
    <ClCompile Include="source\1DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_14055D500.cpp" />
    <ClCompile Include="source\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__14055DF60.cpp" />
    <ClCompile Include="source\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__14055DFA0.cpp" />
    <ClCompile Include="source\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140632510.cpp" />
    <ClCompile Include="source\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__1406325B0.cpp" />
    <ClCompile Include="source\1DL_VerifierImplUDL_SignatureSchemeOptionsUDSACryp_14055D520.cpp" />
    <ClCompile Include="source\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_14055DF80.cpp" />
    <ClCompile Include="source\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_14055DFC0.cpp" />
    <ClCompile Include="source\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140632530.cpp" />
    <ClCompile Include="source\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1406325D0.cpp" />
    <ClCompile Include="source\1member_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600E80.cpp" />
    <ClCompile Include="source\1MessageQueueCryptoPPUEAAXZ_1406552F0.cpp" />
    <ClCompile Include="source\1pairV_Deque_iteratorUMessageRangeMeterFilterCrypt_1406023A0.cpp" />
    <ClCompile Include="source\1PK_DeterministicSignatureMessageEncodingMethodCry_14058FED0.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055C200.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055D060.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055D0A0.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632420.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632490.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055C220.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055D080.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055D0C0.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140632440.cpp" />
    <ClCompile Include="source\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_1406324B0.cpp" />
    <ClCompile Include="source\1PK_MessageAccumulatorBaseCryptoPPUEAAXZ_1405631A0.cpp" />
    <ClCompile Include="source\1PK_MessageAccumulatorCryptoPPUEAAXZ_140563180.cpp" />
    <ClCompile Include="source\1PK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUEA_1405680D0.cpp" />
    <ClCompile Include="source\1PK_SignatureMessageEncodingMethodCryptoPPUEAAXZ_14058FEF0.cpp" />
    <ClCompile Include="source\1simple_ptrVDL_SignatureMessageEncodingMethod_DSAC_14058EB80.cpp" />
    <ClCompile Include="source\1simple_ptrVDL_SignatureMessageEncodingMethod_NRCr_14063C2A0.cpp" />
    <ClCompile Include="source\1_Deque_const_iteratorUMessageRangeMeterFilterCryp_1405FE880.cpp" />
    <ClCompile Include="source\1_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031D4C0.cpp" />
    <ClCompile Include="source\1_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1405FE860.cpp" />
    <ClCompile Include="source\1_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D480.cpp" />
    <ClCompile Include="source\1_messageQEAAXZ_140438780.cpp" />
    <ClCompile Include="source\1_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_1405FE8A0.cpp" />
    <ClCompile Include="source\1_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_14031D500.cpp" />
    <ClCompile Include="source\1_socketQEAAXZ_14047F900.cpp" />
    <ClCompile Include="source\4_Deque_const_iteratorUMessageRangeMeterFilterCryp_140602430.cpp" />
    <ClCompile Include="source\4_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140602400.cpp" />
    <ClCompile Include="source\4_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_140602480.cpp" />
    <ClCompile Include="source\8_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603A70.cpp" />
    <ClCompile Include="source\8_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031E640.cpp" />
    <ClCompile Include="source\9_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603AC0.cpp" />
    <ClCompile Include="source\9_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031DB10.cpp" />
    <ClCompile Include="source\Accept_ClientCNetSocketQEAA_NKZ_14047E750.cpp" />
    <ClCompile Include="source\Accept_ServerCNetSocketQEAAKXZ_14047E3B0.cpp" />
    <ClCompile Include="source\AccessHashPK_MessageAccumulatorImplVSHA1CryptoPPCr_14056C620.cpp" />
    <ClCompile Include="source\AccessKeyDL_ObjectImplBaseVDL_SignerBaseUEC2NPoint_14056C2D0.cpp" />
    <ClCompile Include="source\AccessKeyDL_ObjectImplBaseVDL_SignerBaseUECPPointC_14056C000.cpp" />
    <ClCompile Include="source\AccessKeyDL_ObjectImplBaseVDL_SignerBaseVIntegerCr_14056BE90.cpp" />
    <ClCompile Include="source\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseUEC2NPoi_14056C390.cpp" />
    <ClCompile Include="source\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseUECPPoin_14056C080.cpp" />
    <ClCompile Include="source\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseVInteger_14056BF50.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseU_1405645A0.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseU_140566240.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140562140.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140634290.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140634760.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_1405634B0.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140565270.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140566F10.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_1406344C0.cpp" />
    <ClCompile Include="source\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140634990.cpp" />
    <ClCompile Include="source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseUEC_140564560.cpp" />
    <ClCompile Include="source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseUEC_140566200.cpp" />
    <ClCompile Include="source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140562100.cpp" />
    <ClCompile Include="source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140634250.cpp" />
    <ClCompile Include="source\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140634720.cpp" />
    <ClCompile Include="source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseUE_140565230.cpp" />
    <ClCompile Include="source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseUE_140566ED0.cpp" />
    <ClCompile Include="source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140563470.cpp" />
    <ClCompile Include="source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140634480.cpp" />
    <ClCompile Include="source\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140634950.cpp" />
    <ClCompile Include="source\AddPassablePacketCMainThreadAEAAXXZ_1401F95E0.cpp" />
    <ClCompile Include="source\AlgorithmNameAlgorithmImplVDL_SignerBaseUEC2NPoint_140566270.cpp" />
    <ClCompile Include="source\AlgorithmNameAlgorithmImplVDL_SignerBaseUECPPointC_1405645D0.cpp" />
    <ClCompile Include="source\AlgorithmNameAlgorithmImplVDL_SignerBaseVIntegerCr_1406342C0.cpp" />
    <ClCompile Include="source\AlgorithmNameAlgorithmImplVDL_SignerBaseVIntegerCr_140634790.cpp" />
    <ClCompile Include="source\AlgorithmNameAlgorithmImplVDL_VerifierBaseUEC2NPoi_140566F40.cpp" />
    <ClCompile Include="source\AlgorithmNameAlgorithmImplVDL_VerifierBaseUECPPoin_1405652A0.cpp" />
    <ClCompile Include="source\AlgorithmNameAlgorithmImplVDL_VerifierBaseVInteger_1406344F0.cpp" />
    <ClCompile Include="source\AlgorithmNameAlgorithmImplVDL_VerifierBaseVInteger_1406349C0.cpp" />
    <ClCompile Include="source\allocateallocatorPEAUMessageRangeMeterFilterCrypto_140600CA0.cpp" />
    <ClCompile Include="source\allocateallocatorPEAURECV_DATAstdQEAAPEAPEAURECV_D_14031AD80.cpp" />
    <ClCompile Include="source\allocateallocatorUMessageRangeMeterFilterCryptoPPs_1406009E0.cpp" />
    <ClCompile Include="source\allocateallocatorURECV_DATAstdQEAAPEAURECV_DATA_KZ_14031AB40.cpp" />
    <ClCompile Include="source\AllowNonrecoverablePartPK_SignatureMessageEncoding_140562C80.cpp" />
    <ClCompile Include="source\AnsyncConnectCompleteCNetworkEXEEAAXKKHZ_1401DB640.cpp" />
    <ClCompile Include="source\AnsyncConnectCompleteCNetWorkingUEAAXKKHZ_140482210.cpp" />
    <ClCompile Include="source\AnyMessagesBufferedTransformationCryptoPPUEBA_NXZ_1405F5210.cpp" />
    <ClCompile Include="source\AnyRetrievableMessageQueueCryptoPPUEBA_NXZ_140655190.cpp" />
    <ClCompile Include="source\begindequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFE30.cpp" />
    <ClCompile Include="source\begindequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031D5F0.cpp" />
    <ClCompile Include="source\CashDBInfoRecvResultCNetworkEXAEAA_NHPEADZ_1401D1DD0.cpp" />
    <ClCompile Include="source\Cauto_ptrVPK_MessageAccumulatorCryptoPPstdQEBAPEAV_1405F79C0.cpp" />
    <ClCompile Include="source\ChannelMessageEndBufferedTransformationCryptoPPQEA_1405F7AA0.cpp" />
    <ClCompile Include="source\ChannelMessageSeriesEndBufferedTransformationCrypt_1405F4BA0.cpp" />
    <ClCompile Include="source\ChannelMessageSeriesEndEqualityComparisonFilterCry_140654D40.cpp" />
    <ClCompile Include="source\ChannelMessageSeriesEndInputRejectingVBufferedTran_140453F20.cpp" />
    <ClCompile Include="source\ChannelMessageSeriesEndInputRejectingVFilterCrypto_14044E9E0.cpp" />
    <ClCompile Include="source\ChannelMessageSeriesEndOutputProxyCryptoPPUEAA_NAE_1405FF170.cpp" />
    <ClCompile Include="source\ChatAllRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1401C5A00.cpp" />
    <ClCompile Include="source\ChatMapRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1401C5810.cpp" />
    <ClCompile Include="source\CheckSendNewMissionMsgCDarkHoleChannelQEAAXXZ_140267640.cpp" />
    <ClCompile Include="source\cleardequeUMessageRangeMeterFilterCryptoPPVallocat_1406001C0.cpp" />
    <ClCompile Include="source\CloseAllCNetSocketAEAAXXZ_14047F060.cpp" />
    <ClCompile Include="source\CloseSocketCNetProcessQEAAXK_NZ_14047A140.cpp" />
    <ClCompile Include="source\CloseSocketCNetSocketQEAA_NKZ_14047EC40.cpp" />
    <ClCompile Include="source\CloseSocketCNetWorkingQEAAXKK_NZ_140481AE0.cpp" />
    <ClCompile Include="source\closesocket_0_1404DBA40.cpp" />
    <ClCompile Include="source\Cmember_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600250.cpp" />
    <ClCompile Include="source\CombineMessageAndShiftRegisterCFB_DecryptionTempla_140586920.cpp" />
    <ClCompile Include="source\CombineMessageAndShiftRegisterCFB_DecryptionTempla_1405870D0.cpp" />
    <ClCompile Include="source\CombineMessageAndShiftRegisterCFB_EncryptionTempla_1405868D0.cpp" />
    <ClCompile Include="source\CombineMessageAndShiftRegisterCFB_EncryptionTempla_140587080.cpp" />
    <ClCompile Include="source\CompleteAnsyncConnectCNetProcessQEAAXXZ_140479970.cpp" />
    <ClCompile Include="source\CompleteSendCPostSystemManagerQEAAXPEADZ_1403261A0.cpp" />
    <ClCompile Include="source\Complete_db_Update_Data_For_Post_SendCMainThreadAE_1401B90F0.cpp" />
    <ClCompile Include="source\ComputeMessageRepresentativeDL_SignatureMessageEnc_140630320.cpp" />
    <ClCompile Include="source\ComputeMessageRepresentativeDL_SignatureMessageEnc_1406304D0.cpp" />
    <ClCompile Include="source\ConnectCNetSocketQEAAHKPEAUsockaddr_inZ_14047E830.cpp" />
    <ClCompile Include="source\ConnectCNetWorkingQEAAHKKKGZ_1404819B0.cpp" />
    <ClCompile Include="source\ConnectionStatusRequestCNetworkEXAEAA_NHZ_1401C7490.cpp" />
    <ClCompile Include="source\ConnectThreadCNetProcessCAXPEAXZ_14047B230.cpp" />
    <ClCompile Include="source\ConnectToNcashCEngNetworkBillEXQEAAHXZ_14031A420.cpp" />
    <ClCompile Include="source\connect_0_1404DBA52.cpp" />
    <ClCompile Include="source\constructallocatorUMessageRangeMeterFilterCryptoPP_140600A00.cpp" />
    <ClCompile Include="source\constructallocatorURECV_DATAstdQEAAXPEAURECV_DATAA_14031AB90.cpp" />
    <ClCompile Include="source\CopyMessagesToBufferedTransformationCryptoPPQEBAIA_1405F5590.cpp" />
    <ClCompile Include="source\CopyMessagesToMessageQueueCryptoPPQEBAIAEAVBuffere_1406545F0.cpp" />
    <ClCompile Include="source\CopyMessagesToStoreCryptoPPQEBAIAEAVBufferedTransf_1405FE150.cpp" />
    <ClCompile Include="source\CopyRangeTo2MessageQueueCryptoPPUEBA_KAEAVBuffered_1406543C0.cpp" />
    <ClCompile Include="source\copyV_Deque_iteratorURECV_DATAVallocatorURECV_DATA_14031EFD0.cpp" />
    <ClCompile Include="source\copy_backwardV_Deque_iteratorURECV_DATAVallocatorU_14031EC10.cpp" />
    <ClCompile Include="source\Dauto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEBA_14056C6C0.cpp" />
    <ClCompile Include="source\Dauto_ptrVPK_MessageAccumulatorCryptoPPstdQEBAAEAV_1405F7970.cpp" />
    <ClCompile Include="source\db_sendwebracebosssmsCMainThreadQEAAEPEAU_qry_case_1401B2B10.cpp" />
    <ClCompile Include="source\deallocateallocatorPEAUMessageRangeMeterFilterCryp_140600C70.cpp" />
    <ClCompile Include="source\deallocateallocatorPEAURECV_DATAstdQEAAXPEAPEAUREC_14031AD30.cpp" />
    <ClCompile Include="source\deallocateallocatorUMessageRangeMeterFilterCryptoP_140600C40.cpp" />
    <ClCompile Include="source\deallocateallocatorURECV_DATAstdQEAAXPEAURECV_DATA_14031FD20.cpp" />
    <ClCompile Include="source\destroyallocatorPEAUMessageRangeMeterFilterCryptoP_140600CC0.cpp" />
    <ClCompile Include="source\destroyallocatorPEAURECV_DATAstdQEAAXPEAPEAURECV_D_14031FD70.cpp" />
    <ClCompile Include="source\destroyallocatorUMessageRangeMeterFilterCryptoPPst_140600A30.cpp" />
    <ClCompile Include="source\destroyallocatorURECV_DATAstdQEAAXPEAURECV_DATAZ_14031EAE0.cpp" />
    <ClCompile Include="source\DigestSizePK_MessageAccumulatorCryptoPPUEBAIXZ_140562E10.cpp" />
    <ClCompile Include="source\DispatchMessageA_0_140676E76.cpp" />
    <ClCompile Include="source\Dmember_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600240.cpp" />
    <ClCompile Include="source\DoMessageBoxCWinAppUEAAHPEBDIIZ_0_1404DBFB8.cpp" />
    <ClCompile Include="source\D_Deque_const_iteratorUMessageRangeMeterFilterCryp_140600D30.cpp" />
    <ClCompile Include="source\D_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031E580.cpp" />
    <ClCompile Include="source\D_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140600A90.cpp" />
    <ClCompile Include="source\D_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D900.cpp" />
    <ClCompile Include="source\emptydequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFED0.cpp" />
    <ClCompile Include="source\emptydequeURECV_DATAVallocatorURECV_DATAstdstdQEBA_14031E7F0.cpp" />
    <ClCompile Include="source\EmptySocketBufferCNetSocketQEAAXKZ_14047EB70.cpp" />
    <ClCompile Include="source\enddequeUMessageRangeMeterFilterCryptoPPVallocator_1405FFE80.cpp" />
    <ClCompile Include="source\enddequeURECV_DATAVallocatorURECV_DATAstdstdQEAAAV_14031D670.cpp" />
    <ClCompile Include="source\erasedequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031D700.cpp" />
    <ClCompile Include="source\erasedequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031DC40.cpp" />
    <ClCompile Include="source\E_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C20.cpp" />
    <ClCompile Include="source\E_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EAB0.cpp" />
    <ClCompile Include="source\E_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406037F0.cpp" />
    <ClCompile Include="source\E_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603810.cpp" />
    <ClCompile Include="source\E_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D940.cpp" />
    <ClCompile Include="source\E_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E3D0.cpp" />
    <ClCompile Include="source\failwithmessage_1404DDA60.cpp" />
    <ClCompile Include="source\FindEmptySocketCNetSocketQEAAKXZ_14047F180.cpp" />
    <ClCompile Include="source\FormatMessageA_0_1404DEE80.cpp" />
    <ClCompile Include="source\frontdequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFF00.cpp" />
    <ClCompile Include="source\F_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C50.cpp" />
    <ClCompile Include="source\F_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031F940.cpp" />
    <ClCompile Include="source\F_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406038D0.cpp" />
    <ClCompile Include="source\F_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031F8F0.cpp" />
    <ClCompile Include="source\GetBillingDBConnectionStatusCashDbWorkerQEAA_NXZ_1402EEE50.cpp" />
    <ClCompile Include="source\GetBillingDBConnectionStatusCCashDBWorkManagerQEAA_1402F33F0.cpp" />
    <ClCompile Include="source\GetBitAfterSetLimSocketYAKEZ_14003E470.cpp" />
    <ClCompile Include="source\GetCheckRecvTimeCNetWorkingQEAAKKKZ_140481D60.cpp" />
    <ClCompile Include="source\GetConnectionHookCCmdTargetMEAAPEAUIConnectionPoin_1404DBBF8.cpp" />
    <ClCompile Include="source\GetConnectionMapCCmdTargetMEBAPEBUAFX_CONNECTIONMA_1404DBBD4.cpp" />
    <ClCompile Include="source\GetConnectTime_AddBySecYAKHZ_14043CB80.cpp" />
    <ClCompile Include="source\GetDBTaskConnectionStatusCLogTypeDBTaskManagerQEAA_1402C3870.cpp" />
    <ClCompile Include="source\GetDefItemUpgSocketNumYAEHHZ_14003BB50.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseUEC2NP_140566260.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseUECPPo_1405645C0.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_140562160.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_1406342B0.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_140634780.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseUEC2_140566F30.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseUECP_140565290.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1405634D0.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1406344E0.cpp" />
    <ClCompile Include="source\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1406349B0.cpp" />
    <ClCompile Include="source\GetExtraConnectionPointsCCmdTargetMEAAHPEAVCPtrArr_1404DBBF2.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseUEC2NP_140566190.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseUECPPo_1405644F0.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_140562090.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_1406341E0.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_1406346B0.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseUEC2_140566E60.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseUECP_1405651C0.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_140563400.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_140634410.cpp" />
    <ClCompile Include="source\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_1406348E0.cpp" />
    <ClCompile Include="source\GetItemUpgLimSocketYAEKZ_14003E290.cpp" />
    <ClCompile Include="source\GetKey1_messageQEAAKXZ_1401C0320.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseUEC2_140566250.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseUECP_1405645B0.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_140562150.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_1406342A0.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_140634770.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseUE_140565280.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseUE_140566F20.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1405634C0.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1406344D0.cpp" />
    <ClCompile Include="source\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1406349A0.cpp" />
    <ClCompile Include="source\GetMessageA_0_140676E88.cpp" />
    <ClCompile Include="source\GetMessageA_messageQEAAKXZ_1401C0300.cpp" />
    <ClCompile Include="source\GetMessageBarCFrameWndUEAAPEAVCWndXZ_0_1404DBE56.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1405620C0.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_140564520.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1405661C0.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_140634210.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1406346E0.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140563430.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_1405651F0.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140566E90.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140634440.cpp" />
    <ClCompile Include="source\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140634910.cpp" />
    <ClCompile Include="source\GetMessageMapCAboutDlgMEBAPEBUAFX_MSGMAPXZ_1400297D0.cpp" />
    <ClCompile Include="source\GetMessageMapCDisplayViewMEBAPEBUAFX_MSGMAPXZ_14002BD00.cpp" />
    <ClCompile Include="source\GetMessageMapCGameServerAppMEBAPEBUAFX_MSGMAPXZ_1400293B0.cpp" />
    <ClCompile Include="source\GetMessageMapCGameServerDocMEBAPEBUAFX_MSGMAPXZ_140029E10.cpp" />
    <ClCompile Include="source\GetMessageMapCGameServerViewMEBAPEBUAFX_MSGMAPXZ_14002A690.cpp" />
    <ClCompile Include="source\GetMessageMapCInfoSheetMEBAPEBUAFX_MSGMAPXZ_14002D990.cpp" />
    <ClCompile Include="source\GetMessageMapCIPXTabMEBAPEBUAFX_MSGMAPXZ_14002DFD0.cpp" />
    <ClCompile Include="source\GetMessageMapCMainFrameMEBAPEBUAFX_MSGMAPXZ_140027CD0.cpp" />
    <ClCompile Include="source\GetMessageMapCMapTabMEBAPEBUAFX_MSGMAPXZ_14002E620.cpp" />
    <ClCompile Include="source\GetMessageMapCObjectSearchDlgMEBAPEBUAFX_MSGMAPXZ_14002F290.cpp" />
    <ClCompile Include="source\GetMessageMapCObjectTabMEBAPEBUAFX_MSGMAPXZ_14002FF40.cpp" />
    <ClCompile Include="source\GetMessageMapCOpenDlgMEBAPEBUAFX_MSGMAPXZ_140029210.cpp" />
    <ClCompile Include="source\GetMessageMapCServerTabMEBAPEBUAFX_MSGMAPXZ_140034E10.cpp" />
    <ClCompile Include="source\GetMessageMapCTCPTabMEBAPEBUAFX_MSGMAPXZ_140035CB0.cpp" />
    <ClCompile Include="source\GetMessageMapLicensePopupDlgMEBAPEBUAFX_MSGMAPXZ_140026900.cpp" />
    <ClCompile Include="source\GetMessageStringCFrameWndUEBAXIAEAVCStringTDVStrTr_1404DBE3E.cpp" />
    <ClCompile Include="source\GetNextMessageBufferedTransformationCryptoPPUEAA_N_1405F52A0.cpp" />
    <ClCompile Include="source\GetNextMessageMessageQueueCryptoPPUEAA_NXZ_140654540.cpp" />
    <ClCompile Include="source\GetNextMessageSeriesBufferedTransformationCryptoPP_14044CF10.cpp" />
    <ClCompile Include="source\GetNextMessageStoreCryptoPPUEAA_NXZ_1405FE100.cpp" />
    <ClCompile Include="source\GetRaceWarRecvrCPvpCashPointQEAA_NXZ_140284CF0.cpp" />
    <ClCompile Include="source\GetSendMsgCMsgListManagerRACE_BOSS_MSGQEAAPEAVCMsg_14029FBA0.cpp" />
    <ClCompile Include="source\GetSendMsgCMsgListRACE_BOSS_MSGQEAAPEAVCMsg2XZ_14029E920.cpp" />
    <ClCompile Include="source\GetSendPoint_NET_BUFFERQEAAPEADPEAHPEA_NZ_14047D9B0.cpp" />
    <ClCompile Include="source\GetSendThreadFrameCNetProcessQEAAKXZ_140479F80.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseUE_1405644B0.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseUE_140566150.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_140562050.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_1406341A0.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_140634670.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1405633C0.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_140565180.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_140566E20.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1406343D0.cpp" />
    <ClCompile Include="source\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1406348A0.cpp" />
    <ClCompile Include="source\GetSocketCNetSocketQEAAPEAU_socketKZ_14047D3B0.cpp" />
    <ClCompile Include="source\GetSocketCNetWorkingQEAAPEAU_socketKKZ_140481BD0.cpp" />
    <ClCompile Include="source\GetSocketIPAddressCNetSocketQEAAKKZ_14047F150.cpp" />
    <ClCompile Include="source\GetSocketNameYA_N_KPEADZ_14047FFE0.cpp" />
    <ClCompile Include="source\GetSocketTypeCNetSocketQEAAPEAU_SOCK_TYPE_PARAMXZ_14047F110.cpp" />
    <ClCompile Include="source\GetTalikFromSocketYAEKEZ_14003E2E0.cpp" />
    <ClCompile Include="source\GetTalikRecvrPointCPvpCashMngQEAAHEKZ_1403F6420.cpp" />
    <ClCompile Include="source\GetTalikRecvrPointCPvpCashMngQEAAHHZ_1403F63F0.cpp" />
    <ClCompile Include="source\GetTalikRecvrPointCPvpCashPointQEAAHEKZ_1403F5190.cpp" />
    <ClCompile Include="source\GetThisMessageMapCAboutDlgKAPEBUAFX_MSGMAPXZ_140029810.cpp" />
    <ClCompile Include="source\GetThisMessageMapCDialogKAPEBUAFX_MSGMAPXZ_0_1404DBD90.cpp" />
    <ClCompile Include="source\GetThisMessageMapCDisplayViewKAPEBUAFX_MSGMAPXZ_14002BD40.cpp" />
    <ClCompile Include="source\GetThisMessageMapCDocumentKAPEBUAFX_MSGMAPXZ_0_1404DC042.cpp" />
    <ClCompile Include="source\GetThisMessageMapCFormViewKAPEBUAFX_MSGMAPXZ_0_1404DC10E.cpp" />
    <ClCompile Include="source\GetThisMessageMapCFrameWndKAPEBUAFX_MSGMAPXZ_0_1404DBDCC.cpp" />
    <ClCompile Include="source\GetThisMessageMapCGameServerAppKAPEBUAFX_MSGMAPXZ_1400293F0.cpp" />
    <ClCompile Include="source\GetThisMessageMapCGameServerDocKAPEBUAFX_MSGMAPXZ_140029E50.cpp" />
    <ClCompile Include="source\GetThisMessageMapCGameServerViewKAPEBUAFX_MSGMAPXZ_14002A6D0.cpp" />
    <ClCompile Include="source\GetThisMessageMapCInfoSheetKAPEBUAFX_MSGMAPXZ_14002D9D0.cpp" />
    <ClCompile Include="source\GetThisMessageMapCIPXTabKAPEBUAFX_MSGMAPXZ_14002E010.cpp" />
    <ClCompile Include="source\GetThisMessageMapCMainFrameKAPEBUAFX_MSGMAPXZ_140027D10.cpp" />
    <ClCompile Include="source\GetThisMessageMapCMapTabKAPEBUAFX_MSGMAPXZ_14002E660.cpp" />
    <ClCompile Include="source\GetThisMessageMapCObjectSearchDlgKAPEBUAFX_MSGMAPX_14002F2D0.cpp" />
    <ClCompile Include="source\GetThisMessageMapCObjectTabKAPEBUAFX_MSGMAPXZ_14002FF80.cpp" />
    <ClCompile Include="source\GetThisMessageMapCOpenDlgKAPEBUAFX_MSGMAPXZ_140029250.cpp" />
    <ClCompile Include="source\GetThisMessageMapCPropertyPageKAPEBUAFX_MSGMAPXZ_0_1404DC360.cpp" />
    <ClCompile Include="source\GetThisMessageMapCPropertySheetKAPEBUAFX_MSGMAPXZ__1404DC2F4.cpp" />
    <ClCompile Include="source\GetThisMessageMapCServerTabKAPEBUAFX_MSGMAPXZ_140034E50.cpp" />
    <ClCompile Include="source\GetThisMessageMapCTCPTabKAPEBUAFX_MSGMAPXZ_140035CF0.cpp" />
    <ClCompile Include="source\GetThisMessageMapCWinAppKAPEBUAFX_MSGMAPXZ_0_1404DBF16.cpp" />
    <ClCompile Include="source\GetThisMessageMapLicensePopupDlgKAPEBUAFX_MSGMAPXZ_140026940.cpp" />
    <ClCompile Include="source\GetTotalCountCNetSocketQEAAPEAU_total_countXZ_14047F130.cpp" />
    <ClCompile Include="source\G_Deque_const_iteratorUMessageRangeMeterFilterCryp_140601A00.cpp" />
    <ClCompile Include="source\G_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EB70.cpp" />
    <ClCompile Include="source\G_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140601800.cpp" />
    <ClCompile Include="source\G_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406039B0.cpp" />
    <ClCompile Include="source\G_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E9E0.cpp" />
    <ClCompile Include="source\HandleMouseMessagesCD3DArcBallQEAA_JPEAUHWND__I_K__14052C510.cpp" />
    <ClCompile Include="source\H_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406038F0.cpp" />
    <ClCompile Include="source\H_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E420.cpp" />
    <ClCompile Include="source\InitAcceptSocketCNetSocketAEAA_NPEADZ_14047EE00.cpp" />
    <ClCompile Include="source\InitParam_socketQEAAXXZ_14047F910.cpp" />
    <ClCompile Include="source\InputRecoverableMessageDL_SignerBaseUEC2NPointCryp_1405662B0.cpp" />
    <ClCompile Include="source\InputRecoverableMessageDL_SignerBaseUECPPointCrypt_140564610.cpp" />
    <ClCompile Include="source\InputRecoverableMessageDL_SignerBaseVIntegerCrypto_1405621E0.cpp" />
    <ClCompile Include="source\InputRecoverableMessageTF_SignerBaseCryptoPPUEBAXA_140622820.cpp" />
    <ClCompile Include="source\InputSignatureDL_VerifierBaseUEC2NPointCryptoPPCry_140566F80.cpp" />
    <ClCompile Include="source\InputSignatureDL_VerifierBaseUECPPointCryptoPPCryp_1405652E0.cpp" />
    <ClCompile Include="source\InputSignatureDL_VerifierBaseVIntegerCryptoPPCrypt_140563540.cpp" />
    <ClCompile Include="source\InputSignatureTF_VerifierBaseCryptoPPUEBAXAEAVPK_M_140623020.cpp" />
    <ClCompile Include="source\InternetConnectA_0_14066D832.cpp" />
    <ClCompile Include="source\IsGetConnectedCEngNetworkBillEXQEAA_NXZ_14022C320.cpp" />
    <ClCompile Include="source\IsIdleMessageCWinThreadUEAAHPEAUtagMSGZ_0_1404DBF6A.cpp" />
    <ClCompile Include="source\IsolatedFlushMessageQueueCryptoPPUEAA_N_N0Z_140655100.cpp" />
    <ClCompile Include="source\IsolatedInitializeMessageQueueCryptoPPUEAAXAEBVNam_140654FB0.cpp" />
    <ClCompile Include="source\IsolatedMessageSeriesEndBufferedTransformationCryp_14054B910.cpp" />
    <ClCompile Include="source\IsolatedMessageSeriesEndInputRejectingVBufferedTra_140453E50.cpp" />
    <ClCompile Include="source\IsolatedMessageSeriesEndInputRejectingVFilterCrypt_14044E910.cpp" />
    <ClCompile Include="source\IsolatedMessageSeriesEndMessageQueueCryptoPPUEAA_N_140655120.cpp" />
    <ClCompile Include="source\IsolatedMessageSeriesEndMeterFilterCryptoPPUEAA_N__1405F9D80.cpp" />
    <ClCompile Include="source\IsRecvableContEffectCGameObjectUEAA_NXZ_14012C770.cpp" />
    <ClCompile Include="source\IsRecvableContEffectCMonsterUEAA_NXZ_1401469A0.cpp" />
    <ClCompile Include="source\IsRecvedQuestByNPCCQuestMgrQEAA_NHZ_14028B000.cpp" />
    <ClCompile Include="source\IsRecvedQuestByNPCCQuestMgrQEAA_NPEBDZ_14028B020.cpp" />
    <ClCompile Include="source\IsSendFromWebCMsgRACE_BOSS_MSGQEAA_NXZ_1402A2D10.cpp" />
    <ClCompile Include="source\IsSendTimeCMsgRACE_BOSS_MSGQEAA_NXZ_14029DC50.cpp" />
    <ClCompile Include="source\iter_swapV_Deque_iteratorUMessageRangeMeterFilterC_1406042F0.cpp" />
    <ClCompile Include="source\j_0allocatorURECV_DATAstdQEAAAEBV01Z_140002E6E.cpp" />
    <ClCompile Include="source\j_0allocatorURECV_DATAstdQEAAXZ_140002261.cpp" />
    <ClCompile Include="source\j_0CNetSocketQEAAXZ_140001370.cpp" />
    <ClCompile Include="source\j_0dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14001200D.cpp" />
    <ClCompile Include="source\j_0RECV_DATAQEAAXZ_1400108F7.cpp" />
    <ClCompile Include="source\j_0URECV_DATAallocatorPEAURECV_DATAstdQEAAAEBVallo_140009C1E.cpp" />
    <ClCompile Include="source\j_0_announ_message_receipt_udpQEAAXZ_1400095CA.cpp" />
    <ClCompile Include="source\j_0_apex_send_ipQEAAXZ_14000E903.cpp" />
    <ClCompile Include="source\j_0_chat_message_receipt_udpQEAAXZ_1400054CA.cpp" />
    <ClCompile Include="source\j_0_chat_steal_message_gm_zoclQEAAXZ_140013836.cpp" />
    <ClCompile Include="source\j_0_Deque_const_iteratorURECV_DATAVallocatorURECV__140004C5A.cpp" />
    <ClCompile Include="source\j_0_Deque_const_iteratorURECV_DATAVallocatorURECV__140008B61.cpp" />
    <ClCompile Include="source\j_0_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140003503.cpp" />
    <ClCompile Include="source\j_0_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_1400049EE.cpp" />
    <ClCompile Include="source\j_0_Deque_mapURECV_DATAVallocatorURECV_DATAstdstdI_14000BEBF.cpp" />
    <ClCompile Include="source\j_0_Deque_valURECV_DATAVallocatorURECV_DATAstdstdI_1400118EC.cpp" />
    <ClCompile Include="source\j_0_messageQEAAXZ_140009007.cpp" />
    <ClCompile Include="source\j_0_qry_case_post_sendQEAAXZ_14000A902.cpp" />
    <ClCompile Include="source\j_0_qry_case_update_data_for_post_sendQEAAXZ_140010DF7.cpp" />
    <ClCompile Include="source\j_0_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_1400023A6.cpp" />
    <ClCompile Include="source\j_0_socketQEAAXZ_1400057CC.cpp" />
    <ClCompile Include="source\j_0_talik_recvr_listQEAAXZ_140006109.cpp" />
    <ClCompile Include="source\j_0__list_qry_case_post_sendQEAAXZ_140012D8C.cpp" />
    <ClCompile Include="source\j_1CNetSocketUEAAXZ_14000322E.cpp" />
    <ClCompile Include="source\j_1dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14000D30F.cpp" />
    <ClCompile Include="source\j_1_Deque_const_iteratorURECV_DATAVallocatorURECV__14000ED9A.cpp" />
    <ClCompile Include="source\j_1_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140008314.cpp" />
    <ClCompile Include="source\j_1_messageQEAAXZ_14000C12B.cpp" />
    <ClCompile Include="source\j_1_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_140003229.cpp" />
    <ClCompile Include="source\j_1_socketQEAAXZ_140011CE3.cpp" />
    <ClCompile Include="source\j_8_Deque_const_iteratorURECV_DATAVallocatorURECV__140012030.cpp" />
    <ClCompile Include="source\j_9_Deque_const_iteratorURECV_DATAVallocatorURECV__14000C7AC.cpp" />
    <ClCompile Include="source\j_Accept_ClientCNetSocketQEAA_NKZ_140004377.cpp" />
    <ClCompile Include="source\j_Accept_ServerCNetSocketQEAAKXZ_140011EB4.cpp" />
    <ClCompile Include="source\j_AddPassablePacketCMainThreadAEAAXXZ_14000AE98.cpp" />
    <ClCompile Include="source\j_allocateallocatorPEAURECV_DATAstdQEAAPEAPEAURECV_1400055A6.cpp" />
    <ClCompile Include="source\j_allocateallocatorURECV_DATAstdQEAAPEAURECV_DATA__140001B9F.cpp" />
    <ClCompile Include="source\j_AnsyncConnectCompleteCNetworkEXEEAAXKKHZ_140001ACD.cpp" />
    <ClCompile Include="source\j_AnsyncConnectCompleteCNetWorkingUEAAXKKHZ_140002BD5.cpp" />
    <ClCompile Include="source\j_begindequeURECV_DATAVallocatorURECV_DATAstdstdQE_14001151D.cpp" />
    <ClCompile Include="source\j_CashDBInfoRecvResultCNetworkEXAEAA_NHPEADZ_140009ED5.cpp" />
    <ClCompile Include="source\j_ChannelMessageSeriesEndInputRejectingVBufferedTr_14000A191.cpp" />
    <ClCompile Include="source\j_ChannelMessageSeriesEndInputRejectingVFilterCryp_140005DF8.cpp" />
    <ClCompile Include="source\j_ChatAllRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1400034FE.cpp" />
    <ClCompile Include="source\j_ChatMapRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1400046C4.cpp" />
    <ClCompile Include="source\j_CheckSendNewMissionMsgCDarkHoleChannelQEAAXXZ_140001FD7.cpp" />
    <ClCompile Include="source\j_CloseAllCNetSocketAEAAXXZ_1400139F8.cpp" />
    <ClCompile Include="source\j_CloseSocketCNetProcessQEAAXK_NZ_140006398.cpp" />
    <ClCompile Include="source\j_CloseSocketCNetSocketQEAA_NKZ_14000F664.cpp" />
    <ClCompile Include="source\j_CloseSocketCNetWorkingQEAAXKK_NZ_14000EFCA.cpp" />
    <ClCompile Include="source\j_CompleteAnsyncConnectCNetProcessQEAAXXZ_140005173.cpp" />
    <ClCompile Include="source\j_CompleteSendCPostSystemManagerQEAAXPEADZ_140009AD9.cpp" />
    <ClCompile Include="source\j_Complete_db_Update_Data_For_Post_SendCMainThread_14000DEA9.cpp" />
    <ClCompile Include="source\j_ConnectCNetSocketQEAAHKPEAUsockaddr_inZ_140012EB3.cpp" />
    <ClCompile Include="source\j_ConnectCNetWorkingQEAAHKKKGZ_140003DF0.cpp" />
    <ClCompile Include="source\j_ConnectionStatusRequestCNetworkEXAEAA_NHZ_14000F50B.cpp" />
    <ClCompile Include="source\j_ConnectThreadCNetProcessCAXPEAXZ_1400135B6.cpp" />
    <ClCompile Include="source\j_ConnectToNcashCEngNetworkBillEXQEAAHXZ_140010776.cpp" />
    <ClCompile Include="source\j_constructallocatorURECV_DATAstdQEAAXPEAURECV_DAT_14000433B.cpp" />
    <ClCompile Include="source\j_copyV_Deque_iteratorURECV_DATAVallocatorURECV_DA_1400136FB.cpp" />
    <ClCompile Include="source\j_copy_backwardV_Deque_iteratorURECV_DATAVallocato_14001338B.cpp" />
    <ClCompile Include="source\j_db_sendwebracebosssmsCMainThreadQEAAEPEAU_qry_ca_14000DD4B.cpp" />
    <ClCompile Include="source\j_deallocateallocatorPEAURECV_DATAstdQEAAXPEAPEAUR_140005A38.cpp" />
    <ClCompile Include="source\j_deallocateallocatorURECV_DATAstdQEAAXPEAURECV_DA_140005718.cpp" />
    <ClCompile Include="source\j_destroyallocatorPEAURECV_DATAstdQEAAXPEAPEAURECV_140012869.cpp" />
    <ClCompile Include="source\j_destroyallocatorURECV_DATAstdQEAAXPEAURECV_DATAZ_14000D913.cpp" />
    <ClCompile Include="source\j_D_Deque_const_iteratorURECV_DATAVallocatorURECV__14000C2C5.cpp" />
    <ClCompile Include="source\j_D_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000D6ED.cpp" />
    <ClCompile Include="source\j_emptydequeURECV_DATAVallocatorURECV_DATAstdstdQE_140002BB7.cpp" />
    <ClCompile Include="source\j_EmptySocketBufferCNetSocketQEAAXKZ_1400128D2.cpp" />
    <ClCompile Include="source\j_enddequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_140012887.cpp" />
    <ClCompile Include="source\j_erasedequeURECV_DATAVallocatorURECV_DATAstdstdQE_1400041BF.cpp" />
    <ClCompile Include="source\j_erasedequeURECV_DATAVallocatorURECV_DATAstdstdQE_14000C7E8.cpp" />
    <ClCompile Include="source\j_E_Deque_const_iteratorURECV_DATAVallocatorURECV__140002FF4.cpp" />
    <ClCompile Include="source\j_E_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000363E.cpp" />
    <ClCompile Include="source\j_E_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140009E2B.cpp" />
    <ClCompile Include="source\j_FindEmptySocketCNetSocketQEAAKXZ_14000589E.cpp" />
    <ClCompile Include="source\j_F_Deque_const_iteratorURECV_DATAVallocatorURECV__14000AF42.cpp" />
    <ClCompile Include="source\j_F_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000F902.cpp" />
    <ClCompile Include="source\j_GetBillingDBConnectionStatusCashDbWorkerQEAA_NXZ_140004AD4.cpp" />
    <ClCompile Include="source\j_GetBillingDBConnectionStatusCCashDBWorkManagerQE_14000B9C4.cpp" />
    <ClCompile Include="source\j_GetBitAfterSetLimSocketYAKEZ_14000213F.cpp" />
    <ClCompile Include="source\j_GetCheckRecvTimeCNetWorkingQEAAKKKZ_140004093.cpp" />
    <ClCompile Include="source\j_GetConnectTime_AddBySecYAKHZ_140005BA0.cpp" />
    <ClCompile Include="source\j_GetDBTaskConnectionStatusCLogTypeDBTaskManagerQE_140009F6B.cpp" />
    <ClCompile Include="source\j_GetDefItemUpgSocketNumYAEHHZ_140009A2F.cpp" />
    <ClCompile Include="source\j_GetItemUpgLimSocketYAEKZ_140005A24.cpp" />
    <ClCompile Include="source\j_GetKey1_messageQEAAKXZ_1400108A2.cpp" />
    <ClCompile Include="source\j_GetMessageA_messageQEAAKXZ_14000353F.cpp" />
    <ClCompile Include="source\j_GetMessageMapCAboutDlgMEBAPEBUAFX_MSGMAPXZ_14000F259.cpp" />
    <ClCompile Include="source\j_GetMessageMapCDisplayViewMEBAPEBUAFX_MSGMAPXZ_140002BEE.cpp" />
    <ClCompile Include="source\j_GetMessageMapCGameServerAppMEBAPEBUAFX_MSGMAPXZ_14000B492.cpp" />
    <ClCompile Include="source\j_GetMessageMapCGameServerDocMEBAPEBUAFX_MSGMAPXZ_1400116DA.cpp" />
    <ClCompile Include="source\j_GetMessageMapCGameServerViewMEBAPEBUAFX_MSGMAPXZ_14001189C.cpp" />
    <ClCompile Include="source\j_GetMessageMapCInfoSheetMEBAPEBUAFX_MSGMAPXZ_14001167B.cpp" />
    <ClCompile Include="source\j_GetMessageMapCIPXTabMEBAPEBUAFX_MSGMAPXZ_140004692.cpp" />
    <ClCompile Include="source\j_GetMessageMapCMainFrameMEBAPEBUAFX_MSGMAPXZ_140001401.cpp" />
    <ClCompile Include="source\j_GetMessageMapCMapTabMEBAPEBUAFX_MSGMAPXZ_14000AEAC.cpp" />
    <ClCompile Include="source\j_GetMessageMapCObjectSearchDlgMEBAPEBUAFX_MSGMAPX_1400079AF.cpp" />
    <ClCompile Include="source\j_GetMessageMapCObjectTabMEBAPEBUAFX_MSGMAPXZ_14000D459.cpp" />
    <ClCompile Include="source\j_GetMessageMapCOpenDlgMEBAPEBUAFX_MSGMAPXZ_14000CBAD.cpp" />
    <ClCompile Include="source\j_GetMessageMapCServerTabMEBAPEBUAFX_MSGMAPXZ_1400089E0.cpp" />
    <ClCompile Include="source\j_GetMessageMapCTCPTabMEBAPEBUAFX_MSGMAPXZ_14000DD87.cpp" />
    <ClCompile Include="source\j_GetMessageMapLicensePopupDlgMEBAPEBUAFX_MSGMAPXZ_14001171B.cpp" />
    <ClCompile Include="source\j_GetNextMessageSeriesBufferedTransformationCrypto_1400095CF.cpp" />
    <ClCompile Include="source\j_GetRaceWarRecvrCPvpCashPointQEAA_NXZ_14000758B.cpp" />
    <ClCompile Include="source\j_GetSendMsgCMsgListManagerRACE_BOSS_MSGQEAAPEAVCM_140013223.cpp" />
    <ClCompile Include="source\j_GetSendMsgCMsgListRACE_BOSS_MSGQEAAPEAVCMsg2XZ_14000FBB9.cpp" />
    <ClCompile Include="source\j_GetSendPoint_NET_BUFFERQEAAPEADPEAHPEA_NZ_1400119D2.cpp" />
    <ClCompile Include="source\j_GetSendThreadFrameCNetProcessQEAAKXZ_140008800.cpp" />
    <ClCompile Include="source\j_GetSocketCNetSocketQEAAPEAU_socketKZ_14000C8E7.cpp" />
    <ClCompile Include="source\j_GetSocketCNetWorkingQEAAPEAU_socketKKZ_1400059B1.cpp" />
    <ClCompile Include="source\j_GetSocketIPAddressCNetSocketQEAAKKZ_1400032EC.cpp" />
    <ClCompile Include="source\j_GetSocketNameYA_N_KPEADZ_14000215D.cpp" />
    <ClCompile Include="source\j_GetSocketTypeCNetSocketQEAAPEAU_SOCK_TYPE_PARAMX_140011450.cpp" />
    <ClCompile Include="source\j_GetTalikFromSocketYAEKEZ_140013DBD.cpp" />
    <ClCompile Include="source\j_GetTalikRecvrPointCPvpCashMngQEAAHEKZ_14000C3A1.cpp" />
    <ClCompile Include="source\j_GetTalikRecvrPointCPvpCashMngQEAAHHZ_1400062DF.cpp" />
    <ClCompile Include="source\j_GetTalikRecvrPointCPvpCashPointQEAAHEKZ_140001CDF.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCAboutDlgKAPEBUAFX_MSGMAPXZ_140012BA2.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCDisplayViewKAPEBUAFX_MSGMAPXZ_140010E6A.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCGameServerAppKAPEBUAFX_MSGMAPX_140012607.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCGameServerDocKAPEBUAFX_MSGMAPX_1400043C7.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCGameServerViewKAPEBUAFX_MSGMAP_14001388B.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCInfoSheetKAPEBUAFX_MSGMAPXZ_140003431.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCIPXTabKAPEBUAFX_MSGMAPXZ_14000636B.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCMainFrameKAPEBUAFX_MSGMAPXZ_1400070A9.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCMapTabKAPEBUAFX_MSGMAPXZ_14000C379.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCObjectSearchDlgKAPEBUAFX_MSGMA_14000153C.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCObjectTabKAPEBUAFX_MSGMAPXZ_1400097D7.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCOpenDlgKAPEBUAFX_MSGMAPXZ_14000C63A.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCServerTabKAPEBUAFX_MSGMAPXZ_1400104C4.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapCTCPTabKAPEBUAFX_MSGMAPXZ_14000F0CE.cpp" />
    <ClCompile Include="source\j_GetThisMessageMapLicensePopupDlgKAPEBUAFX_MSGMAP_1400079F5.cpp" />
    <ClCompile Include="source\j_GetTotalCountCNetSocketQEAAPEAU_total_countXZ_14000168B.cpp" />
    <ClCompile Include="source\j_G_Deque_const_iteratorURECV_DATAVallocatorURECV__140002568.cpp" />
    <ClCompile Include="source\j_G_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000742D.cpp" />
    <ClCompile Include="source\j_H_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140001410.cpp" />
    <ClCompile Include="source\j_InitAcceptSocketCNetSocketAEAA_NPEADZ_1400136EC.cpp" />
    <ClCompile Include="source\j_InitParam_socketQEAAXXZ_1400065DC.cpp" />
    <ClCompile Include="source\j_IsGetConnectedCEngNetworkBillEXQEAA_NXZ_14000F150.cpp" />
    <ClCompile Include="source\j_IsolatedMessageSeriesEndInputRejectingVBufferedT_1400120DF.cpp" />
    <ClCompile Include="source\j_IsolatedMessageSeriesEndInputRejectingVFilterCry_140003FFD.cpp" />
    <ClCompile Include="source\j_IsRecvableContEffectCGameObjectUEAA_NXZ_140010681.cpp" />
    <ClCompile Include="source\j_IsRecvableContEffectCMonsterUEAA_NXZ_140004B29.cpp" />
    <ClCompile Include="source\j_IsRecvedQuestByNPCCQuestMgrQEAA_NHZ_140003535.cpp" />
    <ClCompile Include="source\j_IsRecvedQuestByNPCCQuestMgrQEAA_NPEBDZ_14001136A.cpp" />
    <ClCompile Include="source\j_IsSendFromWebCMsgRACE_BOSS_MSGQEAA_NXZ_140012495.cpp" />
    <ClCompile Include="source\j_IsSendTimeCMsgRACE_BOSS_MSGQEAA_NXZ_14000B9BA.cpp" />
    <ClCompile Include="source\j_LoadSendMsgCNetProcessQEAAHKGPEADGZ_140006C67.cpp" />
    <ClCompile Include="source\j_LoadSendMsgCNetProcessQEAAHKPEAEPEADGZ_140009278.cpp" />
    <ClCompile Include="source\j_lobby_disconnectCMgrAccountLobbyHistoryQEAAXPEAU_14000FF10.cpp" />
    <ClCompile Include="source\j_LoopSubProcSendInformCHonorGuildQEAAXEZ_14000BDB1.cpp" />
    <ClCompile Include="source\j_LoopSubProcSendInformCOreAmountMgrQEAAXXZ_140009660.cpp" />
    <ClCompile Include="source\j_MakeBuddyPacketCGuildQEAAXXZ_14001249A.cpp" />
    <ClCompile Include="source\j_MakeConnectionThreadCEnglandBillingMgrQEAA_NXZ_140004F3E.cpp" />
    <ClCompile Include="source\j_MakeDownApplierPacketCGuildQEAAXXZ_1400065FA.cpp" />
    <ClCompile Include="source\j_MakeDownMemberPacketCGuildQEAAXXZ_140011581.cpp" />
    <ClCompile Include="source\j_MakeMoneyIOPacketCGuildQEAAXXZ_14000221B.cpp" />
    <ClCompile Include="source\j_MakeQueryInfoPacketCGuildQEAAXXZ_140007D7E.cpp" />
    <ClCompile Include="source\j_make_minepacketAutominePersonalAEAAXGGEGKZ_140002270.cpp" />
    <ClCompile Include="source\j_max_sizeallocatorURECV_DATAstdQEBA_KXZ_140010127.cpp" />
    <ClCompile Include="source\j_max_sizedequeURECV_DATAVallocatorURECV_DATAstdst_1400069BA.cpp" />
    <ClCompile Include="source\j_MyMessageBoxYAXPEAD0ZZ_140006627.cpp" />
    <ClCompile Include="source\j_NotifyAllProcessEndCNormalGuildBattleGUILD_BATTL_14000107D.cpp" />
    <ClCompile Include="source\j_NumberOfMessageSeriesBufferedTransformationCrypt_14000DBD9.cpp" />
    <ClCompile Include="source\j_NumberOfMessagesInThisSeriesBufferedTransformati_1400125E4.cpp" />
    <ClCompile Include="source\j_NumberOfMessagesStoreCryptoPPUEBAIXZ_14001047E.cpp" />
    <ClCompile Include="source\j_OnBUTTONWorldConnectCGameServerViewQEAAXXZ_1400018E8.cpp" />
    <ClCompile Include="source\j_OnConnectHACKSHEILD_PARAM_ANTICPUEAAXHZ_14000E7EB.cpp" />
    <ClCompile Include="source\j_OnDisConnectHACKSHEILD_PARAM_ANTICPUEAAXXZ_140010B9A.cpp" />
    <ClCompile Include="source\j_OnLoopCNetSocketQEAAXXZ_140012BF7.cpp" />
    <ClCompile Include="source\j_pc_CashDBInfoRecvResultCMainThreadQEAAXPEAD000KZ_140010F9B.cpp" />
    <ClCompile Include="source\j_PopEmptyBufCMsgDataAEAAPEAU_messageXZ_14000996C.cpp" />
    <ClCompile Include="source\j_PopMsgCMsgDataAEAAPEAU_messageXZ_14000E3BD.cpp" />
    <ClCompile Include="source\j_pop_backdequeURECV_DATAVallocatorURECV_DATAstdst_140005D26.cpp" />
    <ClCompile Include="source\j_pop_frontdequeURECV_DATAVallocatorURECV_DATAstds_1400044E9.cpp" />
    <ClCompile Include="source\j_PostSendCPostSystemManagerQEAAEPEADZ_14001252B.cpp" />
    <ClCompile Include="source\j_PostSendRequestCNetworkEXAEAA_NHPEADZ_14000CB3A.cpp" />
    <ClCompile Include="source\j_post_senditemCMgrAvatorItemHistoryQEAAXPEADPEAU__1400036AC.cpp" />
    <ClCompile Include="source\j_PotionSocketDivisionRequestCNetworkEXAEAA_NHPEAD_140003D23.cpp" />
    <ClCompile Include="source\j_PotionSocketSeparationRequestCNetworkEXAEAA_NHPE_1400053C6.cpp" />
    <ClCompile Include="source\j_ProcessMessageCMsgDataEEAAXPEAU_messageZ_14000EC14.cpp" />
    <ClCompile Include="source\j_ProcessMessageCMsgProcessEEAAXPEAU_messageZ_14000F592.cpp" />
    <ClCompile Include="source\j_PumpMessages2SourceTemplateVFileStoreCryptoPPCry_140006645.cpp" />
    <ClCompile Include="source\j_PushAnsyncConnectCNetProcessQEAA_NKPEAUsockaddr__14000E9C1.cpp" />
    <ClCompile Include="source\j_pushdata_qry_case_post_sendQEAA_NKEKKPEAD000U_IN_14000446C.cpp" />
    <ClCompile Include="source\j_PushEmptyBufCMsgDataAEAAXPEAU_messageZ_14000CD15.cpp" />
    <ClCompile Include="source\j_PushIPCheckListCNetSocketQEAA_NKZ_14000ECAF.cpp" />
    <ClCompile Include="source\j_PushMsgCMsgDataAEAAXPEAU_messageZ_14000E67E.cpp" />
    <ClCompile Include="source\j_push_frontdequeURECV_DATAVallocatorURECV_DATAstd_140005E02.cpp" />
    <ClCompile Include="source\j_ReConnectDataBaseCRFNewDatabaseQEAA_NXZ_140002B67.cpp" />
    <ClCompile Include="source\j_RecvClientLineCHackShieldExSystemUEAA_NHPEAU_MSG_14000E282.cpp" />
    <ClCompile Include="source\j_RecvCNetSocketQEAA_NKPEADHPEAHZ_1400134D5.cpp" />
    <ClCompile Include="source\j_RecvGameGuardDataCNationSettingManagerQEAA_NHPEA_1400121FC.cpp" />
    <ClCompile Include="source\j_RecvThreadCNetProcessCAXPEAXZ_140007450.cpp" />
    <ClCompile Include="source\j_Recv_ApexInformCChiNetworkEXQEAAXKKPEADZ_14000C89C.cpp" />
    <ClCompile Include="source\j_Recv_ApexKillCChiNetworkEXQEAAXKKPEADZ_14000A2F4.cpp" />
    <ClCompile Include="source\j_ReleaseCNetSocketQEAAXXZ_140003247.cpp" />
    <ClCompile Include="source\j_Select_PostRecvSerialFromNameCRFWorldDatabaseQEA_140002103.cpp" />
    <ClCompile Include="source\j_Select_PostRecvStorageCheckCRFWorldDatabaseQEAAH_140006C49.cpp" />
    <ClCompile Include="source\j_SendBuyErrorResultCUnmannedTraderUserInfoQEAAXGE_140002FCC.cpp" />
    <ClCompile Include="source\j_SendCancelRegistErrorResultCUnmannedTraderUserIn_14000E205.cpp" />
    <ClCompile Include="source\j_SendCancelRegistSuccessResultCUnmannedTraderUser_14000E6C9.cpp" />
    <ClCompile Include="source\j_SendCancelWebCRaceBossMsgControllerIEAAXEPEAVCMs_140004F9D.cpp" />
    <ClCompile Include="source\j_SendCancleInfomManagerCRaceBossMsgControllerIEAA_14000AF7E.cpp" />
    <ClCompile Include="source\j_SendCancleInfomSenderCRaceBossMsgControllerIEAAX_14000EFA2.cpp" />
    <ClCompile Include="source\j_SendCashDBDSNRequestCNationSettingDataGBUEAAXXZ_140009F43.cpp" />
    <ClCompile Include="source\j_SendCashDBDSNRequestCNationSettingDataKRUEAAXXZ_14000F92F.cpp" />
    <ClCompile Include="source\j_SendCashDBDSNRequestCNationSettingDataNULLUEAAXX_140008AE4.cpp" />
    <ClCompile Include="source\j_SendCashDBDSNRequestCNationSettingDataRUUEAAXXZ_140009250.cpp" />
    <ClCompile Include="source\j_SendCashDBDSNRequestCNationSettingDataUEAAXXZ_14000C851.cpp" />
    <ClCompile Include="source\j_SendCashDBDSNRequestCNationSettingManagerQEAAXXZ_14000E1B5.cpp" />
    <ClCompile Include="source\j_SendCChiNetworkEXQEAAHPEAEKPEADGZ_1400102EE.cpp" />
    <ClCompile Include="source\j_SendCCurrentGuildBattleInfoManagerGUILD_BATTLEQE_140007680.cpp" />
    <ClCompile Include="source\j_SendCEngNetworkBillEXQEAAHPEAEPEADGZ_14000C077.cpp" />
    <ClCompile Include="source\j_SendCGuildBattleRankManagerGUILD_BATTLEQEAAXHEKE_14001159F.cpp" />
    <ClCompile Include="source\j_SendCGuildBattleReservedScheduleListManagerGUILD_140004994.cpp" />
    <ClCompile Include="source\j_SendChangeAggroDataCMonsterAggroMgrQEAAXXZ_140007392.cpp" />
    <ClCompile Include="source\j_SendCMsgListManagerRACE_BOSS_MSGQEAAHEKPEBD0AEAP_140008940.cpp" />
    <ClCompile Include="source\j_SendCNetSocketQEAA_NKPEADHPEAHZ_140005F01.cpp" />
    <ClCompile Include="source\j_SendCNormalGuildBattleGuildMemberGUILD_BATTLEQEA_140008BD4.cpp" />
    <ClCompile Include="source\j_SendComfirmWebCRaceBossMsgControllerIEAAXEPEAVCM_14000E921.cpp" />
    <ClCompile Include="source\j_SendConfirmCtrlCRaceBossMsgControllerIEAAXEPEAVC_14000A033.cpp" />
    <ClCompile Include="source\j_SendCPossibleBattleGuildListManagerGUILD_BATTLEQ_14000FF7E.cpp" />
    <ClCompile Include="source\j_SendCRaceBossMsgControllerQEAA_NEKPEBD0KZ_140004E26.cpp" />
    <ClCompile Include="source\j_SendCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000C19E.cpp" />
    <ClCompile Include="source\j_SendCReservedGuildScheduleMapGroupGUILD_BATTLEQE_140005E16.cpp" />
    <ClCompile Include="source\j_SendCReservedGuildSchedulePageGUILD_BATTLEQEAAXH_140013FC5.cpp" />
    <ClCompile Include="source\j_SendCTotalGuildRankInfoQEAAXKHEEKZ_14000CC16.cpp" />
    <ClCompile Include="source\j_SendCurrentBattleInfoRequestCGuildBattleControll_1400067E4.cpp" />
    <ClCompile Include="source\j_SendCurrHonorGuildListCHonorGuildQEAAXGEEZ_1400059ED.cpp" />
    <ClCompile Include="source\j_SendCWeeklyGuildRankInfoQEAAXKHEEKZ_14000DEA4.cpp" />
    <ClCompile Include="source\j_SendDeleteNotifyPositionMemberCNormalGuildBattle_1400046D3.cpp" />
    <ClCompile Include="source\j_SendDQS_RoomInsertCGuildRoomInfoAEAAXXZ_140013F6B.cpp" />
    <ClCompile Include="source\j_SendDQS_RoomUpdateCGuildRoomInfoAEAAXXZ_14000C1CB.cpp" />
    <ClCompile Include="source\j_SendDrawResultCNormalGuildBattleGUILD_BATTLEIEAA_140001627.cpp" />
    <ClCompile Include="source\j_SendErrorResultCPossibleBattleGuildListManagerGU_140008DA5.cpp" />
    <ClCompile Include="source\j_SendExternMsgUs_HFSMSAXPEAV1KPEAXHZ_14000A18C.cpp" />
    <ClCompile Include="source\j_SendFirstCPossibleBattleGuildListManagerGUILD_BA_1400121E8.cpp" />
    <ClCompile Include="source\j_SendHolyStoneHPToRaceBossCHolyStoneSystemQEAAXXZ_1400075DB.cpp" />
    <ClCompile Include="source\j_SendInfoCPossibleBattleGuildListManagerGUILD_BAT_14001170C.cpp" />
    <ClCompile Include="source\j_SendInfomSenderCRaceBossMsgControllerIEAAXKEZ_140002874.cpp" />
    <ClCompile Include="source\j_SendInformChangeCHonorGuildQEAAXEGZ_14000728E.cpp" />
    <ClCompile Include="source\j_SendIsArriveDestroyerCHolyStoneSystemQEAAXEZ_140011B80.cpp" />
    <ClCompile Include="source\j_SendKillInformCNormalGuildBattleGUILD_BATTLEIEAA_140007FA4.cpp" />
    <ClCompile Include="source\j_SendListCGuildListQEAAXGEEZ_1400017D0.cpp" />
    <ClCompile Include="source\j_SendLoopYAKPEAXZ_14000334B.cpp" />
    <ClCompile Include="source\j_SendMemberPositionCNormalGuildBattleManagerGUILD_1400075BD.cpp" />
    <ClCompile Include="source\j_SendMsgAccount_UILockRefresh_UpdateCUserDBQEAAXX_14000F033.cpp" />
    <ClCompile Include="source\j_SendMsgAlterStateCGravityStoneRegenerAEAAXXZ_140003071.cpp" />
    <ClCompile Include="source\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140003C47.cpp" />
    <ClCompile Include="source\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140003C88.cpp" />
    <ClCompile Include="source\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140007982.cpp" />
    <ClCompile Include="source\j_SendMsgCreateCCircleZoneAEAAXXZ_1400119C8.cpp" />
    <ClCompile Include="source\j_SendMsgGoalCCircleZoneAEAAXXZ_14000776B.cpp" />
    <ClCompile Include="source\j_SendMsgRequestResultCRaceBossMsgControllerIEAAXG_140010F64.cpp" />
    <ClCompile Include="source\j_SendMsgSucceedBuyCashDbWorkerAEAAXGAEBU_param_ca_14000710D.cpp" />
    <ClCompile Include="source\j_SendMsgToMaster_NoCompleteQuestFromNPCCQuestMgrQ_14000EE76.cpp" />
    <ClCompile Include="source\j_SendMsgToMaster_NoHaveGiveItemCQuestMgrQEAAXEZ_140003C06.cpp" />
    <ClCompile Include="source\j_SendMsgToMaster_NoHaveReturnItemCQuestMgrQEAAXEZ_14001320F.cpp" />
    <ClCompile Include="source\j_SendMsgToMaster_ReturnItemAfterQuestCQuestMgrQEA_140004FE8.cpp" />
    <ClCompile Include="source\j_SendMsgUs_HFSMSAXPEAV1KKPEAXZ_14000F957.cpp" />
    <ClCompile Include="source\j_SendMsg_AddEffectCNuclearBombQEAAXXZ_1400027F2.cpp" />
    <ClCompile Include="source\j_SendMsg_AddJoinApplierCGuildQEAAXPEAU_guild_appl_14000BEF6.cpp" />
    <ClCompile Include="source\j_SendMsg_AlterMemberGradeCGuildQEAAXXZ_140006C30.cpp" />
    <ClCompile Include="source\j_SendMsg_AlterMemberStateCGuildQEAAXXZ_140007CA2.cpp" />
    <ClCompile Include="source\j_SendMsg_AlterTransparCTrapQEAAX_NZ_14000E930.cpp" />
    <ClCompile Include="source\j_SendMsg_AnimusActHealInformCAnimusQEAAXKHZ_14000EA61.cpp" />
    <ClCompile Include="source\j_SendMsg_ApplyGuildBattleResultInformCGuildQEAAXE_140011BF3.cpp" />
    <ClCompile Include="source\j_SendMsg_AttackCGuardTowerQEAAXPEAVCAttackZ_14000D161.cpp" />
    <ClCompile Include="source\j_SendMsg_AttackCHolyKeeperQEAAXXZ_140006AC8.cpp" />
    <ClCompile Include="source\j_SendMsg_AttackCNuclearBombQEAAXHHZ_140008729.cpp" />
    <ClCompile Include="source\j_SendMsg_AttackCTrapQEAAXPEAVCAttackZ_140005835.cpp" />
    <ClCompile Include="source\j_SendMsg_Attack_ForceCMonsterQEAAXPEAVCMonsterAtt_140007E64.cpp" />
    <ClCompile Include="source\j_SendMsg_Attack_GenCAnimusQEAAXPEAVCAttackZ_140001465.cpp" />
    <ClCompile Include="source\j_SendMsg_Attack_GenCMonsterQEAAXPEAVCMonsterAttac_140005772.cpp" />
    <ClCompile Include="source\j_SendMsg_BillingInfoCUserDBQEAAXXZ_14000EA1B.cpp" />
    <ClCompile Include="source\j_SendMsg_BreakStopCGameObjectQEAAXXZ_1400019EC.cpp" />
    <ClCompile Include="source\j_SendMsg_BuyCashItemICsSendInterfaceSAXGPEBU_para_140004EB2.cpp" />
    <ClCompile Include="source\j_SendMsg_CashDiscountEventInformICsSendInterfaceS_14000CBFD.cpp" />
    <ClCompile Include="source\j_SendMsg_CashEventInformICsSendInterfaceSAXGEEPEA_1400011B8.cpp" />
    <ClCompile Include="source\j_SendMsg_ChangeTaxRateCGuildQEAAXEZ_1400032BA.cpp" />
    <ClCompile Include="source\j_SendMsg_Change_MonsterRotateCMonsterQEAAXXZ_140007A77.cpp" />
    <ClCompile Include="source\j_SendMsg_Change_MonsterStateCMonsterQEAAXXZ_14000DC10.cpp" />
    <ClCompile Include="source\j_SendMsg_ChannelCloseCDarkHoleChannelQEAAXXZ_140008689.cpp" />
    <ClCompile Include="source\j_SendMsg_ConditionalEventInformICsSendInterfaceSA_140005A83.cpp" />
    <ClCompile Include="source\j_SendMsg_CouponEnsureCCouponMgrQEAAXGEZ_14000AB2D.cpp" />
    <ClCompile Include="source\j_SendMsg_CouponErrorCCouponMgrQEAAXGEZ_140004AC5.cpp" />
    <ClCompile Include="source\j_SendMsg_CouponLendResultCCouponMgrQEAAXGPEAU_db__140001721.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCAnimusQEAAXXZ_14000FAF1.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCDarkHoleQEAAXXZ_140008A58.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCGravityStoneAEAAXXZ_140008BC5.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCGuardTowerQEAAXXZ_14001067C.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCHolyKeeperQEAAXXZ_14000B46F.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCHolyStoneQEAAXXZ_140013C8C.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCItemBoxQEAAXXZ_1400071E9.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCMerchantQEAAXXZ_140006F7D.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCMonsterQEAAXXZ_14001013B.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCParkingUnitQEAAXXZ_14000CA22.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCReturnGateIEAAXXZ_140013EE9.cpp" />
    <ClCompile Include="source\j_SendMsg_CreateCTrapQEAAXXZ_14000E728.cpp" />
    <ClCompile Include="source\j_SendMsg_DelJoinApplierCGuildQEAAXPEAU_guild_appl_140007CD9.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCAnimusQEAAXXZ_14000F8CB.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCDarkHoleQEAAXXZ_140001163.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCGravityStoneAEAAXXZ_14000D382.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCGuardTowerQEAAXEZ_140005088.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCHolyKeeperQEAAXEZ_1400128F5.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCHolyStoneQEAAXEKZ_140013692.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCItemBoxQEAAXXZ_140004B8D.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCMerchantQEAAXXZ_14000D535.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCMonsterQEAAXEZ_14000E9EE.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCParkingUnitQEAAXEZ_14000482C.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCReturnGateIEAAXXZ_1400020D6.cpp" />
    <ClCompile Include="source\j_SendMsg_DestroyCTrapQEAAXEZ_140010479.cpp" />
    <ClCompile Include="source\j_SendMsg_DownPacketCGuildQEAAXEPEAU_guild_member__14000FA42.cpp" />
    <ClCompile Include="source\j_SendMsg_DropMissileCNuclearBombQEAAXXZ_140001A8C.cpp" />
    <ClCompile Include="source\j_SendMsg_EconomyDataToWebYAXXZ_14000D1F2.cpp" />
    <ClCompile Include="source\j_SendMsg_Emotion_PresentationCMonsterQEAAXEGGHZ_14000D4BD.cpp" />
    <ClCompile Include="source\j_SendMsg_EndBattleCHolyStoneSystemQEAAXEZ_140011900.cpp" />
    <ClCompile Include="source\j_SendMsg_EnterKeeperCHolyStoneSystemQEAAXHZ_14000BAAF.cpp" />
    <ClCompile Include="source\j_SendMsg_EnterStoneCHolyStoneSystemQEAAXHZ_14000C6DF.cpp" />
    <ClCompile Include="source\j_SendMsg_ErrorICsSendInterfaceSAXGHZ_140011BDF.cpp" />
    <ClCompile Include="source\j_SendMsg_ExitStoneCHolyStoneSystemQEAAXXZ_140012931.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionAutominePersonalUEAAXHZ_14000DA94.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCAnimusUEAAXHZ_140004BEC.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCCircleZoneEEAAXHZ_14000BD84.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCDarkHoleUEAAXHZ_14000B7FD.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCGameObjectUEAAXHZ_140013840.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCGravityStoneRegenerEEAAXHZ_140013386.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCGravityStoneUEAAXHZ_140009C8C.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCGuardTowerUEAAXHZ_140005E70.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCHolyKeeperUEAAXHZ_14000128A.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCHolyStoneUEAAXHZ_1400043FE.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCItemBoxUEAAXHZ_14000DE4F.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCMerchantUEAAXHZ_14000344A.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCMonsterUEAAXHZ_14000217B.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCParkingUnitUEAAXHZ_140011E64.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCReturnGateUEAAXHZ_14000C275.cpp" />
    <ClCompile Include="source\j_SendMsg_FixPositionCTrapUEAAXHZ_140004EDF.cpp" />
    <ClCompile Include="source\j_SendMsg_GateDestroyCDarkHoleChannelQEAAXPEAEPEAD_140013B9C.cpp" />
    <ClCompile Include="source\j_SendMsg_GoodsListICsSendInterfaceSAXGPEBU_param__14000390E.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildBattleProposedCGuildQEAAHPEADZ_14000B3B6.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildBattleRefusedCGuildQEAAXPEADZ_14000837D.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildBattleSuggestResultCGuildQEAAXEPEAD_14000BFFA.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildDisjointInformCGuildQEAAXXZ_140004859.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildInfoUpdateInformCGuildQEAAXXZ_14000D643.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildJoinAcceptInformCGuildQEAAXPEAU_gui_14000E845.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildMemberLogoffCGuildQEAAXKZ_14000D977.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildMemberPosInformCGuildQEAAXKGGZ_14000BE10.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildOutputMoneyFailCGuildQEAAXKZ_1400072ED.cpp" />
    <ClCompile Include="source\j_SendMsg_GuildRoomRentedCGuildQEAAXEZ_14000E06B.cpp" />
    <ClCompile Include="source\j_SendMsg_HolyKeeperAttackAbleStateCHolyStoneSyste_1400130A2.cpp" />
    <ClCompile Include="source\j_SendMsg_HolyKeeperStateChaosCHolyStoneSystemQEAA_14000C6D0.cpp" />
    <ClCompile Include="source\j_SendMsg_HolyStoneSystemStateCHolyStoneSystemQEAA_14000CB9E.cpp" />
    <ClCompile Include="source\j_SendMsg_InformAttackCNuclearBombQEAAXXZ_14000ED45.cpp" />
    <ClCompile Include="source\j_SendMsg_InformDropPosCNuclearBombQEAAXXZ_140001771.cpp" />
    <ClCompile Include="source\j_SendMsg_Inform_UILockCUserDBQEAAXXZ_140009C50.cpp" />
    <ClCompile Include="source\j_SendMsg_InPcBangTimeCCouponMgrQEAAXGZ_140003706.cpp" />
    <ClCompile Include="source\j_SendMsg_IOMoneyCGuildQEAAXKNN_NPEAEZ_140005966.cpp" />
    <ClCompile Include="source\j_SendMsg_JobCountCDarkHoleChannelQEAAXHHZ_140010AAF.cpp" />
    <ClCompile Include="source\j_SendMsg_JobPassCDarkHoleChannelQEAAXHZ_140002C52.cpp" />
    <ClCompile Include="source\j_SendMsg_KickForSailCTransportShipQEAAXHZ_14000D85A.cpp" />
    <ClCompile Include="source\j_SendMsg_LeaveMemberCGuildQEAAXK_N0Z_1400041C4.cpp" />
    <ClCompile Include="source\j_SendMsg_LimitedsaleEventInformICsSendInterfaceSA_140011C16.cpp" />
    <ClCompile Include="source\j_SendMsg_MachineInfoAutoMineMachineQEAAXHZ_1400085D5.cpp" />
    <ClCompile Include="source\j_SendMsg_ManageGuildCommitteeResultCGuildQEAAX_NP_140008986.cpp" />
    <ClCompile Include="source\j_SendMsg_MasterDieCNuclearBombQEAAXXZ_140010573.cpp" />
    <ClCompile Include="source\j_SendMsg_MasterElectPossibleCGuildQEAAX_NZ_140009BB5.cpp" />
    <ClCompile Include="source\j_SendMsg_MissionPassCDarkHoleChannelQEAAXXZ_140013D9F.cpp" />
    <ClCompile Include="source\j_SendMsg_MoneySupplyDataToWebCMoneySupplyMgrQEAAX_14001401A.cpp" />
    <ClCompile Include="source\j_SendMsg_MoveCAnimusQEAAXXZ_14000BA8C.cpp" />
    <ClCompile Include="source\j_SendMsg_MoveCHolyKeeperQEAAXXZ_140008FDF.cpp" />
    <ClCompile Include="source\j_SendMsg_MoveCMerchantQEAAXXZ_14000E340.cpp" />
    <ClCompile Include="source\j_SendMsg_MoveCMonsterQEAAXXZ_140008486.cpp" />
    <ClCompile Include="source\j_SendMsg_NewMissionCDarkHoleChannelQEAAXXZ_1400056A0.cpp" />
    <ClCompile Include="source\j_SendMsg_NoticeNextQuestCHolyStoneSystemQEAAXHEZ_140001BB8.cpp" />
    <ClCompile Include="source\j_SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeperCH_140001ECE.cpp" />
    <ClCompile Include="source\j_SendMsg_NuclearFindCNuclearBombQEAAXHEZ_14000696A.cpp" />
    <ClCompile Include="source\j_SendMsg_OpenPortalByReactCDarkHoleChannelQEAAXHZ_14000B307.cpp" />
    <ClCompile Include="source\j_SendMsg_OpenPortalByResultCDarkHoleChannelQEAAXH_14000FB5A.cpp" />
    <ClCompile Include="source\j_SendMsg_PatriarchTaxRateTRC_AutoTradeQEAAXHZ_14000367A.cpp" />
    <ClCompile Include="source\j_SendMsg_PvpCashInformCPvpCashPointQEAAXHEZ_1400097F0.cpp" />
    <ClCompile Include="source\j_SendMsg_PvpRankListDataCPvpUserRankingInfoAEAAXG_140002A18.cpp" />
    <ClCompile Include="source\j_SendMsg_PvpRankListNodataCPvpUserRankingInfoAEAA_1400030A8.cpp" />
    <ClCompile Include="source\j_SendMsg_QueryAppointResultClassOrderProcessorQEA_1400136C4.cpp" />
    <ClCompile Include="source\j_SendMsg_QueryPacket_InfoCGuildQEAAXHZ_1400131C9.cpp" />
    <ClCompile Include="source\j_SendMsg_QuestPassCDarkHoleChannelQEAAXXZ_140008823.cpp" />
    <ClCompile Include="source\j_SendMsg_RealAddLimTimeCDarkHoleChannelQEAAXHPEAD_14000F8DA.cpp" />
    <ClCompile Include="source\j_SendMsg_RealFixPositionCGameObjectUEAAX_NZ_1400110C2.cpp" />
    <ClCompile Include="source\j_SendMsg_RealFixPositionCMerchantUEAAX_NZ_14000F222.cpp" />
    <ClCompile Include="source\j_SendMsg_RealMovePointCAnimusUEAAXHZ_14001230F.cpp" />
    <ClCompile Include="source\j_SendMsg_RealMovePointCGameObjectUEAAXHZ_14000E859.cpp" />
    <ClCompile Include="source\j_SendMsg_RealMovePointCHolyKeeperUEAAXHZ_14000A7CC.cpp" />
    <ClCompile Include="source\j_SendMsg_RealMovePointCMerchantUEAAXHZ_140013912.cpp" />
    <ClCompile Include="source\j_SendMsg_RealMovePointCMonsterUEAAXHZ_1400138A9.cpp" />
    <ClCompile Include="source\j_SendMsg_RealMsgInformCDarkHoleChannelQEAAXPEADZ_140012B98.cpp" />
    <ClCompile Include="source\j_SendMsg_RecoverResultCPvpCashPointQEAAXHEHZ_14000AC27.cpp" />
    <ClCompile Include="source\j_SendMsg_RemainBufUseTimeCExtPotionBufQEAAX_NGHHH_1400114B9.cpp" />
    <ClCompile Include="source\j_SendMsg_RemainCouponInformCCouponMgrQEAAXGEZ_14000FFC9.cpp" />
    <ClCompile Include="source\j_SendMsg_ResultCNuclearBombMgrQEAAXHEZ_140010069.cpp" />
    <ClCompile Include="source\j_SendMsg_ResultCNuclearBombQEAAXHEZ_14000CD3D.cpp" />
    <ClCompile Include="source\j_SendMsg_ResultCodeAutoMineMachineMngQEBAXHEEZ_1400138D6.cpp" />
    <ClCompile Include="source\j_SendMsg_ResultCodeAutoMineMachineQEBAXHEEZ_14000B85C.cpp" />
    <ClCompile Include="source\j_SendMsg_ResultCodePatriarchElectProcessorQEAAXKE_14000F114.cpp" />
    <ClCompile Include="source\j_SendMsg_RoomTimeOverCGuildRoomInfoAEAAXXZ_14000EC0A.cpp" />
    <ClCompile Include="source\j_SendMsg_SetHPInformCGameObjectUEAAXXZ_140005542.cpp" />
    <ClCompile Include="source\j_SendMsg_StartBattleCHolyStoneSystemQEAAXXZ_14000C77A.cpp" />
    <ClCompile Include="source\j_SendMsg_StartBillingCBillingIEAAXXZ_1400015EB.cpp" />
    <ClCompile Include="source\j_SendMsg_StartedVoteInformCVoteSystemQEAAXHK_NZ_140003A44.cpp" />
    <ClCompile Include="source\j_SendMsg_StateChangeCDarkHoleQEAAXXZ_140002158.cpp" />
    <ClCompile Include="source\j_SendMsg_StateChangeCItemBoxQEAAXXZ_14000C5AE.cpp" />
    <ClCompile Include="source\j_SendMsg_StoneAlterOperCHolyStoneQEAAXXZ_140005F6A.cpp" />
    <ClCompile Include="source\j_SendMsg_StunInformCGameObjectUEAAXXZ_1400012FD.cpp" />
    <ClCompile Include="source\j_SendMsg_TalikListCPvpCashPointQEAAXHZ_140007F04.cpp" />
    <ClCompile Include="source\j_sendmsg_taxrateTRC_AutoTradeQEAAXHEZ_14001195F.cpp" />
    <ClCompile Include="source\j_SendMsg_TicketCheckCTransportShipQEAAXH_NGZ_14000AA65.cpp" />
    <ClCompile Include="source\j_SendMsg_TimeOutCDarkHoleChannelQEAAXXZ_14000B834.cpp" />
    <ClCompile Include="source\j_SendMsg_TowerCompleteInformCGuardTowerQEAAXXZ_1400015C3.cpp" />
    <ClCompile Include="source\j_SendMsg_TransportShipStateCTransportShipQEAAXHZ_14000C757.cpp" />
    <ClCompile Include="source\j_SendMsg_TransShipTicketNumInformCMerchantQEAAXHZ_140008F1C.cpp" />
    <ClCompile Include="source\j_SendMsg_TrapCompleteInformCTrapQEAAXXZ_140005F38.cpp" />
    <ClCompile Include="source\j_SendMsg_VoteCancelInformCGuildQEAAXXZ_14000E6E7.cpp" />
    <ClCompile Include="source\j_SendMsg_VoteCompleteCGuildQEAAX_NZ_14000E688.cpp" />
    <ClCompile Include="source\j_SendMsg_VoteProcessInform_ContinueCGuildQEAAXPEA_14000978C.cpp" />
    <ClCompile Include="source\j_SendMsg_VoteProcessInform_StartCGuildQEAAXXZ_140006410.cpp" />
    <ClCompile Include="source\j_SendMsg_VoteStateCGuildQEAAXXZ_14000E1E2.cpp" />
    <ClCompile Include="source\j_SendMsg_VoteStopCGuildQEAAXKZ_140011275.cpp" />
    <ClCompile Include="source\j_SendMsg_WaitKeeperCHolyStoneSystemQEAAXHEZ_14000E90D.cpp" />
    <ClCompile Include="source\j_SendMsg_WaitStoneCHolyStoneSystemQEAAXHZ_14000D378.cpp" />
    <ClCompile Include="source\j_SendMsg_ZoneAliveCheckCBillingManagerQEAAXKZ_14000D517.cpp" />
    <ClCompile Include="source\j_SendMsg_ZoneAliveCheckCBillingQEAAXKZ_140008544.cpp" />
    <ClCompile Include="source\j_SendNextHonorGuildListCHonorGuildQEAAXGEZ_140008D4B.cpp" />
    <ClCompile Include="source\j_SendNotifyCloseItemCUnmannedTraderUserInfoQEAAXG_14000DA6C.cpp" />
    <ClCompile Include="source\j_SendNotifyHolyStoneDestroyedToRaceBossCHolyStone_14000D3FF.cpp" />
    <ClCompile Include="source\j_SendPossibleBattleGuildListCGuildBattleControlle_140006A0F.cpp" />
    <ClCompile Include="source\j_SendPossibleBattleGuildListFirstCGuildBattleCont_1400079AA.cpp" />
    <ClCompile Include="source\j_SendRaceBossMsgFromWebRequestCNetworkEXAEAA_NHPE_1400018BB.cpp" />
    <ClCompile Include="source\j_SendRankListCGuildBattleControllerQEAAXHEKIEKZ_14000F41B.cpp" />
    <ClCompile Include="source\j_SendRegenBallCNormalGuildBattleGuildGUILD_BATTLE_1400072E8.cpp" />
    <ClCompile Include="source\j_SendRegistItemErrorResultCUnmannedTraderUserInfo_140010767.cpp" />
    <ClCompile Include="source\j_SendRegistItemSuccessResultCUnmannedTraderUserIn_140012F49.cpp" />
    <ClCompile Include="source\j_SendRequestWebCRaceBossMsgControllerIEAAXEPEAVCM_14000A8EE.cpp" />
    <ClCompile Include="source\j_SendReservedScheduleListCGuildBattleControllerQE_1400105B9.cpp" />
    <ClCompile Include="source\j_SendSearchErrorResultCUnmannedTraderUserInfoQEAA_14000F263.cpp" />
    <ClCompile Include="source\j_SendSearchResultCUnmannedTraderUserInfoQEAAXGPEA_14000F6E1.cpp" />
    <ClCompile Include="source\j_SendSellInfomCUnmannedTraderUserInfoQEAAXGGKKKZ_140007892.cpp" />
    <ClCompile Include="source\j_SendSMS_CompleteQuestCHolyStoneSystemQEAAXEPEADH_14000B285.cpp" />
    <ClCompile Include="source\j_SendSMS_MineTimeExtendCHolyStoneSystemQEAAXHZ_1400083C3.cpp" />
    <ClCompile Include="source\j_SendStartNotifyCommitteeMemberPositionCNormalGui_140005DAD.cpp" />
    <ClCompile Include="source\j_SendTaxRateCUnmannedTraderTaxRateManagerQEAAXHEZ_1400092B9.cpp" />
    <ClCompile Include="source\j_SendTaxRatePatriarchCUnmannedTraderTaxRateManage_1400135CA.cpp" />
    <ClCompile Include="source\j_SendThreadCNetProcessCAXPEAXZ_140001AE6.cpp" />
    <ClCompile Include="source\j_SendWebAddScheduleInfoCNormalGuildBattleGUILD_BA_140006032.cpp" />
    <ClCompile Include="source\j_SendWebBattleEndInfoCNormalGuildBattleGUILD_BATT_140005FF6.cpp" />
    <ClCompile Include="source\j_SendWebBattleStartInfoCNormalGuildBattleGUILD_BA_14000895E.cpp" />
    <ClCompile Include="source\j_SendWebRaceBossSMSCMainThreadQEAAXPEAU_DB_QRY_SY_1400124C7.cpp" />
    <ClCompile Include="source\j_SendWebRaceBossSMSErrorResultCRaceBossMsgControl_14000C3AB.cpp" />
    <ClCompile Include="source\j_SendWinLoseResultCNormalGuildBattleGUILD_BATTLEI_1400102CB.cpp" />
    <ClCompile Include="source\j_send_attackedAutominePersonalQEAAXXZ_14000FF88.cpp" />
    <ClCompile Include="source\j_send_changed_packetAutominePersonalQEAAXHZ_140007AB3.cpp" />
    <ClCompile Include="source\j_send_current_stateAutominePersonalQEAAXXZ_1400031E8.cpp" />
    <ClCompile Include="source\j_send_ecodeAutominePersonalMgrQEAAXHEZ_14000259F.cpp" />
    <ClCompile Include="source\j_send_ecodeAutominePersonalQEAAXEZ_140009552.cpp" />
    <ClCompile Include="source\j_send_installedAutominePersonalQEAAXXZ_1400070C2.cpp" />
    <ClCompile Include="source\j_SetFtpConnectionWheatyExceptionReportQEAAXPEADI0_14000BA19.cpp" />
    <ClCompile Include="source\j_SetMsg_messageQEAAXKKKKZ_140004C0A.cpp" />
    <ClCompile Include="source\j_SetPassablePacketCNetworkEXQEAAXKEEZ_140013F98.cpp" />
    <ClCompile Include="source\j_SetRaceWarRecvrCPvpCashPointQEAAX_NZ_140002EB4.cpp" />
    <ClCompile Include="source\j_SetReconnectFailExitFlagCRFNewDatabaseQEAAX_NZ_14000588F.cpp" />
    <ClCompile Include="source\j_SetSocketCNetSocketQEAA_NPEAU_SOCK_TYPE_PARAMPEA_140001C8F.cpp" />
    <ClCompile Include="source\j_SFContDelMessageCGameObjectUEAAXEE_N0Z_140002BAD.cpp" />
    <ClCompile Include="source\j_SFContUpdateTimeMessageCGameObjectUEAAXEEHZ_14000CAA9.cpp" />
    <ClCompile Include="source\j_ShouldPropagateMessageEndFilterCryptoPPMEBA_NXZ_14001122F.cpp" />
    <ClCompile Include="source\j_ShouldPropagateMessageSeriesEndFilterCryptoPPMEB_14000C45A.cpp" />
    <ClCompile Include="source\j_size_announ_message_receipt_udpQEAAHXZ_140010B2C.cpp" />
    <ClCompile Include="source\j_size_apex_send_ipQEAAHXZ_14000E0AC.cpp" />
    <ClCompile Include="source\j_size_apex_send_logoutQEAAHXZ_140012224.cpp" />
    <ClCompile Include="source\j_size_apex_send_transQEAAHXZ_1400059D4.cpp" />
    <ClCompile Include="source\j_size_chat_message_receipt_udpQEAAHXZ_14000EB1A.cpp" />
    <ClCompile Include="source\j_size_chat_steal_message_gm_zoclQEAAHXZ_140010E9C.cpp" />
    <ClCompile Include="source\j_size_connection_status_result_zoctQEAAHXZ_14000BF37.cpp" />
    <ClCompile Include="source\j_size_qry_case_post_sendQEAAHXZ_140013F43.cpp" />
    <ClCompile Include="source\j_size_qry_case_sendwebracebosssmsQEAAHXZ_14000ED90.cpp" />
    <ClCompile Include="source\j_size_qry_case_update_data_for_post_sendQEAAHXZ_14000F09C.cpp" />
    <ClCompile Include="source\j_size_talik_recvr_listQEAAHXZ_140005371.cpp" />
    <ClCompile Include="source\j_tutorial_process_report_recvCMgrAccountLobbyHist_14000E980.cpp" />
    <ClCompile Include="source\j_unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAX_1400020A9.cpp" />
    <ClCompile Include="source\j_unchecked_uninitialized_copyPEAPEAURECV_DATAPEAP_14000B280.cpp" />
    <ClCompile Include="source\j_unchecked_uninitialized_fill_nPEAPEAURECV_DATA_K_14001234B.cpp" />
    <ClCompile Include="source\j_UpdatePacketClassOrderProcessorQEAAXEEZ_1400092C8.cpp" />
    <ClCompile Include="source\j_UpdateSendCRaceBossMsgControllerIEAAXXZ_14000D20B.cpp" />
    <ClCompile Include="source\j_Update_PostStorageSendToRecverCRFWorldDatabaseQE_14000B866.cpp" />
    <ClCompile Include="source\j_Update_RaceWarRecvrCPvpOrderViewQEAAX_NZ_140003A62.cpp" />
    <ClCompile Include="source\j_Y_Deque_const_iteratorURECV_DATAVallocatorURECV__140004CC8.cpp" />
    <ClCompile Include="source\j_Y_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000138E.cpp" />
    <ClCompile Include="source\j__AllocatePEAURECV_DATAstdYAPEAPEAURECV_DATA_KPEA_140007518.cpp" />
    <ClCompile Include="source\j__AllocateURECV_DATAstdYAPEAURECV_DATA_KPEAU1Z_140006DB6.cpp" />
    <ClCompile Include="source\j__buybygold_buy_single_item_setsenddataCashItemRe_14000ADAD.cpp" />
    <ClCompile Include="source\j__CheckSendCNetProcessAEAAXGZ_14000B64F.cpp" />
    <ClCompile Include="source\j__CkeckRecvBreakCNetProcessAEAAXXZ_14000791E.cpp" />
    <ClCompile Include="source\j__ConstructURECV_DATAU1stdYAXPEAURECV_DATAAEBU1Z_140005E9D.cpp" />
    <ClCompile Include="source\j__Copy_backward_optV_Deque_iteratorURECV_DATAVall_1400010F5.cpp" />
    <ClCompile Include="source\j__Copy_optV_Deque_iteratorURECV_DATAVallocatorURE_140008198.cpp" />
    <ClCompile Include="source\j__db_Update_Data_For_Post_SendCMainThreadAEAAEPEA_14000E269.cpp" />
    <ClCompile Include="source\j__DestroyPEAURECV_DATAstdYAXPEAPEAURECV_DATAZ_14000105A.cpp" />
    <ClCompile Include="source\j__DestroyURECV_DATAstdYAXPEAURECV_DATAZ_140001A46.cpp" />
    <ClCompile Include="source\j__Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DA_140002F1D.cpp" />
    <ClCompile Include="source\j__Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DA_14000D3F5.cpp" />
    <ClCompile Include="source\j__ECNetSocketUEAAPEAXIZ_0_140009C41.cpp" />
    <ClCompile Include="source\j__ECNetSocketUEAAPEAXIZ_1400011C2.cpp" />
    <ClCompile Include="source\j__E_messageQEAAPEAXIZ_1400106A4.cpp" />
    <ClCompile Include="source\j__E_socketQEAAPEAXIZ_140001528.cpp" />
    <ClCompile Include="source\j__Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV__14000AC77.cpp" />
    <ClCompile Include="source\j__Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_ite_14000FDB2.cpp" />
    <ClCompile Include="source\j__GrowmapdequeURECV_DATAVallocatorURECV_DATAstdst_14000B442.cpp" />
    <ClCompile Include="source\j__InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_1400138F4.cpp" />
    <ClCompile Include="source\j__Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_it_140006028.cpp" />
    <ClCompile Include="source\j__Iter_randomV_Deque_iteratorURECV_DATAVallocator_14000E3EA.cpp" />
    <ClCompile Include="source\j__PopRecvMsgCNetProcessAEAAXGZ_140009FE3.cpp" />
    <ClCompile Include="source\j__Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_p_140012D64.cpp" />
    <ClCompile Include="source\j__Ptr_catV_Deque_iteratorURECV_DATAVallocatorUREC_140011676.cpp" />
    <ClCompile Include="source\j__SendListCandidateRegisterAEAAHGEZ_14000E20F.cpp" />
    <ClCompile Include="source\j__SendLoopCNetProcessAEAAXKZ_140006FE1.cpp" />
    <ClCompile Include="source\j__SendSpeedHackCheckMsgCNetProcessAEAAXHZ_140003F35.cpp" />
    <ClCompile Include="source\j__SendVotePaperAllVoterAEAAXXZ_14000E156.cpp" />
    <ClCompile Include="source\j__SendVoteScoreAllVoterAEAAXEZ_140003D82.cpp" />
    <ClCompile Include="source\j__TidydequeURECV_DATAVallocatorURECV_DATAstdstdIE_1400039EA.cpp" />
    <ClCompile Include="source\j__Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPE_140011B62.cpp" />
    <ClCompile Include="source\j__Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorP_14000E836.cpp" />
    <ClCompile Include="source\j__UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_140011662.cpp" />
    <ClCompile Include="source\j__XlendequeURECV_DATAVallocatorURECV_DATAstdstdKA_14000B9F6.cpp" />
    <ClCompile Include="source\LoadSendMsgCNetProcessQEAAHKGPEADGZ_140479680.cpp" />
    <ClCompile Include="source\LoadSendMsgCNetProcessQEAAHKPEAEPEADGZ_140478F90.cpp" />
    <ClCompile Include="source\lobby_disconnectCMgrAccountLobbyHistoryQEAAXPEAU_q_140234BB0.cpp" />
    <ClCompile Include="source\LoopSubProcSendInformCHonorGuildQEAAXEZ_14025FBE0.cpp" />
    <ClCompile Include="source\LoopSubProcSendInformCOreAmountMgrQEAAXXZ_1403F9A90.cpp" />
    <ClCompile Include="source\MakeBuddyPacketCGuildQEAAXXZ_1402551E0.cpp" />
    <ClCompile Include="source\MakeConnectionThreadCEnglandBillingMgrQEAA_NXZ_1403197B0.cpp" />
    <ClCompile Include="source\MakeDownApplierPacketCGuildQEAAXXZ_140254AC0.cpp" />
    <ClCompile Include="source\MakeDownMemberPacketCGuildQEAAXXZ_140254620.cpp" />
    <ClCompile Include="source\MakeMoneyIOPacketCGuildQEAAXXZ_140254F20.cpp" />
    <ClCompile Include="source\MakeQueryInfoPacketCGuildQEAAXXZ_140254DB0.cpp" />
    <ClCompile Include="source\make_heapV_Deque_iteratorUMessageRangeMeterFilterC_140603360.cpp" />
    <ClCompile Include="source\make_minepacketAutominePersonalAEAAXGGEGKZ_1402DC130.cpp" />
    <ClCompile Include="source\MaxRecoverableLengthPK_SignatureMessageEncodingMet_14058F830.cpp" />
    <ClCompile Include="source\MaxRetrievableMessageQueueCryptoPPUEBA_KXZ_140655160.cpp" />
    <ClCompile Include="source\max_sizeallocatorUMessageRangeMeterFilterCryptoPPs_140600DD0.cpp" />
    <ClCompile Include="source\max_sizeallocatorURECV_DATAstdQEBA_KXZ_14031ADD0.cpp" />
    <ClCompile Include="source\max_sizedequeUMessageRangeMeterFilterCryptoPPVallo_140600AB0.cpp" />
    <ClCompile Include="source\max_sizedequeURECV_DATAVallocatorURECV_DATAstdstdQ_14031ABF0.cpp" />
    <ClCompile Include="source\MessageBoxACWndQEAAHPEBD0IZ_0_1404DBEF8.cpp" />
    <ClCompile Include="source\MessageBoxA_0_1404DEF9A.cpp" />
    <ClCompile Include="source\MessageEndBERGeneralDecoderCryptoPPQEAAXXZ_14054D430.cpp" />
    <ClCompile Include="source\MessageEndBufferedTransformationCryptoPPQEAA_NH_NZ_14054F440.cpp" />
    <ClCompile Include="source\MessageEndDERGeneralEncoderCryptoPPQEAAXXZ_14054D7F0.cpp" />
    <ClCompile Include="source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056BED0.cpp" />
    <ClCompile Include="source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056BF80.cpp" />
    <ClCompile Include="source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C040.cpp" />
    <ClCompile Include="source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C0B0.cpp" />
    <ClCompile Include="source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C310.cpp" />
    <ClCompile Include="source\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C3C0.cpp" />
    <ClCompile Include="source\MessageRepresentativeBitLengthTF_SignatureSchemeBa_140624300.cpp" />
    <ClCompile Include="source\MessageRepresentativeBitLengthTF_SignatureSchemeBa_1406243D0.cpp" />
    <ClCompile Include="source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056BEB0.cpp" />
    <ClCompile Include="source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056BF60.cpp" />
    <ClCompile Include="source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C020.cpp" />
    <ClCompile Include="source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C090.cpp" />
    <ClCompile Include="source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C2F0.cpp" />
    <ClCompile Include="source\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C3A0.cpp" />
    <ClCompile Include="source\MessageRepresentativeLengthTF_SignatureSchemeBaseV_1406242E0.cpp" />
    <ClCompile Include="source\MessageRepresentativeLengthTF_SignatureSchemeBaseV_1406243B0.cpp" />
    <ClCompile Include="source\MessageSeriesEndBufferedTransformationCryptoPPUEAA_1405F48F0.cpp" />
    <ClCompile Include="source\MessageSeriesEndFilterCryptoPPUEAA_NH_NZ_1405F8FE0.cpp" />
    <ClCompile Include="source\MessageSeriesEndOutputProxyCryptoPPUEAA_NH_NZ_1405FEF50.cpp" />
    <ClCompile Include="source\MinRepresentativeBitLengthPK_SignatureMessageEncod_14058F810.cpp" />
    <ClCompile Include="source\MMessageRangeMeterFilterCryptoPPQEBA_NAEBU012Z_140603300.cpp" />
    <ClCompile Include="source\MyMessageBoxYAXPEAD0ZZ_14043B010.cpp" />
    <ClCompile Include="source\M_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603B00.cpp" />
    <ClCompile Include="source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140561F60.cpp" />
    <ClCompile Include="source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_1405643C0.cpp" />
    <ClCompile Include="source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140566060.cpp" />
    <ClCompile Include="source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_1406340B0.cpp" />
    <ClCompile Include="source\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140634580.cpp" />
    <ClCompile Include="source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140563340.cpp" />
    <ClCompile Include="source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140565100.cpp" />
    <ClCompile Include="source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140566DA0.cpp" />
    <ClCompile Include="source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140634350.cpp" />
    <ClCompile Include="source\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140634820.cpp" />
    <ClCompile Include="source\NotifyAllProcessEndCNormalGuildBattleGUILD_BATTLEQ_1403E57A0.cpp" />
    <ClCompile Include="source\NumberOfMessagesBufferedTransformationCryptoPPUEBA_1405F5190.cpp" />
    <ClCompile Include="source\NumberOfMessageSeriesBufferedTransformationCryptoP_14044CF70.cpp" />
    <ClCompile Include="source\NumberOfMessageSeriesMessageQueueCryptoPPUEBAIXZ_140655260.cpp" />
    <ClCompile Include="source\NumberOfMessagesInThisSeriesBufferedTransformation_14044CF20.cpp" />
    <ClCompile Include="source\NumberOfMessagesInThisSeriesMessageQueueCryptoPPUE_140655230.cpp" />
    <ClCompile Include="source\NumberOfMessagesMessageQueueCryptoPPUEBAIXZ_140655200.cpp" />
    <ClCompile Include="source\NumberOfMessagesStoreCryptoPPUEBAIXZ_140453B40.cpp" />
    <ClCompile Include="source\OnBUTTONWorldConnectCGameServerViewQEAAXXZ_14002B4E0.cpp" />
    <ClCompile Include="source\OnConnectHACKSHEILD_PARAM_ANTICPUEAAXHZ_1404179D0.cpp" />
    <ClCompile Include="source\OnDisConnectHACKSHEILD_PARAM_ANTICPUEAAXXZ_140417A10.cpp" />
    <ClCompile Include="source\OnLoopCNetSocketQEAAXXZ_14047E2B0.cpp" />
    <ClCompile Include="source\OutputMessageSeriesEndFilterCryptoPPIEAA_NHH_NAEBV_1405F9330.cpp" />
    <ClCompile Include="source\PaddedBlockBitLengthTF_CryptoSystemBaseVPK_Decrypt_140624160.cpp" />
    <ClCompile Include="source\PaddedBlockBitLengthTF_CryptoSystemBaseVPK_Encrypt_140624230.cpp" />
    <ClCompile Include="source\PaddedBlockByteLengthTF_CryptoSystemBaseVPK_Decryp_140624140.cpp" />
    <ClCompile Include="source\PaddedBlockByteLengthTF_CryptoSystemBaseVPK_Encryp_140624210.cpp" />
    <ClCompile Include="source\pc_CashDBInfoRecvResultCMainThreadQEAAXPEAD000KZ_1401F5200.cpp" />
    <ClCompile Include="source\PeekMessageA_0_140676E8E.cpp" />
    <ClCompile Include="source\PopEmptyBufCMsgDataAEAAPEAU_messageXZ_140438380.cpp" />
    <ClCompile Include="source\PopMsgCMsgDataAEAAPEAU_messageXZ_140438220.cpp" />
    <ClCompile Include="source\pop_backdequeUMessageRangeMeterFilterCryptoPPVallo_140600AD0.cpp" />
    <ClCompile Include="source\pop_backdequeURECV_DATAVallocatorURECV_DATAstdstdQ_14031E850.cpp" />
    <ClCompile Include="source\pop_frontdequeUMessageRangeMeterFilterCryptoPPVall_1405FFF80.cpp" />
    <ClCompile Include="source\pop_frontdequeURECV_DATAVallocatorURECV_DATAstdstd_14031E6D0.cpp" />
    <ClCompile Include="source\pop_heapV_Deque_iteratorUMessageRangeMeterFilterCr_140605360.cpp" />
    <ClCompile Include="source\PostQuitMessage_0_140676EAC.cpp" />
    <ClCompile Include="source\PostSendCPostSystemManagerQEAAEPEADZ_140325EF0.cpp" />
    <ClCompile Include="source\PostSendRequestCNetworkEXAEAA_NHPEADZ_1401CE6B0.cpp" />
    <ClCompile Include="source\post_senditemCMgrAvatorItemHistoryQEAAXPEADPEAU_db_14023E200.cpp" />
    <ClCompile Include="source\PotionSocketDivisionRequestCNetworkEXAEAA_NHPEADZ_1401D6F90.cpp" />
    <ClCompile Include="source\PotionSocketSeparationRequestCNetworkEXAEAA_NHPEAD_1401D6E90.cpp" />
    <ClCompile Include="source\PreTranslateMessageCDialogUEAAHPEAUtagMSGZ_0_1404DBD00.cpp" />
    <ClCompile Include="source\PreTranslateMessageCFormViewMEAAHPEAUtagMSGZ_0_1404DC14A.cpp" />
    <ClCompile Include="source\PreTranslateMessageCFrameWndUEAAHPEAUtagMSGZ_0_1404DBE02.cpp" />
    <ClCompile Include="source\PreTranslateMessageCPropertyPageMEAAHPEAUtagMSGZ_0_1404DC318.cpp" />
    <ClCompile Include="source\PreTranslateMessageCPropertySheetUEAAHPEAUtagMSGZ__1404DC2BE.cpp" />
    <ClCompile Include="source\PreTranslateMessageCWinThreadUEAAHPEAUtagMSGZ_0_1404DBF58.cpp" />
    <ClCompile Include="source\ProcessMessageCMsgDataEEAAXPEAU_messageZ_1404387D0.cpp" />
    <ClCompile Include="source\ProcessMessageCMsgProcessEEAAXPEAU_messageZ_1401BFFD0.cpp" />
    <ClCompile Include="source\ProcessMessageFilterCWinThreadUEAAHHPEAUtagMSGZ_0_1404DBF7C.cpp" />
    <ClCompile Include="source\ProcessRecoverableMessagePK_SignatureMessageEncodi_14058F910.cpp" />
    <ClCompile Include="source\ProcessSemisignaturePK_SignatureMessageEncodingMet_14058F8F0.cpp" />
    <ClCompile Include="source\PumpMessageCWinThreadUEAAHXZ_0_1404DBF5E.cpp" />
    <ClCompile Include="source\PumpMessages2SourceTemplateVFileStoreCryptoPPCrypt_140454160.cpp" />
    <ClCompile Include="source\PumpMessages2SourceTemplateVStringStoreCryptoPPCry_14057E280.cpp" />
    <ClCompile Include="source\PushAnsyncConnectCNetProcessQEAA_NKPEAUsockaddr_in_140479890.cpp" />
    <ClCompile Include="source\pushdata_qry_case_post_sendQEAA_NKEKKPEAD000U_INVE_140328330.cpp" />
    <ClCompile Include="source\PushEmptyBufCMsgDataAEAAXPEAU_messageZ_1404382E0.cpp" />
    <ClCompile Include="source\PushIPCheckListCNetSocketQEAA_NKZ_14047ED20.cpp" />
    <ClCompile Include="source\PushMsgCMsgDataAEAAXPEAU_messageZ_140438180.cpp" />
    <ClCompile Include="source\push_backdequeUMessageRangeMeterFilterCryptoPPVall_140600050.cpp" />
    <ClCompile Include="source\push_frontdequeURECV_DATAVallocatorURECV_DATAstdst_14031A500.cpp" />
    <ClCompile Include="source\Put2MessageQueueCryptoPPUEAA_KPEBE_KH_NZ_140655040.cpp" />
    <ClCompile Include="source\PutMessageNameCryptoPPYAPEBDXZ_1405FF5C0.cpp" />
    <ClCompile Include="source\PutMessageYAXPEAD0Z_140509BF0.cpp" />
    <ClCompile Include="source\ReConnectDataBaseCRFNewDatabaseQEAA_NXZ_140486B00.cpp" />
    <ClCompile Include="source\RecoverablePartFirstPK_SignatureMessageEncodingMet_14058F850.cpp" />
    <ClCompile Include="source\RecoverAndRestartDL_VerifierBaseUEC2NPointCryptoPP_1405674D0.cpp" />
    <ClCompile Include="source\RecoverAndRestartDL_VerifierBaseUECPPointCryptoPPC_140565830.cpp" />
    <ClCompile Include="source\RecoverAndRestartDL_VerifierBaseVIntegerCryptoPPCr_140563A90.cpp" />
    <ClCompile Include="source\RecoverAndRestartTF_VerifierBaseCryptoPPUEBAAUDeco_1406234C0.cpp" />
    <ClCompile Include="source\RecoverMessageFromRepresentativePK_SignatureMessag_14058F970.cpp" />
    <ClCompile Include="source\RecoverMessageFromSemisignaturePK_SignatureMessage_14058FA20.cpp" />
    <ClCompile Include="source\RecoverMessagePK_VerifierCryptoPPUEBAAUDecodingRes_1405F6430.cpp" />
    <ClCompile Include="source\RecoverPK_VerifierCryptoPPUEBAAUDecodingResult2PEA_1405F6390.cpp" />
    <ClCompile Include="source\RecvClientLineCHackShieldExSystemUEAA_NHPEAU_MSG_H_1404172C0.cpp" />
    <ClCompile Include="source\RecvCNetSocketQEAA_NKPEADHPEAHZ_14047E9C0.cpp" />
    <ClCompile Include="source\RecvGameGuardDataCNationSettingManagerQEAA_NHPEAU__140229370.cpp" />
    <ClCompile Include="source\RecvThreadCNetProcessCAXPEAXZ_140478340.cpp" />
    <ClCompile Include="source\recv_0_1404DBA5E.cpp" />
    <ClCompile Include="source\Recv_ApexInformCChiNetworkEXQEAAXKKPEADZ_1404103A0.cpp" />
    <ClCompile Include="source\Recv_ApexKillCChiNetworkEXQEAAXKKPEADZ_140410460.cpp" />
    <ClCompile Include="source\RefSingletonVDL_SignatureMessageEncodingMethod_DSA_140589FA0.cpp" />
    <ClCompile Include="source\RefSingletonVDL_SignatureMessageEncodingMethod_NRC_14063AB10.cpp" />
    <ClCompile Include="source\releaseauto_ptrVPK_MessageAccumulatorBaseCryptoPPs_14056C6D0.cpp" />
    <ClCompile Include="source\ReleaseCNetSocketQEAAXXZ_14047E140.cpp" />
    <ClCompile Include="source\releasemember_ptrVPK_MessageAccumulatorCryptoPPCry_140600260.cpp" />
    <ClCompile Include="source\resetmember_ptrVPK_MessageAccumulatorCryptoPPCrypt_140600EE0.cpp" />
    <ClCompile Include="source\RestartMessageAccumulatorDL_SignerBaseUEC2NPointCr_14056C2E0.cpp" />
    <ClCompile Include="source\RestartMessageAccumulatorDL_SignerBaseUECPPointCry_14056C010.cpp" />
    <ClCompile Include="source\RestartMessageAccumulatorDL_SignerBaseVIntegerCryp_14056BEA0.cpp" />
    <ClCompile Include="source\RNewObjectVDL_SignatureMessageEncodingMethod_DSACr_14058CE00.cpp" />
    <ClCompile Include="source\RNewObjectVDL_SignatureMessageEncodingMethod_NRCry_14063BAA0.cpp" />
    <ClCompile Include="source\Select_PostRecvSerialFromNameCRFWorldDatabaseQEAAE_1404B1FE0.cpp" />
    <ClCompile Include="source\Select_PostRecvStorageCheckCRFWorldDatabaseQEAAHKZ_1404B23E0.cpp" />
    <ClCompile Include="source\SendBuyErrorResultCUnmannedTraderUserInfoQEAAXGEZ_140357FE0.cpp" />
    <ClCompile Include="source\SendCancelRegistErrorResultCUnmannedTraderUserInfo_140357EA0.cpp" />
    <ClCompile Include="source\SendCancelRegistSuccessResultCUnmannedTraderUserIn_140357F30.cpp" />
    <ClCompile Include="source\SendCancelWebCRaceBossMsgControllerIEAAXEPEAVCMsgR_1402A17B0.cpp" />
    <ClCompile Include="source\SendCancleInfomManagerCRaceBossMsgControllerIEAAXG_1402A16C0.cpp" />
    <ClCompile Include="source\SendCancleInfomSenderCRaceBossMsgControllerIEAAXKZ_1402A1610.cpp" />
    <ClCompile Include="source\SendCashDBDSNRequestCNationSettingDataGBUEAAXXZ_14022C0F0.cpp" />
    <ClCompile Include="source\SendCashDBDSNRequestCNationSettingDataKRUEAAXXZ_14022B4C0.cpp" />
    <ClCompile Include="source\SendCashDBDSNRequestCNationSettingDataNULLUEAAXXZ_1402130F0.cpp" />
    <ClCompile Include="source\SendCashDBDSNRequestCNationSettingDataRUUEAAXXZ_14022E840.cpp" />
    <ClCompile Include="source\SendCashDBDSNRequestCNationSettingDataUEAAXXZ_140211D80.cpp" />
    <ClCompile Include="source\SendCashDBDSNRequestCNationSettingManagerQEAAXXZ_140208060.cpp" />
    <ClCompile Include="source\SendCChiNetworkEXQEAAHPEAEKPEADGZ_14040FD20.cpp" />
    <ClCompile Include="source\SendCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA_1403CE3D0.cpp" />
    <ClCompile Include="source\SendCEngNetworkBillEXQEAAHPEAEPEADGZ_14031BA40.cpp" />
    <ClCompile Include="source\SendCGuildBattleRankManagerGUILD_BATTLEQEAAXHEKEEK_1403CA9C0.cpp" />
    <ClCompile Include="source\SendCGuildBattleReservedScheduleListManagerGUILD_B_1403CD830.cpp" />
    <ClCompile Include="source\SendChangeAggroDataCMonsterAggroMgrQEAAXXZ_14015E950.cpp" />
    <ClCompile Include="source\SendCMsgListManagerRACE_BOSS_MSGQEAAHEKPEBD0AEAPEA_14029F930.cpp" />
    <ClCompile Include="source\SendCNetSocketQEAA_NKPEADHPEAHZ_14047EA70.cpp" />
    <ClCompile Include="source\SendCNormalGuildBattleGuildMemberGUILD_BATTLEQEAAX_1403E0360.cpp" />
    <ClCompile Include="source\SendComfirmWebCRaceBossMsgControllerIEAAXEPEAVCMsg_1402A10F0.cpp" />
    <ClCompile Include="source\SendConfirmCtrlCRaceBossMsgControllerIEAAXEPEAVCMs_1402A1250.cpp" />
    <ClCompile Include="source\SendCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403D9990.cpp" />
    <ClCompile Include="source\SendCRaceBossMsgControllerQEAA_NEKPEBD0KZ_1402A07C0.cpp" />
    <ClCompile Include="source\SendCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CD040.cpp" />
    <ClCompile Include="source\SendCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCAD0.cpp" />
    <ClCompile Include="source\SendCReservedGuildSchedulePageGUILD_BATTLEQEAAXHKP_1403CBE20.cpp" />
    <ClCompile Include="source\SendCTotalGuildRankInfoQEAAXKHEEKZ_1402C8E60.cpp" />
    <ClCompile Include="source\SendCurrentBattleInfoRequestCGuildBattleController_1403D6A80.cpp" />
    <ClCompile Include="source\SendCurrHonorGuildListCHonorGuildQEAAXGEEZ_14025ED00.cpp" />
    <ClCompile Include="source\SendCWeeklyGuildRankInfoQEAAXKHEEKZ_1402CAFD0.cpp" />
    <ClCompile Include="source\SendDeleteNotifyPositionMemberCNormalGuildBattleGu_1403E2D80.cpp" />
    <ClCompile Include="source\SendDQS_RoomInsertCGuildRoomInfoAEAAXXZ_1402E66F0.cpp" />
    <ClCompile Include="source\SendDQS_RoomUpdateCGuildRoomInfoAEAAXXZ_1402E6790.cpp" />
    <ClCompile Include="source\SendDrawResultCNormalGuildBattleGUILD_BATTLEIEAAXX_1403E78E0.cpp" />
    <ClCompile Include="source\SendErrorResultCPossibleBattleGuildListManagerGUIL_1403CA260.cpp" />
    <ClCompile Include="source\SendExternMsgUs_HFSMSAXPEAV1KPEAXHZ_140162D30.cpp" />
    <ClCompile Include="source\SendFirstCPossibleBattleGuildListManagerGUILD_BATT_1403D9920.cpp" />
    <ClCompile Include="source\SendHolyStoneHPToRaceBossCHolyStoneSystemQEAAXXZ_14027E9C0.cpp" />
    <ClCompile Include="source\SendInfoCPossibleBattleGuildListManagerGUILD_BATTL_1403CA110.cpp" />
    <ClCompile Include="source\SendInfomSenderCRaceBossMsgControllerIEAAXKEZ_1402A13B0.cpp" />
    <ClCompile Include="source\SendInformChangeCHonorGuildQEAAXEGZ_14025F850.cpp" />
    <ClCompile Include="source\SendIsArriveDestroyerCHolyStoneSystemQEAAXEZ_14027E860.cpp" />
    <ClCompile Include="source\SendKillInformCNormalGuildBattleGUILD_BATTLEIEAAXX_1403E7590.cpp" />
    <ClCompile Include="source\SendListCGuildListQEAAXGEEZ_14025DB20.cpp" />
    <ClCompile Include="source\SendLoopYAKPEAXZ_140319840.cpp" />
    <ClCompile Include="source\SendMemberPositionCNormalGuildBattleManagerGUILD_B_1403D4CD0.cpp" />
    <ClCompile Include="source\SendMessageACWndQEAA_JI_K_JZ_0_1404DC40E.cpp" />
    <ClCompile Include="source\SendMessageA_0_1404DEF6A.cpp" />
    <ClCompile Include="source\SendMsgAccount_UILockRefresh_UpdateCUserDBQEAAXXZ_140118720.cpp" />
    <ClCompile Include="source\SendMsgAlterStateCGravityStoneRegenerAEAAXXZ_14012EF50.cpp" />
    <ClCompile Include="source\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E2730.cpp" />
    <ClCompile Include="source\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E2800.cpp" />
    <ClCompile Include="source\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E28E0.cpp" />
    <ClCompile Include="source\SendMsgCreateCCircleZoneAEAAXXZ_14012DC60.cpp" />
    <ClCompile Include="source\SendMsgGoalCCircleZoneAEAAXXZ_14012DDA0.cpp" />
    <ClCompile Include="source\SendMsgRequestResultCRaceBossMsgControllerIEAAXGEZ_1402A1060.cpp" />
    <ClCompile Include="source\SendMsgSucceedBuyCashDbWorkerAEAAXGAEBU_param_cash_1402F14F0.cpp" />
    <ClCompile Include="source\SendMsgToMaster_NoCompleteQuestFromNPCCQuestMgrQEA_14028B530.cpp" />
    <ClCompile Include="source\SendMsgToMaster_NoHaveGiveItemCQuestMgrQEAAXEZ_14028B5C0.cpp" />
    <ClCompile Include="source\SendMsgToMaster_NoHaveReturnItemCQuestMgrQEAAXEZ_14028B650.cpp" />
    <ClCompile Include="source\SendMsgToMaster_ReturnItemAfterQuestCQuestMgrQEAAX_14028B480.cpp" />
    <ClCompile Include="source\SendMsgUs_HFSMSAXPEAV1KKPEAXZ_140162C00.cpp" />
    <ClCompile Include="source\SendMsg_AddEffectCNuclearBombQEAAXXZ_14013CE00.cpp" />
    <ClCompile Include="source\SendMsg_AddJoinApplierCGuildQEAAXPEAU_guild_applie_140256750.cpp" />
    <ClCompile Include="source\SendMsg_AlterMemberGradeCGuildQEAAXXZ_1402576B0.cpp" />
    <ClCompile Include="source\SendMsg_AlterMemberStateCGuildQEAAXXZ_140257490.cpp" />
    <ClCompile Include="source\SendMsg_AlterTransparCTrapQEAAX_NZ_14013FD90.cpp" />
    <ClCompile Include="source\SendMsg_AnimusActHealInformCAnimusQEAAXKHZ_14012A730.cpp" />
    <ClCompile Include="source\SendMsg_ApplyGuildBattleResultInformCGuildQEAAXEPE_140258550.cpp" />
    <ClCompile Include="source\SendMsg_AttackCGuardTowerQEAAXPEAVCAttackZ_140130840.cpp" />
    <ClCompile Include="source\SendMsg_AttackCHolyKeeperQEAAXXZ_140134EF0.cpp" />
    <ClCompile Include="source\SendMsg_AttackCNuclearBombQEAAXHHZ_14013CDE0.cpp" />
    <ClCompile Include="source\SendMsg_AttackCTrapQEAAXPEAVCAttackZ_14013FA00.cpp" />
    <ClCompile Include="source\SendMsg_Attack_ForceCMonsterQEAAXPEAVCMonsterAttac_14014EE20.cpp" />
    <ClCompile Include="source\SendMsg_Attack_GenCAnimusQEAAXPEAVCAttackZ_14012A4C0.cpp" />
    <ClCompile Include="source\SendMsg_Attack_GenCMonsterQEAAXPEAVCMonsterAttackZ_14014EC70.cpp" />
    <ClCompile Include="source\SendMsg_BillingInfoCUserDBQEAAXXZ_140118270.cpp" />
    <ClCompile Include="source\SendMsg_BreakStopCGameObjectQEAAXXZ_14017B140.cpp" />
    <ClCompile Include="source\SendMsg_BuyCashItemICsSendInterfaceSAXGPEBU_param__14030C760.cpp" />
    <ClCompile Include="source\SendMsg_CashDiscountEventInformICsSendInterfaceSAX_14030CC30.cpp" />
    <ClCompile Include="source\SendMsg_CashEventInformICsSendInterfaceSAXGEEPEAU__14030CE00.cpp" />
    <ClCompile Include="source\SendMsg_ChangeTaxRateCGuildQEAAXEZ_140255730.cpp" />
    <ClCompile Include="source\SendMsg_Change_MonsterRotateCMonsterQEAAXXZ_140148790.cpp" />
    <ClCompile Include="source\SendMsg_Change_MonsterStateCMonsterQEAAXXZ_140148700.cpp" />
    <ClCompile Include="source\SendMsg_ChannelCloseCDarkHoleChannelQEAAXXZ_14026C4A0.cpp" />
    <ClCompile Include="source\SendMsg_ConditionalEventInformICsSendInterfaceSAXG_14030D1A0.cpp" />
    <ClCompile Include="source\SendMsg_CouponEnsureCCouponMgrQEAAXGEZ_1403FDFB0.cpp" />
    <ClCompile Include="source\SendMsg_CouponErrorCCouponMgrQEAAXGEZ_1403FE110.cpp" />
    <ClCompile Include="source\SendMsg_CouponLendResultCCouponMgrQEAAXGPEAU_db_co_1403FE230.cpp" />
    <ClCompile Include="source\SendMsg_CreateCAnimusQEAAXXZ_140129EF0.cpp" />
    <ClCompile Include="source\SendMsg_CreateCDarkHoleQEAAXXZ_140163F10.cpp" />
    <ClCompile Include="source\SendMsg_CreateCGravityStoneAEAAXXZ_140165140.cpp" />
    <ClCompile Include="source\SendMsg_CreateCGuardTowerQEAAXXZ_140130680.cpp" />
    <ClCompile Include="source\SendMsg_CreateCHolyKeeperQEAAXXZ_140134D70.cpp" />
    <ClCompile Include="source\SendMsg_CreateCHolyStoneQEAAXXZ_1401379D0.cpp" />
    <ClCompile Include="source\SendMsg_CreateCItemBoxQEAAXXZ_140166660.cpp" />
    <ClCompile Include="source\SendMsg_CreateCMerchantQEAAXXZ_1401393E0.cpp" />
    <ClCompile Include="source\SendMsg_CreateCMonsterQEAAXXZ_140148380.cpp" />
    <ClCompile Include="source\SendMsg_CreateCParkingUnitQEAAXXZ_140167CE0.cpp" />
    <ClCompile Include="source\SendMsg_CreateCReturnGateIEAAXXZ_140168D50.cpp" />
    <ClCompile Include="source\SendMsg_CreateCTrapQEAAXXZ_14013F7F0.cpp" />
    <ClCompile Include="source\SendMsg_DelJoinApplierCGuildQEAAXPEAU_guild_applie_140256970.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCAnimusQEAAXXZ_14012A020.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCDarkHoleQEAAXXZ_140164040.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCGravityStoneAEAAXXZ_140165220.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCGuardTowerQEAAXEZ_140130780.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCHolyKeeperQEAAXEZ_140134E60.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCHolyStoneQEAAXEKZ_140137AD0.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCItemBoxQEAAXXZ_140166830.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCMerchantQEAAXXZ_1401394D0.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCMonsterQEAAXEZ_1401489D0.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCParkingUnitQEAAXEZ_140167E20.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCReturnGateIEAAXXZ_140168E60.cpp" />
    <ClCompile Include="source\SendMsg_DestroyCTrapQEAAXEZ_14013F900.cpp" />
    <ClCompile Include="source\SendMsg_DownPacketCGuildQEAAXEPEAU_guild_member_in_140255470.cpp" />
    <ClCompile Include="source\SendMsg_DropMissileCNuclearBombQEAAXXZ_14013CD10.cpp" />
    <ClCompile Include="source\SendMsg_EconomyDataToWebYAXXZ_1402A5110.cpp" />
    <ClCompile Include="source\SendMsg_Emotion_PresentationCMonsterQEAAXEGGHZ_1401488E0.cpp" />
    <ClCompile Include="source\SendMsg_EndBattleCHolyStoneSystemQEAAXEZ_14027EC30.cpp" />
    <ClCompile Include="source\SendMsg_EnterKeeperCHolyStoneSystemQEAAXHZ_14027F510.cpp" />
    <ClCompile Include="source\SendMsg_EnterStoneCHolyStoneSystemQEAAXHZ_14027F9A0.cpp" />
    <ClCompile Include="source\SendMsg_ErrorICsSendInterfaceSAXGHZ_14030C590.cpp" />
    <ClCompile Include="source\SendMsg_ExitStoneCHolyStoneSystemQEAAXXZ_14027EB70.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionAutominePersonalUEAAXHZ_1402DCD60.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCAnimusUEAAXHZ_14012A1E0.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCCircleZoneEEAAXHZ_14012DD00.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCDarkHoleUEAAXHZ_1401642F0.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCGameObjectUEAAXHZ_14013E3F0.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCGravityStoneRegenerEEAAXHZ_14012EE90.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCGravityStoneUEAAXHZ_140164F70.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCGuardTowerUEAAXHZ_140130930.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCHolyKeeperUEAAXHZ_1401351D0.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCHolyStoneUEAAXHZ_140137B80.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCItemBoxUEAAXHZ_1401668B0.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCMerchantUEAAXHZ_140139A80.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCMonsterUEAAXHZ_140148490.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCParkingUnitUEAAXHZ_140167FD0.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCReturnGateUEAAXHZ_140168BD0.cpp" />
    <ClCompile Include="source\SendMsg_FixPositionCTrapUEAAXHZ_14013FB80.cpp" />
    <ClCompile Include="source\SendMsg_GateDestroyCDarkHoleChannelQEAAXPEAEPEADHZ_14026CD30.cpp" />
    <ClCompile Include="source\SendMsg_GoodsListICsSendInterfaceSAXGPEBU_param_ca_14030C620.cpp" />
    <ClCompile Include="source\SendMsg_GuildBattleProposedCGuildQEAAHPEADZ_14025A120.cpp" />
    <ClCompile Include="source\SendMsg_GuildBattleRefusedCGuildQEAAXPEADZ_14025A220.cpp" />
    <ClCompile Include="source\SendMsg_GuildBattleSuggestResultCGuildQEAAXEPEADZ_1402583F0.cpp" />
    <ClCompile Include="source\SendMsg_GuildDisjointInformCGuildQEAAXXZ_140256FE0.cpp" />
    <ClCompile Include="source\SendMsg_GuildInfoUpdateInformCGuildQEAAXXZ_140255850.cpp" />
    <ClCompile Include="source\SendMsg_GuildJoinAcceptInformCGuildQEAAXPEAU_guild_140256AE0.cpp" />
    <ClCompile Include="source\SendMsg_GuildMemberLogoffCGuildQEAAXKZ_140257250.cpp" />
    <ClCompile Include="source\SendMsg_GuildMemberPosInformCGuildQEAAXKGGZ_140257360.cpp" />
    <ClCompile Include="source\SendMsg_GuildOutputMoneyFailCGuildQEAAXKZ_140256ED0.cpp" />
    <ClCompile Include="source\SendMsg_GuildRoomRentedCGuildQEAAXEZ_1402586A0.cpp" />
    <ClCompile Include="source\SendMsg_HolyKeeperAttackAbleStateCHolyStoneSystemQ_14027FEE0.cpp" />
    <ClCompile Include="source\SendMsg_HolyKeeperStateChaosCHolyStoneSystemQEAAXX_14027FE10.cpp" />
    <ClCompile Include="source\SendMsg_HolyStoneSystemStateCHolyStoneSystemQEAAXH_14027F410.cpp" />
    <ClCompile Include="source\SendMsg_InformAttackCNuclearBombQEAAXXZ_14013CF70.cpp" />
    <ClCompile Include="source\SendMsg_InformDropPosCNuclearBombQEAAXXZ_14013CB90.cpp" />
    <ClCompile Include="source\SendMsg_Inform_UILockCUserDBQEAAXXZ_140118650.cpp" />
    <ClCompile Include="source\SendMsg_InPcBangTimeCCouponMgrQEAAXGZ_1403FE040.cpp" />
    <ClCompile Include="source\SendMsg_IOMoneyCGuildQEAAXKNN_NPEAEZ_140256CF0.cpp" />
    <ClCompile Include="source\SendMsg_JobCountCDarkHoleChannelQEAAXHHZ_14026BCF0.cpp" />
    <ClCompile Include="source\SendMsg_JobPassCDarkHoleChannelQEAAXHZ_14026C140.cpp" />
    <ClCompile Include="source\SendMsg_KickForSailCTransportShipQEAAXHZ_1402653D0.cpp" />
    <ClCompile Include="source\SendMsg_LeaveMemberCGuildQEAAXK_N0Z_140255A30.cpp" />
    <ClCompile Include="source\SendMsg_LimitedsaleEventInformICsSendInterfaceSAXG_14030D2B0.cpp" />
    <ClCompile Include="source\SendMsg_MachineInfoAutoMineMachineQEAAXHZ_1402D2830.cpp" />
    <ClCompile Include="source\SendMsg_ManageGuildCommitteeResultCGuildQEAAX_NPEA_140259B00.cpp" />
    <ClCompile Include="source\SendMsg_MasterDieCNuclearBombQEAAXXZ_14013D0D0.cpp" />
    <ClCompile Include="source\SendMsg_MasterElectPossibleCGuildQEAAX_NZ_140259D50.cpp" />
    <ClCompile Include="source\SendMsg_MissionPassCDarkHoleChannelQEAAXXZ_14026C230.cpp" />
    <ClCompile Include="source\SendMsg_MoneySupplyDataToWebCMoneySupplyMgrQEAAXPE_14042F5F0.cpp" />
    <ClCompile Include="source\SendMsg_MoveCAnimusQEAAXXZ_14012A0D0.cpp" />
    <ClCompile Include="source\SendMsg_MoveCHolyKeeperQEAAXXZ_1401350A0.cpp" />
    <ClCompile Include="source\SendMsg_MoveCMerchantQEAAXXZ_140139560.cpp" />
    <ClCompile Include="source\SendMsg_MoveCMonsterQEAAXXZ_140148A70.cpp" />
    <ClCompile Include="source\SendMsg_NewMissionCDarkHoleChannelQEAAXXZ_14026C580.cpp" />
    <ClCompile Include="source\SendMsg_NoticeNextQuestCHolyStoneSystemQEAAXHEZ_140280150.cpp" />
    <ClCompile Include="source\SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeperCHol_14027C7E0.cpp" />
    <ClCompile Include="source\SendMsg_NuclearFindCNuclearBombQEAAXHEZ_14013CAF0.cpp" />
    <ClCompile Include="source\SendMsg_OpenPortalByReactCDarkHoleChannelQEAAXHZ_14026BE00.cpp" />
    <ClCompile Include="source\SendMsg_OpenPortalByResultCDarkHoleChannelQEAAXHZ_14026BEF0.cpp" />
    <ClCompile Include="source\SendMsg_PatriarchTaxRateTRC_AutoTradeQEAAXHZ_1402D8480.cpp" />
    <ClCompile Include="source\SendMsg_PvpCashInformCPvpCashPointQEAAXHEZ_1403F5940.cpp" />
    <ClCompile Include="source\SendMsg_PvpRankListDataCPvpUserRankingInfoAEAAXGEE_14032E020.cpp" />
    <ClCompile Include="source\SendMsg_PvpRankListNodataCPvpUserRankingInfoAEAAXG_14032DF70.cpp" />
    <ClCompile Include="source\SendMsg_QueryAppointResultClassOrderProcessorQEAAX_1402B8E60.cpp" />
    <ClCompile Include="source\SendMsg_QueryPacket_InfoCGuildQEAAXHZ_1402559B0.cpp" />
    <ClCompile Include="source\SendMsg_QuestPassCDarkHoleChannelQEAAXXZ_14026C3C0.cpp" />
    <ClCompile Include="source\SendMsg_RealAddLimTimeCDarkHoleChannelQEAAXHPEADZ_14026CB80.cpp" />
    <ClCompile Include="source\SendMsg_RealFixPositionCGameObjectUEAAX_NZ_14017B050.cpp" />
    <ClCompile Include="source\SendMsg_RealFixPositionCMerchantUEAAX_NZ_14013A1E0.cpp" />
    <ClCompile Include="source\SendMsg_RealMovePointCAnimusUEAAXHZ_14012A330.cpp" />
    <ClCompile Include="source\SendMsg_RealMovePointCGameObjectUEAAXHZ_14012E0A0.cpp" />
    <ClCompile Include="source\SendMsg_RealMovePointCHolyKeeperUEAAXHZ_1401352D0.cpp" />
    <ClCompile Include="source\SendMsg_RealMovePointCMerchantUEAAXHZ_140139BA0.cpp" />
    <ClCompile Include="source\SendMsg_RealMovePointCMonsterUEAAXHZ_1401485C0.cpp" />
    <ClCompile Include="source\SendMsg_RealMsgInformCDarkHoleChannelQEAAXPEADZ_14026CA60.cpp" />
    <ClCompile Include="source\SendMsg_RecoverResultCPvpCashPointQEAAXHEHZ_1403F58A0.cpp" />
    <ClCompile Include="source\SendMsg_RemainBufUseTimeCExtPotionBufQEAAX_NGHHHZ_14039FC70.cpp" />
    <ClCompile Include="source\SendMsg_RemainCouponInformCCouponMgrQEAAXGEZ_1403FE1A0.cpp" />
    <ClCompile Include="source\SendMsg_ResultCNuclearBombMgrQEAAXHEZ_14013B5B0.cpp" />
    <ClCompile Include="source\SendMsg_ResultCNuclearBombQEAAXHEZ_14013D260.cpp" />
    <ClCompile Include="source\SendMsg_ResultCodeAutoMineMachineMngQEBAXHEEZ_1402D6B90.cpp" />
    <ClCompile Include="source\SendMsg_ResultCodeAutoMineMachineQEBAXHEEZ_1402D2A80.cpp" />
    <ClCompile Include="source\SendMsg_ResultCodePatriarchElectProcessorQEAAXKEZ_1402BAFB0.cpp" />
    <ClCompile Include="source\SendMsg_RoomTimeOverCGuildRoomInfoAEAAXXZ_1402E6890.cpp" />
    <ClCompile Include="source\SendMsg_SetHPInformCGameObjectUEAAXXZ_14012C650.cpp" />
    <ClCompile Include="source\SendMsg_StartBattleCHolyStoneSystemQEAAXXZ_14027FD40.cpp" />
    <ClCompile Include="source\SendMsg_StartBillingCBillingIEAAXXZ_14028D550.cpp" />
    <ClCompile Include="source\SendMsg_StartedVoteInformCVoteSystemQEAAXHK_NZ_1402B0890.cpp" />
    <ClCompile Include="source\SendMsg_StateChangeCDarkHoleQEAAXXZ_140164240.cpp" />
    <ClCompile Include="source\SendMsg_StateChangeCItemBoxQEAAXXZ_140166A10.cpp" />
    <ClCompile Include="source\SendMsg_StoneAlterOperCHolyStoneQEAAXXZ_140137CD0.cpp" />
    <ClCompile Include="source\SendMsg_StunInformCGameObjectUEAAXXZ_140164850.cpp" />
    <ClCompile Include="source\SendMsg_TalikListCPvpCashPointQEAAXHZ_1403F57A0.cpp" />
    <ClCompile Include="source\sendmsg_taxrateTRC_AutoTradeQEAAXHEZ_1402D8320.cpp" />
    <ClCompile Include="source\SendMsg_TicketCheckCTransportShipQEAAXH_NGZ_140265330.cpp" />
    <ClCompile Include="source\SendMsg_TimeOutCDarkHoleChannelQEAAXXZ_14026C8D0.cpp" />
    <ClCompile Include="source\SendMsg_TowerCompleteInformCGuardTowerQEAAXXZ_140130A80.cpp" />
    <ClCompile Include="source\SendMsg_TransportShipStateCTransportShipQEAAXHZ_140265450.cpp" />
    <ClCompile Include="source\SendMsg_TransShipTicketNumInformCMerchantQEAAXHZ_140139650.cpp" />
    <ClCompile Include="source\SendMsg_TrapCompleteInformCTrapQEAAXXZ_14013FD10.cpp" />
    <ClCompile Include="source\SendMsg_VoteCancelInformCGuildQEAAXXZ_140256410.cpp" />
    <ClCompile Include="source\SendMsg_VoteCompleteCGuildQEAAX_NZ_140256630.cpp" />
    <ClCompile Include="source\SendMsg_VoteProcessInform_ContinueCGuildQEAAXPEAU__140255F20.cpp" />
    <ClCompile Include="source\SendMsg_VoteProcessInform_StartCGuildQEAAXXZ_140255B90.cpp" />
    <ClCompile Include="source\SendMsg_VoteStateCGuildQEAAXXZ_140256270.cpp" />
    <ClCompile Include="source\SendMsg_VoteStopCGuildQEAAXKZ_140256540.cpp" />
    <ClCompile Include="source\SendMsg_WaitKeeperCHolyStoneSystemQEAAXHEZ_14027F7C0.cpp" />
    <ClCompile Include="source\SendMsg_WaitStoneCHolyStoneSystemQEAAXHZ_14027F8C0.cpp" />
    <ClCompile Include="source\SendMsg_ZoneAliveCheckCBillingManagerQEAAXKZ_1401C42C0.cpp" />
    <ClCompile Include="source\SendMsg_ZoneAliveCheckCBillingQEAAXKZ_14028D760.cpp" />
    <ClCompile Include="source\SendNextHonorGuildListCHonorGuildQEAAXGEZ_14025EDB0.cpp" />
    <ClCompile Include="source\SendNotifyCloseItemCUnmannedTraderUserInfoQEAAXGGK_140357A50.cpp" />
    <ClCompile Include="source\SendNotifyHolyStoneDestroyedToRaceBossCHolyStoneSy_14027FC00.cpp" />
    <ClCompile Include="source\SendPossibleBattleGuildListCGuildBattleControllerQ_1403D6910.cpp" />
    <ClCompile Include="source\SendPossibleBattleGuildListFirstCGuildBattleContro_1403D68B0.cpp" />
    <ClCompile Include="source\SendRaceBossMsgFromWebRequestCNetworkEXAEAA_NHPEAD_1401DA990.cpp" />
    <ClCompile Include="source\SendRankListCGuildBattleControllerQEAAXHEKIEKZ_1403D6980.cpp" />
    <ClCompile Include="source\SendRegenBallCNormalGuildBattleGuildGUILD_BATTLEQE_1403E2150.cpp" />
    <ClCompile Include="source\SendRegistItemErrorResultCUnmannedTraderUserInfoQE_140357B10.cpp" />
    <ClCompile Include="source\SendRegistItemSuccessResultCUnmannedTraderUserInfo_140357BE0.cpp" />
    <ClCompile Include="source\SendRequestWebCRaceBossMsgControllerIEAAXEPEAVCMsg_1402A1480.cpp" />
    <ClCompile Include="source\SendReservedScheduleListCGuildBattleControllerQEAA_1403D6A00.cpp" />
    <ClCompile Include="source\SendSearchErrorResultCUnmannedTraderUserInfoQEAAXG_140358150.cpp" />
    <ClCompile Include="source\SendSearchResultCUnmannedTraderUserInfoQEAAXGPEADZ_140358210.cpp" />
    <ClCompile Include="source\SendSellInfomCUnmannedTraderUserInfoQEAAXGGKKKZ_140358090.cpp" />
    <ClCompile Include="source\SendSMS_CompleteQuestCHolyStoneSystemQEAAXEPEADH0E_14027ED20.cpp" />
    <ClCompile Include="source\SendSMS_MineTimeExtendCHolyStoneSystemQEAAXHZ_14027E720.cpp" />
    <ClCompile Include="source\SendStartNotifyCommitteeMemberPositionCNormalGuild_1403E2250.cpp" />
    <ClCompile Include="source\SendTaxRateCUnmannedTraderTaxRateManagerQEAAXHEZ_14038E420.cpp" />
    <ClCompile Include="source\SendTaxRatePatriarchCUnmannedTraderTaxRateManagerQ_14038E4C0.cpp" />
    <ClCompile Include="source\SendThreadCNetProcessCAXPEAXZ_140478BD0.cpp" />
    <ClCompile Include="source\SendWebAddScheduleInfoCNormalGuildBattleGUILD_BATT_1403E8690.cpp" />
    <ClCompile Include="source\SendWebBattleEndInfoCNormalGuildBattleGUILD_BATTLE_1403E6D50.cpp" />
    <ClCompile Include="source\SendWebBattleStartInfoCNormalGuildBattleGUILD_BATT_1403E6C90.cpp" />
    <ClCompile Include="source\SendWebRaceBossSMSCMainThreadQEAAXPEAU_DB_QRY_SYN__1401F40B0.cpp" />
    <ClCompile Include="source\SendWebRaceBossSMSErrorResultCRaceBossMsgControlle_1402A1890.cpp" />
    <ClCompile Include="source\SendWinLoseResultCNormalGuildBattleGUILD_BATTLEIEA_1403E79F0.cpp" />
    <ClCompile Include="source\send_0_1404DBA64.cpp" />
    <ClCompile Include="source\send_attackedAutominePersonalQEAAXXZ_1402DCB70.cpp" />
    <ClCompile Include="source\send_changed_packetAutominePersonalQEAAXHZ_1402DC930.cpp" />
    <ClCompile Include="source\send_current_stateAutominePersonalQEAAXXZ_1402DCEA0.cpp" />
    <ClCompile Include="source\send_ecodeAutominePersonalMgrQEAAXHEZ_1402E0C20.cpp" />
    <ClCompile Include="source\send_ecodeAutominePersonalQEAAXEZ_1402DCC90.cpp" />
    <ClCompile Include="source\send_installedAutominePersonalQEAAXXZ_1402DC9F0.cpp" />
    <ClCompile Include="source\SetErrorButRunMessageProcYAXP6AXPEADZZ_140509890.cpp" />
    <ClCompile Include="source\SetErrorMessageProcYAXP6AXPEADZZ_140509870.cpp" />
    <ClCompile Include="source\SetFtpConnectionWheatyExceptionReportQEAAXPEADI000_14043F640.cpp" />
    <ClCompile Include="source\SetMsg_messageQEAAXKKKKZ_140438920.cpp" />
    <ClCompile Include="source\SetPassablePacketCNetworkEXQEAAXKEEZ_140208100.cpp" />
    <ClCompile Include="source\SetRaceWarRecvrCPvpCashPointQEAAX_NZ_14007C320.cpp" />
    <ClCompile Include="source\SetReconnectFailExitFlagCRFNewDatabaseQEAAX_NZ_1402F2AB0.cpp" />
    <ClCompile Include="source\SetSocketCNetSocketQEAA_NPEAU_SOCK_TYPE_PARAMPEADZ_14047DDA0.cpp" />
    <ClCompile Include="source\SetWarningMessageProcYAXP6AXPEADZZ_140509880.cpp" />
    <ClCompile Include="source\SFContDelMessageCGameObjectUEAAXEE_N0Z_14012C6A0.cpp" />
    <ClCompile Include="source\SFContUpdateTimeMessageCGameObjectUEAAXEEHZ_14012C6C0.cpp" />
    <ClCompile Include="source\ShouldPropagateMessageEndFilterCryptoPPMEBA_NXZ_14044CF90.cpp" />
    <ClCompile Include="source\ShouldPropagateMessageSeriesEndFilterCryptoPPMEBA__14044CFA0.cpp" />
    <ClCompile Include="source\SignAndRestartDL_SignerBaseUEC2NPointCryptoPPCrypt_1405663C0.cpp" />
    <ClCompile Include="source\SignAndRestartDL_SignerBaseUECPPointCryptoPPCrypto_140564720.cpp" />
    <ClCompile Include="source\SignAndRestartDL_SignerBaseVIntegerCryptoPPCryptoP_1405622F0.cpp" />
    <ClCompile Include="source\SignAndRestartTF_SignerBaseCryptoPPUEBA_KAEAVRando_140622BF0.cpp" />
    <ClCompile Include="source\SignMessagePK_SignerCryptoPPUEBA_KAEAVRandomNumber_1405F5FF0.cpp" />
    <ClCompile Include="source\SignMessageWithRecoveryPK_SignerCryptoPPUEBA_KAEAV_1405F60E0.cpp" />
    <ClCompile Include="source\SignPK_SignerCryptoPPUEBA_KAEAVRandomNumberGenerat_1405F5F50.cpp" />
    <ClCompile Include="source\size_announ_message_receipt_udpQEAAHXZ_140095000.cpp" />
    <ClCompile Include="source\size_apex_send_ipQEAAHXZ_140410C20.cpp" />
    <ClCompile Include="source\size_apex_send_logoutQEAAHXZ_140410C40.cpp" />
    <ClCompile Include="source\size_apex_send_transQEAAHXZ_140410C30.cpp" />
    <ClCompile Include="source\size_chat_message_receipt_udpQEAAHXZ_140094EB0.cpp" />
    <ClCompile Include="source\size_chat_steal_message_gm_zoclQEAAHXZ_1403F8D50.cpp" />
    <ClCompile Include="source\size_connection_status_result_zoctQEAAHXZ_1401C7750.cpp" />
    <ClCompile Include="source\size_qry_case_post_sendQEAAHXZ_140328320.cpp" />
    <ClCompile Include="source\size_qry_case_sendwebracebosssmsQEAAHXZ_1401DAB00.cpp" />
    <ClCompile Include="source\size_qry_case_update_data_for_post_sendQEAAHXZ_1400CA7D0.cpp" />
    <ClCompile Include="source\size_talik_recvr_listQEAAHXZ_1403F6F30.cpp" />
    <ClCompile Include="source\SkipMessagesBufferedTransformationCryptoPPUEAAIIZ_1405F5340.cpp" />
    <ClCompile Include="source\socket_0_1404DBA58.cpp" />
    <ClCompile Include="source\sortV_Deque_iteratorUMessageRangeMeterFilterCrypto_140600FA0.cpp" />
    <ClCompile Include="source\sort_heapV_Deque_iteratorUMessageRangeMeterFilterC_140603520.cpp" />
    <ClCompile Include="source\SpyMessageQueueCryptoPPQEBAPEBEAEA_KZ_1406548E0.cpp" />
    <ClCompile Include="source\SQLConnect_0_1404DB9D4.cpp" />
    <ClCompile Include="source\SQLDisconnect_0_1404DB9F2.cpp" />
    <ClCompile Include="source\SQLSetConnectAttr_0_1404DB9DA.cpp" />
    <ClCompile Include="source\StaticAlgorithmNameDL_SSUDL_Keys_ECDSAVEC2NCryptoP_14056C440.cpp" />
    <ClCompile Include="source\StaticAlgorithmNameDL_SSUDL_Keys_ECDSAVECPCryptoPP_14056C0F0.cpp" />
    <ClCompile Include="source\StaticAlgorithmNameDL_SSUDL_SignatureKeys_GFPCrypt_140639280.cpp" />
    <ClCompile Include="source\StaticAlgorithmNameDL_SSUDL_SignatureKeys_GFPCrypt_1406395B0.cpp" />
    <ClCompile Include="source\swapMessageQueueCryptoPPQEAAXAEAV12Z_140654890.cpp" />
    <ClCompile Include="source\swapUMessageRangeMeterFilterCryptoPPstdYAXAEAUMess_140604E80.cpp" />
    <ClCompile Include="source\TotalBytesRetrievableMessageQueueCryptoPPUEBA_KXZ_1406551D0.cpp" />
    <ClCompile Include="source\TransferMessagesTo2BufferedTransformationCryptoPPQ_1405F53D0.cpp" />
    <ClCompile Include="source\TransferMessagesToBufferedTransformationCryptoPPQE_1405F7980.cpp" />
    <ClCompile Include="source\TransferTo2MessageQueueCryptoPPUEAA_KAEAVBufferedT_140654470.cpp" />
    <ClCompile Include="source\TranslateMessage_0_140676E7C.cpp" />
    <ClCompile Include="source\TruncatedFinalPK_MessageAccumulatorCryptoPPUEAAXPE_140562EB0.cpp" />
    <ClCompile Include="source\tutorial_process_report_recvCMgrAccountLobbyHistor_140234980.cpp" />
    <ClCompile Include="source\unchecked_copy_backwardV_Deque_iteratorUMessageRan_1406054E0.cpp" />
    <ClCompile Include="source\unchecked_fill_nPEAPEAUMessageRangeMeterFilterCryp_140603790.cpp" />
    <ClCompile Include="source\unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAXPE_14031B3E0.cpp" />
    <ClCompile Include="source\unchecked_uninitialized_copyPEAPEAUMessageRangeMet_140601520.cpp" />
    <ClCompile Include="source\unchecked_uninitialized_copyPEAPEAURECV_DATAPEAPEA_14031AE40.cpp" />
    <ClCompile Include="source\unchecked_uninitialized_fill_nPEAPEAUMessageRangeM_140601590.cpp" />
    <ClCompile Include="source\unchecked_uninitialized_fill_nPEAPEAURECV_DATA_KPE_14031AEF0.cpp" />
    <ClCompile Include="source\UpdatePacketClassOrderProcessorQEAAXEEZ_1402B9180.cpp" />
    <ClCompile Include="source\UpdatePK_MessageAccumulatorBaseCryptoPPUEAAXPEBE_K_1405630D0.cpp" />
    <ClCompile Include="source\UpdateSendCRaceBossMsgControllerIEAAXXZ_1402A0C80.cpp" />
    <ClCompile Include="source\Update_PostStorageSendToRecverCRFWorldDatabaseQEAA_1404B2F60.cpp" />
    <ClCompile Include="source\Update_RaceWarRecvrCPvpOrderViewQEAAX_NZ_1403F8140.cpp" />
    <ClCompile Include="source\VerifyAndRestartDL_VerifierBaseUEC2NPointCryptoPPC_140567100.cpp" />
    <ClCompile Include="source\VerifyAndRestartDL_VerifierBaseUECPPointCryptoPPCr_140565460.cpp" />
    <ClCompile Include="source\VerifyAndRestartDL_VerifierBaseVIntegerCryptoPPCry_1405636C0.cpp" />
    <ClCompile Include="source\VerifyAndRestartTF_VerifierBaseCryptoPPUEBA_NAEAVP_1406232D0.cpp" />
    <ClCompile Include="source\VerifyMessagePK_VerifierCryptoPPUEBA_NPEBE_K01Z_1405F6290.cpp" />
    <ClCompile Include="source\VerifyMessageRepresentativePK_DeterministicSignatu_140622520.cpp" />
    <ClCompile Include="source\VerifyMessageRepresentativePK_RecoverableSignature_1406226A0.cpp" />
    <ClCompile Include="source\VerifyPK_VerifierCryptoPPUEBA_NPEAVPK_MessageAccum_1405F6200.cpp" />
    <ClCompile Include="source\Y_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C80.cpp" />
    <ClCompile Include="source\Y_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EB30.cpp" />
    <ClCompile Include="source\Y_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603BC0.cpp" />
    <ClCompile Include="source\Y_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E980.cpp" />
    <ClCompile Include="source\Z_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603BF0.cpp" />
    <ClCompile Include="source\_Adjust_heapV_Deque_iteratorUMessageRangeMeterFilt_140604F00.cpp" />
    <ClCompile Include="source\_AllocatePEAUMessageRangeMeterFilterCryptoPPstdYAP_140601750.cpp" />
    <ClCompile Include="source\_AllocatePEAURECV_DATAstdYAPEAPEAURECV_DATA_KPEAPE_14031B190.cpp" />
    <ClCompile Include="source\_AllocateUMessageRangeMeterFilterCryptoPPstdYAPEAU_140601650.cpp" />
    <ClCompile Include="source\_AllocateURECV_DATAstdYAPEAURECV_DATA_KPEAU1Z_14031B020.cpp" />
    <ClCompile Include="source\_buybygold_buy_single_item_setsenddataCashItemRemo_140300650.cpp" />
    <ClCompile Include="source\_CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7C0.cpp" />
    <ClCompile Include="source\_CcrFG_rs_EncryptPacketYAHPEAXPEAEHZ_14066D7D2.cpp" />
    <ClCompile Include="source\_CheckSendCNetProcessAEAAXGZ_140479A30.cpp" />
    <ClCompile Include="source\_CkeckRecvBreakCNetProcessAEAAXXZ_14047B010.cpp" />
    <ClCompile Include="source\_CNetSocketCNetSocket__1_dtor0_14047DC60.cpp" />
    <ClCompile Include="source\_CNetSocketCNetSocket__1_dtor1_14047DC90.cpp" />
    <ClCompile Include="source\_CNetSocketSetSocket__1_dtor0_14047E110.cpp" />
    <ClCompile Include="source\_CNetSocket_CNetSocket__1_dtor0_14047DD40.cpp" />
    <ClCompile Include="source\_CNetSocket_CNetSocket__1_dtor1_14047DD70.cpp" />
    <ClCompile Include="source\_ConstructUMessageRangeMeterFilterCryptoPPU123stdY_1406016C0.cpp" />
    <ClCompile Include="source\_ConstructURECV_DATAU1stdYAXPEAURECV_DATAAEBU1Z_14031B0D0.cpp" />
    <ClCompile Include="source\_Copy_backward_optV_Deque_iteratorUMessageRangeMet_140605DE0.cpp" />
    <ClCompile Include="source\_Copy_backward_optV_Deque_iteratorURECV_DATAValloc_14031F460.cpp" />
    <ClCompile Include="source\_Copy_optV_Deque_iteratorURECV_DATAVallocatorURECV_14031F6A0.cpp" />
    <ClCompile Include="source\_db_Update_Data_For_Post_SendCMainThreadAEAAEPEADZ_1401B8FD0.cpp" />
    <ClCompile Include="source\_DestroyPEAUMessageRangeMeterFilterCryptoPPstdYAXP_1406017C0.cpp" />
    <ClCompile Include="source\_DestroyPEAURECV_DATAstdYAXPEAPEAURECV_DATAZ_14031FDC0.cpp" />
    <ClCompile Include="source\_DestroyUMessageRangeMeterFilterCryptoPPstdYAXPEAU_140601740.cpp" />
    <ClCompile Include="source\_DestroyURECV_DATAstdYAXPEAURECV_DATAZ_14031F390.cpp" />
    <ClCompile Include="source\_Destroy_rangePEAUMessageRangeMeterFilterCryptoPPV_140601600.cpp" />
    <ClCompile Include="source\_Destroy_rangePEAUMessageRangeMeterFilterCryptoPPV_140602380.cpp" />
    <ClCompile Include="source\_Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DATA_14031AFA0.cpp" />
    <ClCompile Include="source\_Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DATA_14031B3C0.cpp" />
    <ClCompile Include="source\_Dist_typeV_Deque_iteratorUMessageRangeMeterFilter_140604390.cpp" />
    <ClCompile Include="source\_dynamic_atexit_destructor_for__g_vRecvData___1406E9180.cpp" />
    <ClCompile Include="source\_dynamic_initializer_for__g_vRecvData___1406E2110.cpp" />
    <ClCompile Include="source\_ECNetSocketUEAAPEAXIZ_14047F840.cpp" />
    <ClCompile Include="source\_EDL_SignatureMessageEncodingMethod_DSACryptoPPUEA_14058FDB0.cpp" />
    <ClCompile Include="source\_EDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_1405AE280.cpp" />
    <ClCompile Include="source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567EF0.cpp" />
    <ClCompile Include="source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_1405ADDC0.cpp" />
    <ClCompile Include="source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_1405ADE20.cpp" />
    <ClCompile Include="source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140635950.cpp" />
    <ClCompile Include="source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_14063DBF0.cpp" />
    <ClCompile Include="source\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_14063DF50.cpp" />
    <ClCompile Include="source\_EDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_1405AE0E0.cpp" />
    <ClCompile Include="source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1405AD470.cpp" />
    <ClCompile Include="source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1405ADCF0.cpp" />
    <ClCompile Include="source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1406359B0.cpp" />
    <ClCompile Include="source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_14063DE30.cpp" />
    <ClCompile Include="source\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_14063DF30.cpp" />
    <ClCompile Include="source\_EMessageQueueCryptoPPW7EAAPEAXIZ_14065EFB0.cpp" />
    <ClCompile Include="source\_E_messageQEAAPEAXIZ_140438850.cpp" />
    <ClCompile Include="source\_E_socketQEAAPEAXIZ_14047FA70.cpp" />
    <ClCompile Include="source\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140604AF0.cpp" />
    <ClCompile Include="source\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760.cpp" />
    <ClCompile Include="source\_Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560.cpp" />
    <ClCompile Include="source\_Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_itera_14031B4E0.cpp" />
    <ClCompile Include="source\_GDL_SignatureMessageEncodingMethod_NRCryptoPPUEAA_14063D300.cpp" />
    <ClCompile Include="source\_GDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_140567CD0.cpp" />
    <ClCompile Include="source\_GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567FB0.cpp" />
    <ClCompile Include="source\_GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140635A10.cpp" />
    <ClCompile Include="source\_GDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_140567D90.cpp" />
    <ClCompile Include="source\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140567F50.cpp" />
    <ClCompile Include="source\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140568010.cpp" />
    <ClCompile Include="source\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140635A70.cpp" />
    <ClCompile Include="source\_GMessageQueueCryptoPPUEAAPEAXIZ_1406552B0.cpp" />
    <ClCompile Include="source\_GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30.cpp" />
    <ClCompile Include="source\_GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530.cpp" />
    <ClCompile Include="source\_GrowmapdequeURECV_DATAVallocatorURECV_DATAstdstdI_14031A690.cpp" />
    <ClCompile Include="source\_Insertion_sort1V_Deque_iteratorUMessageRangeMeter_140604710.cpp" />
    <ClCompile Include="source\_Insertion_sortV_Deque_iteratorUMessageRangeMeterF_140603630.cpp" />
    <ClCompile Include="source\_InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_H_14047A4F0.cpp" />
    <ClCompile Include="source\_Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0.cpp" />
    <ClCompile Include="source\_Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480.cpp" />
    <ClCompile Include="source\_Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0.cpp" />
    <ClCompile Include="source\_Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0.cpp" />
    <ClCompile Include="source\_Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0.cpp" />
    <ClCompile Include="source\_Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40.cpp" />
    <ClCompile Include="source\_MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0.cpp" />
    <ClCompile Include="source\_PopRecvMsgCNetProcessAEAAXGZ_140478680.cpp" />
    <ClCompile Include="source\_Pop_heapV_Deque_iteratorUMessageRangeMeterFilterC_140605F60.cpp" />
    <ClCompile Include="source\_Pop_heap_0V_Deque_iteratorUMessageRangeMeterFilte_140605B00.cpp" />
    <ClCompile Include="source\_Ptr_catPEAPEAUMessageRangeMeterFilterCryptoPPPEAP_1406022A0.cpp" />
    <ClCompile Include="source\_Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240.cpp" />
    <ClCompile Include="source\_Ptr_catV_Deque_iteratorUMessageRangeMeterFilterCr_140605DC0.cpp" />
    <ClCompile Include="source\_Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400.cpp" />
    <ClCompile Include="source\_Push_heapV_Deque_iteratorUMessageRangeMeterFilter_1406057B0.cpp" />
    <ClCompile Include="source\_SendListCandidateRegisterAEAAHGEZ_1402B6CC0.cpp" />
    <ClCompile Include="source\_SendLoopCNetProcessAEAAXKZ_140478D60.cpp" />
    <ClCompile Include="source\_SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0.cpp" />
    <ClCompile Include="source\_SendVotePaperAllVoterAEAAXXZ_1402BEB20.cpp" />
    <ClCompile Include="source\_SendVoteScoreAllVoterAEAAXEZ_1402BEEC0.cpp" />
    <ClCompile Include="source\_SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10.cpp" />
    <ClCompile Include="source\_Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0.cpp" />
    <ClCompile Include="source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EDE0.cpp" />
    <ClCompile Include="source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE10.cpp" />
    <ClCompile Include="source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE40.cpp" />
    <ClCompile Include="source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE70.cpp" />
    <ClCompile Include="source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EEA0.cpp" />
    <ClCompile Include="source\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EED0.cpp" />
    <ClCompile Include="source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F1A0.cpp" />
    <ClCompile Include="source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F1D0.cpp" />
    <ClCompile Include="source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F200.cpp" />
    <ClCompile Include="source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F230.cpp" />
    <ClCompile Include="source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F260.cpp" />
    <ClCompile Include="source\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F290.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D800.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D830.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D860.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E010.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E040.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E070.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0A0.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0D0.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E100.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E130.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E160.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E190.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E1C0.cpp" />
    <ClCompile Include="source\_stddeque_RECV_DATA_stdallocator_RECV_DATA____Xlen_14031ACD0.cpp" />
    <ClCompile Include="source\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F560.cpp" />
    <ClCompile Include="source\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F590.cpp" />
    <ClCompile Include="source\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5C0.cpp" />
    <ClCompile Include="source\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5F0.cpp" />
    <ClCompile Include="source\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F7B0.cpp" />
    <ClCompile Include="source\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F7E0.cpp" />
    <ClCompile Include="source\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F810.cpp" />
    <ClCompile Include="source\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F840.cpp" />
    <ClCompile Include="source\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031D9E0.cpp" />
    <ClCompile Include="source\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031DA10.cpp" />
    <ClCompile Include="source\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031E4D0.cpp" />
    <ClCompile Include="source\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031E500.cpp" />
    <ClCompile Include="source\_TidydequeUMessageRangeMeterFilterCryptoPPVallocat_1406008E0.cpp" />
    <ClCompile Include="source\_TidydequeURECV_DATAVallocatorURECV_DATAstdstdIEAA_14031FBC0.cpp" />
    <ClCompile Include="source\_Unguarded_partitionV_Deque_iteratorUMessageRangeM_140602520.cpp" />
    <ClCompile Include="source\_Uninit_copyPEAPEAUMessageRangeMeterFilterCryptoPP_1406022C0.cpp" />
    <ClCompile Include="source\_Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0.cpp" />
    <ClCompile Include="source\_Uninit_fill_nPEAPEAUMessageRangeMeterFilterCrypto_140602340.cpp" />
    <ClCompile Include="source\_Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360.cpp" />
    <ClCompile Include="source\_UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430.cpp" />
    <ClCompile Include="source\_Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0.cpp" />
    <ClCompile Include="source\_XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0.cpp" />
    <ClCompile Include="source\_XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40.cpp" />
    <ClCompile Include="source\__imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4.cpp" />
    <ClCompile Include="source\__imp_load__CcrFG_rs_EncryptPacketYAHPEAXPEAEHZ_14066D7C6.cpp" />
  </ItemGroup>
  
  <ItemGroup>
    <ClInclude Include="headers\0AlgorithmImplVDL_SignerBaseUEC2NPointCryptoPPCryp_140569C60.h" />
    <ClInclude Include="headers\0AlgorithmImplVDL_SignerBaseUECPPointCryptoPPCrypt_140569B30.h" />
    <ClInclude Include="headers\0AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635DD0.h" />
    <ClInclude Include="headers\0AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140637170.h" />
    <ClInclude Include="headers\0AlgorithmImplVDL_VerifierBaseUEC2NPointCryptoPPCr_140569E20.h" />
    <ClInclude Include="headers\0AlgorithmImplVDL_VerifierBaseUECPPointCryptoPPCry_140569C40.h" />
    <ClInclude Include="headers\0AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140636800.h" />
    <ClInclude Include="headers\0AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140637190.h" />
    <ClInclude Include="headers\0allocatorURECV_DATAstdQEAAAEBV01Z_14031FB40.h" />
    <ClInclude Include="headers\0allocatorURECV_DATAstdQEAAXZ_14031FAC0.h" />
    <ClInclude Include="headers\0auto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEAA_14056C640.h" />
    <ClInclude Include="headers\0auto_ptrVPK_MessageAccumulatorCryptoPPstdQEAAPEAV_1405F78F0.h" />
    <ClInclude Include="headers\0CNetSocketQEAAXZ_14047DB60.h" />
    <ClInclude Include="headers\0dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14031F980.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_SignerBaseUEC2NPointCryptoPP_140568290.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_SignerBaseUECPPointCryptoPPC_1405681D0.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140568070.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_1406327E0.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140635B90.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140635C50.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_VerifierBaseUEC2NPointCrypto_1405682F0.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_VerifierBaseUECPPointCryptoP_140568230.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140568160.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140635BF0.h" />
    <ClInclude Include="headers\0DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140635CB0.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_SignerBaseUEC2NPointCryptoPPCryp_140567FF0.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_SignerBaseUECPPointCryptoPPCrypt_140567F30.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140567D10.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632670.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635990.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140635A50.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_VerifierBaseUEC2NPointCryptoPPCr_140568050.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_VerifierBaseUECPPointCryptoPPCry_140567F90.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140567DD0.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_1406359F0.h" />
    <ClInclude Include="headers\0DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140635AB0.h" />
    <ClInclude Include="headers\0DL_SignatureMessageEncodingMethod_DSACryptoPPQEAA_14058F7E0.h" />
    <ClInclude Include="headers\0DL_SignatureMessageEncodingMethod_NRCryptoPPQEAAX_14063C950.h" />
    <ClInclude Include="headers\0DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_140561ED0.h" />
    <ClInclude Include="headers\0DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_140632550.h" />
    <ClInclude Include="headers\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140564370.h" />
    <ClInclude Include="headers\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140566010.h" />
    <ClInclude Include="headers\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140634060.h" />
    <ClInclude Include="headers\0DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140634530.h" />
    <ClInclude Include="headers\0DL_VerifierImplUDL_SignatureSchemeOptionsUDSACryp_1405632F0.h" />
    <ClInclude Include="headers\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1405650B0.h" />
    <ClInclude Include="headers\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140566D50.h" />
    <ClInclude Include="headers\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140634300.h" />
    <ClInclude Include="headers\0DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1406347D0.h" />
    <ClInclude Include="headers\0member_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600220.h" />
    <ClInclude Include="headers\0MessageQueueCryptoPPQEAAIZ_1406542A0.h" />
    <ClInclude Include="headers\0pairV_Deque_iteratorUMessageRangeMeterFilterCrypt_140603B50.h" />
    <ClInclude Include="headers\0PK_DeterministicSignatureMessageEncodingMethodCry_14058FDF0.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_1405617E0.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140561BF0.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140561D50.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632460.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140633F90.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140633FD0.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561880.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561C90.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140561DF0.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140633FB0.h" />
    <ClInclude Include="headers\0PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140633FF0.h" />
    <ClInclude Include="headers\0PK_MessageAccumulatorBaseCryptoPPQEAAXZ_140562F60.h" />
    <ClInclude Include="headers\0PK_MessageAccumulatorCryptoPPQEAAXZ_140563160.h" />
    <ClInclude Include="headers\0PK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPQEA_140562D60.h" />
    <ClInclude Include="headers\0PK_SignatureMessageEncodingMethodCryptoPPQEAAXZ_14058FF00.h" />
    <ClInclude Include="headers\0RECV_DATAQEAAXZ_14031A490.h" />
    <ClInclude Include="headers\0simple_ptrVDL_SignatureMessageEncodingMethod_DSAC_14058EB60.h" />
    <ClInclude Include="headers\0simple_ptrVDL_SignatureMessageEncodingMethod_NRCr_14063C280.h" />
    <ClInclude Include="headers\0SingletonVDL_SignatureMessageEncodingMethod_DSACr_14056C710.h" />
    <ClInclude Include="headers\0SingletonVDL_SignatureMessageEncodingMethod_NRCry_1406395A0.h" />
    <ClInclude Include="headers\0URECV_DATAallocatorPEAURECV_DATAstdQEAAAEBValloca_14031FB60.h" />
    <ClInclude Include="headers\0VRandomNumberGeneratorCryptoPPHPK_FinalTemplateVD_140639D90.h" />
    <ClInclude Include="headers\0_announ_message_receipt_udpQEAAXZ_140094FE0.h" />
    <ClInclude Include="headers\0_apex_send_ipQEAAXZ_140410C00.h" />
    <ClInclude Include="headers\0_chat_message_receipt_udpQEAAXZ_140094E90.h" />
    <ClInclude Include="headers\0_chat_steal_message_gm_zoclQEAAXZ_1403F8D30.h" />
    <ClInclude Include="headers\0_Deque_const_iteratorUMessageRangeMeterFilterCryp_140600CE0.h" />
    <ClInclude Include="headers\0_Deque_const_iteratorUMessageRangeMeterFilterCryp_140601830.h" />
    <ClInclude Include="headers\0_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031DA90.h" />
    <ClInclude Include="headers\0_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EA30.h" />
    <ClInclude Include="headers\0_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140600A50.h" />
    <ClInclude Include="headers\0_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406017D0.h" />
    <ClInclude Include="headers\0_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D540.h" />
    <ClInclude Include="headers\0_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E370.h" />
    <ClInclude Include="headers\0_Deque_mapURECV_DATAVallocatorURECV_DATAstdstdIEA_14031FAD0.h" />
    <ClInclude Include="headers\0_Deque_valURECV_DATAVallocatorURECV_DATAstdstdIEA_14031FA30.h" />
    <ClInclude Include="headers\0_messageQEAAXZ_140438750.h" />
    <ClInclude Include="headers\0_qry_case_post_sendQEAAXZ_1403282A0.h" />
    <ClInclude Include="headers\0_qry_case_update_data_for_post_sendQEAAXZ_1400CA7E0.h" />
    <ClInclude Include="headers\0_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_140600DB0.h" />
    <ClInclude Include="headers\0_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_14031DBF0.h" />
    <ClInclude Include="headers\0_socketQEAAXZ_14047F8B0.h" />
    <ClInclude Include="headers\0_talik_recvr_listQEAAXZ_1403F6ED0.h" />
    <ClInclude Include="headers\0__list_qry_case_post_sendQEAAXZ_1403285E0.h" />
    <ClInclude Include="headers\1AlgorithmImplVDL_SignerBaseUEC2NPointCryptoPPCryp_14055F6A0.h" />
    <ClInclude Include="headers\1AlgorithmImplVDL_SignerBaseUECPPointCryptoPPCrypt_14055F630.h" />
    <ClInclude Include="headers\1AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_1406329E0.h" />
    <ClInclude Include="headers\1AlgorithmImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632BE0.h" />
    <ClInclude Include="headers\1AlgorithmImplVDL_VerifierBaseUEC2NPointCryptoPPCr_14055F6F0.h" />
    <ClInclude Include="headers\1AlgorithmImplVDL_VerifierBaseUECPPointCryptoPPCry_14055F680.h" />
    <ClInclude Include="headers\1AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632A30.h" />
    <ClInclude Include="headers\1AlgorithmImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632C00.h" />
    <ClInclude Include="headers\1auto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEAA_14056C660.h" />
    <ClInclude Include="headers\1auto_ptrVPK_MessageAccumulatorCryptoPPstdQEAAXZ_1405F7910.h" />
    <ClInclude Include="headers\1CNetSocketUEAAXZ_14047DCC0.h" />
    <ClInclude Include="headers\1dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14031FB80.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_SignerBaseUEC2NPointCryptoPP_14055ED20.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_SignerBaseUECPPointCryptoPPC_14055EC60.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_14055E750.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140632720.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_SignerBaseVIntegerCryptoPPCr_140632860.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_VerifierBaseUEC2NPointCrypto_14055ED80.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_VerifierBaseUECPPointCryptoP_14055ECC0.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_14055E7B0.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_140632780.h" />
    <ClInclude Include="headers\1DL_ObjectImplBaseVDL_VerifierBaseVIntegerCryptoPP_1406328C0.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_SignerBaseUEC2NPointCryptoPPCryp_14055E5B0.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_SignerBaseUECPPointCryptoPPCrypt_14055E570.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_14055E1B0.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_140632630.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_SignerBaseVIntegerCryptoPPCrypto_1406326A0.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_VerifierBaseUEC2NPointCryptoPPCr_14055E5D0.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_VerifierBaseUECPPointCryptoPPCry_14055E590.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_14055E1D0.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_140632650.h" />
    <ClInclude Include="headers\1DL_ObjectImplVDL_VerifierBaseVIntegerCryptoPPCryp_1406326C0.h" />
    <ClInclude Include="headers\1DL_SignatureMessageEncodingMethod_DSACryptoPPUEAA_14058FEB0.h" />
    <ClInclude Include="headers\1DL_SignatureMessageEncodingMethod_NRCryptoPPUEAAX_14063D350.h" />
    <ClInclude Include="headers\1DL_SignerImplUDL_SignatureSchemeOptionsUDSACrypto_14055D500.h" />
    <ClInclude Include="headers\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__14055DF60.h" />
    <ClInclude Include="headers\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__14055DFA0.h" />
    <ClInclude Include="headers\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__140632510.h" />
    <ClInclude Include="headers\1DL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL__1406325B0.h" />
    <ClInclude Include="headers\1DL_VerifierImplUDL_SignatureSchemeOptionsUDSACryp_14055D520.h" />
    <ClInclude Include="headers\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_14055DF80.h" />
    <ClInclude Include="headers\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_14055DFC0.h" />
    <ClInclude Include="headers\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_140632530.h" />
    <ClInclude Include="headers\1DL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSUD_1406325D0.h" />
    <ClInclude Include="headers\1member_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600E80.h" />
    <ClInclude Include="headers\1MessageQueueCryptoPPUEAAXZ_1406552F0.h" />
    <ClInclude Include="headers\1pairV_Deque_iteratorUMessageRangeMeterFilterCrypt_1406023A0.h" />
    <ClInclude Include="headers\1PK_DeterministicSignatureMessageEncodingMethodCry_14058FED0.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055C200.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055D060.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_14055D0A0.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632420.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_SignerImplUDL_SignatureScheme_140632490.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055C220.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055D080.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_14055D0C0.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_140632440.h" />
    <ClInclude Include="headers\1PK_FinalTemplateVDL_VerifierImplUDL_SignatureSche_1406324B0.h" />
    <ClInclude Include="headers\1PK_MessageAccumulatorBaseCryptoPPUEAAXZ_1405631A0.h" />
    <ClInclude Include="headers\1PK_MessageAccumulatorCryptoPPUEAAXZ_140563180.h" />
    <ClInclude Include="headers\1PK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUEA_1405680D0.h" />
    <ClInclude Include="headers\1PK_SignatureMessageEncodingMethodCryptoPPUEAAXZ_14058FEF0.h" />
    <ClInclude Include="headers\1simple_ptrVDL_SignatureMessageEncodingMethod_DSAC_14058EB80.h" />
    <ClInclude Include="headers\1simple_ptrVDL_SignatureMessageEncodingMethod_NRCr_14063C2A0.h" />
    <ClInclude Include="headers\1_Deque_const_iteratorUMessageRangeMeterFilterCryp_1405FE880.h" />
    <ClInclude Include="headers\1_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031D4C0.h" />
    <ClInclude Include="headers\1_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1405FE860.h" />
    <ClInclude Include="headers\1_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D480.h" />
    <ClInclude Include="headers\1_messageQEAAXZ_140438780.h" />
    <ClInclude Include="headers\1_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_1405FE8A0.h" />
    <ClInclude Include="headers\1_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_14031D500.h" />
    <ClInclude Include="headers\1_socketQEAAXZ_14047F900.h" />
    <ClInclude Include="headers\4_Deque_const_iteratorUMessageRangeMeterFilterCryp_140602430.h" />
    <ClInclude Include="headers\4_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140602400.h" />
    <ClInclude Include="headers\4_RanitUMessageRangeMeterFilterCryptoPP_JPEBU123AE_140602480.h" />
    <ClInclude Include="headers\8_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603A70.h" />
    <ClInclude Include="headers\8_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031E640.h" />
    <ClInclude Include="headers\9_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603AC0.h" />
    <ClInclude Include="headers\9_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031DB10.h" />
    <ClInclude Include="headers\Accept_ClientCNetSocketQEAA_NKZ_14047E750.h" />
    <ClInclude Include="headers\Accept_ServerCNetSocketQEAAKXZ_14047E3B0.h" />
    <ClInclude Include="headers\AccessHashPK_MessageAccumulatorImplVSHA1CryptoPPCr_14056C620.h" />
    <ClInclude Include="headers\AccessKeyDL_ObjectImplBaseVDL_SignerBaseUEC2NPoint_14056C2D0.h" />
    <ClInclude Include="headers\AccessKeyDL_ObjectImplBaseVDL_SignerBaseUECPPointC_14056C000.h" />
    <ClInclude Include="headers\AccessKeyDL_ObjectImplBaseVDL_SignerBaseVIntegerCr_14056BE90.h" />
    <ClInclude Include="headers\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseUEC2NPoi_14056C390.h" />
    <ClInclude Include="headers\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseUECPPoin_14056C080.h" />
    <ClInclude Include="headers\AccessKeyDL_ObjectImplBaseVDL_VerifierBaseVInteger_14056BF50.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseU_1405645A0.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseU_140566240.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140562140.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140634290.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseV_140634760.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_1405634B0.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140565270.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140566F10.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_1406344C0.h" />
    <ClInclude Include="headers\AccessKeyInterfaceDL_ObjectImplBaseVDL_VerifierBas_140634990.h" />
    <ClInclude Include="headers\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseUEC_140564560.h" />
    <ClInclude Include="headers\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseUEC_140566200.h" />
    <ClInclude Include="headers\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140562100.h" />
    <ClInclude Include="headers\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140634250.h" />
    <ClInclude Include="headers\AccessPrivateKeyDL_ObjectImplBaseVDL_SignerBaseVIn_140634720.h" />
    <ClInclude Include="headers\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseUE_140565230.h" />
    <ClInclude Include="headers\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseUE_140566ED0.h" />
    <ClInclude Include="headers\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140563470.h" />
    <ClInclude Include="headers\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140634480.h" />
    <ClInclude Include="headers\AccessPublicKeyDL_ObjectImplBaseVDL_VerifierBaseVI_140634950.h" />
    <ClInclude Include="headers\AddPassablePacketCMainThreadAEAAXXZ_1401F95E0.h" />
    <ClInclude Include="headers\AlgorithmNameAlgorithmImplVDL_SignerBaseUEC2NPoint_140566270.h" />
    <ClInclude Include="headers\AlgorithmNameAlgorithmImplVDL_SignerBaseUECPPointC_1405645D0.h" />
    <ClInclude Include="headers\AlgorithmNameAlgorithmImplVDL_SignerBaseVIntegerCr_1406342C0.h" />
    <ClInclude Include="headers\AlgorithmNameAlgorithmImplVDL_SignerBaseVIntegerCr_140634790.h" />
    <ClInclude Include="headers\AlgorithmNameAlgorithmImplVDL_VerifierBaseUEC2NPoi_140566F40.h" />
    <ClInclude Include="headers\AlgorithmNameAlgorithmImplVDL_VerifierBaseUECPPoin_1405652A0.h" />
    <ClInclude Include="headers\AlgorithmNameAlgorithmImplVDL_VerifierBaseVInteger_1406344F0.h" />
    <ClInclude Include="headers\AlgorithmNameAlgorithmImplVDL_VerifierBaseVInteger_1406349C0.h" />
    <ClInclude Include="headers\allocateallocatorPEAUMessageRangeMeterFilterCrypto_140600CA0.h" />
    <ClInclude Include="headers\allocateallocatorPEAURECV_DATAstdQEAAPEAPEAURECV_D_14031AD80.h" />
    <ClInclude Include="headers\allocateallocatorUMessageRangeMeterFilterCryptoPPs_1406009E0.h" />
    <ClInclude Include="headers\allocateallocatorURECV_DATAstdQEAAPEAURECV_DATA_KZ_14031AB40.h" />
    <ClInclude Include="headers\AllowNonrecoverablePartPK_SignatureMessageEncoding_140562C80.h" />
    <ClInclude Include="headers\AnsyncConnectCompleteCNetworkEXEEAAXKKHZ_1401DB640.h" />
    <ClInclude Include="headers\AnsyncConnectCompleteCNetWorkingUEAAXKKHZ_140482210.h" />
    <ClInclude Include="headers\AnyMessagesBufferedTransformationCryptoPPUEBA_NXZ_1405F5210.h" />
    <ClInclude Include="headers\AnyRetrievableMessageQueueCryptoPPUEBA_NXZ_140655190.h" />
    <ClInclude Include="headers\begindequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFE30.h" />
    <ClInclude Include="headers\begindequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031D5F0.h" />
    <ClInclude Include="headers\CashDBInfoRecvResultCNetworkEXAEAA_NHPEADZ_1401D1DD0.h" />
    <ClInclude Include="headers\Cauto_ptrVPK_MessageAccumulatorCryptoPPstdQEBAPEAV_1405F79C0.h" />
    <ClInclude Include="headers\ChannelMessageEndBufferedTransformationCryptoPPQEA_1405F7AA0.h" />
    <ClInclude Include="headers\ChannelMessageSeriesEndBufferedTransformationCrypt_1405F4BA0.h" />
    <ClInclude Include="headers\ChannelMessageSeriesEndEqualityComparisonFilterCry_140654D40.h" />
    <ClInclude Include="headers\ChannelMessageSeriesEndInputRejectingVBufferedTran_140453F20.h" />
    <ClInclude Include="headers\ChannelMessageSeriesEndInputRejectingVFilterCrypto_14044E9E0.h" />
    <ClInclude Include="headers\ChannelMessageSeriesEndOutputProxyCryptoPPUEAA_NAE_1405FF170.h" />
    <ClInclude Include="headers\ChatAllRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1401C5A00.h" />
    <ClInclude Include="headers\ChatMapRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1401C5810.h" />
    <ClInclude Include="headers\CheckSendNewMissionMsgCDarkHoleChannelQEAAXXZ_140267640.h" />
    <ClInclude Include="headers\cleardequeUMessageRangeMeterFilterCryptoPPVallocat_1406001C0.h" />
    <ClInclude Include="headers\CloseAllCNetSocketAEAAXXZ_14047F060.h" />
    <ClInclude Include="headers\CloseSocketCNetProcessQEAAXK_NZ_14047A140.h" />
    <ClInclude Include="headers\CloseSocketCNetSocketQEAA_NKZ_14047EC40.h" />
    <ClInclude Include="headers\CloseSocketCNetWorkingQEAAXKK_NZ_140481AE0.h" />
    <ClInclude Include="headers\closesocket_0_1404DBA40.h" />
    <ClInclude Include="headers\Cmember_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600250.h" />
    <ClInclude Include="headers\CombineMessageAndShiftRegisterCFB_DecryptionTempla_140586920.h" />
    <ClInclude Include="headers\CombineMessageAndShiftRegisterCFB_DecryptionTempla_1405870D0.h" />
    <ClInclude Include="headers\CombineMessageAndShiftRegisterCFB_EncryptionTempla_1405868D0.h" />
    <ClInclude Include="headers\CombineMessageAndShiftRegisterCFB_EncryptionTempla_140587080.h" />
    <ClInclude Include="headers\CompleteAnsyncConnectCNetProcessQEAAXXZ_140479970.h" />
    <ClInclude Include="headers\CompleteSendCPostSystemManagerQEAAXPEADZ_1403261A0.h" />
    <ClInclude Include="headers\Complete_db_Update_Data_For_Post_SendCMainThreadAE_1401B90F0.h" />
    <ClInclude Include="headers\ComputeMessageRepresentativeDL_SignatureMessageEnc_140630320.h" />
    <ClInclude Include="headers\ComputeMessageRepresentativeDL_SignatureMessageEnc_1406304D0.h" />
    <ClInclude Include="headers\ConnectCNetSocketQEAAHKPEAUsockaddr_inZ_14047E830.h" />
    <ClInclude Include="headers\ConnectCNetWorkingQEAAHKKKGZ_1404819B0.h" />
    <ClInclude Include="headers\ConnectionStatusRequestCNetworkEXAEAA_NHZ_1401C7490.h" />
    <ClInclude Include="headers\ConnectThreadCNetProcessCAXPEAXZ_14047B230.h" />
    <ClInclude Include="headers\ConnectToNcashCEngNetworkBillEXQEAAHXZ_14031A420.h" />
    <ClInclude Include="headers\connect_0_1404DBA52.h" />
    <ClInclude Include="headers\constructallocatorUMessageRangeMeterFilterCryptoPP_140600A00.h" />
    <ClInclude Include="headers\constructallocatorURECV_DATAstdQEAAXPEAURECV_DATAA_14031AB90.h" />
    <ClInclude Include="headers\CopyMessagesToBufferedTransformationCryptoPPQEBAIA_1405F5590.h" />
    <ClInclude Include="headers\CopyMessagesToMessageQueueCryptoPPQEBAIAEAVBuffere_1406545F0.h" />
    <ClInclude Include="headers\CopyMessagesToStoreCryptoPPQEBAIAEAVBufferedTransf_1405FE150.h" />
    <ClInclude Include="headers\CopyRangeTo2MessageQueueCryptoPPUEBA_KAEAVBuffered_1406543C0.h" />
    <ClInclude Include="headers\copyV_Deque_iteratorURECV_DATAVallocatorURECV_DATA_14031EFD0.h" />
    <ClInclude Include="headers\copy_backwardV_Deque_iteratorURECV_DATAVallocatorU_14031EC10.h" />
    <ClInclude Include="headers\Dauto_ptrVPK_MessageAccumulatorBaseCryptoPPstdQEBA_14056C6C0.h" />
    <ClInclude Include="headers\Dauto_ptrVPK_MessageAccumulatorCryptoPPstdQEBAAEAV_1405F7970.h" />
    <ClInclude Include="headers\db_sendwebracebosssmsCMainThreadQEAAEPEAU_qry_case_1401B2B10.h" />
    <ClInclude Include="headers\deallocateallocatorPEAUMessageRangeMeterFilterCryp_140600C70.h" />
    <ClInclude Include="headers\deallocateallocatorPEAURECV_DATAstdQEAAXPEAPEAUREC_14031AD30.h" />
    <ClInclude Include="headers\deallocateallocatorUMessageRangeMeterFilterCryptoP_140600C40.h" />
    <ClInclude Include="headers\deallocateallocatorURECV_DATAstdQEAAXPEAURECV_DATA_14031FD20.h" />
    <ClInclude Include="headers\destroyallocatorPEAUMessageRangeMeterFilterCryptoP_140600CC0.h" />
    <ClInclude Include="headers\destroyallocatorPEAURECV_DATAstdQEAAXPEAPEAURECV_D_14031FD70.h" />
    <ClInclude Include="headers\destroyallocatorUMessageRangeMeterFilterCryptoPPst_140600A30.h" />
    <ClInclude Include="headers\destroyallocatorURECV_DATAstdQEAAXPEAURECV_DATAZ_14031EAE0.h" />
    <ClInclude Include="headers\DigestSizePK_MessageAccumulatorCryptoPPUEBAIXZ_140562E10.h" />
    <ClInclude Include="headers\DispatchMessageA_0_140676E76.h" />
    <ClInclude Include="headers\Dmember_ptrVPK_MessageAccumulatorCryptoPPCryptoPPQ_140600240.h" />
    <ClInclude Include="headers\DoMessageBoxCWinAppUEAAHPEBDIIZ_0_1404DBFB8.h" />
    <ClInclude Include="headers\D_Deque_const_iteratorUMessageRangeMeterFilterCryp_140600D30.h" />
    <ClInclude Include="headers\D_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031E580.h" />
    <ClInclude Include="headers\D_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140600A90.h" />
    <ClInclude Include="headers\D_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D900.h" />
    <ClInclude Include="headers\emptydequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFED0.h" />
    <ClInclude Include="headers\emptydequeURECV_DATAVallocatorURECV_DATAstdstdQEBA_14031E7F0.h" />
    <ClInclude Include="headers\EmptySocketBufferCNetSocketQEAAXKZ_14047EB70.h" />
    <ClInclude Include="headers\enddequeUMessageRangeMeterFilterCryptoPPVallocator_1405FFE80.h" />
    <ClInclude Include="headers\enddequeURECV_DATAVallocatorURECV_DATAstdstdQEAAAV_14031D670.h" />
    <ClInclude Include="headers\erasedequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031D700.h" />
    <ClInclude Include="headers\erasedequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_14031DC40.h" />
    <ClInclude Include="headers\E_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C20.h" />
    <ClInclude Include="headers\E_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EAB0.h" />
    <ClInclude Include="headers\E_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406037F0.h" />
    <ClInclude Include="headers\E_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603810.h" />
    <ClInclude Include="headers\E_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031D940.h" />
    <ClInclude Include="headers\E_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E3D0.h" />
    <ClInclude Include="headers\failwithmessage_1404DDA60.h" />
    <ClInclude Include="headers\FindEmptySocketCNetSocketQEAAKXZ_14047F180.h" />
    <ClInclude Include="headers\FormatMessageA_0_1404DEE80.h" />
    <ClInclude Include="headers\frontdequeUMessageRangeMeterFilterCryptoPPVallocat_1405FFF00.h" />
    <ClInclude Include="headers\F_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C50.h" />
    <ClInclude Include="headers\F_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031F940.h" />
    <ClInclude Include="headers\F_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406038D0.h" />
    <ClInclude Include="headers\F_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031F8F0.h" />
    <ClInclude Include="headers\GetBillingDBConnectionStatusCashDbWorkerQEAA_NXZ_1402EEE50.h" />
    <ClInclude Include="headers\GetBillingDBConnectionStatusCCashDBWorkManagerQEAA_1402F33F0.h" />
    <ClInclude Include="headers\GetBitAfterSetLimSocketYAKEZ_14003E470.h" />
    <ClInclude Include="headers\GetCheckRecvTimeCNetWorkingQEAAKKKZ_140481D60.h" />
    <ClInclude Include="headers\GetConnectionHookCCmdTargetMEAAPEAUIConnectionPoin_1404DBBF8.h" />
    <ClInclude Include="headers\GetConnectionMapCCmdTargetMEBAPEBUAFX_CONNECTIONMA_1404DBBD4.h" />
    <ClInclude Include="headers\GetConnectTime_AddBySecYAKHZ_14043CB80.h" />
    <ClInclude Include="headers\GetDBTaskConnectionStatusCLogTypeDBTaskManagerQEAA_1402C3870.h" />
    <ClInclude Include="headers\GetDefItemUpgSocketNumYAEHHZ_14003BB50.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseUEC2NP_140566260.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseUECPPo_1405645C0.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_140562160.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_1406342B0.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_SignerBaseVInteg_140634780.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseUEC2_140566F30.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseUECP_140565290.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1405634D0.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1406344E0.h" />
    <ClInclude Include="headers\GetDigestSizeDL_ObjectImplBaseVDL_VerifierBaseVInt_1406349B0.h" />
    <ClInclude Include="headers\GetExtraConnectionPointsCCmdTargetMEAAHPEAVCPtrArr_1404DBBF2.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_SignerBaseUEC2NP_140566190.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_SignerBaseUECPPo_1405644F0.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_140562090.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_1406341E0.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_SignerBaseVInteg_1406346B0.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseUEC2_140566E60.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseUECP_1405651C0.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_140563400.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_140634410.h" />
    <ClInclude Include="headers\GetHashIdentifierDL_ObjectImplVDL_VerifierBaseVInt_1406348E0.h" />
    <ClInclude Include="headers\GetItemUpgLimSocketYAEKZ_14003E290.h" />
    <ClInclude Include="headers\GetKey1_messageQEAAKXZ_1401C0320.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseUEC2_140566250.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseUECP_1405645B0.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_140562150.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_1406342A0.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_SignerBaseVInt_140634770.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseUE_140565280.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseUE_140566F20.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1405634C0.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1406344D0.h" />
    <ClInclude Include="headers\GetKeyInterfaceDL_ObjectImplBaseVDL_VerifierBaseVI_1406349A0.h" />
    <ClInclude Include="headers\GetMessageA_0_140676E88.h" />
    <ClInclude Include="headers\GetMessageA_messageQEAAKXZ_1401C0300.h" />
    <ClInclude Include="headers\GetMessageBarCFrameWndUEAAPEAVCWndXZ_0_1404DBE56.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1405620C0.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_140564520.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1405661C0.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_140634210.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Signer_1406346E0.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140563430.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_1405651F0.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140566E90.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140634440.h" />
    <ClInclude Include="headers\GetMessageEncodingInterfaceDL_ObjectImplVDL_Verifi_140634910.h" />
    <ClInclude Include="headers\GetMessageMapCAboutDlgMEBAPEBUAFX_MSGMAPXZ_1400297D0.h" />
    <ClInclude Include="headers\GetMessageMapCDisplayViewMEBAPEBUAFX_MSGMAPXZ_14002BD00.h" />
    <ClInclude Include="headers\GetMessageMapCGameServerAppMEBAPEBUAFX_MSGMAPXZ_1400293B0.h" />
    <ClInclude Include="headers\GetMessageMapCGameServerDocMEBAPEBUAFX_MSGMAPXZ_140029E10.h" />
    <ClInclude Include="headers\GetMessageMapCGameServerViewMEBAPEBUAFX_MSGMAPXZ_14002A690.h" />
    <ClInclude Include="headers\GetMessageMapCInfoSheetMEBAPEBUAFX_MSGMAPXZ_14002D990.h" />
    <ClInclude Include="headers\GetMessageMapCIPXTabMEBAPEBUAFX_MSGMAPXZ_14002DFD0.h" />
    <ClInclude Include="headers\GetMessageMapCMainFrameMEBAPEBUAFX_MSGMAPXZ_140027CD0.h" />
    <ClInclude Include="headers\GetMessageMapCMapTabMEBAPEBUAFX_MSGMAPXZ_14002E620.h" />
    <ClInclude Include="headers\GetMessageMapCObjectSearchDlgMEBAPEBUAFX_MSGMAPXZ_14002F290.h" />
    <ClInclude Include="headers\GetMessageMapCObjectTabMEBAPEBUAFX_MSGMAPXZ_14002FF40.h" />
    <ClInclude Include="headers\GetMessageMapCOpenDlgMEBAPEBUAFX_MSGMAPXZ_140029210.h" />
    <ClInclude Include="headers\GetMessageMapCServerTabMEBAPEBUAFX_MSGMAPXZ_140034E10.h" />
    <ClInclude Include="headers\GetMessageMapCTCPTabMEBAPEBUAFX_MSGMAPXZ_140035CB0.h" />
    <ClInclude Include="headers\GetMessageMapLicensePopupDlgMEBAPEBUAFX_MSGMAPXZ_140026900.h" />
    <ClInclude Include="headers\GetMessageStringCFrameWndUEBAXIAEAVCStringTDVStrTr_1404DBE3E.h" />
    <ClInclude Include="headers\GetNextMessageBufferedTransformationCryptoPPUEAA_N_1405F52A0.h" />
    <ClInclude Include="headers\GetNextMessageMessageQueueCryptoPPUEAA_NXZ_140654540.h" />
    <ClInclude Include="headers\GetNextMessageSeriesBufferedTransformationCryptoPP_14044CF10.h" />
    <ClInclude Include="headers\GetNextMessageStoreCryptoPPUEAA_NXZ_1405FE100.h" />
    <ClInclude Include="headers\GetRaceWarRecvrCPvpCashPointQEAA_NXZ_140284CF0.h" />
    <ClInclude Include="headers\GetSendMsgCMsgListManagerRACE_BOSS_MSGQEAAPEAVCMsg_14029FBA0.h" />
    <ClInclude Include="headers\GetSendMsgCMsgListRACE_BOSS_MSGQEAAPEAVCMsg2XZ_14029E920.h" />
    <ClInclude Include="headers\GetSendPoint_NET_BUFFERQEAAPEADPEAHPEA_NZ_14047D9B0.h" />
    <ClInclude Include="headers\GetSendThreadFrameCNetProcessQEAAKXZ_140479F80.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseUE_1405644B0.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseUE_140566150.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_140562050.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_1406341A0.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_SignerBaseVI_140634670.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1405633C0.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_140565180.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_140566E20.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1406343D0.h" />
    <ClInclude Include="headers\GetSignatureAlgorithmDL_ObjectImplVDL_VerifierBase_1406348A0.h" />
    <ClInclude Include="headers\GetSocketCNetSocketQEAAPEAU_socketKZ_14047D3B0.h" />
    <ClInclude Include="headers\GetSocketCNetWorkingQEAAPEAU_socketKKZ_140481BD0.h" />
    <ClInclude Include="headers\GetSocketIPAddressCNetSocketQEAAKKZ_14047F150.h" />
    <ClInclude Include="headers\GetSocketNameYA_N_KPEADZ_14047FFE0.h" />
    <ClInclude Include="headers\GetSocketTypeCNetSocketQEAAPEAU_SOCK_TYPE_PARAMXZ_14047F110.h" />
    <ClInclude Include="headers\GetTalikFromSocketYAEKEZ_14003E2E0.h" />
    <ClInclude Include="headers\GetTalikRecvrPointCPvpCashMngQEAAHEKZ_1403F6420.h" />
    <ClInclude Include="headers\GetTalikRecvrPointCPvpCashMngQEAAHHZ_1403F63F0.h" />
    <ClInclude Include="headers\GetTalikRecvrPointCPvpCashPointQEAAHEKZ_1403F5190.h" />
    <ClInclude Include="headers\GetThisMessageMapCAboutDlgKAPEBUAFX_MSGMAPXZ_140029810.h" />
    <ClInclude Include="headers\GetThisMessageMapCDialogKAPEBUAFX_MSGMAPXZ_0_1404DBD90.h" />
    <ClInclude Include="headers\GetThisMessageMapCDisplayViewKAPEBUAFX_MSGMAPXZ_14002BD40.h" />
    <ClInclude Include="headers\GetThisMessageMapCDocumentKAPEBUAFX_MSGMAPXZ_0_1404DC042.h" />
    <ClInclude Include="headers\GetThisMessageMapCFormViewKAPEBUAFX_MSGMAPXZ_0_1404DC10E.h" />
    <ClInclude Include="headers\GetThisMessageMapCFrameWndKAPEBUAFX_MSGMAPXZ_0_1404DBDCC.h" />
    <ClInclude Include="headers\GetThisMessageMapCGameServerAppKAPEBUAFX_MSGMAPXZ_1400293F0.h" />
    <ClInclude Include="headers\GetThisMessageMapCGameServerDocKAPEBUAFX_MSGMAPXZ_140029E50.h" />
    <ClInclude Include="headers\GetThisMessageMapCGameServerViewKAPEBUAFX_MSGMAPXZ_14002A6D0.h" />
    <ClInclude Include="headers\GetThisMessageMapCInfoSheetKAPEBUAFX_MSGMAPXZ_14002D9D0.h" />
    <ClInclude Include="headers\GetThisMessageMapCIPXTabKAPEBUAFX_MSGMAPXZ_14002E010.h" />
    <ClInclude Include="headers\GetThisMessageMapCMainFrameKAPEBUAFX_MSGMAPXZ_140027D10.h" />
    <ClInclude Include="headers\GetThisMessageMapCMapTabKAPEBUAFX_MSGMAPXZ_14002E660.h" />
    <ClInclude Include="headers\GetThisMessageMapCObjectSearchDlgKAPEBUAFX_MSGMAPX_14002F2D0.h" />
    <ClInclude Include="headers\GetThisMessageMapCObjectTabKAPEBUAFX_MSGMAPXZ_14002FF80.h" />
    <ClInclude Include="headers\GetThisMessageMapCOpenDlgKAPEBUAFX_MSGMAPXZ_140029250.h" />
    <ClInclude Include="headers\GetThisMessageMapCPropertyPageKAPEBUAFX_MSGMAPXZ_0_1404DC360.h" />
    <ClInclude Include="headers\GetThisMessageMapCPropertySheetKAPEBUAFX_MSGMAPXZ__1404DC2F4.h" />
    <ClInclude Include="headers\GetThisMessageMapCServerTabKAPEBUAFX_MSGMAPXZ_140034E50.h" />
    <ClInclude Include="headers\GetThisMessageMapCTCPTabKAPEBUAFX_MSGMAPXZ_140035CF0.h" />
    <ClInclude Include="headers\GetThisMessageMapCWinAppKAPEBUAFX_MSGMAPXZ_0_1404DBF16.h" />
    <ClInclude Include="headers\GetThisMessageMapLicensePopupDlgKAPEBUAFX_MSGMAPXZ_140026940.h" />
    <ClInclude Include="headers\GetTotalCountCNetSocketQEAAPEAU_total_countXZ_14047F130.h" />
    <ClInclude Include="headers\G_Deque_const_iteratorUMessageRangeMeterFilterCryp_140601A00.h" />
    <ClInclude Include="headers\G_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EB70.h" />
    <ClInclude Include="headers\G_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140601800.h" />
    <ClInclude Include="headers\G_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406039B0.h" />
    <ClInclude Include="headers\G_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E9E0.h" />
    <ClInclude Include="headers\HandleMouseMessagesCD3DArcBallQEAA_JPEAUHWND__I_K__14052C510.h" />
    <ClInclude Include="headers\H_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_1406038F0.h" />
    <ClInclude Include="headers\H_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E420.h" />
    <ClInclude Include="headers\InitAcceptSocketCNetSocketAEAA_NPEADZ_14047EE00.h" />
    <ClInclude Include="headers\InitParam_socketQEAAXXZ_14047F910.h" />
    <ClInclude Include="headers\InputRecoverableMessageDL_SignerBaseUEC2NPointCryp_1405662B0.h" />
    <ClInclude Include="headers\InputRecoverableMessageDL_SignerBaseUECPPointCrypt_140564610.h" />
    <ClInclude Include="headers\InputRecoverableMessageDL_SignerBaseVIntegerCrypto_1405621E0.h" />
    <ClInclude Include="headers\InputRecoverableMessageTF_SignerBaseCryptoPPUEBAXA_140622820.h" />
    <ClInclude Include="headers\InputSignatureDL_VerifierBaseUEC2NPointCryptoPPCry_140566F80.h" />
    <ClInclude Include="headers\InputSignatureDL_VerifierBaseUECPPointCryptoPPCryp_1405652E0.h" />
    <ClInclude Include="headers\InputSignatureDL_VerifierBaseVIntegerCryptoPPCrypt_140563540.h" />
    <ClInclude Include="headers\InputSignatureTF_VerifierBaseCryptoPPUEBAXAEAVPK_M_140623020.h" />
    <ClInclude Include="headers\InternetConnectA_0_14066D832.h" />
    <ClInclude Include="headers\IsGetConnectedCEngNetworkBillEXQEAA_NXZ_14022C320.h" />
    <ClInclude Include="headers\IsIdleMessageCWinThreadUEAAHPEAUtagMSGZ_0_1404DBF6A.h" />
    <ClInclude Include="headers\IsolatedFlushMessageQueueCryptoPPUEAA_N_N0Z_140655100.h" />
    <ClInclude Include="headers\IsolatedInitializeMessageQueueCryptoPPUEAAXAEBVNam_140654FB0.h" />
    <ClInclude Include="headers\IsolatedMessageSeriesEndBufferedTransformationCryp_14054B910.h" />
    <ClInclude Include="headers\IsolatedMessageSeriesEndInputRejectingVBufferedTra_140453E50.h" />
    <ClInclude Include="headers\IsolatedMessageSeriesEndInputRejectingVFilterCrypt_14044E910.h" />
    <ClInclude Include="headers\IsolatedMessageSeriesEndMessageQueueCryptoPPUEAA_N_140655120.h" />
    <ClInclude Include="headers\IsolatedMessageSeriesEndMeterFilterCryptoPPUEAA_N__1405F9D80.h" />
    <ClInclude Include="headers\IsRecvableContEffectCGameObjectUEAA_NXZ_14012C770.h" />
    <ClInclude Include="headers\IsRecvableContEffectCMonsterUEAA_NXZ_1401469A0.h" />
    <ClInclude Include="headers\IsRecvedQuestByNPCCQuestMgrQEAA_NHZ_14028B000.h" />
    <ClInclude Include="headers\IsRecvedQuestByNPCCQuestMgrQEAA_NPEBDZ_14028B020.h" />
    <ClInclude Include="headers\IsSendFromWebCMsgRACE_BOSS_MSGQEAA_NXZ_1402A2D10.h" />
    <ClInclude Include="headers\IsSendTimeCMsgRACE_BOSS_MSGQEAA_NXZ_14029DC50.h" />
    <ClInclude Include="headers\iter_swapV_Deque_iteratorUMessageRangeMeterFilterC_1406042F0.h" />
    <ClInclude Include="headers\j_0allocatorURECV_DATAstdQEAAAEBV01Z_140002E6E.h" />
    <ClInclude Include="headers\j_0allocatorURECV_DATAstdQEAAXZ_140002261.h" />
    <ClInclude Include="headers\j_0CNetSocketQEAAXZ_140001370.h" />
    <ClInclude Include="headers\j_0dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14001200D.h" />
    <ClInclude Include="headers\j_0RECV_DATAQEAAXZ_1400108F7.h" />
    <ClInclude Include="headers\j_0URECV_DATAallocatorPEAURECV_DATAstdQEAAAEBVallo_140009C1E.h" />
    <ClInclude Include="headers\j_0_announ_message_receipt_udpQEAAXZ_1400095CA.h" />
    <ClInclude Include="headers\j_0_apex_send_ipQEAAXZ_14000E903.h" />
    <ClInclude Include="headers\j_0_chat_message_receipt_udpQEAAXZ_1400054CA.h" />
    <ClInclude Include="headers\j_0_chat_steal_message_gm_zoclQEAAXZ_140013836.h" />
    <ClInclude Include="headers\j_0_Deque_const_iteratorURECV_DATAVallocatorURECV__140004C5A.h" />
    <ClInclude Include="headers\j_0_Deque_const_iteratorURECV_DATAVallocatorURECV__140008B61.h" />
    <ClInclude Include="headers\j_0_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140003503.h" />
    <ClInclude Include="headers\j_0_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_1400049EE.h" />
    <ClInclude Include="headers\j_0_Deque_mapURECV_DATAVallocatorURECV_DATAstdstdI_14000BEBF.h" />
    <ClInclude Include="headers\j_0_Deque_valURECV_DATAVallocatorURECV_DATAstdstdI_1400118EC.h" />
    <ClInclude Include="headers\j_0_messageQEAAXZ_140009007.h" />
    <ClInclude Include="headers\j_0_qry_case_post_sendQEAAXZ_14000A902.h" />
    <ClInclude Include="headers\j_0_qry_case_update_data_for_post_sendQEAAXZ_140010DF7.h" />
    <ClInclude Include="headers\j_0_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_1400023A6.h" />
    <ClInclude Include="headers\j_0_socketQEAAXZ_1400057CC.h" />
    <ClInclude Include="headers\j_0_talik_recvr_listQEAAXZ_140006109.h" />
    <ClInclude Include="headers\j_0__list_qry_case_post_sendQEAAXZ_140012D8C.h" />
    <ClInclude Include="headers\j_1CNetSocketUEAAXZ_14000322E.h" />
    <ClInclude Include="headers\j_1dequeURECV_DATAVallocatorURECV_DATAstdstdQEAAXZ_14000D30F.h" />
    <ClInclude Include="headers\j_1_Deque_const_iteratorURECV_DATAVallocatorURECV__14000ED9A.h" />
    <ClInclude Include="headers\j_1_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140008314.h" />
    <ClInclude Include="headers\j_1_messageQEAAXZ_14000C12B.h" />
    <ClInclude Include="headers\j_1_RanitURECV_DATA_JPEBU1AEBU1stdQEAAXZ_140003229.h" />
    <ClInclude Include="headers\j_1_socketQEAAXZ_140011CE3.h" />
    <ClInclude Include="headers\j_8_Deque_const_iteratorURECV_DATAVallocatorURECV__140012030.h" />
    <ClInclude Include="headers\j_9_Deque_const_iteratorURECV_DATAVallocatorURECV__14000C7AC.h" />
    <ClInclude Include="headers\j_Accept_ClientCNetSocketQEAA_NKZ_140004377.h" />
    <ClInclude Include="headers\j_Accept_ServerCNetSocketQEAAKXZ_140011EB4.h" />
    <ClInclude Include="headers\j_AddPassablePacketCMainThreadAEAAXXZ_14000AE98.h" />
    <ClInclude Include="headers\j_allocateallocatorPEAURECV_DATAstdQEAAPEAPEAURECV_1400055A6.h" />
    <ClInclude Include="headers\j_allocateallocatorURECV_DATAstdQEAAPEAURECV_DATA__140001B9F.h" />
    <ClInclude Include="headers\j_AnsyncConnectCompleteCNetworkEXEEAAXKKHZ_140001ACD.h" />
    <ClInclude Include="headers\j_AnsyncConnectCompleteCNetWorkingUEAAXKKHZ_140002BD5.h" />
    <ClInclude Include="headers\j_begindequeURECV_DATAVallocatorURECV_DATAstdstdQE_14001151D.h" />
    <ClInclude Include="headers\j_CashDBInfoRecvResultCNetworkEXAEAA_NHPEADZ_140009ED5.h" />
    <ClInclude Include="headers\j_ChannelMessageSeriesEndInputRejectingVBufferedTr_14000A191.h" />
    <ClInclude Include="headers\j_ChannelMessageSeriesEndInputRejectingVFilterCryp_140005DF8.h" />
    <ClInclude Include="headers\j_ChatAllRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1400034FE.h" />
    <ClInclude Include="headers\j_ChatMapRecvYesOrNoCNetworkEXAEAA_NHPEADZ_1400046C4.h" />
    <ClInclude Include="headers\j_CheckSendNewMissionMsgCDarkHoleChannelQEAAXXZ_140001FD7.h" />
    <ClInclude Include="headers\j_CloseAllCNetSocketAEAAXXZ_1400139F8.h" />
    <ClInclude Include="headers\j_CloseSocketCNetProcessQEAAXK_NZ_140006398.h" />
    <ClInclude Include="headers\j_CloseSocketCNetSocketQEAA_NKZ_14000F664.h" />
    <ClInclude Include="headers\j_CloseSocketCNetWorkingQEAAXKK_NZ_14000EFCA.h" />
    <ClInclude Include="headers\j_CompleteAnsyncConnectCNetProcessQEAAXXZ_140005173.h" />
    <ClInclude Include="headers\j_CompleteSendCPostSystemManagerQEAAXPEADZ_140009AD9.h" />
    <ClInclude Include="headers\j_Complete_db_Update_Data_For_Post_SendCMainThread_14000DEA9.h" />
    <ClInclude Include="headers\j_ConnectCNetSocketQEAAHKPEAUsockaddr_inZ_140012EB3.h" />
    <ClInclude Include="headers\j_ConnectCNetWorkingQEAAHKKKGZ_140003DF0.h" />
    <ClInclude Include="headers\j_ConnectionStatusRequestCNetworkEXAEAA_NHZ_14000F50B.h" />
    <ClInclude Include="headers\j_ConnectThreadCNetProcessCAXPEAXZ_1400135B6.h" />
    <ClInclude Include="headers\j_ConnectToNcashCEngNetworkBillEXQEAAHXZ_140010776.h" />
    <ClInclude Include="headers\j_constructallocatorURECV_DATAstdQEAAXPEAURECV_DAT_14000433B.h" />
    <ClInclude Include="headers\j_copyV_Deque_iteratorURECV_DATAVallocatorURECV_DA_1400136FB.h" />
    <ClInclude Include="headers\j_copy_backwardV_Deque_iteratorURECV_DATAVallocato_14001338B.h" />
    <ClInclude Include="headers\j_db_sendwebracebosssmsCMainThreadQEAAEPEAU_qry_ca_14000DD4B.h" />
    <ClInclude Include="headers\j_deallocateallocatorPEAURECV_DATAstdQEAAXPEAPEAUR_140005A38.h" />
    <ClInclude Include="headers\j_deallocateallocatorURECV_DATAstdQEAAXPEAURECV_DA_140005718.h" />
    <ClInclude Include="headers\j_destroyallocatorPEAURECV_DATAstdQEAAXPEAPEAURECV_140012869.h" />
    <ClInclude Include="headers\j_destroyallocatorURECV_DATAstdQEAAXPEAURECV_DATAZ_14000D913.h" />
    <ClInclude Include="headers\j_D_Deque_const_iteratorURECV_DATAVallocatorURECV__14000C2C5.h" />
    <ClInclude Include="headers\j_D_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000D6ED.h" />
    <ClInclude Include="headers\j_emptydequeURECV_DATAVallocatorURECV_DATAstdstdQE_140002BB7.h" />
    <ClInclude Include="headers\j_EmptySocketBufferCNetSocketQEAAXKZ_1400128D2.h" />
    <ClInclude Include="headers\j_enddequeURECV_DATAVallocatorURECV_DATAstdstdQEAA_140012887.h" />
    <ClInclude Include="headers\j_erasedequeURECV_DATAVallocatorURECV_DATAstdstdQE_1400041BF.h" />
    <ClInclude Include="headers\j_erasedequeURECV_DATAVallocatorURECV_DATAstdstdQE_14000C7E8.h" />
    <ClInclude Include="headers\j_E_Deque_const_iteratorURECV_DATAVallocatorURECV__140002FF4.h" />
    <ClInclude Include="headers\j_E_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000363E.h" />
    <ClInclude Include="headers\j_E_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140009E2B.h" />
    <ClInclude Include="headers\j_FindEmptySocketCNetSocketQEAAKXZ_14000589E.h" />
    <ClInclude Include="headers\j_F_Deque_const_iteratorURECV_DATAVallocatorURECV__14000AF42.h" />
    <ClInclude Include="headers\j_F_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000F902.h" />
    <ClInclude Include="headers\j_GetBillingDBConnectionStatusCashDbWorkerQEAA_NXZ_140004AD4.h" />
    <ClInclude Include="headers\j_GetBillingDBConnectionStatusCCashDBWorkManagerQE_14000B9C4.h" />
    <ClInclude Include="headers\j_GetBitAfterSetLimSocketYAKEZ_14000213F.h" />
    <ClInclude Include="headers\j_GetCheckRecvTimeCNetWorkingQEAAKKKZ_140004093.h" />
    <ClInclude Include="headers\j_GetConnectTime_AddBySecYAKHZ_140005BA0.h" />
    <ClInclude Include="headers\j_GetDBTaskConnectionStatusCLogTypeDBTaskManagerQE_140009F6B.h" />
    <ClInclude Include="headers\j_GetDefItemUpgSocketNumYAEHHZ_140009A2F.h" />
    <ClInclude Include="headers\j_GetItemUpgLimSocketYAEKZ_140005A24.h" />
    <ClInclude Include="headers\j_GetKey1_messageQEAAKXZ_1400108A2.h" />
    <ClInclude Include="headers\j_GetMessageA_messageQEAAKXZ_14000353F.h" />
    <ClInclude Include="headers\j_GetMessageMapCAboutDlgMEBAPEBUAFX_MSGMAPXZ_14000F259.h" />
    <ClInclude Include="headers\j_GetMessageMapCDisplayViewMEBAPEBUAFX_MSGMAPXZ_140002BEE.h" />
    <ClInclude Include="headers\j_GetMessageMapCGameServerAppMEBAPEBUAFX_MSGMAPXZ_14000B492.h" />
    <ClInclude Include="headers\j_GetMessageMapCGameServerDocMEBAPEBUAFX_MSGMAPXZ_1400116DA.h" />
    <ClInclude Include="headers\j_GetMessageMapCGameServerViewMEBAPEBUAFX_MSGMAPXZ_14001189C.h" />
    <ClInclude Include="headers\j_GetMessageMapCInfoSheetMEBAPEBUAFX_MSGMAPXZ_14001167B.h" />
    <ClInclude Include="headers\j_GetMessageMapCIPXTabMEBAPEBUAFX_MSGMAPXZ_140004692.h" />
    <ClInclude Include="headers\j_GetMessageMapCMainFrameMEBAPEBUAFX_MSGMAPXZ_140001401.h" />
    <ClInclude Include="headers\j_GetMessageMapCMapTabMEBAPEBUAFX_MSGMAPXZ_14000AEAC.h" />
    <ClInclude Include="headers\j_GetMessageMapCObjectSearchDlgMEBAPEBUAFX_MSGMAPX_1400079AF.h" />
    <ClInclude Include="headers\j_GetMessageMapCObjectTabMEBAPEBUAFX_MSGMAPXZ_14000D459.h" />
    <ClInclude Include="headers\j_GetMessageMapCOpenDlgMEBAPEBUAFX_MSGMAPXZ_14000CBAD.h" />
    <ClInclude Include="headers\j_GetMessageMapCServerTabMEBAPEBUAFX_MSGMAPXZ_1400089E0.h" />
    <ClInclude Include="headers\j_GetMessageMapCTCPTabMEBAPEBUAFX_MSGMAPXZ_14000DD87.h" />
    <ClInclude Include="headers\j_GetMessageMapLicensePopupDlgMEBAPEBUAFX_MSGMAPXZ_14001171B.h" />
    <ClInclude Include="headers\j_GetNextMessageSeriesBufferedTransformationCrypto_1400095CF.h" />
    <ClInclude Include="headers\j_GetRaceWarRecvrCPvpCashPointQEAA_NXZ_14000758B.h" />
    <ClInclude Include="headers\j_GetSendMsgCMsgListManagerRACE_BOSS_MSGQEAAPEAVCM_140013223.h" />
    <ClInclude Include="headers\j_GetSendMsgCMsgListRACE_BOSS_MSGQEAAPEAVCMsg2XZ_14000FBB9.h" />
    <ClInclude Include="headers\j_GetSendPoint_NET_BUFFERQEAAPEADPEAHPEA_NZ_1400119D2.h" />
    <ClInclude Include="headers\j_GetSendThreadFrameCNetProcessQEAAKXZ_140008800.h" />
    <ClInclude Include="headers\j_GetSocketCNetSocketQEAAPEAU_socketKZ_14000C8E7.h" />
    <ClInclude Include="headers\j_GetSocketCNetWorkingQEAAPEAU_socketKKZ_1400059B1.h" />
    <ClInclude Include="headers\j_GetSocketIPAddressCNetSocketQEAAKKZ_1400032EC.h" />
    <ClInclude Include="headers\j_GetSocketNameYA_N_KPEADZ_14000215D.h" />
    <ClInclude Include="headers\j_GetSocketTypeCNetSocketQEAAPEAU_SOCK_TYPE_PARAMX_140011450.h" />
    <ClInclude Include="headers\j_GetTalikFromSocketYAEKEZ_140013DBD.h" />
    <ClInclude Include="headers\j_GetTalikRecvrPointCPvpCashMngQEAAHEKZ_14000C3A1.h" />
    <ClInclude Include="headers\j_GetTalikRecvrPointCPvpCashMngQEAAHHZ_1400062DF.h" />
    <ClInclude Include="headers\j_GetTalikRecvrPointCPvpCashPointQEAAHEKZ_140001CDF.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCAboutDlgKAPEBUAFX_MSGMAPXZ_140012BA2.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCDisplayViewKAPEBUAFX_MSGMAPXZ_140010E6A.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCGameServerAppKAPEBUAFX_MSGMAPX_140012607.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCGameServerDocKAPEBUAFX_MSGMAPX_1400043C7.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCGameServerViewKAPEBUAFX_MSGMAP_14001388B.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCInfoSheetKAPEBUAFX_MSGMAPXZ_140003431.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCIPXTabKAPEBUAFX_MSGMAPXZ_14000636B.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCMainFrameKAPEBUAFX_MSGMAPXZ_1400070A9.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCMapTabKAPEBUAFX_MSGMAPXZ_14000C379.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCObjectSearchDlgKAPEBUAFX_MSGMA_14000153C.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCObjectTabKAPEBUAFX_MSGMAPXZ_1400097D7.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCOpenDlgKAPEBUAFX_MSGMAPXZ_14000C63A.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCServerTabKAPEBUAFX_MSGMAPXZ_1400104C4.h" />
    <ClInclude Include="headers\j_GetThisMessageMapCTCPTabKAPEBUAFX_MSGMAPXZ_14000F0CE.h" />
    <ClInclude Include="headers\j_GetThisMessageMapLicensePopupDlgKAPEBUAFX_MSGMAP_1400079F5.h" />
    <ClInclude Include="headers\j_GetTotalCountCNetSocketQEAAPEAU_total_countXZ_14000168B.h" />
    <ClInclude Include="headers\j_G_Deque_const_iteratorURECV_DATAVallocatorURECV__140002568.h" />
    <ClInclude Include="headers\j_G_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000742D.h" />
    <ClInclude Include="headers\j_H_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_140001410.h" />
    <ClInclude Include="headers\j_InitAcceptSocketCNetSocketAEAA_NPEADZ_1400136EC.h" />
    <ClInclude Include="headers\j_InitParam_socketQEAAXXZ_1400065DC.h" />
    <ClInclude Include="headers\j_IsGetConnectedCEngNetworkBillEXQEAA_NXZ_14000F150.h" />
    <ClInclude Include="headers\j_IsolatedMessageSeriesEndInputRejectingVBufferedT_1400120DF.h" />
    <ClInclude Include="headers\j_IsolatedMessageSeriesEndInputRejectingVFilterCry_140003FFD.h" />
    <ClInclude Include="headers\j_IsRecvableContEffectCGameObjectUEAA_NXZ_140010681.h" />
    <ClInclude Include="headers\j_IsRecvableContEffectCMonsterUEAA_NXZ_140004B29.h" />
    <ClInclude Include="headers\j_IsRecvedQuestByNPCCQuestMgrQEAA_NHZ_140003535.h" />
    <ClInclude Include="headers\j_IsRecvedQuestByNPCCQuestMgrQEAA_NPEBDZ_14001136A.h" />
    <ClInclude Include="headers\j_IsSendFromWebCMsgRACE_BOSS_MSGQEAA_NXZ_140012495.h" />
    <ClInclude Include="headers\j_IsSendTimeCMsgRACE_BOSS_MSGQEAA_NXZ_14000B9BA.h" />
    <ClInclude Include="headers\j_LoadSendMsgCNetProcessQEAAHKGPEADGZ_140006C67.h" />
    <ClInclude Include="headers\j_LoadSendMsgCNetProcessQEAAHKPEAEPEADGZ_140009278.h" />
    <ClInclude Include="headers\j_lobby_disconnectCMgrAccountLobbyHistoryQEAAXPEAU_14000FF10.h" />
    <ClInclude Include="headers\j_LoopSubProcSendInformCHonorGuildQEAAXEZ_14000BDB1.h" />
    <ClInclude Include="headers\j_LoopSubProcSendInformCOreAmountMgrQEAAXXZ_140009660.h" />
    <ClInclude Include="headers\j_MakeBuddyPacketCGuildQEAAXXZ_14001249A.h" />
    <ClInclude Include="headers\j_MakeConnectionThreadCEnglandBillingMgrQEAA_NXZ_140004F3E.h" />
    <ClInclude Include="headers\j_MakeDownApplierPacketCGuildQEAAXXZ_1400065FA.h" />
    <ClInclude Include="headers\j_MakeDownMemberPacketCGuildQEAAXXZ_140011581.h" />
    <ClInclude Include="headers\j_MakeMoneyIOPacketCGuildQEAAXXZ_14000221B.h" />
    <ClInclude Include="headers\j_MakeQueryInfoPacketCGuildQEAAXXZ_140007D7E.h" />
    <ClInclude Include="headers\j_make_minepacketAutominePersonalAEAAXGGEGKZ_140002270.h" />
    <ClInclude Include="headers\j_max_sizeallocatorURECV_DATAstdQEBA_KXZ_140010127.h" />
    <ClInclude Include="headers\j_max_sizedequeURECV_DATAVallocatorURECV_DATAstdst_1400069BA.h" />
    <ClInclude Include="headers\j_MyMessageBoxYAXPEAD0ZZ_140006627.h" />
    <ClInclude Include="headers\j_NotifyAllProcessEndCNormalGuildBattleGUILD_BATTL_14000107D.h" />
    <ClInclude Include="headers\j_NumberOfMessageSeriesBufferedTransformationCrypt_14000DBD9.h" />
    <ClInclude Include="headers\j_NumberOfMessagesInThisSeriesBufferedTransformati_1400125E4.h" />
    <ClInclude Include="headers\j_NumberOfMessagesStoreCryptoPPUEBAIXZ_14001047E.h" />
    <ClInclude Include="headers\j_OnBUTTONWorldConnectCGameServerViewQEAAXXZ_1400018E8.h" />
    <ClInclude Include="headers\j_OnConnectHACKSHEILD_PARAM_ANTICPUEAAXHZ_14000E7EB.h" />
    <ClInclude Include="headers\j_OnDisConnectHACKSHEILD_PARAM_ANTICPUEAAXXZ_140010B9A.h" />
    <ClInclude Include="headers\j_OnLoopCNetSocketQEAAXXZ_140012BF7.h" />
    <ClInclude Include="headers\j_pc_CashDBInfoRecvResultCMainThreadQEAAXPEAD000KZ_140010F9B.h" />
    <ClInclude Include="headers\j_PopEmptyBufCMsgDataAEAAPEAU_messageXZ_14000996C.h" />
    <ClInclude Include="headers\j_PopMsgCMsgDataAEAAPEAU_messageXZ_14000E3BD.h" />
    <ClInclude Include="headers\j_pop_backdequeURECV_DATAVallocatorURECV_DATAstdst_140005D26.h" />
    <ClInclude Include="headers\j_pop_frontdequeURECV_DATAVallocatorURECV_DATAstds_1400044E9.h" />
    <ClInclude Include="headers\j_PostSendCPostSystemManagerQEAAEPEADZ_14001252B.h" />
    <ClInclude Include="headers\j_PostSendRequestCNetworkEXAEAA_NHPEADZ_14000CB3A.h" />
    <ClInclude Include="headers\j_post_senditemCMgrAvatorItemHistoryQEAAXPEADPEAU__1400036AC.h" />
    <ClInclude Include="headers\j_PotionSocketDivisionRequestCNetworkEXAEAA_NHPEAD_140003D23.h" />
    <ClInclude Include="headers\j_PotionSocketSeparationRequestCNetworkEXAEAA_NHPE_1400053C6.h" />
    <ClInclude Include="headers\j_ProcessMessageCMsgDataEEAAXPEAU_messageZ_14000EC14.h" />
    <ClInclude Include="headers\j_ProcessMessageCMsgProcessEEAAXPEAU_messageZ_14000F592.h" />
    <ClInclude Include="headers\j_PumpMessages2SourceTemplateVFileStoreCryptoPPCry_140006645.h" />
    <ClInclude Include="headers\j_PushAnsyncConnectCNetProcessQEAA_NKPEAUsockaddr__14000E9C1.h" />
    <ClInclude Include="headers\j_pushdata_qry_case_post_sendQEAA_NKEKKPEAD000U_IN_14000446C.h" />
    <ClInclude Include="headers\j_PushEmptyBufCMsgDataAEAAXPEAU_messageZ_14000CD15.h" />
    <ClInclude Include="headers\j_PushIPCheckListCNetSocketQEAA_NKZ_14000ECAF.h" />
    <ClInclude Include="headers\j_PushMsgCMsgDataAEAAXPEAU_messageZ_14000E67E.h" />
    <ClInclude Include="headers\j_push_frontdequeURECV_DATAVallocatorURECV_DATAstd_140005E02.h" />
    <ClInclude Include="headers\j_ReConnectDataBaseCRFNewDatabaseQEAA_NXZ_140002B67.h" />
    <ClInclude Include="headers\j_RecvClientLineCHackShieldExSystemUEAA_NHPEAU_MSG_14000E282.h" />
    <ClInclude Include="headers\j_RecvCNetSocketQEAA_NKPEADHPEAHZ_1400134D5.h" />
    <ClInclude Include="headers\j_RecvGameGuardDataCNationSettingManagerQEAA_NHPEA_1400121FC.h" />
    <ClInclude Include="headers\j_RecvThreadCNetProcessCAXPEAXZ_140007450.h" />
    <ClInclude Include="headers\j_Recv_ApexInformCChiNetworkEXQEAAXKKPEADZ_14000C89C.h" />
    <ClInclude Include="headers\j_Recv_ApexKillCChiNetworkEXQEAAXKKPEADZ_14000A2F4.h" />
    <ClInclude Include="headers\j_ReleaseCNetSocketQEAAXXZ_140003247.h" />
    <ClInclude Include="headers\j_Select_PostRecvSerialFromNameCRFWorldDatabaseQEA_140002103.h" />
    <ClInclude Include="headers\j_Select_PostRecvStorageCheckCRFWorldDatabaseQEAAH_140006C49.h" />
    <ClInclude Include="headers\j_SendBuyErrorResultCUnmannedTraderUserInfoQEAAXGE_140002FCC.h" />
    <ClInclude Include="headers\j_SendCancelRegistErrorResultCUnmannedTraderUserIn_14000E205.h" />
    <ClInclude Include="headers\j_SendCancelRegistSuccessResultCUnmannedTraderUser_14000E6C9.h" />
    <ClInclude Include="headers\j_SendCancelWebCRaceBossMsgControllerIEAAXEPEAVCMs_140004F9D.h" />
    <ClInclude Include="headers\j_SendCancleInfomManagerCRaceBossMsgControllerIEAA_14000AF7E.h" />
    <ClInclude Include="headers\j_SendCancleInfomSenderCRaceBossMsgControllerIEAAX_14000EFA2.h" />
    <ClInclude Include="headers\j_SendCashDBDSNRequestCNationSettingDataGBUEAAXXZ_140009F43.h" />
    <ClInclude Include="headers\j_SendCashDBDSNRequestCNationSettingDataKRUEAAXXZ_14000F92F.h" />
    <ClInclude Include="headers\j_SendCashDBDSNRequestCNationSettingDataNULLUEAAXX_140008AE4.h" />
    <ClInclude Include="headers\j_SendCashDBDSNRequestCNationSettingDataRUUEAAXXZ_140009250.h" />
    <ClInclude Include="headers\j_SendCashDBDSNRequestCNationSettingDataUEAAXXZ_14000C851.h" />
    <ClInclude Include="headers\j_SendCashDBDSNRequestCNationSettingManagerQEAAXXZ_14000E1B5.h" />
    <ClInclude Include="headers\j_SendCChiNetworkEXQEAAHPEAEKPEADGZ_1400102EE.h" />
    <ClInclude Include="headers\j_SendCCurrentGuildBattleInfoManagerGUILD_BATTLEQE_140007680.h" />
    <ClInclude Include="headers\j_SendCEngNetworkBillEXQEAAHPEAEPEADGZ_14000C077.h" />
    <ClInclude Include="headers\j_SendCGuildBattleRankManagerGUILD_BATTLEQEAAXHEKE_14001159F.h" />
    <ClInclude Include="headers\j_SendCGuildBattleReservedScheduleListManagerGUILD_140004994.h" />
    <ClInclude Include="headers\j_SendChangeAggroDataCMonsterAggroMgrQEAAXXZ_140007392.h" />
    <ClInclude Include="headers\j_SendCMsgListManagerRACE_BOSS_MSGQEAAHEKPEBD0AEAP_140008940.h" />
    <ClInclude Include="headers\j_SendCNetSocketQEAA_NKPEADHPEAHZ_140005F01.h" />
    <ClInclude Include="headers\j_SendCNormalGuildBattleGuildMemberGUILD_BATTLEQEA_140008BD4.h" />
    <ClInclude Include="headers\j_SendComfirmWebCRaceBossMsgControllerIEAAXEPEAVCM_14000E921.h" />
    <ClInclude Include="headers\j_SendConfirmCtrlCRaceBossMsgControllerIEAAXEPEAVC_14000A033.h" />
    <ClInclude Include="headers\j_SendCPossibleBattleGuildListManagerGUILD_BATTLEQ_14000FF7E.h" />
    <ClInclude Include="headers\j_SendCRaceBossMsgControllerQEAA_NEKPEBD0KZ_140004E26.h" />
    <ClInclude Include="headers\j_SendCReservedGuildScheduleDayGroupGUILD_BATTLEQE_14000C19E.h" />
    <ClInclude Include="headers\j_SendCReservedGuildScheduleMapGroupGUILD_BATTLEQE_140005E16.h" />
    <ClInclude Include="headers\j_SendCReservedGuildSchedulePageGUILD_BATTLEQEAAXH_140013FC5.h" />
    <ClInclude Include="headers\j_SendCTotalGuildRankInfoQEAAXKHEEKZ_14000CC16.h" />
    <ClInclude Include="headers\j_SendCurrentBattleInfoRequestCGuildBattleControll_1400067E4.h" />
    <ClInclude Include="headers\j_SendCurrHonorGuildListCHonorGuildQEAAXGEEZ_1400059ED.h" />
    <ClInclude Include="headers\j_SendCWeeklyGuildRankInfoQEAAXKHEEKZ_14000DEA4.h" />
    <ClInclude Include="headers\j_SendDeleteNotifyPositionMemberCNormalGuildBattle_1400046D3.h" />
    <ClInclude Include="headers\j_SendDQS_RoomInsertCGuildRoomInfoAEAAXXZ_140013F6B.h" />
    <ClInclude Include="headers\j_SendDQS_RoomUpdateCGuildRoomInfoAEAAXXZ_14000C1CB.h" />
    <ClInclude Include="headers\j_SendDrawResultCNormalGuildBattleGUILD_BATTLEIEAA_140001627.h" />
    <ClInclude Include="headers\j_SendErrorResultCPossibleBattleGuildListManagerGU_140008DA5.h" />
    <ClInclude Include="headers\j_SendExternMsgUs_HFSMSAXPEAV1KPEAXHZ_14000A18C.h" />
    <ClInclude Include="headers\j_SendFirstCPossibleBattleGuildListManagerGUILD_BA_1400121E8.h" />
    <ClInclude Include="headers\j_SendHolyStoneHPToRaceBossCHolyStoneSystemQEAAXXZ_1400075DB.h" />
    <ClInclude Include="headers\j_SendInfoCPossibleBattleGuildListManagerGUILD_BAT_14001170C.h" />
    <ClInclude Include="headers\j_SendInfomSenderCRaceBossMsgControllerIEAAXKEZ_140002874.h" />
    <ClInclude Include="headers\j_SendInformChangeCHonorGuildQEAAXEGZ_14000728E.h" />
    <ClInclude Include="headers\j_SendIsArriveDestroyerCHolyStoneSystemQEAAXEZ_140011B80.h" />
    <ClInclude Include="headers\j_SendKillInformCNormalGuildBattleGUILD_BATTLEIEAA_140007FA4.h" />
    <ClInclude Include="headers\j_SendListCGuildListQEAAXGEEZ_1400017D0.h" />
    <ClInclude Include="headers\j_SendLoopYAKPEAXZ_14000334B.h" />
    <ClInclude Include="headers\j_SendMemberPositionCNormalGuildBattleManagerGUILD_1400075BD.h" />
    <ClInclude Include="headers\j_SendMsgAccount_UILockRefresh_UpdateCUserDBQEAAXX_14000F033.h" />
    <ClInclude Include="headers\j_SendMsgAlterStateCGravityStoneRegenerAEAAXXZ_140003071.h" />
    <ClInclude Include="headers\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140003C47.h" />
    <ClInclude Include="headers\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140003C88.h" />
    <ClInclude Include="headers\j_SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXP_140007982.h" />
    <ClInclude Include="headers\j_SendMsgCreateCCircleZoneAEAAXXZ_1400119C8.h" />
    <ClInclude Include="headers\j_SendMsgGoalCCircleZoneAEAAXXZ_14000776B.h" />
    <ClInclude Include="headers\j_SendMsgRequestResultCRaceBossMsgControllerIEAAXG_140010F64.h" />
    <ClInclude Include="headers\j_SendMsgSucceedBuyCashDbWorkerAEAAXGAEBU_param_ca_14000710D.h" />
    <ClInclude Include="headers\j_SendMsgToMaster_NoCompleteQuestFromNPCCQuestMgrQ_14000EE76.h" />
    <ClInclude Include="headers\j_SendMsgToMaster_NoHaveGiveItemCQuestMgrQEAAXEZ_140003C06.h" />
    <ClInclude Include="headers\j_SendMsgToMaster_NoHaveReturnItemCQuestMgrQEAAXEZ_14001320F.h" />
    <ClInclude Include="headers\j_SendMsgToMaster_ReturnItemAfterQuestCQuestMgrQEA_140004FE8.h" />
    <ClInclude Include="headers\j_SendMsgUs_HFSMSAXPEAV1KKPEAXZ_14000F957.h" />
    <ClInclude Include="headers\j_SendMsg_AddEffectCNuclearBombQEAAXXZ_1400027F2.h" />
    <ClInclude Include="headers\j_SendMsg_AddJoinApplierCGuildQEAAXPEAU_guild_appl_14000BEF6.h" />
    <ClInclude Include="headers\j_SendMsg_AlterMemberGradeCGuildQEAAXXZ_140006C30.h" />
    <ClInclude Include="headers\j_SendMsg_AlterMemberStateCGuildQEAAXXZ_140007CA2.h" />
    <ClInclude Include="headers\j_SendMsg_AlterTransparCTrapQEAAX_NZ_14000E930.h" />
    <ClInclude Include="headers\j_SendMsg_AnimusActHealInformCAnimusQEAAXKHZ_14000EA61.h" />
    <ClInclude Include="headers\j_SendMsg_ApplyGuildBattleResultInformCGuildQEAAXE_140011BF3.h" />
    <ClInclude Include="headers\j_SendMsg_AttackCGuardTowerQEAAXPEAVCAttackZ_14000D161.h" />
    <ClInclude Include="headers\j_SendMsg_AttackCHolyKeeperQEAAXXZ_140006AC8.h" />
    <ClInclude Include="headers\j_SendMsg_AttackCNuclearBombQEAAXHHZ_140008729.h" />
    <ClInclude Include="headers\j_SendMsg_AttackCTrapQEAAXPEAVCAttackZ_140005835.h" />
    <ClInclude Include="headers\j_SendMsg_Attack_ForceCMonsterQEAAXPEAVCMonsterAtt_140007E64.h" />
    <ClInclude Include="headers\j_SendMsg_Attack_GenCAnimusQEAAXPEAVCAttackZ_140001465.h" />
    <ClInclude Include="headers\j_SendMsg_Attack_GenCMonsterQEAAXPEAVCMonsterAttac_140005772.h" />
    <ClInclude Include="headers\j_SendMsg_BillingInfoCUserDBQEAAXXZ_14000EA1B.h" />
    <ClInclude Include="headers\j_SendMsg_BreakStopCGameObjectQEAAXXZ_1400019EC.h" />
    <ClInclude Include="headers\j_SendMsg_BuyCashItemICsSendInterfaceSAXGPEBU_para_140004EB2.h" />
    <ClInclude Include="headers\j_SendMsg_CashDiscountEventInformICsSendInterfaceS_14000CBFD.h" />
    <ClInclude Include="headers\j_SendMsg_CashEventInformICsSendInterfaceSAXGEEPEA_1400011B8.h" />
    <ClInclude Include="headers\j_SendMsg_ChangeTaxRateCGuildQEAAXEZ_1400032BA.h" />
    <ClInclude Include="headers\j_SendMsg_Change_MonsterRotateCMonsterQEAAXXZ_140007A77.h" />
    <ClInclude Include="headers\j_SendMsg_Change_MonsterStateCMonsterQEAAXXZ_14000DC10.h" />
    <ClInclude Include="headers\j_SendMsg_ChannelCloseCDarkHoleChannelQEAAXXZ_140008689.h" />
    <ClInclude Include="headers\j_SendMsg_ConditionalEventInformICsSendInterfaceSA_140005A83.h" />
    <ClInclude Include="headers\j_SendMsg_CouponEnsureCCouponMgrQEAAXGEZ_14000AB2D.h" />
    <ClInclude Include="headers\j_SendMsg_CouponErrorCCouponMgrQEAAXGEZ_140004AC5.h" />
    <ClInclude Include="headers\j_SendMsg_CouponLendResultCCouponMgrQEAAXGPEAU_db__140001721.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCAnimusQEAAXXZ_14000FAF1.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCDarkHoleQEAAXXZ_140008A58.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCGravityStoneAEAAXXZ_140008BC5.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCGuardTowerQEAAXXZ_14001067C.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCHolyKeeperQEAAXXZ_14000B46F.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCHolyStoneQEAAXXZ_140013C8C.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCItemBoxQEAAXXZ_1400071E9.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCMerchantQEAAXXZ_140006F7D.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCMonsterQEAAXXZ_14001013B.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCParkingUnitQEAAXXZ_14000CA22.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCReturnGateIEAAXXZ_140013EE9.h" />
    <ClInclude Include="headers\j_SendMsg_CreateCTrapQEAAXXZ_14000E728.h" />
    <ClInclude Include="headers\j_SendMsg_DelJoinApplierCGuildQEAAXPEAU_guild_appl_140007CD9.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCAnimusQEAAXXZ_14000F8CB.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCDarkHoleQEAAXXZ_140001163.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCGravityStoneAEAAXXZ_14000D382.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCGuardTowerQEAAXEZ_140005088.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCHolyKeeperQEAAXEZ_1400128F5.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCHolyStoneQEAAXEKZ_140013692.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCItemBoxQEAAXXZ_140004B8D.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCMerchantQEAAXXZ_14000D535.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCMonsterQEAAXEZ_14000E9EE.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCParkingUnitQEAAXEZ_14000482C.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCReturnGateIEAAXXZ_1400020D6.h" />
    <ClInclude Include="headers\j_SendMsg_DestroyCTrapQEAAXEZ_140010479.h" />
    <ClInclude Include="headers\j_SendMsg_DownPacketCGuildQEAAXEPEAU_guild_member__14000FA42.h" />
    <ClInclude Include="headers\j_SendMsg_DropMissileCNuclearBombQEAAXXZ_140001A8C.h" />
    <ClInclude Include="headers\j_SendMsg_EconomyDataToWebYAXXZ_14000D1F2.h" />
    <ClInclude Include="headers\j_SendMsg_Emotion_PresentationCMonsterQEAAXEGGHZ_14000D4BD.h" />
    <ClInclude Include="headers\j_SendMsg_EndBattleCHolyStoneSystemQEAAXEZ_140011900.h" />
    <ClInclude Include="headers\j_SendMsg_EnterKeeperCHolyStoneSystemQEAAXHZ_14000BAAF.h" />
    <ClInclude Include="headers\j_SendMsg_EnterStoneCHolyStoneSystemQEAAXHZ_14000C6DF.h" />
    <ClInclude Include="headers\j_SendMsg_ErrorICsSendInterfaceSAXGHZ_140011BDF.h" />
    <ClInclude Include="headers\j_SendMsg_ExitStoneCHolyStoneSystemQEAAXXZ_140012931.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionAutominePersonalUEAAXHZ_14000DA94.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCAnimusUEAAXHZ_140004BEC.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCCircleZoneEEAAXHZ_14000BD84.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCDarkHoleUEAAXHZ_14000B7FD.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCGameObjectUEAAXHZ_140013840.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCGravityStoneRegenerEEAAXHZ_140013386.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCGravityStoneUEAAXHZ_140009C8C.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCGuardTowerUEAAXHZ_140005E70.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCHolyKeeperUEAAXHZ_14000128A.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCHolyStoneUEAAXHZ_1400043FE.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCItemBoxUEAAXHZ_14000DE4F.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCMerchantUEAAXHZ_14000344A.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCMonsterUEAAXHZ_14000217B.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCParkingUnitUEAAXHZ_140011E64.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCReturnGateUEAAXHZ_14000C275.h" />
    <ClInclude Include="headers\j_SendMsg_FixPositionCTrapUEAAXHZ_140004EDF.h" />
    <ClInclude Include="headers\j_SendMsg_GateDestroyCDarkHoleChannelQEAAXPEAEPEAD_140013B9C.h" />
    <ClInclude Include="headers\j_SendMsg_GoodsListICsSendInterfaceSAXGPEBU_param__14000390E.h" />
    <ClInclude Include="headers\j_SendMsg_GuildBattleProposedCGuildQEAAHPEADZ_14000B3B6.h" />
    <ClInclude Include="headers\j_SendMsg_GuildBattleRefusedCGuildQEAAXPEADZ_14000837D.h" />
    <ClInclude Include="headers\j_SendMsg_GuildBattleSuggestResultCGuildQEAAXEPEAD_14000BFFA.h" />
    <ClInclude Include="headers\j_SendMsg_GuildDisjointInformCGuildQEAAXXZ_140004859.h" />
    <ClInclude Include="headers\j_SendMsg_GuildInfoUpdateInformCGuildQEAAXXZ_14000D643.h" />
    <ClInclude Include="headers\j_SendMsg_GuildJoinAcceptInformCGuildQEAAXPEAU_gui_14000E845.h" />
    <ClInclude Include="headers\j_SendMsg_GuildMemberLogoffCGuildQEAAXKZ_14000D977.h" />
    <ClInclude Include="headers\j_SendMsg_GuildMemberPosInformCGuildQEAAXKGGZ_14000BE10.h" />
    <ClInclude Include="headers\j_SendMsg_GuildOutputMoneyFailCGuildQEAAXKZ_1400072ED.h" />
    <ClInclude Include="headers\j_SendMsg_GuildRoomRentedCGuildQEAAXEZ_14000E06B.h" />
    <ClInclude Include="headers\j_SendMsg_HolyKeeperAttackAbleStateCHolyStoneSyste_1400130A2.h" />
    <ClInclude Include="headers\j_SendMsg_HolyKeeperStateChaosCHolyStoneSystemQEAA_14000C6D0.h" />
    <ClInclude Include="headers\j_SendMsg_HolyStoneSystemStateCHolyStoneSystemQEAA_14000CB9E.h" />
    <ClInclude Include="headers\j_SendMsg_InformAttackCNuclearBombQEAAXXZ_14000ED45.h" />
    <ClInclude Include="headers\j_SendMsg_InformDropPosCNuclearBombQEAAXXZ_140001771.h" />
    <ClInclude Include="headers\j_SendMsg_Inform_UILockCUserDBQEAAXXZ_140009C50.h" />
    <ClInclude Include="headers\j_SendMsg_InPcBangTimeCCouponMgrQEAAXGZ_140003706.h" />
    <ClInclude Include="headers\j_SendMsg_IOMoneyCGuildQEAAXKNN_NPEAEZ_140005966.h" />
    <ClInclude Include="headers\j_SendMsg_JobCountCDarkHoleChannelQEAAXHHZ_140010AAF.h" />
    <ClInclude Include="headers\j_SendMsg_JobPassCDarkHoleChannelQEAAXHZ_140002C52.h" />
    <ClInclude Include="headers\j_SendMsg_KickForSailCTransportShipQEAAXHZ_14000D85A.h" />
    <ClInclude Include="headers\j_SendMsg_LeaveMemberCGuildQEAAXK_N0Z_1400041C4.h" />
    <ClInclude Include="headers\j_SendMsg_LimitedsaleEventInformICsSendInterfaceSA_140011C16.h" />
    <ClInclude Include="headers\j_SendMsg_MachineInfoAutoMineMachineQEAAXHZ_1400085D5.h" />
    <ClInclude Include="headers\j_SendMsg_ManageGuildCommitteeResultCGuildQEAAX_NP_140008986.h" />
    <ClInclude Include="headers\j_SendMsg_MasterDieCNuclearBombQEAAXXZ_140010573.h" />
    <ClInclude Include="headers\j_SendMsg_MasterElectPossibleCGuildQEAAX_NZ_140009BB5.h" />
    <ClInclude Include="headers\j_SendMsg_MissionPassCDarkHoleChannelQEAAXXZ_140013D9F.h" />
    <ClInclude Include="headers\j_SendMsg_MoneySupplyDataToWebCMoneySupplyMgrQEAAX_14001401A.h" />
    <ClInclude Include="headers\j_SendMsg_MoveCAnimusQEAAXXZ_14000BA8C.h" />
    <ClInclude Include="headers\j_SendMsg_MoveCHolyKeeperQEAAXXZ_140008FDF.h" />
    <ClInclude Include="headers\j_SendMsg_MoveCMerchantQEAAXXZ_14000E340.h" />
    <ClInclude Include="headers\j_SendMsg_MoveCMonsterQEAAXXZ_140008486.h" />
    <ClInclude Include="headers\j_SendMsg_NewMissionCDarkHoleChannelQEAAXXZ_1400056A0.h" />
    <ClInclude Include="headers\j_SendMsg_NoticeNextQuestCHolyStoneSystemQEAAXHEZ_140001BB8.h" />
    <ClInclude Include="headers\j_SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeperCH_140001ECE.h" />
    <ClInclude Include="headers\j_SendMsg_NuclearFindCNuclearBombQEAAXHEZ_14000696A.h" />
    <ClInclude Include="headers\j_SendMsg_OpenPortalByReactCDarkHoleChannelQEAAXHZ_14000B307.h" />
    <ClInclude Include="headers\j_SendMsg_OpenPortalByResultCDarkHoleChannelQEAAXH_14000FB5A.h" />
    <ClInclude Include="headers\j_SendMsg_PatriarchTaxRateTRC_AutoTradeQEAAXHZ_14000367A.h" />
    <ClInclude Include="headers\j_SendMsg_PvpCashInformCPvpCashPointQEAAXHEZ_1400097F0.h" />
    <ClInclude Include="headers\j_SendMsg_PvpRankListDataCPvpUserRankingInfoAEAAXG_140002A18.h" />
    <ClInclude Include="headers\j_SendMsg_PvpRankListNodataCPvpUserRankingInfoAEAA_1400030A8.h" />
    <ClInclude Include="headers\j_SendMsg_QueryAppointResultClassOrderProcessorQEA_1400136C4.h" />
    <ClInclude Include="headers\j_SendMsg_QueryPacket_InfoCGuildQEAAXHZ_1400131C9.h" />
    <ClInclude Include="headers\j_SendMsg_QuestPassCDarkHoleChannelQEAAXXZ_140008823.h" />
    <ClInclude Include="headers\j_SendMsg_RealAddLimTimeCDarkHoleChannelQEAAXHPEAD_14000F8DA.h" />
    <ClInclude Include="headers\j_SendMsg_RealFixPositionCGameObjectUEAAX_NZ_1400110C2.h" />
    <ClInclude Include="headers\j_SendMsg_RealFixPositionCMerchantUEAAX_NZ_14000F222.h" />
    <ClInclude Include="headers\j_SendMsg_RealMovePointCAnimusUEAAXHZ_14001230F.h" />
    <ClInclude Include="headers\j_SendMsg_RealMovePointCGameObjectUEAAXHZ_14000E859.h" />
    <ClInclude Include="headers\j_SendMsg_RealMovePointCHolyKeeperUEAAXHZ_14000A7CC.h" />
    <ClInclude Include="headers\j_SendMsg_RealMovePointCMerchantUEAAXHZ_140013912.h" />
    <ClInclude Include="headers\j_SendMsg_RealMovePointCMonsterUEAAXHZ_1400138A9.h" />
    <ClInclude Include="headers\j_SendMsg_RealMsgInformCDarkHoleChannelQEAAXPEADZ_140012B98.h" />
    <ClInclude Include="headers\j_SendMsg_RecoverResultCPvpCashPointQEAAXHEHZ_14000AC27.h" />
    <ClInclude Include="headers\j_SendMsg_RemainBufUseTimeCExtPotionBufQEAAX_NGHHH_1400114B9.h" />
    <ClInclude Include="headers\j_SendMsg_RemainCouponInformCCouponMgrQEAAXGEZ_14000FFC9.h" />
    <ClInclude Include="headers\j_SendMsg_ResultCNuclearBombMgrQEAAXHEZ_140010069.h" />
    <ClInclude Include="headers\j_SendMsg_ResultCNuclearBombQEAAXHEZ_14000CD3D.h" />
    <ClInclude Include="headers\j_SendMsg_ResultCodeAutoMineMachineMngQEBAXHEEZ_1400138D6.h" />
    <ClInclude Include="headers\j_SendMsg_ResultCodeAutoMineMachineQEBAXHEEZ_14000B85C.h" />
    <ClInclude Include="headers\j_SendMsg_ResultCodePatriarchElectProcessorQEAAXKE_14000F114.h" />
    <ClInclude Include="headers\j_SendMsg_RoomTimeOverCGuildRoomInfoAEAAXXZ_14000EC0A.h" />
    <ClInclude Include="headers\j_SendMsg_SetHPInformCGameObjectUEAAXXZ_140005542.h" />
    <ClInclude Include="headers\j_SendMsg_StartBattleCHolyStoneSystemQEAAXXZ_14000C77A.h" />
    <ClInclude Include="headers\j_SendMsg_StartBillingCBillingIEAAXXZ_1400015EB.h" />
    <ClInclude Include="headers\j_SendMsg_StartedVoteInformCVoteSystemQEAAXHK_NZ_140003A44.h" />
    <ClInclude Include="headers\j_SendMsg_StateChangeCDarkHoleQEAAXXZ_140002158.h" />
    <ClInclude Include="headers\j_SendMsg_StateChangeCItemBoxQEAAXXZ_14000C5AE.h" />
    <ClInclude Include="headers\j_SendMsg_StoneAlterOperCHolyStoneQEAAXXZ_140005F6A.h" />
    <ClInclude Include="headers\j_SendMsg_StunInformCGameObjectUEAAXXZ_1400012FD.h" />
    <ClInclude Include="headers\j_SendMsg_TalikListCPvpCashPointQEAAXHZ_140007F04.h" />
    <ClInclude Include="headers\j_sendmsg_taxrateTRC_AutoTradeQEAAXHEZ_14001195F.h" />
    <ClInclude Include="headers\j_SendMsg_TicketCheckCTransportShipQEAAXH_NGZ_14000AA65.h" />
    <ClInclude Include="headers\j_SendMsg_TimeOutCDarkHoleChannelQEAAXXZ_14000B834.h" />
    <ClInclude Include="headers\j_SendMsg_TowerCompleteInformCGuardTowerQEAAXXZ_1400015C3.h" />
    <ClInclude Include="headers\j_SendMsg_TransportShipStateCTransportShipQEAAXHZ_14000C757.h" />
    <ClInclude Include="headers\j_SendMsg_TransShipTicketNumInformCMerchantQEAAXHZ_140008F1C.h" />
    <ClInclude Include="headers\j_SendMsg_TrapCompleteInformCTrapQEAAXXZ_140005F38.h" />
    <ClInclude Include="headers\j_SendMsg_VoteCancelInformCGuildQEAAXXZ_14000E6E7.h" />
    <ClInclude Include="headers\j_SendMsg_VoteCompleteCGuildQEAAX_NZ_14000E688.h" />
    <ClInclude Include="headers\j_SendMsg_VoteProcessInform_ContinueCGuildQEAAXPEA_14000978C.h" />
    <ClInclude Include="headers\j_SendMsg_VoteProcessInform_StartCGuildQEAAXXZ_140006410.h" />
    <ClInclude Include="headers\j_SendMsg_VoteStateCGuildQEAAXXZ_14000E1E2.h" />
    <ClInclude Include="headers\j_SendMsg_VoteStopCGuildQEAAXKZ_140011275.h" />
    <ClInclude Include="headers\j_SendMsg_WaitKeeperCHolyStoneSystemQEAAXHEZ_14000E90D.h" />
    <ClInclude Include="headers\j_SendMsg_WaitStoneCHolyStoneSystemQEAAXHZ_14000D378.h" />
    <ClInclude Include="headers\j_SendMsg_ZoneAliveCheckCBillingManagerQEAAXKZ_14000D517.h" />
    <ClInclude Include="headers\j_SendMsg_ZoneAliveCheckCBillingQEAAXKZ_140008544.h" />
    <ClInclude Include="headers\j_SendNextHonorGuildListCHonorGuildQEAAXGEZ_140008D4B.h" />
    <ClInclude Include="headers\j_SendNotifyCloseItemCUnmannedTraderUserInfoQEAAXG_14000DA6C.h" />
    <ClInclude Include="headers\j_SendNotifyHolyStoneDestroyedToRaceBossCHolyStone_14000D3FF.h" />
    <ClInclude Include="headers\j_SendPossibleBattleGuildListCGuildBattleControlle_140006A0F.h" />
    <ClInclude Include="headers\j_SendPossibleBattleGuildListFirstCGuildBattleCont_1400079AA.h" />
    <ClInclude Include="headers\j_SendRaceBossMsgFromWebRequestCNetworkEXAEAA_NHPE_1400018BB.h" />
    <ClInclude Include="headers\j_SendRankListCGuildBattleControllerQEAAXHEKIEKZ_14000F41B.h" />
    <ClInclude Include="headers\j_SendRegenBallCNormalGuildBattleGuildGUILD_BATTLE_1400072E8.h" />
    <ClInclude Include="headers\j_SendRegistItemErrorResultCUnmannedTraderUserInfo_140010767.h" />
    <ClInclude Include="headers\j_SendRegistItemSuccessResultCUnmannedTraderUserIn_140012F49.h" />
    <ClInclude Include="headers\j_SendRequestWebCRaceBossMsgControllerIEAAXEPEAVCM_14000A8EE.h" />
    <ClInclude Include="headers\j_SendReservedScheduleListCGuildBattleControllerQE_1400105B9.h" />
    <ClInclude Include="headers\j_SendSearchErrorResultCUnmannedTraderUserInfoQEAA_14000F263.h" />
    <ClInclude Include="headers\j_SendSearchResultCUnmannedTraderUserInfoQEAAXGPEA_14000F6E1.h" />
    <ClInclude Include="headers\j_SendSellInfomCUnmannedTraderUserInfoQEAAXGGKKKZ_140007892.h" />
    <ClInclude Include="headers\j_SendSMS_CompleteQuestCHolyStoneSystemQEAAXEPEADH_14000B285.h" />
    <ClInclude Include="headers\j_SendSMS_MineTimeExtendCHolyStoneSystemQEAAXHZ_1400083C3.h" />
    <ClInclude Include="headers\j_SendStartNotifyCommitteeMemberPositionCNormalGui_140005DAD.h" />
    <ClInclude Include="headers\j_SendTaxRateCUnmannedTraderTaxRateManagerQEAAXHEZ_1400092B9.h" />
    <ClInclude Include="headers\j_SendTaxRatePatriarchCUnmannedTraderTaxRateManage_1400135CA.h" />
    <ClInclude Include="headers\j_SendThreadCNetProcessCAXPEAXZ_140001AE6.h" />
    <ClInclude Include="headers\j_SendWebAddScheduleInfoCNormalGuildBattleGUILD_BA_140006032.h" />
    <ClInclude Include="headers\j_SendWebBattleEndInfoCNormalGuildBattleGUILD_BATT_140005FF6.h" />
    <ClInclude Include="headers\j_SendWebBattleStartInfoCNormalGuildBattleGUILD_BA_14000895E.h" />
    <ClInclude Include="headers\j_SendWebRaceBossSMSCMainThreadQEAAXPEAU_DB_QRY_SY_1400124C7.h" />
    <ClInclude Include="headers\j_SendWebRaceBossSMSErrorResultCRaceBossMsgControl_14000C3AB.h" />
    <ClInclude Include="headers\j_SendWinLoseResultCNormalGuildBattleGUILD_BATTLEI_1400102CB.h" />
    <ClInclude Include="headers\j_send_attackedAutominePersonalQEAAXXZ_14000FF88.h" />
    <ClInclude Include="headers\j_send_changed_packetAutominePersonalQEAAXHZ_140007AB3.h" />
    <ClInclude Include="headers\j_send_current_stateAutominePersonalQEAAXXZ_1400031E8.h" />
    <ClInclude Include="headers\j_send_ecodeAutominePersonalMgrQEAAXHEZ_14000259F.h" />
    <ClInclude Include="headers\j_send_ecodeAutominePersonalQEAAXEZ_140009552.h" />
    <ClInclude Include="headers\j_send_installedAutominePersonalQEAAXXZ_1400070C2.h" />
    <ClInclude Include="headers\j_SetFtpConnectionWheatyExceptionReportQEAAXPEADI0_14000BA19.h" />
    <ClInclude Include="headers\j_SetMsg_messageQEAAXKKKKZ_140004C0A.h" />
    <ClInclude Include="headers\j_SetPassablePacketCNetworkEXQEAAXKEEZ_140013F98.h" />
    <ClInclude Include="headers\j_SetRaceWarRecvrCPvpCashPointQEAAX_NZ_140002EB4.h" />
    <ClInclude Include="headers\j_SetReconnectFailExitFlagCRFNewDatabaseQEAAX_NZ_14000588F.h" />
    <ClInclude Include="headers\j_SetSocketCNetSocketQEAA_NPEAU_SOCK_TYPE_PARAMPEA_140001C8F.h" />
    <ClInclude Include="headers\j_SFContDelMessageCGameObjectUEAAXEE_N0Z_140002BAD.h" />
    <ClInclude Include="headers\j_SFContUpdateTimeMessageCGameObjectUEAAXEEHZ_14000CAA9.h" />
    <ClInclude Include="headers\j_ShouldPropagateMessageEndFilterCryptoPPMEBA_NXZ_14001122F.h" />
    <ClInclude Include="headers\j_ShouldPropagateMessageSeriesEndFilterCryptoPPMEB_14000C45A.h" />
    <ClInclude Include="headers\j_size_announ_message_receipt_udpQEAAHXZ_140010B2C.h" />
    <ClInclude Include="headers\j_size_apex_send_ipQEAAHXZ_14000E0AC.h" />
    <ClInclude Include="headers\j_size_apex_send_logoutQEAAHXZ_140012224.h" />
    <ClInclude Include="headers\j_size_apex_send_transQEAAHXZ_1400059D4.h" />
    <ClInclude Include="headers\j_size_chat_message_receipt_udpQEAAHXZ_14000EB1A.h" />
    <ClInclude Include="headers\j_size_chat_steal_message_gm_zoclQEAAHXZ_140010E9C.h" />
    <ClInclude Include="headers\j_size_connection_status_result_zoctQEAAHXZ_14000BF37.h" />
    <ClInclude Include="headers\j_size_qry_case_post_sendQEAAHXZ_140013F43.h" />
    <ClInclude Include="headers\j_size_qry_case_sendwebracebosssmsQEAAHXZ_14000ED90.h" />
    <ClInclude Include="headers\j_size_qry_case_update_data_for_post_sendQEAAHXZ_14000F09C.h" />
    <ClInclude Include="headers\j_size_talik_recvr_listQEAAHXZ_140005371.h" />
    <ClInclude Include="headers\j_tutorial_process_report_recvCMgrAccountLobbyHist_14000E980.h" />
    <ClInclude Include="headers\j_unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAX_1400020A9.h" />
    <ClInclude Include="headers\j_unchecked_uninitialized_copyPEAPEAURECV_DATAPEAP_14000B280.h" />
    <ClInclude Include="headers\j_unchecked_uninitialized_fill_nPEAPEAURECV_DATA_K_14001234B.h" />
    <ClInclude Include="headers\j_UpdatePacketClassOrderProcessorQEAAXEEZ_1400092C8.h" />
    <ClInclude Include="headers\j_UpdateSendCRaceBossMsgControllerIEAAXXZ_14000D20B.h" />
    <ClInclude Include="headers\j_Update_PostStorageSendToRecverCRFWorldDatabaseQE_14000B866.h" />
    <ClInclude Include="headers\j_Update_RaceWarRecvrCPvpOrderViewQEAAX_NZ_140003A62.h" />
    <ClInclude Include="headers\j_Y_Deque_const_iteratorURECV_DATAVallocatorURECV__140004CC8.h" />
    <ClInclude Include="headers\j_Y_Deque_iteratorURECV_DATAVallocatorURECV_DATAst_14000138E.h" />
    <ClInclude Include="headers\j__AllocatePEAURECV_DATAstdYAPEAPEAURECV_DATA_KPEA_140007518.h" />
    <ClInclude Include="headers\j__AllocateURECV_DATAstdYAPEAURECV_DATA_KPEAU1Z_140006DB6.h" />
    <ClInclude Include="headers\j__buybygold_buy_single_item_setsenddataCashItemRe_14000ADAD.h" />
    <ClInclude Include="headers\j__CheckSendCNetProcessAEAAXGZ_14000B64F.h" />
    <ClInclude Include="headers\j__CkeckRecvBreakCNetProcessAEAAXXZ_14000791E.h" />
    <ClInclude Include="headers\j__ConstructURECV_DATAU1stdYAXPEAURECV_DATAAEBU1Z_140005E9D.h" />
    <ClInclude Include="headers\j__Copy_backward_optV_Deque_iteratorURECV_DATAVall_1400010F5.h" />
    <ClInclude Include="headers\j__Copy_optV_Deque_iteratorURECV_DATAVallocatorURE_140008198.h" />
    <ClInclude Include="headers\j__db_Update_Data_For_Post_SendCMainThreadAEAAEPEA_14000E269.h" />
    <ClInclude Include="headers\j__DestroyPEAURECV_DATAstdYAXPEAPEAURECV_DATAZ_14000105A.h" />
    <ClInclude Include="headers\j__DestroyURECV_DATAstdYAXPEAURECV_DATAZ_140001A46.h" />
    <ClInclude Include="headers\j__Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DA_140002F1D.h" />
    <ClInclude Include="headers\j__Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DA_14000D3F5.h" />
    <ClInclude Include="headers\j__ECNetSocketUEAAPEAXIZ_0_140009C41.h" />
    <ClInclude Include="headers\j__ECNetSocketUEAAPEAXIZ_1400011C2.h" />
    <ClInclude Include="headers\j__E_messageQEAAPEAXIZ_1400106A4.h" />
    <ClInclude Include="headers\j__E_socketQEAAPEAXIZ_140001528.h" />
    <ClInclude Include="headers\j__Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV__14000AC77.h" />
    <ClInclude Include="headers\j__Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_ite_14000FDB2.h" />
    <ClInclude Include="headers\j__GrowmapdequeURECV_DATAVallocatorURECV_DATAstdst_14000B442.h" />
    <ClInclude Include="headers\j__InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_1400138F4.h" />
    <ClInclude Include="headers\j__Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_it_140006028.h" />
    <ClInclude Include="headers\j__Iter_randomV_Deque_iteratorURECV_DATAVallocator_14000E3EA.h" />
    <ClInclude Include="headers\j__PopRecvMsgCNetProcessAEAAXGZ_140009FE3.h" />
    <ClInclude Include="headers\j__Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_p_140012D64.h" />
    <ClInclude Include="headers\j__Ptr_catV_Deque_iteratorURECV_DATAVallocatorUREC_140011676.h" />
    <ClInclude Include="headers\j__SendListCandidateRegisterAEAAHGEZ_14000E20F.h" />
    <ClInclude Include="headers\j__SendLoopCNetProcessAEAAXKZ_140006FE1.h" />
    <ClInclude Include="headers\j__SendSpeedHackCheckMsgCNetProcessAEAAXHZ_140003F35.h" />
    <ClInclude Include="headers\j__SendVotePaperAllVoterAEAAXXZ_14000E156.h" />
    <ClInclude Include="headers\j__SendVoteScoreAllVoterAEAAXEZ_140003D82.h" />
    <ClInclude Include="headers\j__TidydequeURECV_DATAVallocatorURECV_DATAstdstdIE_1400039EA.h" />
    <ClInclude Include="headers\j__Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPE_140011B62.h" />
    <ClInclude Include="headers\j__Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorP_14000E836.h" />
    <ClInclude Include="headers\j__UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_140011662.h" />
    <ClInclude Include="headers\j__XlendequeURECV_DATAVallocatorURECV_DATAstdstdKA_14000B9F6.h" />
    <ClInclude Include="headers\LoadSendMsgCNetProcessQEAAHKGPEADGZ_140479680.h" />
    <ClInclude Include="headers\LoadSendMsgCNetProcessQEAAHKPEAEPEADGZ_140478F90.h" />
    <ClInclude Include="headers\lobby_disconnectCMgrAccountLobbyHistoryQEAAXPEAU_q_140234BB0.h" />
    <ClInclude Include="headers\LoopSubProcSendInformCHonorGuildQEAAXEZ_14025FBE0.h" />
    <ClInclude Include="headers\LoopSubProcSendInformCOreAmountMgrQEAAXXZ_1403F9A90.h" />
    <ClInclude Include="headers\MakeBuddyPacketCGuildQEAAXXZ_1402551E0.h" />
    <ClInclude Include="headers\MakeConnectionThreadCEnglandBillingMgrQEAA_NXZ_1403197B0.h" />
    <ClInclude Include="headers\MakeDownApplierPacketCGuildQEAAXXZ_140254AC0.h" />
    <ClInclude Include="headers\MakeDownMemberPacketCGuildQEAAXXZ_140254620.h" />
    <ClInclude Include="headers\MakeMoneyIOPacketCGuildQEAAXXZ_140254F20.h" />
    <ClInclude Include="headers\MakeQueryInfoPacketCGuildQEAAXXZ_140254DB0.h" />
    <ClInclude Include="headers\make_heapV_Deque_iteratorUMessageRangeMeterFilterC_140603360.h" />
    <ClInclude Include="headers\make_minepacketAutominePersonalAEAAXGGEGKZ_1402DC130.h" />
    <ClInclude Include="headers\MaxRecoverableLengthPK_SignatureMessageEncodingMet_14058F830.h" />
    <ClInclude Include="headers\MaxRetrievableMessageQueueCryptoPPUEBA_KXZ_140655160.h" />
    <ClInclude Include="headers\max_sizeallocatorUMessageRangeMeterFilterCryptoPPs_140600DD0.h" />
    <ClInclude Include="headers\max_sizeallocatorURECV_DATAstdQEBA_KXZ_14031ADD0.h" />
    <ClInclude Include="headers\max_sizedequeUMessageRangeMeterFilterCryptoPPVallo_140600AB0.h" />
    <ClInclude Include="headers\max_sizedequeURECV_DATAVallocatorURECV_DATAstdstdQ_14031ABF0.h" />
    <ClInclude Include="headers\MessageBoxACWndQEAAHPEBD0IZ_0_1404DBEF8.h" />
    <ClInclude Include="headers\MessageBoxA_0_1404DEF9A.h" />
    <ClInclude Include="headers\MessageEndBERGeneralDecoderCryptoPPQEAAXXZ_14054D430.h" />
    <ClInclude Include="headers\MessageEndBufferedTransformationCryptoPPQEAA_NH_NZ_14054F440.h" />
    <ClInclude Include="headers\MessageEndDERGeneralEncoderCryptoPPQEAAXXZ_14054D7F0.h" />
    <ClInclude Include="headers\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056BED0.h" />
    <ClInclude Include="headers\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056BF80.h" />
    <ClInclude Include="headers\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C040.h" />
    <ClInclude Include="headers\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C0B0.h" />
    <ClInclude Include="headers\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C310.h" />
    <ClInclude Include="headers\MessageRepresentativeBitLengthDL_SignatureSchemeBa_14056C3C0.h" />
    <ClInclude Include="headers\MessageRepresentativeBitLengthTF_SignatureSchemeBa_140624300.h" />
    <ClInclude Include="headers\MessageRepresentativeBitLengthTF_SignatureSchemeBa_1406243D0.h" />
    <ClInclude Include="headers\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056BEB0.h" />
    <ClInclude Include="headers\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056BF60.h" />
    <ClInclude Include="headers\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C020.h" />
    <ClInclude Include="headers\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C090.h" />
    <ClInclude Include="headers\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C2F0.h" />
    <ClInclude Include="headers\MessageRepresentativeLengthDL_SignatureSchemeBaseV_14056C3A0.h" />
    <ClInclude Include="headers\MessageRepresentativeLengthTF_SignatureSchemeBaseV_1406242E0.h" />
    <ClInclude Include="headers\MessageRepresentativeLengthTF_SignatureSchemeBaseV_1406243B0.h" />
    <ClInclude Include="headers\MessageSeriesEndBufferedTransformationCryptoPPUEAA_1405F48F0.h" />
    <ClInclude Include="headers\MessageSeriesEndFilterCryptoPPUEAA_NH_NZ_1405F8FE0.h" />
    <ClInclude Include="headers\MessageSeriesEndOutputProxyCryptoPPUEAA_NH_NZ_1405FEF50.h" />
    <ClInclude Include="headers\MinRepresentativeBitLengthPK_SignatureMessageEncod_14058F810.h" />
    <ClInclude Include="headers\MMessageRangeMeterFilterCryptoPPQEBA_NAEBU012Z_140603300.h" />
    <ClInclude Include="headers\MyMessageBoxYAXPEAD0ZZ_14043B010.h" />
    <ClInclude Include="headers\M_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603B00.h" />
    <ClInclude Include="headers\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140561F60.h" />
    <ClInclude Include="headers\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_1405643C0.h" />
    <ClInclude Include="headers\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140566060.h" />
    <ClInclude Include="headers\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_1406340B0.h" />
    <ClInclude Include="headers\NewSignatureAccumulatorDL_SignerImplUDL_SignatureS_140634580.h" />
    <ClInclude Include="headers\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140563340.h" />
    <ClInclude Include="headers\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140565100.h" />
    <ClInclude Include="headers\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140566DA0.h" />
    <ClInclude Include="headers\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140634350.h" />
    <ClInclude Include="headers\NewVerificationAccumulatorDL_VerifierImplUDL_Signa_140634820.h" />
    <ClInclude Include="headers\NotifyAllProcessEndCNormalGuildBattleGUILD_BATTLEQ_1403E57A0.h" />
    <ClInclude Include="headers\NumberOfMessagesBufferedTransformationCryptoPPUEBA_1405F5190.h" />
    <ClInclude Include="headers\NumberOfMessageSeriesBufferedTransformationCryptoP_14044CF70.h" />
    <ClInclude Include="headers\NumberOfMessageSeriesMessageQueueCryptoPPUEBAIXZ_140655260.h" />
    <ClInclude Include="headers\NumberOfMessagesInThisSeriesBufferedTransformation_14044CF20.h" />
    <ClInclude Include="headers\NumberOfMessagesInThisSeriesMessageQueueCryptoPPUE_140655230.h" />
    <ClInclude Include="headers\NumberOfMessagesMessageQueueCryptoPPUEBAIXZ_140655200.h" />
    <ClInclude Include="headers\NumberOfMessagesStoreCryptoPPUEBAIXZ_140453B40.h" />
    <ClInclude Include="headers\OnBUTTONWorldConnectCGameServerViewQEAAXXZ_14002B4E0.h" />
    <ClInclude Include="headers\OnConnectHACKSHEILD_PARAM_ANTICPUEAAXHZ_1404179D0.h" />
    <ClInclude Include="headers\OnDisConnectHACKSHEILD_PARAM_ANTICPUEAAXXZ_140417A10.h" />
    <ClInclude Include="headers\OnLoopCNetSocketQEAAXXZ_14047E2B0.h" />
    <ClInclude Include="headers\OutputMessageSeriesEndFilterCryptoPPIEAA_NHH_NAEBV_1405F9330.h" />
    <ClInclude Include="headers\PaddedBlockBitLengthTF_CryptoSystemBaseVPK_Decrypt_140624160.h" />
    <ClInclude Include="headers\PaddedBlockBitLengthTF_CryptoSystemBaseVPK_Encrypt_140624230.h" />
    <ClInclude Include="headers\PaddedBlockByteLengthTF_CryptoSystemBaseVPK_Decryp_140624140.h" />
    <ClInclude Include="headers\PaddedBlockByteLengthTF_CryptoSystemBaseVPK_Encryp_140624210.h" />
    <ClInclude Include="headers\pc_CashDBInfoRecvResultCMainThreadQEAAXPEAD000KZ_1401F5200.h" />
    <ClInclude Include="headers\PeekMessageA_0_140676E8E.h" />
    <ClInclude Include="headers\PopEmptyBufCMsgDataAEAAPEAU_messageXZ_140438380.h" />
    <ClInclude Include="headers\PopMsgCMsgDataAEAAPEAU_messageXZ_140438220.h" />
    <ClInclude Include="headers\pop_backdequeUMessageRangeMeterFilterCryptoPPVallo_140600AD0.h" />
    <ClInclude Include="headers\pop_backdequeURECV_DATAVallocatorURECV_DATAstdstdQ_14031E850.h" />
    <ClInclude Include="headers\pop_frontdequeUMessageRangeMeterFilterCryptoPPVall_1405FFF80.h" />
    <ClInclude Include="headers\pop_frontdequeURECV_DATAVallocatorURECV_DATAstdstd_14031E6D0.h" />
    <ClInclude Include="headers\pop_heapV_Deque_iteratorUMessageRangeMeterFilterCr_140605360.h" />
    <ClInclude Include="headers\PostQuitMessage_0_140676EAC.h" />
    <ClInclude Include="headers\PostSendCPostSystemManagerQEAAEPEADZ_140325EF0.h" />
    <ClInclude Include="headers\PostSendRequestCNetworkEXAEAA_NHPEADZ_1401CE6B0.h" />
    <ClInclude Include="headers\post_senditemCMgrAvatorItemHistoryQEAAXPEADPEAU_db_14023E200.h" />
    <ClInclude Include="headers\PotionSocketDivisionRequestCNetworkEXAEAA_NHPEADZ_1401D6F90.h" />
    <ClInclude Include="headers\PotionSocketSeparationRequestCNetworkEXAEAA_NHPEAD_1401D6E90.h" />
    <ClInclude Include="headers\PreTranslateMessageCDialogUEAAHPEAUtagMSGZ_0_1404DBD00.h" />
    <ClInclude Include="headers\PreTranslateMessageCFormViewMEAAHPEAUtagMSGZ_0_1404DC14A.h" />
    <ClInclude Include="headers\PreTranslateMessageCFrameWndUEAAHPEAUtagMSGZ_0_1404DBE02.h" />
    <ClInclude Include="headers\PreTranslateMessageCPropertyPageMEAAHPEAUtagMSGZ_0_1404DC318.h" />
    <ClInclude Include="headers\PreTranslateMessageCPropertySheetUEAAHPEAUtagMSGZ__1404DC2BE.h" />
    <ClInclude Include="headers\PreTranslateMessageCWinThreadUEAAHPEAUtagMSGZ_0_1404DBF58.h" />
    <ClInclude Include="headers\ProcessMessageCMsgDataEEAAXPEAU_messageZ_1404387D0.h" />
    <ClInclude Include="headers\ProcessMessageCMsgProcessEEAAXPEAU_messageZ_1401BFFD0.h" />
    <ClInclude Include="headers\ProcessMessageFilterCWinThreadUEAAHHPEAUtagMSGZ_0_1404DBF7C.h" />
    <ClInclude Include="headers\ProcessRecoverableMessagePK_SignatureMessageEncodi_14058F910.h" />
    <ClInclude Include="headers\ProcessSemisignaturePK_SignatureMessageEncodingMet_14058F8F0.h" />
    <ClInclude Include="headers\PumpMessageCWinThreadUEAAHXZ_0_1404DBF5E.h" />
    <ClInclude Include="headers\PumpMessages2SourceTemplateVFileStoreCryptoPPCrypt_140454160.h" />
    <ClInclude Include="headers\PumpMessages2SourceTemplateVStringStoreCryptoPPCry_14057E280.h" />
    <ClInclude Include="headers\PushAnsyncConnectCNetProcessQEAA_NKPEAUsockaddr_in_140479890.h" />
    <ClInclude Include="headers\pushdata_qry_case_post_sendQEAA_NKEKKPEAD000U_INVE_140328330.h" />
    <ClInclude Include="headers\PushEmptyBufCMsgDataAEAAXPEAU_messageZ_1404382E0.h" />
    <ClInclude Include="headers\PushIPCheckListCNetSocketQEAA_NKZ_14047ED20.h" />
    <ClInclude Include="headers\PushMsgCMsgDataAEAAXPEAU_messageZ_140438180.h" />
    <ClInclude Include="headers\push_backdequeUMessageRangeMeterFilterCryptoPPVall_140600050.h" />
    <ClInclude Include="headers\push_frontdequeURECV_DATAVallocatorURECV_DATAstdst_14031A500.h" />
    <ClInclude Include="headers\Put2MessageQueueCryptoPPUEAA_KPEBE_KH_NZ_140655040.h" />
    <ClInclude Include="headers\PutMessageNameCryptoPPYAPEBDXZ_1405FF5C0.h" />
    <ClInclude Include="headers\PutMessageYAXPEAD0Z_140509BF0.h" />
    <ClInclude Include="headers\ReConnectDataBaseCRFNewDatabaseQEAA_NXZ_140486B00.h" />
    <ClInclude Include="headers\RecoverablePartFirstPK_SignatureMessageEncodingMet_14058F850.h" />
    <ClInclude Include="headers\RecoverAndRestartDL_VerifierBaseUEC2NPointCryptoPP_1405674D0.h" />
    <ClInclude Include="headers\RecoverAndRestartDL_VerifierBaseUECPPointCryptoPPC_140565830.h" />
    <ClInclude Include="headers\RecoverAndRestartDL_VerifierBaseVIntegerCryptoPPCr_140563A90.h" />
    <ClInclude Include="headers\RecoverAndRestartTF_VerifierBaseCryptoPPUEBAAUDeco_1406234C0.h" />
    <ClInclude Include="headers\RecoverMessageFromRepresentativePK_SignatureMessag_14058F970.h" />
    <ClInclude Include="headers\RecoverMessageFromSemisignaturePK_SignatureMessage_14058FA20.h" />
    <ClInclude Include="headers\RecoverMessagePK_VerifierCryptoPPUEBAAUDecodingRes_1405F6430.h" />
    <ClInclude Include="headers\RecoverPK_VerifierCryptoPPUEBAAUDecodingResult2PEA_1405F6390.h" />
    <ClInclude Include="headers\RecvClientLineCHackShieldExSystemUEAA_NHPEAU_MSG_H_1404172C0.h" />
    <ClInclude Include="headers\RecvCNetSocketQEAA_NKPEADHPEAHZ_14047E9C0.h" />
    <ClInclude Include="headers\RecvGameGuardDataCNationSettingManagerQEAA_NHPEAU__140229370.h" />
    <ClInclude Include="headers\RecvThreadCNetProcessCAXPEAXZ_140478340.h" />
    <ClInclude Include="headers\recv_0_1404DBA5E.h" />
    <ClInclude Include="headers\Recv_ApexInformCChiNetworkEXQEAAXKKPEADZ_1404103A0.h" />
    <ClInclude Include="headers\Recv_ApexKillCChiNetworkEXQEAAXKKPEADZ_140410460.h" />
    <ClInclude Include="headers\RefSingletonVDL_SignatureMessageEncodingMethod_DSA_140589FA0.h" />
    <ClInclude Include="headers\RefSingletonVDL_SignatureMessageEncodingMethod_NRC_14063AB10.h" />
    <ClInclude Include="headers\releaseauto_ptrVPK_MessageAccumulatorBaseCryptoPPs_14056C6D0.h" />
    <ClInclude Include="headers\ReleaseCNetSocketQEAAXXZ_14047E140.h" />
    <ClInclude Include="headers\releasemember_ptrVPK_MessageAccumulatorCryptoPPCry_140600260.h" />
    <ClInclude Include="headers\resetmember_ptrVPK_MessageAccumulatorCryptoPPCrypt_140600EE0.h" />
    <ClInclude Include="headers\RestartMessageAccumulatorDL_SignerBaseUEC2NPointCr_14056C2E0.h" />
    <ClInclude Include="headers\RestartMessageAccumulatorDL_SignerBaseUECPPointCry_14056C010.h" />
    <ClInclude Include="headers\RestartMessageAccumulatorDL_SignerBaseVIntegerCryp_14056BEA0.h" />
    <ClInclude Include="headers\RNewObjectVDL_SignatureMessageEncodingMethod_DSACr_14058CE00.h" />
    <ClInclude Include="headers\RNewObjectVDL_SignatureMessageEncodingMethod_NRCry_14063BAA0.h" />
    <ClInclude Include="headers\Select_PostRecvSerialFromNameCRFWorldDatabaseQEAAE_1404B1FE0.h" />
    <ClInclude Include="headers\Select_PostRecvStorageCheckCRFWorldDatabaseQEAAHKZ_1404B23E0.h" />
    <ClInclude Include="headers\SendBuyErrorResultCUnmannedTraderUserInfoQEAAXGEZ_140357FE0.h" />
    <ClInclude Include="headers\SendCancelRegistErrorResultCUnmannedTraderUserInfo_140357EA0.h" />
    <ClInclude Include="headers\SendCancelRegistSuccessResultCUnmannedTraderUserIn_140357F30.h" />
    <ClInclude Include="headers\SendCancelWebCRaceBossMsgControllerIEAAXEPEAVCMsgR_1402A17B0.h" />
    <ClInclude Include="headers\SendCancleInfomManagerCRaceBossMsgControllerIEAAXG_1402A16C0.h" />
    <ClInclude Include="headers\SendCancleInfomSenderCRaceBossMsgControllerIEAAXKZ_1402A1610.h" />
    <ClInclude Include="headers\SendCashDBDSNRequestCNationSettingDataGBUEAAXXZ_14022C0F0.h" />
    <ClInclude Include="headers\SendCashDBDSNRequestCNationSettingDataKRUEAAXXZ_14022B4C0.h" />
    <ClInclude Include="headers\SendCashDBDSNRequestCNationSettingDataNULLUEAAXXZ_1402130F0.h" />
    <ClInclude Include="headers\SendCashDBDSNRequestCNationSettingDataRUUEAAXXZ_14022E840.h" />
    <ClInclude Include="headers\SendCashDBDSNRequestCNationSettingDataUEAAXXZ_140211D80.h" />
    <ClInclude Include="headers\SendCashDBDSNRequestCNationSettingManagerQEAAXXZ_140208060.h" />
    <ClInclude Include="headers\SendCChiNetworkEXQEAAHPEAEKPEADGZ_14040FD20.h" />
    <ClInclude Include="headers\SendCCurrentGuildBattleInfoManagerGUILD_BATTLEQEAA_1403CE3D0.h" />
    <ClInclude Include="headers\SendCEngNetworkBillEXQEAAHPEAEPEADGZ_14031BA40.h" />
    <ClInclude Include="headers\SendCGuildBattleRankManagerGUILD_BATTLEQEAAXHEKEEK_1403CA9C0.h" />
    <ClInclude Include="headers\SendCGuildBattleReservedScheduleListManagerGUILD_B_1403CD830.h" />
    <ClInclude Include="headers\SendChangeAggroDataCMonsterAggroMgrQEAAXXZ_14015E950.h" />
    <ClInclude Include="headers\SendCMsgListManagerRACE_BOSS_MSGQEAAHEKPEBD0AEAPEA_14029F930.h" />
    <ClInclude Include="headers\SendCNetSocketQEAA_NKPEADHPEAHZ_14047EA70.h" />
    <ClInclude Include="headers\SendCNormalGuildBattleGuildMemberGUILD_BATTLEQEAAX_1403E0360.h" />
    <ClInclude Include="headers\SendComfirmWebCRaceBossMsgControllerIEAAXEPEAVCMsg_1402A10F0.h" />
    <ClInclude Include="headers\SendConfirmCtrlCRaceBossMsgControllerIEAAXEPEAVCMs_1402A1250.h" />
    <ClInclude Include="headers\SendCPossibleBattleGuildListManagerGUILD_BATTLEQEA_1403D9990.h" />
    <ClInclude Include="headers\SendCRaceBossMsgControllerQEAA_NEKPEBD0KZ_1402A07C0.h" />
    <ClInclude Include="headers\SendCReservedGuildScheduleDayGroupGUILD_BATTLEQEAA_1403CD040.h" />
    <ClInclude Include="headers\SendCReservedGuildScheduleMapGroupGUILD_BATTLEQEAA_1403CCAD0.h" />
    <ClInclude Include="headers\SendCReservedGuildSchedulePageGUILD_BATTLEQEAAXHKP_1403CBE20.h" />
    <ClInclude Include="headers\SendCTotalGuildRankInfoQEAAXKHEEKZ_1402C8E60.h" />
    <ClInclude Include="headers\SendCurrentBattleInfoRequestCGuildBattleController_1403D6A80.h" />
    <ClInclude Include="headers\SendCurrHonorGuildListCHonorGuildQEAAXGEEZ_14025ED00.h" />
    <ClInclude Include="headers\SendCWeeklyGuildRankInfoQEAAXKHEEKZ_1402CAFD0.h" />
    <ClInclude Include="headers\SendDeleteNotifyPositionMemberCNormalGuildBattleGu_1403E2D80.h" />
    <ClInclude Include="headers\SendDQS_RoomInsertCGuildRoomInfoAEAAXXZ_1402E66F0.h" />
    <ClInclude Include="headers\SendDQS_RoomUpdateCGuildRoomInfoAEAAXXZ_1402E6790.h" />
    <ClInclude Include="headers\SendDrawResultCNormalGuildBattleGUILD_BATTLEIEAAXX_1403E78E0.h" />
    <ClInclude Include="headers\SendErrorResultCPossibleBattleGuildListManagerGUIL_1403CA260.h" />
    <ClInclude Include="headers\SendExternMsgUs_HFSMSAXPEAV1KPEAXHZ_140162D30.h" />
    <ClInclude Include="headers\SendFirstCPossibleBattleGuildListManagerGUILD_BATT_1403D9920.h" />
    <ClInclude Include="headers\SendHolyStoneHPToRaceBossCHolyStoneSystemQEAAXXZ_14027E9C0.h" />
    <ClInclude Include="headers\SendInfoCPossibleBattleGuildListManagerGUILD_BATTL_1403CA110.h" />
    <ClInclude Include="headers\SendInfomSenderCRaceBossMsgControllerIEAAXKEZ_1402A13B0.h" />
    <ClInclude Include="headers\SendInformChangeCHonorGuildQEAAXEGZ_14025F850.h" />
    <ClInclude Include="headers\SendIsArriveDestroyerCHolyStoneSystemQEAAXEZ_14027E860.h" />
    <ClInclude Include="headers\SendKillInformCNormalGuildBattleGUILD_BATTLEIEAAXX_1403E7590.h" />
    <ClInclude Include="headers\SendListCGuildListQEAAXGEEZ_14025DB20.h" />
    <ClInclude Include="headers\SendLoopYAKPEAXZ_140319840.h" />
    <ClInclude Include="headers\SendMemberPositionCNormalGuildBattleManagerGUILD_B_1403D4CD0.h" />
    <ClInclude Include="headers\SendMessageACWndQEAA_JI_K_JZ_0_1404DC40E.h" />
    <ClInclude Include="headers\SendMessageA_0_1404DEF6A.h" />
    <ClInclude Include="headers\SendMsgAccount_UILockRefresh_UpdateCUserDBQEAAXXZ_140118720.h" />
    <ClInclude Include="headers\SendMsgAlterStateCGravityStoneRegenerAEAAXXZ_14012EF50.h" />
    <ClInclude Include="headers\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E2730.h" />
    <ClInclude Include="headers\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E2800.h" />
    <ClInclude Include="headers\SendMsgCNormalGuildBattleGuildGUILD_BATTLEQEAAXPEA_1403E28E0.h" />
    <ClInclude Include="headers\SendMsgCreateCCircleZoneAEAAXXZ_14012DC60.h" />
    <ClInclude Include="headers\SendMsgGoalCCircleZoneAEAAXXZ_14012DDA0.h" />
    <ClInclude Include="headers\SendMsgRequestResultCRaceBossMsgControllerIEAAXGEZ_1402A1060.h" />
    <ClInclude Include="headers\SendMsgSucceedBuyCashDbWorkerAEAAXGAEBU_param_cash_1402F14F0.h" />
    <ClInclude Include="headers\SendMsgToMaster_NoCompleteQuestFromNPCCQuestMgrQEA_14028B530.h" />
    <ClInclude Include="headers\SendMsgToMaster_NoHaveGiveItemCQuestMgrQEAAXEZ_14028B5C0.h" />
    <ClInclude Include="headers\SendMsgToMaster_NoHaveReturnItemCQuestMgrQEAAXEZ_14028B650.h" />
    <ClInclude Include="headers\SendMsgToMaster_ReturnItemAfterQuestCQuestMgrQEAAX_14028B480.h" />
    <ClInclude Include="headers\SendMsgUs_HFSMSAXPEAV1KKPEAXZ_140162C00.h" />
    <ClInclude Include="headers\SendMsg_AddEffectCNuclearBombQEAAXXZ_14013CE00.h" />
    <ClInclude Include="headers\SendMsg_AddJoinApplierCGuildQEAAXPEAU_guild_applie_140256750.h" />
    <ClInclude Include="headers\SendMsg_AlterMemberGradeCGuildQEAAXXZ_1402576B0.h" />
    <ClInclude Include="headers\SendMsg_AlterMemberStateCGuildQEAAXXZ_140257490.h" />
    <ClInclude Include="headers\SendMsg_AlterTransparCTrapQEAAX_NZ_14013FD90.h" />
    <ClInclude Include="headers\SendMsg_AnimusActHealInformCAnimusQEAAXKHZ_14012A730.h" />
    <ClInclude Include="headers\SendMsg_ApplyGuildBattleResultInformCGuildQEAAXEPE_140258550.h" />
    <ClInclude Include="headers\SendMsg_AttackCGuardTowerQEAAXPEAVCAttackZ_140130840.h" />
    <ClInclude Include="headers\SendMsg_AttackCHolyKeeperQEAAXXZ_140134EF0.h" />
    <ClInclude Include="headers\SendMsg_AttackCNuclearBombQEAAXHHZ_14013CDE0.h" />
    <ClInclude Include="headers\SendMsg_AttackCTrapQEAAXPEAVCAttackZ_14013FA00.h" />
    <ClInclude Include="headers\SendMsg_Attack_ForceCMonsterQEAAXPEAVCMonsterAttac_14014EE20.h" />
    <ClInclude Include="headers\SendMsg_Attack_GenCAnimusQEAAXPEAVCAttackZ_14012A4C0.h" />
    <ClInclude Include="headers\SendMsg_Attack_GenCMonsterQEAAXPEAVCMonsterAttackZ_14014EC70.h" />
    <ClInclude Include="headers\SendMsg_BillingInfoCUserDBQEAAXXZ_140118270.h" />
    <ClInclude Include="headers\SendMsg_BreakStopCGameObjectQEAAXXZ_14017B140.h" />
    <ClInclude Include="headers\SendMsg_BuyCashItemICsSendInterfaceSAXGPEBU_param__14030C760.h" />
    <ClInclude Include="headers\SendMsg_CashDiscountEventInformICsSendInterfaceSAX_14030CC30.h" />
    <ClInclude Include="headers\SendMsg_CashEventInformICsSendInterfaceSAXGEEPEAU__14030CE00.h" />
    <ClInclude Include="headers\SendMsg_ChangeTaxRateCGuildQEAAXEZ_140255730.h" />
    <ClInclude Include="headers\SendMsg_Change_MonsterRotateCMonsterQEAAXXZ_140148790.h" />
    <ClInclude Include="headers\SendMsg_Change_MonsterStateCMonsterQEAAXXZ_140148700.h" />
    <ClInclude Include="headers\SendMsg_ChannelCloseCDarkHoleChannelQEAAXXZ_14026C4A0.h" />
    <ClInclude Include="headers\SendMsg_ConditionalEventInformICsSendInterfaceSAXG_14030D1A0.h" />
    <ClInclude Include="headers\SendMsg_CouponEnsureCCouponMgrQEAAXGEZ_1403FDFB0.h" />
    <ClInclude Include="headers\SendMsg_CouponErrorCCouponMgrQEAAXGEZ_1403FE110.h" />
    <ClInclude Include="headers\SendMsg_CouponLendResultCCouponMgrQEAAXGPEAU_db_co_1403FE230.h" />
    <ClInclude Include="headers\SendMsg_CreateCAnimusQEAAXXZ_140129EF0.h" />
    <ClInclude Include="headers\SendMsg_CreateCDarkHoleQEAAXXZ_140163F10.h" />
    <ClInclude Include="headers\SendMsg_CreateCGravityStoneAEAAXXZ_140165140.h" />
    <ClInclude Include="headers\SendMsg_CreateCGuardTowerQEAAXXZ_140130680.h" />
    <ClInclude Include="headers\SendMsg_CreateCHolyKeeperQEAAXXZ_140134D70.h" />
    <ClInclude Include="headers\SendMsg_CreateCHolyStoneQEAAXXZ_1401379D0.h" />
    <ClInclude Include="headers\SendMsg_CreateCItemBoxQEAAXXZ_140166660.h" />
    <ClInclude Include="headers\SendMsg_CreateCMerchantQEAAXXZ_1401393E0.h" />
    <ClInclude Include="headers\SendMsg_CreateCMonsterQEAAXXZ_140148380.h" />
    <ClInclude Include="headers\SendMsg_CreateCParkingUnitQEAAXXZ_140167CE0.h" />
    <ClInclude Include="headers\SendMsg_CreateCReturnGateIEAAXXZ_140168D50.h" />
    <ClInclude Include="headers\SendMsg_CreateCTrapQEAAXXZ_14013F7F0.h" />
    <ClInclude Include="headers\SendMsg_DelJoinApplierCGuildQEAAXPEAU_guild_applie_140256970.h" />
    <ClInclude Include="headers\SendMsg_DestroyCAnimusQEAAXXZ_14012A020.h" />
    <ClInclude Include="headers\SendMsg_DestroyCDarkHoleQEAAXXZ_140164040.h" />
    <ClInclude Include="headers\SendMsg_DestroyCGravityStoneAEAAXXZ_140165220.h" />
    <ClInclude Include="headers\SendMsg_DestroyCGuardTowerQEAAXEZ_140130780.h" />
    <ClInclude Include="headers\SendMsg_DestroyCHolyKeeperQEAAXEZ_140134E60.h" />
    <ClInclude Include="headers\SendMsg_DestroyCHolyStoneQEAAXEKZ_140137AD0.h" />
    <ClInclude Include="headers\SendMsg_DestroyCItemBoxQEAAXXZ_140166830.h" />
    <ClInclude Include="headers\SendMsg_DestroyCMerchantQEAAXXZ_1401394D0.h" />
    <ClInclude Include="headers\SendMsg_DestroyCMonsterQEAAXEZ_1401489D0.h" />
    <ClInclude Include="headers\SendMsg_DestroyCParkingUnitQEAAXEZ_140167E20.h" />
    <ClInclude Include="headers\SendMsg_DestroyCReturnGateIEAAXXZ_140168E60.h" />
    <ClInclude Include="headers\SendMsg_DestroyCTrapQEAAXEZ_14013F900.h" />
    <ClInclude Include="headers\SendMsg_DownPacketCGuildQEAAXEPEAU_guild_member_in_140255470.h" />
    <ClInclude Include="headers\SendMsg_DropMissileCNuclearBombQEAAXXZ_14013CD10.h" />
    <ClInclude Include="headers\SendMsg_EconomyDataToWebYAXXZ_1402A5110.h" />
    <ClInclude Include="headers\SendMsg_Emotion_PresentationCMonsterQEAAXEGGHZ_1401488E0.h" />
    <ClInclude Include="headers\SendMsg_EndBattleCHolyStoneSystemQEAAXEZ_14027EC30.h" />
    <ClInclude Include="headers\SendMsg_EnterKeeperCHolyStoneSystemQEAAXHZ_14027F510.h" />
    <ClInclude Include="headers\SendMsg_EnterStoneCHolyStoneSystemQEAAXHZ_14027F9A0.h" />
    <ClInclude Include="headers\SendMsg_ErrorICsSendInterfaceSAXGHZ_14030C590.h" />
    <ClInclude Include="headers\SendMsg_ExitStoneCHolyStoneSystemQEAAXXZ_14027EB70.h" />
    <ClInclude Include="headers\SendMsg_FixPositionAutominePersonalUEAAXHZ_1402DCD60.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCAnimusUEAAXHZ_14012A1E0.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCCircleZoneEEAAXHZ_14012DD00.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCDarkHoleUEAAXHZ_1401642F0.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCGameObjectUEAAXHZ_14013E3F0.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCGravityStoneRegenerEEAAXHZ_14012EE90.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCGravityStoneUEAAXHZ_140164F70.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCGuardTowerUEAAXHZ_140130930.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCHolyKeeperUEAAXHZ_1401351D0.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCHolyStoneUEAAXHZ_140137B80.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCItemBoxUEAAXHZ_1401668B0.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCMerchantUEAAXHZ_140139A80.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCMonsterUEAAXHZ_140148490.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCParkingUnitUEAAXHZ_140167FD0.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCReturnGateUEAAXHZ_140168BD0.h" />
    <ClInclude Include="headers\SendMsg_FixPositionCTrapUEAAXHZ_14013FB80.h" />
    <ClInclude Include="headers\SendMsg_GateDestroyCDarkHoleChannelQEAAXPEAEPEADHZ_14026CD30.h" />
    <ClInclude Include="headers\SendMsg_GoodsListICsSendInterfaceSAXGPEBU_param_ca_14030C620.h" />
    <ClInclude Include="headers\SendMsg_GuildBattleProposedCGuildQEAAHPEADZ_14025A120.h" />
    <ClInclude Include="headers\SendMsg_GuildBattleRefusedCGuildQEAAXPEADZ_14025A220.h" />
    <ClInclude Include="headers\SendMsg_GuildBattleSuggestResultCGuildQEAAXEPEADZ_1402583F0.h" />
    <ClInclude Include="headers\SendMsg_GuildDisjointInformCGuildQEAAXXZ_140256FE0.h" />
    <ClInclude Include="headers\SendMsg_GuildInfoUpdateInformCGuildQEAAXXZ_140255850.h" />
    <ClInclude Include="headers\SendMsg_GuildJoinAcceptInformCGuildQEAAXPEAU_guild_140256AE0.h" />
    <ClInclude Include="headers\SendMsg_GuildMemberLogoffCGuildQEAAXKZ_140257250.h" />
    <ClInclude Include="headers\SendMsg_GuildMemberPosInformCGuildQEAAXKGGZ_140257360.h" />
    <ClInclude Include="headers\SendMsg_GuildOutputMoneyFailCGuildQEAAXKZ_140256ED0.h" />
    <ClInclude Include="headers\SendMsg_GuildRoomRentedCGuildQEAAXEZ_1402586A0.h" />
    <ClInclude Include="headers\SendMsg_HolyKeeperAttackAbleStateCHolyStoneSystemQ_14027FEE0.h" />
    <ClInclude Include="headers\SendMsg_HolyKeeperStateChaosCHolyStoneSystemQEAAXX_14027FE10.h" />
    <ClInclude Include="headers\SendMsg_HolyStoneSystemStateCHolyStoneSystemQEAAXH_14027F410.h" />
    <ClInclude Include="headers\SendMsg_InformAttackCNuclearBombQEAAXXZ_14013CF70.h" />
    <ClInclude Include="headers\SendMsg_InformDropPosCNuclearBombQEAAXXZ_14013CB90.h" />
    <ClInclude Include="headers\SendMsg_Inform_UILockCUserDBQEAAXXZ_140118650.h" />
    <ClInclude Include="headers\SendMsg_InPcBangTimeCCouponMgrQEAAXGZ_1403FE040.h" />
    <ClInclude Include="headers\SendMsg_IOMoneyCGuildQEAAXKNN_NPEAEZ_140256CF0.h" />
    <ClInclude Include="headers\SendMsg_JobCountCDarkHoleChannelQEAAXHHZ_14026BCF0.h" />
    <ClInclude Include="headers\SendMsg_JobPassCDarkHoleChannelQEAAXHZ_14026C140.h" />
    <ClInclude Include="headers\SendMsg_KickForSailCTransportShipQEAAXHZ_1402653D0.h" />
    <ClInclude Include="headers\SendMsg_LeaveMemberCGuildQEAAXK_N0Z_140255A30.h" />
    <ClInclude Include="headers\SendMsg_LimitedsaleEventInformICsSendInterfaceSAXG_14030D2B0.h" />
    <ClInclude Include="headers\SendMsg_MachineInfoAutoMineMachineQEAAXHZ_1402D2830.h" />
    <ClInclude Include="headers\SendMsg_ManageGuildCommitteeResultCGuildQEAAX_NPEA_140259B00.h" />
    <ClInclude Include="headers\SendMsg_MasterDieCNuclearBombQEAAXXZ_14013D0D0.h" />
    <ClInclude Include="headers\SendMsg_MasterElectPossibleCGuildQEAAX_NZ_140259D50.h" />
    <ClInclude Include="headers\SendMsg_MissionPassCDarkHoleChannelQEAAXXZ_14026C230.h" />
    <ClInclude Include="headers\SendMsg_MoneySupplyDataToWebCMoneySupplyMgrQEAAXPE_14042F5F0.h" />
    <ClInclude Include="headers\SendMsg_MoveCAnimusQEAAXXZ_14012A0D0.h" />
    <ClInclude Include="headers\SendMsg_MoveCHolyKeeperQEAAXXZ_1401350A0.h" />
    <ClInclude Include="headers\SendMsg_MoveCMerchantQEAAXXZ_140139560.h" />
    <ClInclude Include="headers\SendMsg_MoveCMonsterQEAAXXZ_140148A70.h" />
    <ClInclude Include="headers\SendMsg_NewMissionCDarkHoleChannelQEAAXXZ_14026C580.h" />
    <ClInclude Include="headers\SendMsg_NoticeNextQuestCHolyStoneSystemQEAAXHEZ_140280150.h" />
    <ClInclude Include="headers\SendMsg_NotifyHolyKeeperAttackTimeBeKeepKeeperCHol_14027C7E0.h" />
    <ClInclude Include="headers\SendMsg_NuclearFindCNuclearBombQEAAXHEZ_14013CAF0.h" />
    <ClInclude Include="headers\SendMsg_OpenPortalByReactCDarkHoleChannelQEAAXHZ_14026BE00.h" />
    <ClInclude Include="headers\SendMsg_OpenPortalByResultCDarkHoleChannelQEAAXHZ_14026BEF0.h" />
    <ClInclude Include="headers\SendMsg_PatriarchTaxRateTRC_AutoTradeQEAAXHZ_1402D8480.h" />
    <ClInclude Include="headers\SendMsg_PvpCashInformCPvpCashPointQEAAXHEZ_1403F5940.h" />
    <ClInclude Include="headers\SendMsg_PvpRankListDataCPvpUserRankingInfoAEAAXGEE_14032E020.h" />
    <ClInclude Include="headers\SendMsg_PvpRankListNodataCPvpUserRankingInfoAEAAXG_14032DF70.h" />
    <ClInclude Include="headers\SendMsg_QueryAppointResultClassOrderProcessorQEAAX_1402B8E60.h" />
    <ClInclude Include="headers\SendMsg_QueryPacket_InfoCGuildQEAAXHZ_1402559B0.h" />
    <ClInclude Include="headers\SendMsg_QuestPassCDarkHoleChannelQEAAXXZ_14026C3C0.h" />
    <ClInclude Include="headers\SendMsg_RealAddLimTimeCDarkHoleChannelQEAAXHPEADZ_14026CB80.h" />
    <ClInclude Include="headers\SendMsg_RealFixPositionCGameObjectUEAAX_NZ_14017B050.h" />
    <ClInclude Include="headers\SendMsg_RealFixPositionCMerchantUEAAX_NZ_14013A1E0.h" />
    <ClInclude Include="headers\SendMsg_RealMovePointCAnimusUEAAXHZ_14012A330.h" />
    <ClInclude Include="headers\SendMsg_RealMovePointCGameObjectUEAAXHZ_14012E0A0.h" />
    <ClInclude Include="headers\SendMsg_RealMovePointCHolyKeeperUEAAXHZ_1401352D0.h" />
    <ClInclude Include="headers\SendMsg_RealMovePointCMerchantUEAAXHZ_140139BA0.h" />
    <ClInclude Include="headers\SendMsg_RealMovePointCMonsterUEAAXHZ_1401485C0.h" />
    <ClInclude Include="headers\SendMsg_RealMsgInformCDarkHoleChannelQEAAXPEADZ_14026CA60.h" />
    <ClInclude Include="headers\SendMsg_RecoverResultCPvpCashPointQEAAXHEHZ_1403F58A0.h" />
    <ClInclude Include="headers\SendMsg_RemainBufUseTimeCExtPotionBufQEAAX_NGHHHZ_14039FC70.h" />
    <ClInclude Include="headers\SendMsg_RemainCouponInformCCouponMgrQEAAXGEZ_1403FE1A0.h" />
    <ClInclude Include="headers\SendMsg_ResultCNuclearBombMgrQEAAXHEZ_14013B5B0.h" />
    <ClInclude Include="headers\SendMsg_ResultCNuclearBombQEAAXHEZ_14013D260.h" />
    <ClInclude Include="headers\SendMsg_ResultCodeAutoMineMachineMngQEBAXHEEZ_1402D6B90.h" />
    <ClInclude Include="headers\SendMsg_ResultCodeAutoMineMachineQEBAXHEEZ_1402D2A80.h" />
    <ClInclude Include="headers\SendMsg_ResultCodePatriarchElectProcessorQEAAXKEZ_1402BAFB0.h" />
    <ClInclude Include="headers\SendMsg_RoomTimeOverCGuildRoomInfoAEAAXXZ_1402E6890.h" />
    <ClInclude Include="headers\SendMsg_SetHPInformCGameObjectUEAAXXZ_14012C650.h" />
    <ClInclude Include="headers\SendMsg_StartBattleCHolyStoneSystemQEAAXXZ_14027FD40.h" />
    <ClInclude Include="headers\SendMsg_StartBillingCBillingIEAAXXZ_14028D550.h" />
    <ClInclude Include="headers\SendMsg_StartedVoteInformCVoteSystemQEAAXHK_NZ_1402B0890.h" />
    <ClInclude Include="headers\SendMsg_StateChangeCDarkHoleQEAAXXZ_140164240.h" />
    <ClInclude Include="headers\SendMsg_StateChangeCItemBoxQEAAXXZ_140166A10.h" />
    <ClInclude Include="headers\SendMsg_StoneAlterOperCHolyStoneQEAAXXZ_140137CD0.h" />
    <ClInclude Include="headers\SendMsg_StunInformCGameObjectUEAAXXZ_140164850.h" />
    <ClInclude Include="headers\SendMsg_TalikListCPvpCashPointQEAAXHZ_1403F57A0.h" />
    <ClInclude Include="headers\sendmsg_taxrateTRC_AutoTradeQEAAXHEZ_1402D8320.h" />
    <ClInclude Include="headers\SendMsg_TicketCheckCTransportShipQEAAXH_NGZ_140265330.h" />
    <ClInclude Include="headers\SendMsg_TimeOutCDarkHoleChannelQEAAXXZ_14026C8D0.h" />
    <ClInclude Include="headers\SendMsg_TowerCompleteInformCGuardTowerQEAAXXZ_140130A80.h" />
    <ClInclude Include="headers\SendMsg_TransportShipStateCTransportShipQEAAXHZ_140265450.h" />
    <ClInclude Include="headers\SendMsg_TransShipTicketNumInformCMerchantQEAAXHZ_140139650.h" />
    <ClInclude Include="headers\SendMsg_TrapCompleteInformCTrapQEAAXXZ_14013FD10.h" />
    <ClInclude Include="headers\SendMsg_VoteCancelInformCGuildQEAAXXZ_140256410.h" />
    <ClInclude Include="headers\SendMsg_VoteCompleteCGuildQEAAX_NZ_140256630.h" />
    <ClInclude Include="headers\SendMsg_VoteProcessInform_ContinueCGuildQEAAXPEAU__140255F20.h" />
    <ClInclude Include="headers\SendMsg_VoteProcessInform_StartCGuildQEAAXXZ_140255B90.h" />
    <ClInclude Include="headers\SendMsg_VoteStateCGuildQEAAXXZ_140256270.h" />
    <ClInclude Include="headers\SendMsg_VoteStopCGuildQEAAXKZ_140256540.h" />
    <ClInclude Include="headers\SendMsg_WaitKeeperCHolyStoneSystemQEAAXHEZ_14027F7C0.h" />
    <ClInclude Include="headers\SendMsg_WaitStoneCHolyStoneSystemQEAAXHZ_14027F8C0.h" />
    <ClInclude Include="headers\SendMsg_ZoneAliveCheckCBillingManagerQEAAXKZ_1401C42C0.h" />
    <ClInclude Include="headers\SendMsg_ZoneAliveCheckCBillingQEAAXKZ_14028D760.h" />
    <ClInclude Include="headers\SendNextHonorGuildListCHonorGuildQEAAXGEZ_14025EDB0.h" />
    <ClInclude Include="headers\SendNotifyCloseItemCUnmannedTraderUserInfoQEAAXGGK_140357A50.h" />
    <ClInclude Include="headers\SendNotifyHolyStoneDestroyedToRaceBossCHolyStoneSy_14027FC00.h" />
    <ClInclude Include="headers\SendPossibleBattleGuildListCGuildBattleControllerQ_1403D6910.h" />
    <ClInclude Include="headers\SendPossibleBattleGuildListFirstCGuildBattleContro_1403D68B0.h" />
    <ClInclude Include="headers\SendRaceBossMsgFromWebRequestCNetworkEXAEAA_NHPEAD_1401DA990.h" />
    <ClInclude Include="headers\SendRankListCGuildBattleControllerQEAAXHEKIEKZ_1403D6980.h" />
    <ClInclude Include="headers\SendRegenBallCNormalGuildBattleGuildGUILD_BATTLEQE_1403E2150.h" />
    <ClInclude Include="headers\SendRegistItemErrorResultCUnmannedTraderUserInfoQE_140357B10.h" />
    <ClInclude Include="headers\SendRegistItemSuccessResultCUnmannedTraderUserInfo_140357BE0.h" />
    <ClInclude Include="headers\SendRequestWebCRaceBossMsgControllerIEAAXEPEAVCMsg_1402A1480.h" />
    <ClInclude Include="headers\SendReservedScheduleListCGuildBattleControllerQEAA_1403D6A00.h" />
    <ClInclude Include="headers\SendSearchErrorResultCUnmannedTraderUserInfoQEAAXG_140358150.h" />
    <ClInclude Include="headers\SendSearchResultCUnmannedTraderUserInfoQEAAXGPEADZ_140358210.h" />
    <ClInclude Include="headers\SendSellInfomCUnmannedTraderUserInfoQEAAXGGKKKZ_140358090.h" />
    <ClInclude Include="headers\SendSMS_CompleteQuestCHolyStoneSystemQEAAXEPEADH0E_14027ED20.h" />
    <ClInclude Include="headers\SendSMS_MineTimeExtendCHolyStoneSystemQEAAXHZ_14027E720.h" />
    <ClInclude Include="headers\SendStartNotifyCommitteeMemberPositionCNormalGuild_1403E2250.h" />
    <ClInclude Include="headers\SendTaxRateCUnmannedTraderTaxRateManagerQEAAXHEZ_14038E420.h" />
    <ClInclude Include="headers\SendTaxRatePatriarchCUnmannedTraderTaxRateManagerQ_14038E4C0.h" />
    <ClInclude Include="headers\SendThreadCNetProcessCAXPEAXZ_140478BD0.h" />
    <ClInclude Include="headers\SendWebAddScheduleInfoCNormalGuildBattleGUILD_BATT_1403E8690.h" />
    <ClInclude Include="headers\SendWebBattleEndInfoCNormalGuildBattleGUILD_BATTLE_1403E6D50.h" />
    <ClInclude Include="headers\SendWebBattleStartInfoCNormalGuildBattleGUILD_BATT_1403E6C90.h" />
    <ClInclude Include="headers\SendWebRaceBossSMSCMainThreadQEAAXPEAU_DB_QRY_SYN__1401F40B0.h" />
    <ClInclude Include="headers\SendWebRaceBossSMSErrorResultCRaceBossMsgControlle_1402A1890.h" />
    <ClInclude Include="headers\SendWinLoseResultCNormalGuildBattleGUILD_BATTLEIEA_1403E79F0.h" />
    <ClInclude Include="headers\send_0_1404DBA64.h" />
    <ClInclude Include="headers\send_attackedAutominePersonalQEAAXXZ_1402DCB70.h" />
    <ClInclude Include="headers\send_changed_packetAutominePersonalQEAAXHZ_1402DC930.h" />
    <ClInclude Include="headers\send_current_stateAutominePersonalQEAAXXZ_1402DCEA0.h" />
    <ClInclude Include="headers\send_ecodeAutominePersonalMgrQEAAXHEZ_1402E0C20.h" />
    <ClInclude Include="headers\send_ecodeAutominePersonalQEAAXEZ_1402DCC90.h" />
    <ClInclude Include="headers\send_installedAutominePersonalQEAAXXZ_1402DC9F0.h" />
    <ClInclude Include="headers\SetErrorButRunMessageProcYAXP6AXPEADZZ_140509890.h" />
    <ClInclude Include="headers\SetErrorMessageProcYAXP6AXPEADZZ_140509870.h" />
    <ClInclude Include="headers\SetFtpConnectionWheatyExceptionReportQEAAXPEADI000_14043F640.h" />
    <ClInclude Include="headers\SetMsg_messageQEAAXKKKKZ_140438920.h" />
    <ClInclude Include="headers\SetPassablePacketCNetworkEXQEAAXKEEZ_140208100.h" />
    <ClInclude Include="headers\SetRaceWarRecvrCPvpCashPointQEAAX_NZ_14007C320.h" />
    <ClInclude Include="headers\SetReconnectFailExitFlagCRFNewDatabaseQEAAX_NZ_1402F2AB0.h" />
    <ClInclude Include="headers\SetSocketCNetSocketQEAA_NPEAU_SOCK_TYPE_PARAMPEADZ_14047DDA0.h" />
    <ClInclude Include="headers\SetWarningMessageProcYAXP6AXPEADZZ_140509880.h" />
    <ClInclude Include="headers\SFContDelMessageCGameObjectUEAAXEE_N0Z_14012C6A0.h" />
    <ClInclude Include="headers\SFContUpdateTimeMessageCGameObjectUEAAXEEHZ_14012C6C0.h" />
    <ClInclude Include="headers\ShouldPropagateMessageEndFilterCryptoPPMEBA_NXZ_14044CF90.h" />
    <ClInclude Include="headers\ShouldPropagateMessageSeriesEndFilterCryptoPPMEBA__14044CFA0.h" />
    <ClInclude Include="headers\SignAndRestartDL_SignerBaseUEC2NPointCryptoPPCrypt_1405663C0.h" />
    <ClInclude Include="headers\SignAndRestartDL_SignerBaseUECPPointCryptoPPCrypto_140564720.h" />
    <ClInclude Include="headers\SignAndRestartDL_SignerBaseVIntegerCryptoPPCryptoP_1405622F0.h" />
    <ClInclude Include="headers\SignAndRestartTF_SignerBaseCryptoPPUEBA_KAEAVRando_140622BF0.h" />
    <ClInclude Include="headers\SignMessagePK_SignerCryptoPPUEBA_KAEAVRandomNumber_1405F5FF0.h" />
    <ClInclude Include="headers\SignMessageWithRecoveryPK_SignerCryptoPPUEBA_KAEAV_1405F60E0.h" />
    <ClInclude Include="headers\SignPK_SignerCryptoPPUEBA_KAEAVRandomNumberGenerat_1405F5F50.h" />
    <ClInclude Include="headers\size_announ_message_receipt_udpQEAAHXZ_140095000.h" />
    <ClInclude Include="headers\size_apex_send_ipQEAAHXZ_140410C20.h" />
    <ClInclude Include="headers\size_apex_send_logoutQEAAHXZ_140410C40.h" />
    <ClInclude Include="headers\size_apex_send_transQEAAHXZ_140410C30.h" />
    <ClInclude Include="headers\size_chat_message_receipt_udpQEAAHXZ_140094EB0.h" />
    <ClInclude Include="headers\size_chat_steal_message_gm_zoclQEAAHXZ_1403F8D50.h" />
    <ClInclude Include="headers\size_connection_status_result_zoctQEAAHXZ_1401C7750.h" />
    <ClInclude Include="headers\size_qry_case_post_sendQEAAHXZ_140328320.h" />
    <ClInclude Include="headers\size_qry_case_sendwebracebosssmsQEAAHXZ_1401DAB00.h" />
    <ClInclude Include="headers\size_qry_case_update_data_for_post_sendQEAAHXZ_1400CA7D0.h" />
    <ClInclude Include="headers\size_talik_recvr_listQEAAHXZ_1403F6F30.h" />
    <ClInclude Include="headers\SkipMessagesBufferedTransformationCryptoPPUEAAIIZ_1405F5340.h" />
    <ClInclude Include="headers\socket_0_1404DBA58.h" />
    <ClInclude Include="headers\sortV_Deque_iteratorUMessageRangeMeterFilterCrypto_140600FA0.h" />
    <ClInclude Include="headers\sort_heapV_Deque_iteratorUMessageRangeMeterFilterC_140603520.h" />
    <ClInclude Include="headers\SpyMessageQueueCryptoPPQEBAPEBEAEA_KZ_1406548E0.h" />
    <ClInclude Include="headers\SQLConnect_0_1404DB9D4.h" />
    <ClInclude Include="headers\SQLDisconnect_0_1404DB9F2.h" />
    <ClInclude Include="headers\SQLSetConnectAttr_0_1404DB9DA.h" />
    <ClInclude Include="headers\StaticAlgorithmNameDL_SSUDL_Keys_ECDSAVEC2NCryptoP_14056C440.h" />
    <ClInclude Include="headers\StaticAlgorithmNameDL_SSUDL_Keys_ECDSAVECPCryptoPP_14056C0F0.h" />
    <ClInclude Include="headers\StaticAlgorithmNameDL_SSUDL_SignatureKeys_GFPCrypt_140639280.h" />
    <ClInclude Include="headers\StaticAlgorithmNameDL_SSUDL_SignatureKeys_GFPCrypt_1406395B0.h" />
    <ClInclude Include="headers\swapMessageQueueCryptoPPQEAAXAEAV12Z_140654890.h" />
    <ClInclude Include="headers\swapUMessageRangeMeterFilterCryptoPPstdYAXAEAUMess_140604E80.h" />
    <ClInclude Include="headers\TotalBytesRetrievableMessageQueueCryptoPPUEBA_KXZ_1406551D0.h" />
    <ClInclude Include="headers\TransferMessagesTo2BufferedTransformationCryptoPPQ_1405F53D0.h" />
    <ClInclude Include="headers\TransferMessagesToBufferedTransformationCryptoPPQE_1405F7980.h" />
    <ClInclude Include="headers\TransferTo2MessageQueueCryptoPPUEAA_KAEAVBufferedT_140654470.h" />
    <ClInclude Include="headers\TranslateMessage_0_140676E7C.h" />
    <ClInclude Include="headers\TruncatedFinalPK_MessageAccumulatorCryptoPPUEAAXPE_140562EB0.h" />
    <ClInclude Include="headers\tutorial_process_report_recvCMgrAccountLobbyHistor_140234980.h" />
    <ClInclude Include="headers\unchecked_copy_backwardV_Deque_iteratorUMessageRan_1406054E0.h" />
    <ClInclude Include="headers\unchecked_fill_nPEAPEAUMessageRangeMeterFilterCryp_140603790.h" />
    <ClInclude Include="headers\unchecked_fill_nPEAPEAURECV_DATA_KPEAU1stdextYAXPE_14031B3E0.h" />
    <ClInclude Include="headers\unchecked_uninitialized_copyPEAPEAUMessageRangeMet_140601520.h" />
    <ClInclude Include="headers\unchecked_uninitialized_copyPEAPEAURECV_DATAPEAPEA_14031AE40.h" />
    <ClInclude Include="headers\unchecked_uninitialized_fill_nPEAPEAUMessageRangeM_140601590.h" />
    <ClInclude Include="headers\unchecked_uninitialized_fill_nPEAPEAURECV_DATA_KPE_14031AEF0.h" />
    <ClInclude Include="headers\UpdatePacketClassOrderProcessorQEAAXEEZ_1402B9180.h" />
    <ClInclude Include="headers\UpdatePK_MessageAccumulatorBaseCryptoPPUEAAXPEBE_K_1405630D0.h" />
    <ClInclude Include="headers\UpdateSendCRaceBossMsgControllerIEAAXXZ_1402A0C80.h" />
    <ClInclude Include="headers\Update_PostStorageSendToRecverCRFWorldDatabaseQEAA_1404B2F60.h" />
    <ClInclude Include="headers\Update_RaceWarRecvrCPvpOrderViewQEAAX_NZ_1403F8140.h" />
    <ClInclude Include="headers\VerifyAndRestartDL_VerifierBaseUEC2NPointCryptoPPC_140567100.h" />
    <ClInclude Include="headers\VerifyAndRestartDL_VerifierBaseUECPPointCryptoPPCr_140565460.h" />
    <ClInclude Include="headers\VerifyAndRestartDL_VerifierBaseVIntegerCryptoPPCry_1405636C0.h" />
    <ClInclude Include="headers\VerifyAndRestartTF_VerifierBaseCryptoPPUEBA_NAEAVP_1406232D0.h" />
    <ClInclude Include="headers\VerifyMessagePK_VerifierCryptoPPUEBA_NPEBE_K01Z_1405F6290.h" />
    <ClInclude Include="headers\VerifyMessageRepresentativePK_DeterministicSignatu_140622520.h" />
    <ClInclude Include="headers\VerifyMessageRepresentativePK_RecoverableSignature_1406226A0.h" />
    <ClInclude Include="headers\VerifyPK_VerifierCryptoPPUEBA_NPEAVPK_MessageAccum_1405F6200.h" />
    <ClInclude Include="headers\Y_Deque_const_iteratorUMessageRangeMeterFilterCryp_140603C80.h" />
    <ClInclude Include="headers\Y_Deque_const_iteratorURECV_DATAVallocatorURECV_DA_14031EB30.h" />
    <ClInclude Include="headers\Y_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603BC0.h" />
    <ClInclude Include="headers\Y_Deque_iteratorURECV_DATAVallocatorURECV_DATAstd0_14031E980.h" />
    <ClInclude Include="headers\Z_Deque_iteratorUMessageRangeMeterFilterCryptoPPVa_140603BF0.h" />
    <ClInclude Include="headers\_Adjust_heapV_Deque_iteratorUMessageRangeMeterFilt_140604F00.h" />
    <ClInclude Include="headers\_AllocatePEAUMessageRangeMeterFilterCryptoPPstdYAP_140601750.h" />
    <ClInclude Include="headers\_AllocatePEAURECV_DATAstdYAPEAPEAURECV_DATA_KPEAPE_14031B190.h" />
    <ClInclude Include="headers\_AllocateUMessageRangeMeterFilterCryptoPPstdYAPEAU_140601650.h" />
    <ClInclude Include="headers\_AllocateURECV_DATAstdYAPEAURECV_DATA_KPEAU1Z_14031B020.h" />
    <ClInclude Include="headers\_buybygold_buy_single_item_setsenddataCashItemRemo_140300650.h" />
    <ClInclude Include="headers\_CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7C0.h" />
    <ClInclude Include="headers\_CcrFG_rs_EncryptPacketYAHPEAXPEAEHZ_14066D7D2.h" />
    <ClInclude Include="headers\_CheckSendCNetProcessAEAAXGZ_140479A30.h" />
    <ClInclude Include="headers\_CkeckRecvBreakCNetProcessAEAAXXZ_14047B010.h" />
    <ClInclude Include="headers\_CNetSocketCNetSocket__1_dtor0_14047DC60.h" />
    <ClInclude Include="headers\_CNetSocketCNetSocket__1_dtor1_14047DC90.h" />
    <ClInclude Include="headers\_CNetSocketSetSocket__1_dtor0_14047E110.h" />
    <ClInclude Include="headers\_CNetSocket_CNetSocket__1_dtor0_14047DD40.h" />
    <ClInclude Include="headers\_CNetSocket_CNetSocket__1_dtor1_14047DD70.h" />
    <ClInclude Include="headers\_ConstructUMessageRangeMeterFilterCryptoPPU123stdY_1406016C0.h" />
    <ClInclude Include="headers\_ConstructURECV_DATAU1stdYAXPEAURECV_DATAAEBU1Z_14031B0D0.h" />
    <ClInclude Include="headers\_Copy_backward_optV_Deque_iteratorUMessageRangeMet_140605DE0.h" />
    <ClInclude Include="headers\_Copy_backward_optV_Deque_iteratorURECV_DATAValloc_14031F460.h" />
    <ClInclude Include="headers\_Copy_optV_Deque_iteratorURECV_DATAVallocatorURECV_14031F6A0.h" />
    <ClInclude Include="headers\_db_Update_Data_For_Post_SendCMainThreadAEAAEPEADZ_1401B8FD0.h" />
    <ClInclude Include="headers\_DestroyPEAUMessageRangeMeterFilterCryptoPPstdYAXP_1406017C0.h" />
    <ClInclude Include="headers\_DestroyPEAURECV_DATAstdYAXPEAPEAURECV_DATAZ_14031FDC0.h" />
    <ClInclude Include="headers\_DestroyUMessageRangeMeterFilterCryptoPPstdYAXPEAU_140601740.h" />
    <ClInclude Include="headers\_DestroyURECV_DATAstdYAXPEAURECV_DATAZ_14031F390.h" />
    <ClInclude Include="headers\_Destroy_rangePEAUMessageRangeMeterFilterCryptoPPV_140601600.h" />
    <ClInclude Include="headers\_Destroy_rangePEAUMessageRangeMeterFilterCryptoPPV_140602380.h" />
    <ClInclude Include="headers\_Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DATA_14031AFA0.h" />
    <ClInclude Include="headers\_Destroy_rangePEAURECV_DATAVallocatorPEAURECV_DATA_14031B3C0.h" />
    <ClInclude Include="headers\_Dist_typeV_Deque_iteratorUMessageRangeMeterFilter_140604390.h" />
    <ClInclude Include="headers\_dynamic_atexit_destructor_for__g_vRecvData___1406E9180.h" />
    <ClInclude Include="headers\_dynamic_initializer_for__g_vRecvData___1406E2110.h" />
    <ClInclude Include="headers\_ECNetSocketUEAAPEAXIZ_14047F840.h" />
    <ClInclude Include="headers\_EDL_SignatureMessageEncodingMethod_DSACryptoPPUEA_14058FDB0.h" />
    <ClInclude Include="headers\_EDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_1405AE280.h" />
    <ClInclude Include="headers\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567EF0.h" />
    <ClInclude Include="headers\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_1405ADDC0.h" />
    <ClInclude Include="headers\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_1405ADE20.h" />
    <ClInclude Include="headers\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140635950.h" />
    <ClInclude Include="headers\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_14063DBF0.h" />
    <ClInclude Include="headers\_EDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_14063DF50.h" />
    <ClInclude Include="headers\_EDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_1405AE0E0.h" />
    <ClInclude Include="headers\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1405AD470.h" />
    <ClInclude Include="headers\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1405ADCF0.h" />
    <ClInclude Include="headers\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_1406359B0.h" />
    <ClInclude Include="headers\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_14063DE30.h" />
    <ClInclude Include="headers\_EDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_14063DF30.h" />
    <ClInclude Include="headers\_EMessageQueueCryptoPPW7EAAPEAXIZ_14065EFB0.h" />
    <ClInclude Include="headers\_E_messageQEAAPEAXIZ_140438850.h" />
    <ClInclude Include="headers\_E_socketQEAAPEAXIZ_14047FA70.h" />
    <ClInclude Include="headers\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140604AF0.h" />
    <ClInclude Include="headers\_Fill_nPEAPEAUMessageRangeMeterFilterCryptoPP_KPEA_140605760.h" />
    <ClInclude Include="headers\_Fill_nPEAPEAURECV_DATA_KPEAU1stdYAXPEAPEAURECV_DA_14031B560.h" />
    <ClInclude Include="headers\_Fill_nPEAPEAURECV_DATA_KPEAU1Urandom_access_itera_14031B4E0.h" />
    <ClInclude Include="headers\_GDL_SignatureMessageEncodingMethod_NRCryptoPPUEAA_14063D300.h" />
    <ClInclude Include="headers\_GDL_SignerImplUDL_SignatureSchemeOptionsUDSACrypt_140567CD0.h" />
    <ClInclude Include="headers\_GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140567FB0.h" />
    <ClInclude Include="headers\_GDL_SignerImplUDL_SignatureSchemeOptionsVDL_SSUDL_140635A10.h" />
    <ClInclude Include="headers\_GDL_VerifierImplUDL_SignatureSchemeOptionsUDSACry_140567D90.h" />
    <ClInclude Include="headers\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140567F50.h" />
    <ClInclude Include="headers\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140568010.h" />
    <ClInclude Include="headers\_GDL_VerifierImplUDL_SignatureSchemeOptionsVDL_SSU_140635A70.h" />
    <ClInclude Include="headers\_GMessageQueueCryptoPPUEAAPEAXIZ_1406552B0.h" />
    <ClInclude Include="headers\_GPK_MessageAccumulatorImplVSHA1CryptoPPCryptoPPUE_140567D30.h" />
    <ClInclude Include="headers\_GrowmapdequeUMessageRangeMeterFilterCryptoPPVallo_140600530.h" />
    <ClInclude Include="headers\_GrowmapdequeURECV_DATAVallocatorURECV_DATAstdstdI_14031A690.h" />
    <ClInclude Include="headers\_Insertion_sort1V_Deque_iteratorUMessageRangeMeter_140604710.h" />
    <ClInclude Include="headers\_Insertion_sortV_Deque_iteratorUMessageRangeMeterF_140603630.h" />
    <ClInclude Include="headers\_InternalPacketProcessCNetProcessAEAA_NKPEAU_MSG_H_14047A4F0.h" />
    <ClInclude Include="headers\_Iter_catPEAPEAUMessageRangeMeterFilterCryptoPPstd_140604AD0.h" />
    <ClInclude Include="headers\_Iter_catPEAPEAURECV_DATAstdYAAUrandom_access_iter_14031B480.h" />
    <ClInclude Include="headers\_Iter_randomV_Deque_iteratorUMessageRangeMeterFilt_140605DA0.h" />
    <ClInclude Include="headers\_Iter_randomV_Deque_iteratorURECV_DATAVallocatorUR_14031F3A0.h" />
    <ClInclude Include="headers\_Make_heapV_Deque_iteratorUMessageRangeMeterFilter_1406043F0.h" />
    <ClInclude Include="headers\_Med3V_Deque_iteratorUMessageRangeMeterFilterCrypt_140604B40.h" />
    <ClInclude Include="headers\_MedianV_Deque_iteratorUMessageRangeMeterFilterCry_140603CB0.h" />
    <ClInclude Include="headers\_PopRecvMsgCNetProcessAEAAXGZ_140478680.h" />
    <ClInclude Include="headers\_Pop_heapV_Deque_iteratorUMessageRangeMeterFilterC_140605F60.h" />
    <ClInclude Include="headers\_Pop_heap_0V_Deque_iteratorUMessageRangeMeterFilte_140605B00.h" />
    <ClInclude Include="headers\_Ptr_catPEAPEAUMessageRangeMeterFilterCryptoPPPEAP_1406022A0.h" />
    <ClInclude Include="headers\_Ptr_catPEAPEAURECV_DATAPEAPEAU1stdYAAU_Scalar_ptr_14031B240.h" />
    <ClInclude Include="headers\_Ptr_catV_Deque_iteratorUMessageRangeMeterFilterCr_140605DC0.h" />
    <ClInclude Include="headers\_Ptr_catV_Deque_iteratorURECV_DATAVallocatorURECV__14031F400.h" />
    <ClInclude Include="headers\_Push_heapV_Deque_iteratorUMessageRangeMeterFilter_1406057B0.h" />
    <ClInclude Include="headers\_SendListCandidateRegisterAEAAHGEZ_1402B6CC0.h" />
    <ClInclude Include="headers\_SendLoopCNetProcessAEAAXKZ_140478D60.h" />
    <ClInclude Include="headers\_SendSpeedHackCheckMsgCNetProcessAEAAXHZ_14047B0E0.h" />
    <ClInclude Include="headers\_SendVotePaperAllVoterAEAAXXZ_1402BEB20.h" />
    <ClInclude Include="headers\_SendVoteScoreAllVoterAEAAXEZ_1402BEEC0.h" />
    <ClInclude Include="headers\_SortV_Deque_iteratorUMessageRangeMeterFilterCrypt_140601B10.h" />
    <ClInclude Include="headers\_Sort_heapV_Deque_iteratorUMessageRangeMeterFilter_1406045D0.h" />
    <ClInclude Include="headers\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EDE0.h" />
    <ClInclude Include="headers\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE10.h" />
    <ClInclude Include="headers\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE40.h" />
    <ClInclude Include="headers\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EE70.h" />
    <ClInclude Include="headers\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EEA0.h" />
    <ClInclude Include="headers\_stdcopy_backward_std_Deque_iterator_RECV_DATA_std_14031EED0.h" />
    <ClInclude Include="headers\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F1A0.h" />
    <ClInclude Include="headers\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F1D0.h" />
    <ClInclude Include="headers\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F200.h" />
    <ClInclude Include="headers\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F230.h" />
    <ClInclude Include="headers\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F260.h" />
    <ClInclude Include="headers\_stdcopy_std_Deque_iterator_RECV_DATA_stdallocator_14031F290.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D800.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D830.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031D860.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E010.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E040.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E070.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0A0.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E0D0.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E100.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E130.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E160.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E190.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA___erase_14031E1C0.h" />
    <ClInclude Include="headers\_stddeque_RECV_DATA_stdallocator_RECV_DATA____Xlen_14031ACD0.h" />
    <ClInclude Include="headers\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F560.h" />
    <ClInclude Include="headers\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F590.h" />
    <ClInclude Include="headers\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5C0.h" />
    <ClInclude Include="headers\_std_Copy_backward_opt_std_Deque_iterator_RECV_DAT_14031F5F0.h" />
    <ClInclude Include="headers\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F7B0.h" />
    <ClInclude Include="headers\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F7E0.h" />
    <ClInclude Include="headers\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F810.h" />
    <ClInclude Include="headers\_std_Copy_opt_std_Deque_iterator_RECV_DATA_stdallo_14031F840.h" />
    <ClInclude Include="headers\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031D9E0.h" />
    <ClInclude Include="headers\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031DA10.h" />
    <ClInclude Include="headers\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031E4D0.h" />
    <ClInclude Include="headers\_std_Deque_iterator_RECV_DATA_stdallocator_RECV_DA_14031E500.h" />
    <ClInclude Include="headers\_TidydequeUMessageRangeMeterFilterCryptoPPVallocat_1406008E0.h" />
    <ClInclude Include="headers\_TidydequeURECV_DATAVallocatorURECV_DATAstdstdIEAA_14031FBC0.h" />
    <ClInclude Include="headers\_Unguarded_partitionV_Deque_iteratorUMessageRangeM_140602520.h" />
    <ClInclude Include="headers\_Uninit_copyPEAPEAUMessageRangeMeterFilterCryptoPP_1406022C0.h" />
    <ClInclude Include="headers\_Uninit_copyPEAPEAURECV_DATAPEAPEAU1VallocatorPEAU_14031B2A0.h" />
    <ClInclude Include="headers\_Uninit_fill_nPEAPEAUMessageRangeMeterFilterCrypto_140602340.h" />
    <ClInclude Include="headers\_Uninit_fill_nPEAPEAURECV_DATA_KPEAU1VallocatorPEA_14031B360.h" />
    <ClInclude Include="headers\_UpdatePacketWinCandidateRegisterAEAAXEPEADKZ_1402B7430.h" />
    <ClInclude Include="headers\_Val_typeV_Deque_iteratorUMessageRangeMeterFilterC_1406043C0.h" />
    <ClInclude Include="headers\_XlendequeUMessageRangeMeterFilterCryptoPPVallocat_140600BA0.h" />
    <ClInclude Include="headers\_XlendequeURECV_DATAVallocatorURECV_DATAstdstdKAXX_14031AC40.h" />
    <ClInclude Include="headers\__imp_load__CcrFG_rs_DecryptPacketYAHPEAXPEAEHZ_14066D7B4.h" />
    <ClInclude Include="headers\__imp_load__CcrFG_rs_EncryptPacketYAHPEAXPEAEHZ_14066D7C6.h" />
  </ItemGroup>
  
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>