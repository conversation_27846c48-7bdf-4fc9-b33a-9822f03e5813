/*
 * Function: ?pc_PlayAttack_Skill@CPlayer@@QEAAXPEAVCCharacter@@PEAMEGGPEAGG@Z
 * Address: 0x140081190
 */

void __fastcall CPlayer::pc_PlayAttack_Skill(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char byEffectCode, unsigned __int16 wSkillIndex, unsigned __int16 wBulletSerial, unsigned __int16 *pConsumeSerial, unsigned __int16 wEffBtSerial)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  int v10; // eax@5
  float v11; // xmm0_4@22
  int v12; // eax@42
  int v13; // eax@43
  unsigned __int64 v14; // rdx@58
  int v15; // eax@63
  CCharacter *v16; // rdx@64
  int v17; // eax@64
  CCharacter *v18; // rdx@65
  int v19; // eax@65
  int v20; // eax@71
  signed int v21; // eax@72
  int v22; // eax@74
  int v23; // eax@74
  int v24; // eax@75
  int v25; // eax@75
  _STORAGE_LIST::_db_con *v26; // rax@93
  __int64 v27; // [sp+0h] [bp-6B8h]@1
  _skill_fld *pSkillFld; // [sp+20h] [bp-698h]@50
  unsigned __int16 v29[4]; // [sp+28h] [bp-690h]@50
  _STORAGE_LIST::_db_con **ppBulletProp; // [sp+30h] [bp-688h]@63
  _BulletItem_fld **ppfldBullet; // [sp+38h] [bp-680h]@63
  _STORAGE_LIST::_db_con *pBulletItem; // [sp+78h] [bp-640h]@4
  _BulletItem_fld *v33; // [sp+98h] [bp-620h]@4
  _skill_fld *v34; // [sp+A8h] [bp-610h]@4
  int nAttType; // [sp+B0h] [bp-608h]@4
  CCharacter *pDsta; // [sp+B8h] [bp-600h]@7
  __int16 Dst[18]; // [sp+C4h] [bp-5F4h]@7
  _STORAGE_LIST::_db_con *pEffBulletItem; // [sp+E8h] [bp-5D0h]@11
  _BulletItem_fld *v39; // [sp+108h] [bp-5B0h]@11
  char v40; // [sp+114h] [bp-5A4h]@11
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+128h] [bp-590h]@11
  char v42; // [sp+130h] [bp-588h]@11
  int pnConsume; // [sp+158h] [bp-560h]@11
  char v44; // [sp+15Ch] [bp-55Ch]@11
  bool pbOverLap; // [sp+184h] [bp-534h]@11
  char v46; // [sp+185h] [bp-533h]@11
  float v47; // [sp+194h] [bp-524h]@18
  __int16 v48; // [sp+198h] [bp-520h]@18
  CPlayerAttack pAt; // [sp+1B0h] [bp-508h]@20
  _attack_param pAP; // [sp+4D0h] [bp-1E8h]@20
  unsigned __int16 v51; // [sp+554h] [bp-164h]@20
  float v52; // [sp+558h] [bp-160h]@20
  bool v53; // [sp+55Ch] [bp-15Ch]@26
  int nParamCode; // [sp+560h] [bp-158h]@39
  int nValue; // [sp+564h] [bp-154h]@45
  CPartyModeKillMonsterExpNotify kPartyExpNotify; // [sp+580h] [bp-138h]@47
  int nTotalDam; // [sp+614h] [bp-A4h]@48
  unsigned __int16 v58; // [sp+618h] [bp-A0h]@50
  unsigned __int16 v59; // [sp+61Ch] [bp-9Ch]@55
  int v60; // [sp+620h] [bp-98h]@60
  int v61; // [sp+624h] [bp-94h]@60
  int j; // [sp+628h] [bp-90h]@60
  int v63; // [sp+62Ch] [bp-8Ch]@63
  CPlayer *v64; // [sp+630h] [bp-88h]@67
  unsigned int dwAlter; // [sp+638h] [bp-80h]@80
  _STORAGE_LIST::_db_con *pItem; // [sp+640h] [bp-78h]@93
  __int16 v67; // [sp+648h] [bp-70h]@93
  __int64 v68; // [sp+658h] [bp-60h]@4
  int v69; // [sp+660h] [bp-58h]@43
  int v70; // [sp+664h] [bp-54h]@63
  CCharacter *v71; // [sp+668h] [bp-50h]@63
  CGameObjectVtbl *v72; // [sp+670h] [bp-48h]@63
  int v73; // [sp+678h] [bp-40h]@64
  CGameObjectVtbl *v74; // [sp+680h] [bp-38h]@64
  int v75; // [sp+688h] [bp-30h]@65
  CGameObjectVtbl *v76; // [sp+690h] [bp-28h]@65
  float v77; // [sp+698h] [bp-20h]@72
  unsigned __int64 v78; // [sp+6A0h] [bp-18h]@4
  CPlayer *v79; // [sp+6C0h] [bp+8h]@1
  CCharacter *v80; // [sp+6C8h] [bp+10h]@1
  float *pfAttackPosa; // [sp+6D0h] [bp+18h]@1
  char v82; // [sp+6D8h] [bp+20h]@1

  v82 = byEffectCode;
  pfAttackPosa = pfAttackPos;
  v80 = pDst;
  v79 = this;
  v8 = &v27;
  for ( i = 428i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v68 = -2i64;
  v78 = (unsigned __int64)&v27 ^ _security_cookie;
  pBulletItem = 0i64;
  v33 = 0i64;
  v34 = (_skill_fld *)CRecordData::GetRecord(&stru_1799C8410 + (unsigned __int8)byEffectCode, wSkillIndex);
  nAttType = 0;
  if ( v82 )
  {
    nAttType = v34->m_nAttType[0];
  }
  else
  {
    v10 = _MASTERY_PARAM::GetSkillLv(&v79->m_pmMst, v34->m_dwIndex);
    nAttType = v34->m_nAttType[v10 - 1];
  }
  pDsta = v80;
  memset_0(Dst, 0, 6ui64);
  if ( nAttType == 4 )
  {
    pDsta = 0i64;
    memcpy_0(pfAttackPosa, v79->m_fCurPos, 0xCui64);
  }
  if ( pDsta )
    memcpy_0(pfAttackPosa, pDsta->m_fCurPos, 0xCui64);
  pEffBulletItem = 0i64;
  v39 = 0i64;
  v40 = CPlayer::_pre_check_skill_attack(
          v79,
          pDsta,
          pfAttackPosa,
          v82,
          v34,
          wBulletSerial,
          &pBulletItem,
          &v33,
          nAttType,
          (unsigned __int16 *)Dst,
          wEffBtSerial,
          &pEffBulletItem,
          &v39);
  ppConsumeItems = 0i64;
  memset(&v42, 0, 0x10ui64);
  pnConsume = 0;
  memset(&v44, 0, 8ui64);
  pbOverLap = 0;
  memset(&v46, 0, 2ui64);
  if ( !v40
    && !CPlayer::GetUseConsumeItem(v79, v34->m_ConsumeItemList, pConsumeSerial, &ppConsumeItems, &pnConsume, &pbOverLap) )
  {
    v40 = -61;
  }
  if ( v40 )
  {
    CPlayer::SendMsg_AttackResult_Error(v79, v40);
    if ( v79->m_bMove )
    {
      CCharacter::Stop((CCharacter *)&v79->vfptr);
      CGameObject::SendMsg_BreakStop((CGameObject *)&v79->vfptr);
    }
    return;
  }
  v47 = FLOAT_1_0;
  v48 = -1;
  if ( pBulletItem )
  {
    v47 = v33->m_fGAAF;
    v48 = pBulletItem->m_wItemIndex;
  }
  CPlayerAttack::CPlayerAttack(&pAt, (CCharacter *)&v79->vfptr);
  _attack_param::_attack_param(&pAP);
  v51 = -1;
  v52 = FLOAT_1_0;
  if ( pEffBulletItem )
  {
    v52 = v39->m_fGAAF;
    v51 = pEffBulletItem->m_wItemIndex;
  }
  v11 = v47;
  CPlayer::make_skill_attack_param(
    v79,
    pDsta,
    pfAttackPosa,
    v82,
    v34,
    nAttType,
    pBulletItem,
    v47,
    &pAP,
    pEffBulletItem,
    v52);
  if ( wEffBtSerial != 0xFFFF && pEffBulletItem )
    CPlayerAttack::AttackSkill(&pAt, &pAP, 1);
  else
    CPlayerAttack::AttackSkill(&pAt, &pAP, 0);
  v53 = 0;
  if ( pAt.m_DamList[0].m_nDamage > 0 && v79->m_pmWpn.nActiveType > -1 && rand() % 100 < v79->m_pmWpn.nActiveProb )
    v53 = CPlayer::WeaponSFActive(v79, pAt.m_DamList, &pAt.m_nDamagedObjNum, &pAP.nShotNum, wBulletSerial);
  CAttack::SetActiveSucc((CAttack *)&pAt.m_pp, v53);
  _effect_parameter::GetEff_Plus(&v79->m_EP, 12);
  _ATTACK_DELAY_CHECKER::SetDelay(&v79->m_AttDelayChker, (signed int)ffloor(v34->m_fActDelay + v11));
  if ( _effect_parameter::GetEff_State(&v79->m_EP, 14) )
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v79->vfptr, 2, 14);
  if ( _effect_parameter::GetEff_State(&v79->m_EP, 21) )
  {
    if ( pAP.nAttactType != 4 && pAP.nAttactType != 5 && pAP.nAttactType != 6 && pAP.nAttactType != 7 )
      return;
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v79->vfptr, 2, 21);
  }
  for ( nParamCode = 0; nParamCode < 3; ++nParamCode )
  {
    if ( (signed int)(unsigned __int16)Dst[nParamCode] > 0 )
    {
      v12 = CPlayer::GetGauge(v79, nParamCode);
      if ( v12 - (unsigned __int16)Dst[nParamCode] <= 0 )
      {
        v69 = 0;
      }
      else
      {
        v13 = CPlayer::GetGauge(v79, nParamCode);
        v69 = v13 - (unsigned __int16)Dst[nParamCode];
      }
      nValue = v69;
      CPlayer::SetGauge(v79, nParamCode, v69, 1);
    }
    CPlayer::SendMsg_Recover(v79);
  }
  CPartyModeKillMonsterExpNotify::CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
  if ( !pAt.m_bFailure )
  {
    nTotalDam = CPlayer::_check_exp_after_attack(v79, pAt.m_nDamagedObjNum, pAt.m_DamList, &kPartyExpNotify);
    CPlayer::_check_dst_param_after_attack(v79, nTotalDam, pDsta);
  }
  if ( pAP.nShotNum > 0 )
  {
    LOBYTE(v29[0]) = 1;
    LOBYTE(pSkillFld) = 0;
    v58 = CPlayer::Emb_AlterDurPoint(v79, 2, pBulletItem->m_byStorageIndex, -pAP.nShotNum, 0, 1);
    if ( v58 )
      CPlayer::SendMsg_AlterWeaponBulletInform(v79, pBulletItem->m_wSerial, v58);
    else
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v79->m_ObjID.m_wIndex,
        pBulletItem,
        v79->m_szItemHistoryFileName);
  }
  if ( pEffBulletItem && pAP.nEffShotNum > 0 )
  {
    LOBYTE(v29[0]) = 1;
    LOBYTE(pSkillFld) = 0;
    v59 = CPlayer::Emb_AlterDurPoint(v79, 2, pEffBulletItem->m_byStorageIndex, -pAP.nEffShotNum, 0, 1);
    if ( v59 )
      CPlayer::SendMsg_AlterWeaponBulletInform(v79, pEffBulletItem->m_wSerial, v59);
    else
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v79->m_ObjID.m_wIndex,
        pEffBulletItem,
        v79->m_szItemHistoryFileName);
  }
  CPlayer::DeleteUseConsumeItem(v79, &ppConsumeItems, &pnConsume, &pbOverLap);
  CPlayer::SendMsg_AttackResult_Skill(v79, v82, &pAt, v48);
  CPartyModeKillMonsterExpNotify::Notify(&kPartyExpNotify);
  if ( v34->m_nAttackable == 2 )
    pAt.m_bIsCrtAtt = 1;
  v60 = 0;
  v61 = 0;
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v63 = pAt.m_DamList[j].m_nActiveDamage + pAt.m_DamList[j].m_nDamage;
    v70 = (unsigned __int8)v82;
    v15 = CPlayerDB::GetLevel(&v79->m_Param);
    v71 = pAt.m_DamList[j].m_pChar;
    v72 = v71->vfptr;
    LOBYTE(ppfldBullet) = 1;
    LODWORD(ppBulletProp) = v34->m_dwIndex;
    *(_DWORD *)v29 = v70;
    LOBYTE(pSkillFld) = pAt.m_bIsCrtAtt;
    ((void (__fastcall *)(CCharacter *, _QWORD, CPlayer *, _QWORD))v72->SetDamage)(
      v71,
      (unsigned int)v63,
      v79,
      (unsigned int)v15);
    if ( CPlayer::IsChaosMode(v79) )
    {
      v73 = ((int (__fastcall *)(CPlayer *))v79->vfptr->GetObjRace)(v79);
      v16 = pAt.m_DamList[j].m_pChar;
      v74 = pAt.m_DamList[j].m_pChar->vfptr;
      v17 = ((int (__fastcall *)(CCharacter *))v74->GetObjRace)(v16);
      if ( v73 == v17 )
        continue;
    }
    v75 = ((int (__fastcall *)(CPlayer *))v79->vfptr->GetObjRace)(v79);
    v18 = pAt.m_DamList[j].m_pChar;
    v76 = pAt.m_DamList[j].m_pChar->vfptr;
    v19 = ((int (__fastcall *)(CCharacter *))v76->GetObjRace)(v18);
    if ( v75 == v19 && !pAt.m_DamList[j].m_pChar->m_ObjID.m_byID )
    {
      v64 = (CPlayer *)pAt.m_DamList[j].m_pChar;
      if ( CPlayer::IsPunished(v64, 1, 0) )
        continue;
    }
    if ( !pAt.m_bFailure && !v82 && v34->m_nMastIndex < 8u )
    {
      v20 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
      if ( CPlayer::IsPassMasteryLimitLvDiff(v79, v20) )
      {
        v77 = (float)pAt.m_DamList[j].m_nDamage;
        v21 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetMaxHP)(pAt.m_DamList[j].m_pChar);
        if ( (float)(v77 / (float)v21) >= 0.0099999998 )
        {
          if ( v34->m_nClass == 1 )
          {
            v24 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
            v25 = CPlayer::GetMasteryCumAfterAttack(v79, v24);
            v60 += 2 * v25;
          }
          else
          {
            v22 = ((int (__fastcall *)(_QWORD))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
            v23 = CPlayer::GetMasteryCumAfterAttack(v79, v22);
            v60 += v23;
          }
          ++v61;
        }
      }
    }
  }
  if ( v61 > 0 && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v79->vfptr->IsInTown)(v79) )
  {
    v14 = (unsigned __int64)v60 >> 32;
    LODWORD(v14) = v60 % v61;
    dwAlter = v60 / v61;
    if ( v60 / v61 > 0 && !v82 )
    {
      LOBYTE(ppBulletProp) = 1;
      *(_QWORD *)v29 = "CPlayer::pc_PlayAttack_Skill()---0";
      LOBYTE(pSkillFld) = 0;
      CPlayer::Emb_AlterStat(v79, 3, v34->m_dwIndex, dwAlter, 0, "CPlayer::pc_PlayAttack_Skill()---0", 1);
      if ( v79->m_pmWpn.byWpType == 7 )
      {
        CPlayer::Emb_AlterStat(v79, 6, 0, 1u, 0, "CPlayer::pc_PlayAttack_Skill()---1", 1);
      }
      else
      {
        LOBYTE(ppBulletProp) = 1;
        *(_QWORD *)v29 = "CPlayer::pc_PlayAttack_Skill()---2";
        LOBYTE(pSkillFld) = 0;
        CPlayer::Emb_AlterStat(v79, 0, v79->m_pmWpn.byWpClass, 1u, 0, "CPlayer::pc_PlayAttack_Skill()---2", 1);
      }
    }
  }
  if ( pDsta
    && v79->m_pRecalledAnimusChar
    && ((int (__fastcall *)(CCharacter *, unsigned __int64))pDsta->vfptr->GetHP)(pDsta, v14) > 0 )
  {
    CAnimus::MasterAttack_MasterInform(v79->m_pRecalledAnimusChar, pDsta);
  }
  if ( pDsta && ((int (__fastcall *)(CCharacter *, unsigned __int64))pDsta->vfptr->GetHP)(pDsta, v14) > 0 )
    _TOWER_PARAM::NotifyOwnerAttackInform(&v79->m_pmTwr, pDsta);
  if ( CPlayer::IsSiegeMode(v79) )
  {
    pItem = v79->m_pSiegeItem;
    v26 = v79->m_pSiegeItem;
    LOBYTE(v29[0]) = 1;
    LOBYTE(pSkillFld) = 0;
    v67 = CPlayer::Emb_AlterDurPoint(v79, 0, v26->m_byStorageIndex, -1, 0, 1);
    if ( !v67 )
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v79->m_ObjID.m_wIndex,
        pItem,
        v79->m_szItemHistoryFileName);
  }
  CPlayer::SetBattleMode(v79, 1);
  CPartyModeKillMonsterExpNotify::~CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
}
