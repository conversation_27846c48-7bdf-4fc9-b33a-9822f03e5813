/*
 * Function: ?pc_MakeTowerRequest@CPlayer@@QEAAXGGEPEAU__material@_make_tower_request_clzo@@PEAMPEAG@Z
 * Address: 0x14009C6E0
 */

void __usercall CPlayer::pc_MakeTowerRequest(CPlayer *this@<rcx>, unsigned __int16 wSkillIndex@<dx>, unsigned __int16 wTowerItemSerial@<r8w>, char byMaterialNum@<r9b>, float a5@<xmm0>, _make_tower_request_clzo::__material *pMaterial, float *pfPos, unsigned __int16 *pConsumeSerial)
{
  __int64 *v8; // rdi@1
  signed __int64 i; // rcx@1
  float v10; // xmm0_4@26
  int v11; // eax@28
  int v12; // eax@56
  int v13; // eax@57
  char v14; // al@59
  int v15; // eax@63
  __int64 v16; // rcx@63
  __int64 v17; // [sp+0h] [bp-278h]@1
  int *pnConsume; // [sp+20h] [bp-258h]@59
  bool *pbOverLap; // [sp+28h] [bp-250h]@59
  bool bQuick; // [sp+30h] [bp-248h]@59
  char v21; // [sp+40h] [bp-238h]@4
  _base_fld *v22; // [sp+48h] [bp-230h]@4
  _STORAGE_LIST::_db_con *pItem; // [sp+50h] [bp-228h]@4
  __int64 v24; // [sp+70h] [bp-208h]@4
  char v25; // [sp+78h] [bp-200h]@4
  char v26; // [sp+174h] [bp-104h]@4
  char v27; // [sp+175h] [bp-103h]@4
  _base_fld *v28; // [sp+188h] [bp-F0h]@4
  int v29; // [sp+190h] [bp-E8h]@4
  int j; // [sp+194h] [bp-E4h]@36
  int pnClassGrade; // [sp+1A4h] [bp-D4h]@4
  _STORAGE_LIST::_db_con *ppConsumeItems; // [sp+1C8h] [bp-B0h]@17
  char v33; // [sp+1D0h] [bp-A8h]@17
  int v34; // [sp+1F8h] [bp-80h]@17
  char v35; // [sp+1FCh] [bp-7Ch]@17
  bool v36; // [sp+224h] [bp-54h]@17
  char v37; // [sp+225h] [bp-53h]@17
  bool (__fastcall *v38)(CCharacter *, CCharacter *, float, char *); // [sp+238h] [bp-40h]@24
  unsigned __int16 v39; // [sp+240h] [bp-38h]@38
  int k; // [sp+244h] [bp-34h]@38
  _base_fld *v41; // [sp+248h] [bp-30h]@45
  unsigned int dwTowerObjSerial; // [sp+250h] [bp-28h]@55
  int nFP; // [sp+254h] [bp-24h]@59
  CGuardTower *pTowerObj; // [sp+258h] [bp-20h]@59
  float v45; // [sp+260h] [bp-18h]@26
  int v46; // [sp+264h] [bp-14h]@57
  CPlayer *pMaster; // [sp+280h] [bp+8h]@1
  unsigned __int16 v48; // [sp+290h] [bp+18h]@1
  char v49; // [sp+298h] [bp+20h]@1

  v49 = byMaterialNum;
  v48 = wTowerItemSerial;
  pMaster = this;
  v8 = &v17;
  for ( i = 156i64; i; --i )
  {
    *(_DWORD *)v8 = -858993460;
    v8 = (__int64 *)((char *)v8 + 4);
  }
  v21 = 0;
  v22 = 0i64;
  pItem = 0i64;
  v24 = 0i64;
  memset(&v25, 0, 0xE8ui64);
  v26 = 0;
  memset(&v27, 0, 2ui64);
  v28 = CRecordData::GetRecord(&stru_1799C8410 + 2, wSkillIndex);
  v29 = 0;
  pnClassGrade = -1;
  if ( pMaster->m_byPosRaceTown == 255 )
  {
    if ( CGuardTower::IsHaveEmpty() )
    {
      if ( pMaster->m_byMoveType == 2 )
      {
        v21 = 13;
      }
      else if ( _TOWER_PARAM::IsEmpty(&pMaster->m_pmTwr) )
      {
        if ( CPlayerDB::IsActableClassSkill(&pMaster->m_Param, v28->m_strCode, &pnClassGrade) )
        {
          if ( pMaster->m_bSFDelayNotCheck
            || _ATTACK_DELAY_CHECKER::IsDelay(&pMaster->m_AttDelayChker, 2, v28->m_dwIndex, pnClassGrade) )
          {
            ppConsumeItems = 0i64;
            memset(&v33, 0, 0x10ui64);
            v34 = 0;
            memset(&v35, 0, 8ui64);
            v36 = 0;
            memset(&v37, 0, 2ui64);
            if ( CPlayer::GetUseConsumeItem(
                   pMaster,
                   (_consume_item_list *)&v28[9].m_strCode[16],
                   pConsumeSerial,
                   &ppConsumeItems,
                   &v34,
                   &v36) )
            {
              GetSqrt(pMaster->m_fCurPos, pfPos);
              if ( a5 <= 40.0 )
              {
                if ( IsOtherTowerNear((CGameObject *)&pMaster->vfptr, pfPos, 0i64) )
                {
                  v21 = 15;
                }
                else if ( v28[13].m_dwIndex == -1 )
                {
                  v21 = 12;
                }
                else
                {
                  v38 = (bool (__fastcall *)(CCharacter *, CCharacter *, float, char *))g_TempEffectFunc[v28[13].m_dwIndex];
                  if ( v38 == DE_MakeGuardTower )
                  {
                    v45 = (float)*(signed int *)&v28[9].m_strCode[8];
                    v10 = v45;
                    _effect_parameter::GetEff_Rate(&pMaster->m_EP, 7);
                    a5 = v45 * v10;
                    v29 = (signed int)ffloor(a5);
                    v11 = CPlayer::GetFP(pMaster);
                    if ( v29 <= v11 )
                    {
                      pItem = _STORAGE_LIST::GetPtrFromSerial(
                                (_STORAGE_LIST *)&pMaster->m_Param.m_dbInven.m_nListNum,
                                v48);
                      if ( pItem )
                      {
                        if ( pItem->m_byTableCode == 25 )
                        {
                          v22 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 25, pItem->m_wItemIndex);
                          if ( pItem->m_bLock )
                          {
                            v21 = 17;
                          }
                          else
                          {
                            for ( j = 0; j < (unsigned __int8)v49; ++j )
                            {
                              v39 = pMaterial[j].wItemSerial;
                              for ( k = 0; k < j; ++k )
                              {
                                if ( v39 == pMaterial[k].wItemSerial )
                                {
                                  v21 = 4;
                                  goto $RESULT_13;
                                }
                              }
                              *(&v24 + j) = (__int64)_STORAGE_LIST::GetPtrFromSerial(
                                                       (_STORAGE_LIST *)&pMaster->m_Param.m_dbInven.m_nListNum,
                                                       v39);
                              if ( !*(&v24 + j) )
                              {
                                v21 = 5;
                                goto $RESULT_13;
                              }
                              v41 = CRecordData::GetRecord(
                                      (CRecordData *)&unk_1799C6AA0 + *(_BYTE *)(*(&v24 + j) + 1),
                                      *(_WORD *)(*(&v24 + j) + 3));
                              if ( strncmp(v41->m_strCode, &v22[pMaterial[j].byMaterSlotIndex + 7].m_strCode[4], 7ui64) )
                              {
                                v21 = 6;
                                goto $RESULT_13;
                              }
                              if ( (unsigned __int64)pMaterial[j].byAmount > *(_QWORD *)(*(&v24 + j) + 5) )
                              {
                                v21 = 7;
                                goto $RESULT_13;
                              }
                              *(&v26 + pMaterial[j].byMaterSlotIndex) += pMaterial[j].byAmount;
                            }
                            for ( j = 0; j < 3; ++j )
                            {
                              if ( (unsigned __int8)*(&v26 + j) != *(_DWORD *)&v22[j + 8].m_strCode[0] )
                              {
                                v21 = 8;
                                break;
                              }
                            }
                          }
                        }
                        else
                        {
                          v21 = 3;
                        }
                      }
                      else
                      {
                        v21 = 2;
                      }
                    }
                    else
                    {
                      v21 = 14;
                    }
                  }
                  else
                  {
                    v21 = 12;
                  }
                }
              }
              else
              {
                v21 = 9;
              }
            }
            else
            {
              v21 = 20;
            }
          }
          else
          {
            v21 = 18;
          }
        }
        else
        {
          v21 = 13;
        }
      }
      else
      {
        v21 = 11;
      }
    }
    else
    {
      v21 = 1;
    }
  }
  else
  {
    v21 = 16;
  }
$RESULT_13:
  dwTowerObjSerial = -1;
  if ( !v21 )
  {
    v12 = CPlayer::GetFP(pMaster);
    if ( v12 - v29 <= 0 )
    {
      v46 = 0;
    }
    else
    {
      v13 = CPlayer::GetFP(pMaster);
      v46 = v13 - v29;
    }
    nFP = v46;
    CPlayer::SetFP(pMaster, v46, 1);
    v14 = CPlayerDB::GetRaceCode(&pMaster->m_Param);
    bQuick = 0;
    LOBYTE(pbOverLap) = v14;
    pnConsume = (int *)pMaster;
    pTowerObj = CreateGuardTower(pMaster->m_pCurMap, pMaster->m_wMapLayerIndex, pfPos, pItem, pMaster, v14, 0);
    if ( pTowerObj )
    {
      for ( j = 0; j < (unsigned __int8)v49; ++j )
      {
        v15 = -pMaterial[j].byAmount;
        v16 = *(&v24 + j);
        LOBYTE(pbOverLap) = 0;
        LOBYTE(pnConsume) = 0;
        CPlayer::Emb_AlterDurPoint(pMaster, 0, *(_BYTE *)(v16 + 49), v15, 0, 0);
      }
      _STORAGE_LIST::_storage_con::lock((_STORAGE_LIST::_storage_con *)&pItem->m_bLoad, 1);
      _TOWER_PARAM::PushList(&pMaster->m_pmTwr, pItem, pTowerObj);
      ++pMaster->m_pmTwr.m_nCount;
      dwTowerObjSerial = pTowerObj->m_dwObjSerial;
      CPlayer::DeleteUseConsumeItem(pMaster, &ppConsumeItems, &v34, &v36);
      _effect_parameter::GetEff_Plus(&pMaster->m_EP, 12);
      _ATTACK_DELAY_CHECKER::SetDelay(
        &pMaster->m_AttDelayChker,
        (signed int)ffloor(*(float *)&v28[9].m_strCode[52] + a5));
    }
    else
    {
      v21 = 1;
    }
  }
  CPlayer::SendMsg_CreateTowerResult(pMaster, v21, dwTowerObjSerial);
}
