/*
 * Function: ?pc_RegistBind@CPlayer@@QEAAXPEAVCItemStore@@@Z
 * Address: 0x1400C7640
 */

void __usercall CPlayer::pc_RegistBind(CPlayer *this@<rcx>, CItemStore *pStore@<rdx>, float a3@<xmm0>)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v5; // [sp+0h] [bp-48h]@1
  char v6; // [sp+20h] [bp-28h]@4
  char *pDummyCode; // [sp+28h] [bp-20h]@4
  char v8; // [sp+30h] [bp-18h]@5
  int j; // [sp+34h] [bp-14h]@5
  CPlayer *v10; // [sp+50h] [bp+8h]@1
  CItemStore *v11; // [sp+58h] [bp+10h]@1

  v11 = pStore;
  v10 = this;
  v3 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v6 = 0;
  pDummyCode = 0i64;
  if ( v10->m_pUserDB )
  {
    v8 = 0;
    for ( j = 0; j < 10; ++j )
    {
      if ( pStore->m_pRec->m_nNpc_Class[j] == 13 )
      {
        v8 = 1;
        break;
      }
    }
    if ( v8 )
    {
      GetSqrt(pStore->m_pDum->m_pDumPos->m_fCenterPos, v10->m_fCurPos);
      if ( a3 <= 100.0 )
      {
        if ( v10->m_pCurMap->m_pMapSet->m_nMapType )
        {
          v6 = 1;
        }
        else if ( v10->m_pCurMap->m_pMapSet->m_nMapClass )
        {
          v6 = 1;
        }
        else
        {
          pDummyCode = (char *)CDummyPosTable::GetRecord(
                                 &v10->m_pCurMap->m_tbBindDumPos,
                                 v11->m_pRec->m_strBinding_DummyName);
          if ( !pDummyCode )
            v6 = 1;
        }
      }
      else
      {
        v6 = 1;
      }
    }
    else
    {
      v6 = 1;
    }
    if ( !v6 )
    {
      v10->m_pBindMapData = v10->m_pCurMap;
      v10->m_pBindDummyData = (_dummy_position *)pDummyCode;
      CUserDB::Update_Bind(v10->m_pUserDB, v10->m_pCurMap->m_pMapSet->m_strCode, pDummyCode, 1);
    }
    CPlayer::SendMsg_RegistBindResult(v10, v6);
  }
}
