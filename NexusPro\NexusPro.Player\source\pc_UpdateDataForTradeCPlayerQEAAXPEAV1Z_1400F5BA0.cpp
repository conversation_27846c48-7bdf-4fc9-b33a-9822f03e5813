/*
 * Function: ?pc_UpdateDataForTrade@CPlayer@@QEAAXPEAV1@@Z
 * Address: 0x1400F5BA0
 */

void __fastcall CPlayer::pc_UpdateDataForTrade(CPlayer *this, CPlayer *pTrader)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // eax@4
  __int64 v5; // [sp+0h] [bp-98h]@1
  _qry_case_update_data_for_trade v6; // [sp+40h] [bp-58h]@4
  CPlayer *v7; // [sp+A0h] [bp+8h]@1
  CPlayer *v8; // [sp+A8h] [bp+10h]@1

  v8 = pTrader;
  v7 = this;
  v2 = &v5;
  for ( i = 36i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  _qry_case_update_data_for_trade::_qry_case_update_data_for_trade(&v6);
  v6.tradelist[0].dwSerial = v7->m_pUserDB->m_dwSerial;
  v6.tradelist[0].dwDalant = CPlayerDB::GetDalant(&v7->m_Param);
  v6.tradelist[0].dwGlod = CPlayerDB::GetGold(&v7->m_Param);
  v6.tradelist[0].pNewData = &v7->m_pUserDB->m_AvatorData;
  v6.tradelist[0].pOldData = &v7->m_pUserDB->m_AvatorData_bk;
  v6.tradelist[1].dwSerial = v8->m_pUserDB->m_dwSerial;
  v6.tradelist[1].dwDalant = CPlayerDB::GetDalant(&v8->m_Param);
  v6.tradelist[1].dwGlod = CPlayerDB::GetGold(&v8->m_Param);
  v6.tradelist[1].pNewData = &v8->m_pUserDB->m_AvatorData;
  v6.tradelist[1].pOldData = &v8->m_pUserDB->m_AvatorData_bk;
  v4 = _qry_case_update_data_for_trade::size(&v6);
  CMainThread::PushDQSData(&g_Main, 0xFFFFFFFF, 0i64, -80, (char *)&v6, v4);
}
