/*
 * Function: ?pc_PostReturnConfirmRequest@CPlayer@@QEAAXK@Z
 * Address: 0x1400C9450
 */

void __fastcall CPlayer::pc_PostReturnConfirmRequest(CPlayer *this, unsigned int dwPostSerial)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v4; // eax@12
  __int64 v5; // [sp+0h] [bp-A8h]@1
  bool bAdd[8]; // [sp+20h] [bp-88h]@22
  unsigned int dwGold; // [sp+28h] [bp-80h]@22
  char *pFileName; // [sp+30h] [bp-78h]@22
  CPostData *v9; // [sp+40h] [bp-68h]@5
  _STORAGE_LIST::_db_con *pItem; // [sp+48h] [bp-60h]@9
  _STORAGE_LIST::_db_con v11; // [sp+58h] [bp-50h]@15
  CPlayer *v12; // [sp+B0h] [bp+8h]@1
  unsigned int dwPostSeriala; // [sp+B8h] [bp+10h]@1

  dwPostSeriala = dwPostSerial;
  v12 = this;
  v2 = &v5;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( dwPostSerial )
  {
    v9 = CPostReturnStorage::GetPostDataFromSerial(&v12->m_Param.m_ReturnPostStorage, dwPostSerial);
    if ( !v9 )
    {
      CPlayer::SendMsg_PostReturnConfirm(v12, 11, dwPostSeriala);
      return;
    }
    if ( !CPlayer::IsReturnPostUpdate(v12) )
    {
      CPlayer::SendMsg_PostReturnConfirm(v12, 8, dwPostSeriala);
      return;
    }
    pItem = 0i64;
    if ( _INVENKEY::IsFilled(&v9->m_Key)
      && _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v12->m_Param.m_dbInven.m_nListNum) == 255 )
    {
      CPlayer::SendMsg_PostReturnConfirm(v12, 14, dwPostSeriala);
      return;
    }
    v4 = CPlayer::GetMoney(v12, 1);
    if ( v9->m_dwGold + v4 + 5 > 0x7A120 )
    {
      CPlayer::SendMsg_PostReturnConfirm(v12, 15, dwPostSeriala);
      return;
    }
    if ( _INVENKEY::IsFilled(&v9->m_Key) )
    {
      _STORAGE_LIST::_db_con::_db_con(&v11);
      v11.m_byTableCode = v9->m_Key.byTableCode;
      v11.m_wItemIndex = v9->m_Key.wItemIndex;
      v11.m_dwDur = v9->m_dwDur;
      v11.m_dwLv = v9->m_dwUpt;
      v11.m_lnUID = v9->m_lnUID;
      v11.m_wSerial = CPlayerDB::GetNewItemSerial(&v12->m_Param);
      pItem = CPlayer::Emb_AddStorage(v12, 0, (_STORAGE_LIST::_storage_con *)&v11.m_bLoad, 0, 1);
      if ( !pItem )
      {
        CPlayer::SendMsg_PostReturnConfirm(v12, 8, dwPostSeriala);
        return;
      }
      CPlayer::SendMsg_RewardAddItem(v12, pItem, 5);
    }
    if ( v9->m_dwGold )
    {
      CPlayer::AddGold(v12, v9->m_dwGold, 1);
      CPlayer::SendMsg_AlterMoneyInform(v12, 0);
    }
    if ( pItem || v9->m_dwGold )
    {
      pFileName = v12->m_szItemHistoryFileName;
      dwGold = v9->m_dwGold;
      *(_QWORD *)bAdd = v9->m_dwDur;
      CMgrAvatorItemHistory::post_return(
        &CPlayer::s_MgrItemHistory,
        v9->m_wszRecvName,
        v9->m_dwPSSerial,
        pItem,
        *(unsigned __int64 *)bAdd,
        dwGold,
        v12->m_szItemHistoryFileName);
    }
    _INVENKEY::SetRelease(&v9->m_Key);
    v9->m_dwDur = 0i64;
    v9->m_dwUpt = 0xFFFFFFF;
    v9->m_lnUID = 0i64;
    v9->m_dwGold = 0;
    CPlayer::AddGold(v12, 5u, 1);
    CPlayer::SendMsg_AlterMoneyInform(v12, 0);
    CPlayer::UpdateReturnPost(v12, dwPostSeriala);
    CPostReturnStorage::DelPostData(&v12->m_Param.m_ReturnPostStorage, dwPostSeriala);
    CPlayer::SendMsg_PostReturnConfirm(v12, 0, dwPostSeriala);
  }
}
