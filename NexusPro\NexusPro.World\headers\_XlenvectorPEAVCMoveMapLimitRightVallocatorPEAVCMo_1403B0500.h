#pragma once
#ifndef _XLENVECTORPEAVCMOVEMAPLIMITRIGHTVALLOCATORPEAVCMO_1403B0500_H
#define _XLENVECTORPEAVCMOVEMAPLIMITRIGHTVALLOCATORPEAVCMO_1403B0500_H

// Auto-generated header for _XlenvectorPEAVCMoveMapLimitRightVallocatorPEAVCMo_1403B0500.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

#endif // _XLENVECTORPEAVCMOVEMAPLIMITRIGHTVALLOCATORPEAVCMO_1403B0500_H
