/*
 * Function: ?skill_process_for_aura@CPlayer@@QEAAXH@Z
 * Address: 0x14009C450
 */

void __fastcall CPlayer::skill_process_for_aura(CPlayer *this, int nSkillIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v4; // rax@13
  __int64 v5; // [sp+0h] [bp-1C8h]@1
  float *pTar; // [sp+20h] [bp-1A8h]@7
  char *psActableDst; // [sp+28h] [bp-1A0h]@7
  CCharacter **ppDsts; // [sp+30h] [bp-198h]@7
  bool pbUpMty; // [sp+44h] [bp-184h]@16
  char pbyErrorCode; // [sp+64h] [bp-164h]@16
  CCharacter *pDstChar; // [sp+90h] [bp-138h]@7
  _base_fld *v12; // [sp+188h] [bp-40h]@4
  _skill_fld *pSkillFld; // [sp+190h] [bp-38h]@6
  int v14; // [sp+198h] [bp-30h]@7
  int j; // [sp+19Ch] [bp-2Ch]@7
  __int64 v16; // [sp+1A0h] [bp-28h]@13
  char v17; // [sp+1A8h] [bp-20h]@13
  bool v18; // [sp+1A9h] [bp-1Fh]@7
  unsigned int dwSerial; // [sp+1ACh] [bp-1Ch]@13
  int v20; // [sp+1B0h] [bp-18h]@13
  CPlayer *pOriDst; // [sp+1D0h] [bp+8h]@1

  pOriDst = this;
  v2 = &v5;
  for ( i = 112i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  v12 = CRecordData::GetRecord(&stru_1799C8410, nSkillIndex);
  if ( v12 )
  {
    if ( v12[1].m_dwIndex == 4 )
    {
      pSkillFld = (_skill_fld *)CRecordData::GetRecord(&stru_1799C8410, v12[12].m_strCode);
      if ( pSkillFld )
      {
        v18 = pSkillFld->m_nContEffectType == 1;
        ppDsts = &pDstChar;
        psActableDst = pSkillFld->m_strActableDst;
        pTar = pOriDst->m_fCurPos;
        v14 = CCharacter::_GetAreaEffectMember(
                (CCharacter *)&pOriDst->vfptr,
                (CCharacter *)&pOriDst->vfptr,
                v18,
                v12[12].m_dwIndex,
                pOriDst->m_fCurPos,
                pSkillFld->m_strActableDst,
                &pDstChar);
        for ( j = 0; j < v14; ++j )
        {
          if ( !(*(&pDstChar + j))->m_ObjID.m_byKind && !(*(&pDstChar + j))->m_ObjID.m_byID )
          {
            v16 = (__int64)*(&pDstChar + j);
            dwSerial = CPlayerDB::GetCharSerial((CPlayerDB *)(v16 + 1952));
            v20 = CPlayerDB::GetRaceCode((CPlayerDB *)(v16 + 1952));
            v4 = CPvpUserAndGuildRankingSystem::Instance();
            v17 = CPvpUserAndGuildRankingSystem::GetBossType(v4, v20, dwSerial);
            if ( v17 != 4 && v17 != 8 )
            {
              ppDsts = (CCharacter **)&pbUpMty;
              psActableDst = &pbyErrorCode;
              LODWORD(pTar) = 7;
              CCharacter::AssistSkill(
                (CCharacter *)&pOriDst->vfptr,
                *(&pDstChar + j),
                0,
                pSkillFld,
                7,
                &pbyErrorCode,
                &pbUpMty);
            }
          }
        }
      }
    }
  }
}
