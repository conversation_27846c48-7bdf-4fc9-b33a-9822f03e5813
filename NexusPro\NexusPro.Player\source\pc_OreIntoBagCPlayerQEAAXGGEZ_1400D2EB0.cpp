/*
 * Function: ?pc_OreIntoBag@CPlayer@@QEAAXGGE@Z
 * Address: 0x1400D2EB0
 */

void __fastcall CPlayer::pc_OreIntoBag(CPlayer *this, unsigned __int16 wResIndex, unsigned __int16 wSerial, char byAddAmount)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-C8h]@1
  bool bUpdate[4]; // [sp+20h] [bp-A8h]@22
  bool bSend; // [sp+28h] [bp-A0h]@22
  char v9; // [sp+30h] [bp-98h]@4
  _STORAGE_LIST::_db_con *v10; // [sp+38h] [bp-90h]@4
  unsigned __int16 v11; // [sp+40h] [bp-88h]@4
  int v12; // [sp+44h] [bp-84h]@17
  _TimeItem_fld *v13; // [sp+48h] [bp-80h]@19
  _STORAGE_LIST::_db_con v14; // [sp+58h] [bp-70h]@19
  __time32_t Time; // [sp+A4h] [bp-24h]@25
  _base_fld *v16; // [sp+B8h] [bp-10h]@31
  CPlayer *v17; // [sp+D0h] [bp+8h]@1
  unsigned __int16 v18; // [sp+D8h] [bp+10h]@1
  unsigned __int16 v19; // [sp+E0h] [bp+18h]@1
  char v20; // [sp+E8h] [bp+20h]@1

  v20 = byAddAmount;
  v19 = wSerial;
  v18 = wResIndex;
  v17 = this;
  v4 = &v6;
  for ( i = 48i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v9 = 0;
  v10 = 0i64;
  v11 = wSerial;
  if ( (unsigned __int8)byAddAmount <= (signed int)v17->m_Param.m_wCuttingResBuffer[wResIndex] )
  {
    if ( wSerial != 0xFFFF
      || _STORAGE_LIST::GetIndexEmptyCon((_STORAGE_LIST *)&v17->m_Param.m_dbInven.m_nListNum) != 255 )
    {
      if ( v19 != 0xFFFF )
      {
        v10 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v17->m_Param.m_dbInven.m_nListNum, v19);
        if ( v10 )
        {
          if ( v10->m_byTableCode == 18 && v10->m_wItemIndex == v18 )
          {
            if ( v10->m_bLock )
            {
              v9 = 11;
            }
            else
            {
              v12 = v10->m_dwDur;
              if ( (unsigned __int8)v20 + v12 > 99 )
                v9 = 6;
            }
          }
          else
          {
            v9 = 7;
          }
        }
        else
        {
          v9 = 5;
        }
      }
    }
    else
    {
      v9 = 4;
    }
  }
  else
  {
    v9 = 2;
  }
  v13 = TimeItem::FindTimeRec(18, v18);
  _STORAGE_LIST::_db_con::_db_con(&v14);
  if ( !v9 )
  {
    v17->m_Param.m_wCuttingResBuffer[v18] -= (unsigned __int8)v20;
    if ( !v10 || v13 )
    {
      v14.m_byTableCode = 18;
      v14.m_wItemIndex = v18;
      v14.m_dwDur = (unsigned __int8)v20;
      v14.m_dwLv = 0xFFFFFFF;
      v14.m_wSerial = CPlayerDB::GetNewItemSerial(&v17->m_Param);
      if ( v13 && v13->m_nCheckType )
      {
        _time32(&Time);
        v14.m_byCsMethod = v13->m_nCheckType;
        v14.m_dwT = v13->m_nUseTime + Time;
        v14.m_dwLendRegdTime = Time;
      }
      if ( !CPlayer::Emb_AddStorage(v17, 0, (_STORAGE_LIST::_storage_con *)&v14.m_bLoad, 0, 0) )
      {
        CPlayer::SendMsg_OreIntoBagResult(v17, -1, v11, 0, 0);
        return;
      }
      v11 = v14.m_wSerial;
    }
    else
    {
      bSend = 0;
      bUpdate[0] = 0;
      CPlayer::Emb_AlterDurPoint(v17, 0, v10->m_byStorageIndex, (unsigned __int8)v20, 0, 0);
    }
    if ( v17->m_pUserDB )
      CUserDB::Update_CuttingTrans(v17->m_pUserDB, v18, v17->m_Param.m_wCuttingResBuffer[v18]);
    v16 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 18, v18);
    CPlayer::Emb_CheckActForQuest(v17, 13, v16->m_strCode, (unsigned __int8)v20, 0);
  }
  CPlayer::SendMsg_OreIntoBagResult(v17, v9, v11, v14.m_byCsMethod, v14.m_dwT);
}
