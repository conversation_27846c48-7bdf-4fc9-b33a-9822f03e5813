#pragma once
#ifndef _CMOVEMAPLIMITRIGHTINFOCREATECOMPLETE__1_DTOR2_1403AD310_H
#define _CMOVEMAPLIMITRIGHTINFOCREATECOMPLETE__1_DTOR2_1403AD310_H

// Auto-generated header for _CMoveMapLimitRightInfoCreateComplete__1_dtor2_1403AD310.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_2(__int64 a1, __int64 a2)
;

#endif // _CMOVEMAPLIMITRIGHTINFOCREATECOMPLETE__1_DTOR2_1403AD310_H
