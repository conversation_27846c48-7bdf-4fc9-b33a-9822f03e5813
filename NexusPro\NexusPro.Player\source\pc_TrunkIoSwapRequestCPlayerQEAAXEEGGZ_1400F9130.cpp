/*
 * Function: ?pc_TrunkIoSwapRequest@CPlayer@@QEAAXEEGG@Z
 * Address: 0x1400F9130
 */

void __fastcall CPlayer::pc_TrunkIoSwapRequest(CPlayer *this, char byStartStorageIndex, char byTarStorageIndex, unsigned __int16 wStartItemSerial, unsigned __int16 wTarItemSerial)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char v7; // al@39
  int v8; // ecx@39
  char v9; // al@56
  int v10; // ecx@56
  int v11; // eax@71
  int v12; // ecx@71
  int v13; // eax@73
  unsigned int v14; // eax@74
  int v15; // eax@77
  int v16; // eax@80
  CMoneySupplyMgr *v17; // rax@107
  unsigned int v18; // eax@111
  __int64 v19; // [sp+0h] [bp-178h]@1
  bool bDelete[4]; // [sp+20h] [bp-158h]@83
  char *strErrorCodePos; // [sp+28h] [bp-150h]@83
  char v22; // [sp+40h] [bp-138h]@4
  unsigned int dwConsumDanlant; // [sp+44h] [bp-134h]@4
  _STORAGE_LIST::_storage_con *pCon; // [sp+48h] [bp-130h]@4
  _STORAGE_LIST::_storage_con *v25; // [sp+50h] [bp-128h]@4
  _STORAGE_LIST::_storage_con *v26; // [sp+58h] [bp-120h]@4
  unsigned int v27; // [sp+60h] [bp-118h]@73
  unsigned __int64 v28; // [sp+68h] [bp-110h]@73
  char v29; // [sp+70h] [bp-108h]@77
  char v30; // [sp+71h] [bp-107h]@80
  char v31; // [sp+72h] [bp-106h]@83
  char v32; // [sp+73h] [bp-105h]@83
  _STORAGE_LIST::_db_con Dst; // [sp+88h] [bp-F0h]@83
  _STORAGE_LIST::_db_con v34; // [sp+D8h] [bp-A0h]@83
  unsigned int dwLeftDalant; // [sp+114h] [bp-64h]@84
  _STORAGE_LIST::_db_con *v36; // [sp+118h] [bp-60h]@85
  unsigned int v37; // [sp+120h] [bp-58h]@86
  unsigned int v38; // [sp+124h] [bp-54h]@94
  _STORAGE_LIST::_db_con *v39; // [sp+128h] [bp-50h]@95
  unsigned int v40; // [sp+130h] [bp-48h]@96
  int v41; // [sp+134h] [bp-44h]@103
  _STORAGE_LIST::_db_con *pInputItem; // [sp+138h] [bp-40h]@108
  _STORAGE_LIST::_db_con *pOutputItem; // [sp+140h] [bp-38h]@108
  unsigned int v44; // [sp+148h] [bp-30h]@112
  int nTableCode; // [sp+14Ch] [bp-2Ch]@39
  int v46; // [sp+150h] [bp-28h]@56
  int v47; // [sp+154h] [bp-24h]@71
  int v48; // [sp+158h] [bp-20h]@77
  int v49; // [sp+15Ch] [bp-1Ch]@80
  int nLv; // [sp+160h] [bp-18h]@107
  int v51; // [sp+164h] [bp-14h]@107
  char *v52; // [sp+168h] [bp-10h]@111
  CPlayer *p; // [sp+180h] [bp+8h]@1
  char v54; // [sp+188h] [bp+10h]@1
  char v55; // [sp+190h] [bp+18h]@1
  unsigned __int16 v56; // [sp+198h] [bp+20h]@1

  v56 = wStartItemSerial;
  v55 = byTarStorageIndex;
  v54 = byStartStorageIndex;
  p = this;
  v5 = &v19;
  for ( i = 92i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v22 = 0;
  dwConsumDanlant = 0;
  pCon = 0i64;
  v25 = 0i64;
  v26 = 0i64;
  if ( !IsBeNearStore(p, 10) )
  {
    v22 = 13;
    goto $RESULT_86;
  }
  if ( !p->m_Param.m_bTrunkOpen )
  {
    v22 = 14;
    goto $RESULT_86;
  }
  if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) <= 0 )
  {
    v22 = 2;
    goto $RESULT_86;
  }
  pCon = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                          p->m_Param.m_pStoragePtr[(unsigned __int8)v54],
                                          v56);
  if ( !pCon )
  {
    v22 = 9;
    goto $RESULT_86;
  }
  if ( pCon->m_bLock )
  {
    v22 = 10;
    goto $RESULT_86;
  }
  v25 = (_STORAGE_LIST::_storage_con *)_STORAGE_LIST::GetPtrFromSerial(
                                         p->m_Param.m_pStoragePtr[(unsigned __int8)v55],
                                         wTarItemSerial);
  if ( !v25 )
  {
    v22 = 9;
    goto $RESULT_86;
  }
  if ( v25->m_bLock )
  {
    v22 = 10;
    goto $RESULT_86;
  }
  if ( (v55 == 5 || v55 == 7) && !IsTrunkIOAble(pCon->m_byTableCode, pCon->m_wItemIndex) )
  {
    v22 = 18;
    goto $RESULT_86;
  }
  if ( (v54 == 5 || v54 == 7) && !IsTrunkIOAble(v25->m_byTableCode, v25->m_wItemIndex) )
  {
    v22 = 18;
    goto $RESULT_86;
  }
  if ( v55 == 1 )
  {
    if ( pCon->m_byTableCode >= 8 )
    {
      v22 = 44;
      goto $RESULT_86;
    }
  }
  else if ( v55 == 2 && pCon->m_byTableCode != 8 && pCon->m_byTableCode != 9 && pCon->m_byTableCode != 10 )
  {
    v22 = 44;
    goto $RESULT_86;
  }
  if ( v55 == 1 || v55 == 2 )
  {
    if ( pCon->m_byTableCode != v25->m_byTableCode )
    {
      v22 = 44;
      goto $RESULT_86;
    }
    v7 = CPlayerDB::GetRaceSexCode(&p->m_Param);
    v8 = pCon->m_wItemIndex;
    nTableCode = pCon->m_byTableCode;
    if ( !IsItemEquipCivil(nTableCode, v8, v7) )
    {
      v22 = 44;
      goto $RESULT_86;
    }
    if ( !CPlayer::IsEffectableEquip(p, pCon) )
    {
      v22 = 44;
      goto $RESULT_86;
    }
  }
  if ( v54 == 1 )
  {
    if ( v25->m_byTableCode >= 8 )
    {
      v22 = 44;
      goto $RESULT_86;
    }
  }
  else if ( v54 == 2 && v25->m_byTableCode != 8 && v25->m_byTableCode != 9 && v25->m_byTableCode != 10 )
  {
    v22 = 44;
    goto $RESULT_86;
  }
  if ( v54 != 1 && v54 != 2 )
    goto LABEL_115;
  if ( v25->m_byTableCode != pCon->m_byTableCode )
  {
    v22 = 44;
    goto $RESULT_86;
  }
  v9 = CPlayerDB::GetRaceSexCode(&p->m_Param);
  v10 = v25->m_wItemIndex;
  v46 = v25->m_byTableCode;
  if ( !IsItemEquipCivil(v46, v10, v9) )
  {
    v22 = 44;
    goto $RESULT_86;
  }
  if ( CPlayer::IsEffectableEquip(p, v25) )
  {
LABEL_115:
    if ( v54 == 5 && v55 != 7 || v54 == 7 && v55 != 5 )
      v26 = pCon;
    if ( v55 == 5 && v54 != 7 || v55 == 7 && v54 != 5 )
      v26 = v25;
    if ( v26 )
    {
      v11 = CPlayerDB::GetRaceCode(&p->m_Param);
      v12 = v26->m_wItemIndex;
      v47 = v26->m_byTableCode;
      dwConsumDanlant = GetItemStoragePrice(v47, v12, v11);
      if ( IsOverLapItem(v26->m_byTableCode) )
        dwConsumDanlant *= LODWORD(v26->m_dwDur);
      v13 = CPlayerDB::GetRaceCode(&p->m_Param);
      v27 = eGetTexRate(v13) + 10000;
      v28 = v27 * (unsigned __int64)dwConsumDanlant;
      dwConsumDanlant = v28 / 0x2710;
    }
    v14 = CPlayerDB::GetDalant(&p->m_Param);
    if ( v14 >= dwConsumDanlant )
    {
      if ( v54 != 5
        || (v29 = CPlayerDB::GetTrunkSlotRace(&p->m_Param, v56),
            v48 = (unsigned __int8)v29,
            v15 = CPlayerDB::GetRaceCode(&p->m_Param),
            v48 == v15) )
      {
        if ( v54 == 7 )
        {
          v30 = CPlayerDB::GetExtTrunkSlotRace(&p->m_Param, v56);
          v49 = (unsigned __int8)v30;
          v16 = CPlayerDB::GetRaceCode(&p->m_Param);
          if ( v49 != v16 )
            v22 = 17;
        }
      }
      else
      {
        v22 = 17;
      }
    }
    else
    {
      v22 = 6;
    }
  }
  else
  {
    v22 = 44;
  }
$RESULT_86:
  if ( !v22 )
  {
    v31 = pCon->m_byClientIndex;
    v32 = v25->m_byClientIndex;
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    _STORAGE_LIST::_db_con::_db_con(&v34);
    memcpy_0(&Dst, pCon, 0x32ui64);
    memcpy_0(&v34, v25, 0x32ui64);
    strErrorCodePos = "CPlayer::pc_TrunkIoSwapRequest() -- 0";
    bDelete[0] = 0;
    if ( !CPlayer::Emb_DelStorage(p, v54, BYTE3(pCon[1].m_dwDur), 0, 0, "CPlayer::pc_TrunkIoSwapRequest() -- 0") )
    {
      dwLeftDalant = CPlayerDB::GetDalant(&p->m_Param);
      CPlayer::SendMsg_TrunkIoResult(p, 1, 18, dwLeftDalant, dwConsumDanlant);
      return;
    }
    v36 = CPlayer::Emb_AddStorage(p, v54, (_STORAGE_LIST::_storage_con *)&v34.m_bLoad, 0, 0);
    if ( !v36 )
    {
      CPlayer::Emb_AddStorage(p, v54, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 0);
      v37 = CPlayerDB::GetDalant(&p->m_Param);
      CPlayer::SendMsg_TrunkIoResult(p, 1, 18, v37, dwConsumDanlant);
      return;
    }
    v36->m_byClientIndex = v31;
    if ( v54 == 5 && v36 )
      CUserDB::Update_ItemSlot(p->m_pUserDB, 5, v36->m_byStorageIndex, v31);
    if ( v54 == 7 && v36 )
      CUserDB::Update_ItemSlot(p->m_pUserDB, 7, v36->m_byStorageIndex, v31);
    strErrorCodePos = "CPlayer::pc_TrunkIoSwapRequest() -- 1";
    bDelete[0] = 0;
    if ( !CPlayer::Emb_DelStorage(p, v55, BYTE3(v25[1].m_dwDur), 0, 0, "CPlayer::pc_TrunkIoSwapRequest() -- 1") )
    {
      strErrorCodePos = "CPlayer::pc_TrunkIoSwapRequest() -- 1 Fail";
      bDelete[0] = 0;
      CPlayer::Emb_DelStorage(p, v54, BYTE3(v25[1].m_dwDur), 0, 0, "CPlayer::pc_TrunkIoSwapRequest() -- 1 Fail");
      CPlayer::Emb_AddStorage(p, v54, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 0);
      v38 = CPlayerDB::GetDalant(&p->m_Param);
      CPlayer::SendMsg_TrunkIoResult(p, 1, 18, v38, dwConsumDanlant);
      return;
    }
    v39 = CPlayer::Emb_AddStorage(p, v55, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 0);
    if ( !v39 )
    {
      CPlayer::Emb_AddStorage(p, v55, (_STORAGE_LIST::_storage_con *)&v34.m_bLoad, 0, 0);
      strErrorCodePos = "CPlayer::pc_TrunkIoSwapRequest() -- 2 Fail";
      bDelete[0] = 0;
      CPlayer::Emb_DelStorage(p, v54, BYTE3(v25[1].m_dwDur), 0, 0, "CPlayer::pc_TrunkIoSwapRequest() -- 2 Fail");
      CPlayer::Emb_AddStorage(p, v54, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 0);
      v22 = 18;
      v40 = CPlayerDB::GetDalant(&p->m_Param);
      CPlayer::SendMsg_TrunkIoResult(p, 1, v22, v40, dwConsumDanlant);
      return;
    }
    v39->m_byClientIndex = v32;
    if ( v55 == 5 && v39 )
      CUserDB::Update_ItemSlot(p->m_pUserDB, 5, v39->m_byStorageIndex, v32);
    if ( v55 == 7 && v39 )
      CUserDB::Update_ItemSlot(p->m_pUserDB, 7, v39->m_byStorageIndex, v32);
    CPlayer::SubDalant(p, dwConsumDanlant);
    v41 = CPlayerDB::GetLevel(&p->m_Param);
    if ( v41 == 30 || v41 == 40 || v41 == 50 || v41 == 60 )
    {
      nLv = CPlayerDB::GetLevel(&p->m_Param);
      v51 = CPlayerDB::GetRaceCode(&p->m_Param);
      v17 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateFeeMoneyData(v17, v51, nLv, dwConsumDanlant);
    }
    pInputItem = &Dst;
    pOutputItem = &v34;
    if ( Dst.m_byStorageIndex == 5 || Dst.m_byStorageIndex == 7 )
    {
      pInputItem = &v34;
      pOutputItem = &Dst;
    }
    v52 = p->m_szItemHistoryFileName;
    v18 = CPlayerDB::GetDalant(&p->m_Param);
    CMgrAvatorItemHistory::trunk_swap_item(
      &CPlayer::s_MgrItemHistory,
      p->m_ObjID.m_wIndex,
      pInputItem,
      pOutputItem,
      dwConsumDanlant,
      v18,
      v52);
  }
  v44 = CPlayerDB::GetDalant(&p->m_Param);
  CPlayer::SendMsg_TrunkIoResult(p, 1, v22, v44, dwConsumDanlant);
}
