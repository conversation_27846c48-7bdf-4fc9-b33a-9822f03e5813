/*
 * Function: ?pc_TrunkEstRequest@CPlayer@@QEAAXPEADE0@Z
 * Address: 0x1400F7D00
 */

void __fastcall CPlayer::pc_TrunkEstRequest(CPlayer *this, char *pwszPassword, char byHintIndex, char *pwszHintAnswer)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@13
  char *v7; // rax@19
  char v8; // al@21
  char *v9; // rax@21
  CMoneySupplyMgr *v10; // rax@25
  char v11; // al@26
  unsigned int v12; // eax@26
  __int64 v13; // [sp+0h] [bp-118h]@1
  unsigned int dwPayGold[2]; // [sp+20h] [bp-F8h]@19
  unsigned int dwNewDalant[2]; // [sp+28h] [bp-F0h]@19
  char v16; // [sp+40h] [bp-D8h]@4
  unsigned int dwSub; // [sp+44h] [bp-D4h]@4
  int v18; // [sp+48h] [bp-D0h]@21
  char Dest; // [sp+60h] [bp-B8h]@26
  unsigned int dwLeftDalant; // [sp+E4h] [bp-34h]@27
  int nLv; // [sp+F0h] [bp-28h]@25
  int v22; // [sp+F4h] [bp-24h]@25
  char *v23; // [sp+F8h] [bp-20h]@26
  unsigned int v24; // [sp+100h] [bp-18h]@26
  unsigned __int64 v25; // [sp+108h] [bp-10h]@4
  CPlayer *p; // [sp+120h] [bp+8h]@1
  const char *Str; // [sp+128h] [bp+10h]@1
  char v28; // [sp+130h] [bp+18h]@1
  const char *wszStr; // [sp+138h] [bp+20h]@1

  wszStr = pwszHintAnswer;
  v28 = byHintIndex;
  Str = pwszPassword;
  p = this;
  v4 = &v13;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v25 = (unsigned __int64)&v13 ^ _security_cookie;
  v16 = 0;
  dwSub = 100000;
  if ( p->m_pUserDB->m_bCreateTrunkFree )
    dwSub = 0;
  if ( IsBeNearStore(p, 10) )
  {
    if ( strlen_0(Str) >= 2 && strlen_0(Str) <= 0xC )
    {
      if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) <= 0 )
      {
        v6 = CPlayerDB::GetDalant(&p->m_Param);
        if ( v6 >= dwSub )
        {
          if ( !strcmp_0(Str, p->m_pUserDB->m_szUILock_PW) )
          {
            v16 = 24;
          }
          else if ( !IsSQLValidString(Str) || !IsSQLValidString(wszStr) )
          {
            v7 = CPlayerDB::GetCharNameA(&p->m_Param);
            *(_QWORD *)dwNewDalant = wszStr;
            *(_QWORD *)dwPayGold = Str;
            CLogFile::Write(
              &stru_1799C8E78,
              "CPlayer::pc_TrunkEstRequest() : %u(%s) !::IsSQLValidString( pwszPassword(%s) ) || !::IsSQLValidString(pwsz"
              "HintAnswer(%s) Invalid!",
              p->m_dwObjSerial,
              v7);
            v16 = 25;
          }
        }
        else
        {
          v16 = 6;
        }
      }
      else
      {
        v16 = 5;
      }
    }
    else
    {
      v16 = 3;
    }
  }
  else
  {
    v16 = 13;
  }
  if ( !v16 )
  {
    CPlayer::SubDalant(p, dwSub);
    p->m_Param.m_byTrunkSlotNum = 20;
    strcpy_0(p->m_Param.m_wszTrunkPasswd, Str);
    p->m_Param.m_byTrunkHintIndex = v28;
    strcpy_0(p->m_Param.m_wszTrunkHintAnswer, wszStr);
    *(_QWORD *)&p->m_Param.m_dTrunkDalant = 0i64;
    *(_QWORD *)&p->m_Param.m_dTrunkGold = 0i64;
    p->m_Param.m_bTrunkOpen = 1;
    _STORAGE_LIST::SetUseListNum((_STORAGE_LIST *)&p->m_Param.m_dbTrunk.m_nListNum, 20);
    v8 = CPlayerDB::GetTrunkSlotNum(&p->m_Param);
    CUserDB::Update_TrunkSlotNum(p->m_pUserDB, v8);
    v9 = CPlayerDB::GetTrunkPasswdW(&p->m_Param);
    CUserDB::Update_TrunkPassword(p->m_pUserDB, v9);
    CUserDB::Update_TrunkMoney(p->m_pUserDB, p->m_Param.m_dTrunkDalant, p->m_Param.m_dTrunkGold);
    CUserDB::Update_TrunkHint(p->m_pUserDB, p->m_Param.m_byTrunkHintIndex, p->m_Param.m_wszTrunkHintAnswer);
    v18 = CPlayerDB::GetLevel(&p->m_Param);
    if ( v18 == 30 || v18 == 40 || v18 == 50 || v18 == 60 )
    {
      nLv = CPlayerDB::GetLevel(&p->m_Param);
      v22 = CPlayerDB::GetRaceCode(&p->m_Param);
      v10 = CMoneySupplyMgr::Instance();
      CMoneySupplyMgr::UpdateFeeMoneyData(v10, v22, nLv, dwSub);
    }
    v11 = CPlayerDB::GetTrunkSlotNum(&p->m_Param);
    sprintf(&Dest, "EST TRUNK(%d)", (unsigned __int8)v11);
    v23 = p->m_szItemHistoryFileName;
    v24 = CPlayerDB::GetGold(&p->m_Param);
    v12 = CPlayerDB::GetDalant(&p->m_Param);
    CMgrAvatorItemHistory::pay_money(&CPlayer::s_MgrItemHistory, p->m_ObjID.m_wIndex, &Dest, dwSub, 0, v12, v24, v23);
  }
  dwLeftDalant = CPlayerDB::GetDalant(&p->m_Param);
  CPlayer::SendMsg_TrunkEstResult(p, v16, dwLeftDalant);
  if ( !v16 )
    CPlayer::SendMsg_TrunkDownloadResult(p, 0);
}
