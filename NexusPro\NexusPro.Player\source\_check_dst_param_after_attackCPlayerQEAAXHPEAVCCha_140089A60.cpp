/*
 * Function: ?_check_dst_param_after_attack@CPlayer@@QEAAXHPEAVCCharacter@@@Z
 * Address: 0x140089A60
 */

void __usercall CPlayer::_check_dst_param_after_attack(CPlayer *this@<rcx>, int nTotalDam@<edx>, <PERSON>haracter *pTarget@<r8>, float a4@<xmm0>)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  float v6; // xmm0_4@5
  int v7; // eax@5
  int v8; // eax@5
  float v9; // xmm0_4@9
  float v10; // xmm0_4@11
  int v11; // eax@11
  int v12; // eax@11
  __int64 v13; // rdx@16
  __int64 v14; // rdx@24
  __int64 v15; // [sp+0h] [bp-88h]@1
  int v16; // [sp+20h] [bp-68h]@5
  int nFP; // [sp+24h] [bp-64h]@5
  int v18; // [sp+28h] [bp-60h]@9
  int v19; // [sp+2Ch] [bp-5Ch]@9
  int v20; // [sp+30h] [bp-58h]@11
  int nSP; // [sp+34h] [bp-54h]@11
  CCharacter *v22; // [sp+38h] [bp-50h]@18
  unsigned __int16 v23; // [sp+40h] [bp-48h]@23
  CCharacter *v24; // [sp+48h] [bp-40h]@26
  float v25; // [sp+50h] [bp-38h]@5
  float v26; // [sp+54h] [bp-34h]@5
  float v27; // [sp+58h] [bp-30h]@9
  float v28; // [sp+5Ch] [bp-2Ch]@9
  CGameObjectVtbl *v29; // [sp+60h] [bp-28h]@9
  float v30; // [sp+68h] [bp-20h]@11
  float v31; // [sp+6Ch] [bp-1Ch]@11
  int v32; // [sp+70h] [bp-18h]@23
  CPlayer *v33; // [sp+90h] [bp+8h]@1
  int v34; // [sp+98h] [bp+10h]@1
  CCharacter *v35; // [sp+A0h] [bp+18h]@1

  v35 = pTarget;
  v34 = nTotalDam;
  v33 = this;
  v4 = &v15;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  _effect_parameter::GetEff_Rate(&v33->m_EP, 13);
  if ( a4 > 1.0 )
  {
    v25 = (float)v34;
    _effect_parameter::GetEff_Rate(&v33->m_EP, 13);
    v6 = v25 * (float)((float)v34 - 1.0);
    v26 = v25 * (float)((float)v34 - 1.0);
    _effect_parameter::GetEff_Rate(&v33->m_EP, 19);
    a4 = v26 * v6;
    v16 = (signed int)ffloor(a4);
    v7 = CPlayerDB::GetFP(&v33->m_Param);
    nFP = v16 + v7;
    v8 = CPlayer::GetMaxFP(v33);
    if ( nFP > v8 )
      nFP = CPlayer::GetMaxFP(v33);
    CPlayer::SetFP(v33, nFP, 0);
  }
  _effect_parameter::GetEff_Rate(&v33->m_EP, 12);
  if ( a4 > 1.0 )
  {
    v27 = (float)v34;
    _effect_parameter::GetEff_Rate(&v33->m_EP, 12);
    v9 = v27 * (float)((float)v34 - 1.0);
    v28 = v27 * (float)((float)v34 - 1.0);
    _effect_parameter::GetEff_Rate(&v33->m_EP, 18);
    a4 = v28 * v9;
    v18 = (signed int)ffloor(a4);
    v19 = CPlayerDB::GetHP(&v33->m_Param);
    v29 = v33->vfptr;
    ((void (__fastcall *)(CPlayer *, _QWORD, _QWORD))v29->SetHP)(v33, (unsigned int)(v18 + v19), 0i64);
  }
  _effect_parameter::GetEff_Rate(&v33->m_EP, 40);
  if ( a4 > 1.0 )
  {
    v30 = (float)v34;
    _effect_parameter::GetEff_Rate(&v33->m_EP, 40);
    v10 = v30 * (float)((float)v34 - 1.0);
    v31 = v30 * (float)((float)v34 - 1.0);
    _effect_parameter::GetEff_Rate(&v33->m_EP, 20);
    a4 = v31 * v10;
    v20 = (signed int)ffloor(a4);
    v11 = CPlayerDB::GetSP(&v33->m_Param);
    nSP = v20 + v11;
    v12 = CPlayer::GetMaxSP(v33);
    if ( nSP > v12 )
      nSP = CPlayer::GetMaxSP(v33);
    CPlayer::SetSP(v33, nSP, 0);
  }
  if ( v35 )
  {
    if ( _effect_parameter::GetEff_State(&v33->m_EP, 13) )
    {
      if ( ((int (__fastcall *)(CCharacter *))v35->vfptr->GetHP)(v35) > 0 )
      {
        if ( v35->m_ObjID.m_byID || (v22 = v35, !BYTE2(v35[1].m_fCurPos[2])) || !LOBYTE(v22[1].m_fCurPos[2]) )
        {
          LOBYTE(v13) = 1;
          (*(void (__fastcall **)(CCharacter *, __int64))&v35->vfptr->gap8[0])(v35, v13);
          (*(void (__fastcall **)(CCharacter *))&v35->vfptr->gap8[64])(v35);
        }
      }
    }
    else
    {
      _effect_parameter::GetEff_Rate(&v33->m_EP, 5);
      if ( a4 > 1.0 )
      {
        _effect_parameter::GetEff_Rate(&v33->m_EP, 5);
        v23 = (signed int)ffloor((float)(a4 - 1.0) * 100.0);
        v32 = v23;
        if ( v32 > rand() % 1000 && ((int (__fastcall *)(CCharacter *))v35->vfptr->GetHP)(v35) > 0 )
        {
          if ( v35->m_ObjID.m_byID || (v24 = v35, !BYTE2(v35[1].m_fCurPos[2])) || !LOBYTE(v24[1].m_fCurPos[2]) )
          {
            LOBYTE(v14) = 1;
            (*(void (__fastcall **)(CCharacter *, __int64))&v35->vfptr->gap8[0])(v35, v14);
            (*(void (__fastcall **)(CCharacter *))&v35->vfptr->gap8[64])(v35);
          }
        }
      }
    }
  }
}
