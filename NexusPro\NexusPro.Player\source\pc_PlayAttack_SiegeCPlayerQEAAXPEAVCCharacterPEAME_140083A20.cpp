/*
 * Function: ?pc_PlayAttack_Siege@CPlayer@@QEAAXP<PERSON><PERSON><PERSON>haracter@@PEAMEGG@Z
 * Address: 0x140083A20
 */

void __fastcall CPlayer::pc_PlayAttack_Siege(CPlayer *this, CCharacter *pDst, float *pfAttackPos, char by<PERSON><PERSON><PERSON><PERSON>, unsigned __int16 wBulletSerial, unsigned __int16 wEffBtSerial)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  double v8; // xmm0_8@14
  int v9; // eax@23
  unsigned int v10; // eax@23
  int v11; // eax@42
  CCharacter *v12; // rdx@43
  int v13; // eax@43
  CCharacter *v14; // rdx@44
  int v15; // eax@44
  int v16; // eax@48
  signed int v17; // eax@49
  int v18; // eax@50
  int v19; // eax@50
  _STORAGE_LIST::_db_con *v20; // rax@63
  __int64 v21; // [sp+0h] [bp-5B8h]@1
  _STORAGE_LIST::_db_con **ppBulletProp; // [sp+20h] [bp-598h]@42
  _BulletItem_fld **ppfldBullet; // [sp+28h] [bp-590h]@42
  _attack_param *pAP; // [sp+30h] [bp-588h]@42
  _STORAGE_LIST::_db_con **ppEffBulletProp; // [sp+38h] [bp-580h]@42
  _STORAGE_LIST::_db_con *pItem; // [sp+58h] [bp-560h]@4
  _BulletItem_fld *pBulletFld; // [sp+78h] [bp-540h]@4
  _STORAGE_LIST::_db_con *v28; // [sp+98h] [bp-520h]@6
  _BulletItem_fld *pEffBulletFld; // [sp+B8h] [bp-500h]@6
  char v30; // [sp+C4h] [bp-4F4h]@6
  float v31; // [sp+C8h] [bp-4F0h]@10
  __int16 v32; // [sp+CCh] [bp-4ECh]@10
  CAttack pAt; // [sp+E0h] [bp-4D8h]@12
  _attack_param pParam; // [sp+3F0h] [bp-1C8h]@12
  unsigned __int16 v35; // [sp+474h] [bp-144h]@12
  float v36; // [sp+478h] [bp-140h]@12
  int j; // [sp+47Ch] [bp-13Ch]@18
  int v38; // [sp+480h] [bp-138h]@21
  unsigned int delay; // [sp+484h] [bp-134h]@23
  CPartyModeKillMonsterExpNotify kPartyExpNotify; // [sp+4A0h] [bp-118h]@36
  int nTotalDam; // [sp+534h] [bp-84h]@37
  int v42; // [sp+538h] [bp-80h]@39
  int v43; // [sp+53Ch] [bp-7Ch]@39
  CPlayer *v44; // [sp+540h] [bp-78h]@46
  unsigned int dwAlter; // [sp+548h] [bp-70h]@54
  unsigned __int16 v46; // [sp+54Ch] [bp-6Ch]@59
  _STORAGE_LIST::_db_con *v47; // [sp+550h] [bp-68h]@63
  __int16 v48; // [sp+558h] [bp-60h]@63
  unsigned __int16 v49; // [sp+55Ch] [bp-5Ch]@66
  __int64 v50; // [sp+560h] [bp-58h]@4
  int nAddDelay; // [sp+568h] [bp-50h]@23
  CGameObjectVtbl *v52; // [sp+570h] [bp-48h]@23
  CCharacter *v53; // [sp+578h] [bp-40h]@42
  CGameObjectVtbl *v54; // [sp+580h] [bp-38h]@42
  int v55; // [sp+588h] [bp-30h]@43
  CGameObjectVtbl *v56; // [sp+590h] [bp-28h]@43
  int v57; // [sp+598h] [bp-20h]@44
  CGameObjectVtbl *v58; // [sp+5A0h] [bp-18h]@44
  float v59; // [sp+5A8h] [bp-10h]@49
  CPlayer *v60; // [sp+5C0h] [bp+8h]@1
  CCharacter *pDsta; // [sp+5C8h] [bp+10h]@1
  float *Dst; // [sp+5D0h] [bp+18h]@1
  char v63; // [sp+5D8h] [bp+20h]@1

  v63 = byAttPart;
  Dst = pfAttackPos;
  pDsta = pDst;
  v60 = this;
  v6 = &v21;
  for ( i = 364i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v50 = -2i64;
  pItem = 0i64;
  pBulletFld = 0i64;
  if ( pDst )
    memcpy_0(pfAttackPos, pDst->m_fCurPos, 0xCui64);
  v28 = 0i64;
  pEffBulletFld = 0i64;
  v30 = CPlayer::_pre_check_siege_attack(
          v60,
          pDsta,
          Dst,
          wBulletSerial,
          &pItem,
          &pBulletFld,
          wEffBtSerial,
          &v28,
          &pEffBulletFld);
  if ( v30 )
  {
    CPlayer::SendMsg_AttackResult_Error(v60, v30);
    if ( v60->m_bMove )
    {
      CCharacter::Stop((CCharacter *)&v60->vfptr);
      CGameObject::SendMsg_BreakStop((CGameObject *)&v60->vfptr);
    }
    return;
  }
  v31 = FLOAT_1_0;
  v32 = -1;
  if ( pItem )
  {
    v31 = pBulletFld->m_fGAAF;
    v32 = pItem->m_wItemIndex;
  }
  CAttack::CAttack(&pAt, (CCharacter *)&v60->vfptr);
  _attack_param::_attack_param(&pParam);
  v35 = -1;
  v36 = 0.0;
  if ( v28 )
  {
    v36 = pEffBulletFld->m_fGAAF;
    v35 = v28->m_wItemIndex;
  }
  *(float *)&v8 = v31;
  CPlayer::make_siege_attack_param(v60, pDsta, Dst, v63, pBulletFld, v31, &pParam, pEffBulletFld, v36);
  if ( v28 && v35 != 0xFFFF )
    CAttack::AttackGen(&pAt, &pParam, 0, 1);
  else
    CAttack::AttackGen(&pAt, &pParam, 0, 0);
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v8 = (double)pAt.m_DamList[j].m_nDamage * 1.25;
    pAt.m_DamList[j].m_nDamage = (signed int)floor(v8);
  }
  v38 = 0;
  if ( v60->m_pmWpn.byWpType != 7 )
  {
    _effect_parameter::GetEff_Plus(&v60->m_EP, v60->m_pmWpn.byWpClass + 9);
    v38 = (signed int)ffloor(*(float *)&v8);
  }
  nAddDelay = CPlayer::CalcEquipAttackDelay(v60);
  v52 = v60->vfptr;
  v9 = ((int (__fastcall *)(CPlayer *))v52->GetLevel)(v60);
  v10 = _WEAPON_PARAM::GetAttackDelay(&v60->m_pmWpn, v9, nAddDelay);
  delay = v38 + v10;
  if ( v60->m_pmWpn.byWpType == 7 )
  {
    _effect_parameter::GetEff_Plus(&v60->m_EP, 11);
    delay = (signed int)ffloor((float)(signed int)delay + *(float *)&v8);
  }
  _ATTACK_DELAY_CHECKER::SetDelay(&v60->m_AttDelayChker, delay);
  if ( _effect_parameter::GetEff_State(&v60->m_EP, 14) )
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v60->vfptr, 2, 14);
  if ( _effect_parameter::GetEff_State(&v60->m_EP, 21) )
  {
    if ( pParam.nAttactType != 4 && pParam.nAttactType != 5 && pParam.nAttactType != 6 && pParam.nAttactType != 7 )
      return;
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v60->vfptr, 2, 21);
  }
  if ( v60->m_bFreeSFByClass )
    pAt.m_bIsCrtAtt = 1;
  CPartyModeKillMonsterExpNotify::CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
  if ( !pAt.m_bFailure )
  {
    nTotalDam = CPlayer::_check_exp_after_attack(v60, pAt.m_nDamagedObjNum, pAt.m_DamList, &kPartyExpNotify);
    if ( nTotalDam > 0 )
      CPlayer::_check_dst_param_after_attack(v60, nTotalDam, pDsta);
  }
  CPlayer::SendMsg_AttackResult_Siege(v60, &pAt, v32);
  CPartyModeKillMonsterExpNotify::Notify(&kPartyExpNotify);
  v42 = 0;
  v43 = 0;
  for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
  {
    v11 = CPlayerDB::GetLevel(&v60->m_Param);
    v53 = pAt.m_DamList[j].m_pChar;
    v54 = v53->vfptr;
    LOBYTE(ppEffBulletProp) = 1;
    LODWORD(pAP) = 0;
    LODWORD(ppfldBullet) = -1;
    LOBYTE(ppBulletProp) = pAt.m_bIsCrtAtt;
    ((void (__fastcall *)(CCharacter *, _QWORD, CPlayer *, _QWORD))v54->SetDamage)(
      v53,
      pAt.m_DamList[j].m_nDamage,
      v60,
      (unsigned int)v11);
    if ( CPlayer::IsChaosMode(v60) )
    {
      v55 = ((int (__fastcall *)(CPlayer *))v60->vfptr->GetObjRace)(v60);
      v12 = pAt.m_DamList[j].m_pChar;
      v56 = pAt.m_DamList[j].m_pChar->vfptr;
      v13 = ((int (__fastcall *)(CCharacter *))v56->GetObjRace)(v12);
      if ( v55 == v13 )
        continue;
    }
    v57 = ((int (__fastcall *)(CPlayer *))v60->vfptr->GetObjRace)(v60);
    v14 = pAt.m_DamList[j].m_pChar;
    v58 = pAt.m_DamList[j].m_pChar->vfptr;
    v15 = ((int (__fastcall *)(CCharacter *))v58->GetObjRace)(v14);
    if ( v57 == v15 && !pAt.m_DamList[j].m_pChar->m_ObjID.m_byID )
    {
      v44 = (CPlayer *)pAt.m_DamList[j].m_pChar;
      if ( CPlayer::IsPunished(v44, 1, 0) )
        continue;
    }
    if ( !pAt.m_bFailure )
    {
      v16 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
      if ( CPlayer::IsPassMasteryLimitLvDiff(v60, v16) )
      {
        v59 = (float)pAt.m_DamList[j].m_nDamage;
        v17 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetMaxHP)(pAt.m_DamList[j].m_pChar);
        if ( (float)(v59 / (float)v17) >= 0.029999999 )
        {
          v18 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
          v19 = CPlayer::GetMasteryCumAfterAttack(v60, v18);
          v42 += v19;
          ++v43;
        }
      }
    }
  }
  if ( v43 > 0 && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v60->vfptr->IsInTown)(v60) )
  {
    dwAlter = v42 / v43;
    if ( v42 / v43 > 0 )
    {
      if ( v60->m_pmWpn.byWpType == 7 )
      {
        CPlayer::Emb_AlterStat(v60, 6, 0, dwAlter, 0, "CPlayer::pc_PlayAttack_Siege()---0", 1);
      }
      else
      {
        LOBYTE(pAP) = 1;
        ppfldBullet = (_BulletItem_fld **)"CPlayer::pc_PlayAttack_Siege()---1";
        LOBYTE(ppBulletProp) = 0;
        CPlayer::Emb_AlterStat(v60, 0, v60->m_pmWpn.byWpClass, dwAlter, 0, "CPlayer::pc_PlayAttack_Siege()---1", 1);
      }
    }
  }
  if ( pItem )
  {
    LOBYTE(ppfldBullet) = 1;
    LOBYTE(ppBulletProp) = 0;
    v46 = CPlayer::Emb_AlterDurPoint(v60, 2, pItem->m_byStorageIndex, -1, 0, 1);
    if ( v46 )
      CPlayer::SendMsg_AlterWeaponBulletInform(v60, pItem->m_wSerial, v46);
    else
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v60->m_ObjID.m_wIndex,
        pItem,
        v60->m_szItemHistoryFileName);
  }
  if ( CPlayer::IsSiegeMode(v60) )
  {
    v47 = v60->m_pSiegeItem;
    v20 = v60->m_pSiegeItem;
    LOBYTE(ppfldBullet) = 1;
    LOBYTE(ppBulletProp) = 0;
    v48 = CPlayer::Emb_AlterDurPoint(v60, 0, v20->m_byStorageIndex, -1, 0, 1);
    if ( !v48 )
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v60->m_ObjID.m_wIndex,
        v47,
        v60->m_szItemHistoryFileName);
  }
  CPlayer::SetBattleMode(v60, 1);
  if ( v28 )
  {
    LOBYTE(ppfldBullet) = 1;
    LOBYTE(ppBulletProp) = 0;
    v49 = CPlayer::Emb_AlterDurPoint(v60, 2, v28->m_byStorageIndex, -1, 0, 1);
    if ( v49 )
      CPlayer::SendMsg_AlterWeaponBulletInform(v60, v28->m_wSerial, v49);
    else
      CMgrAvatorItemHistory::consume_del_item(
        &CPlayer::s_MgrItemHistory,
        v60->m_ObjID.m_wIndex,
        v28,
        v60->m_szItemHistoryFileName);
  }
  CPartyModeKillMonsterExpNotify::~CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
}
