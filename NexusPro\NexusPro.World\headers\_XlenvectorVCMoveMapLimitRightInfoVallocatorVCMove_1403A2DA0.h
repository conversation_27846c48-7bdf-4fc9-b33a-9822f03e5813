#pragma once
#ifndef _XLENVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVCMOVE_1403A2DA0_H
#define _XLENVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVCMOVE_1403A2DA0_H

// Auto-generated header for _XlenvectorVCMoveMapLimitRightInfoVallocatorVCMove_1403A2DA0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

#endif // _XLENVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVCMOVE_1403A2DA0_H
