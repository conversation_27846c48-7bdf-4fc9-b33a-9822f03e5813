#pragma once
#ifndef _CMOVEMAPLIMITMANAGER_CMOVEMAPLIMITMANAGER__1_DTOR_1403A1F60_H
#define _CMOVEMAPLIMITMANAGER_CMOVEMAPLIMITMANAGER__1_DTOR_1403A1F60_H

// Auto-generated header for _CMoveMapLimitManager_CMoveMapLimitManager__1_dtor_1403A1F60.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _CMOVEMAPLIMITMANAGER_CMOVEMAPLIMITMANAGER__1_DTOR_1403A1F60_H
