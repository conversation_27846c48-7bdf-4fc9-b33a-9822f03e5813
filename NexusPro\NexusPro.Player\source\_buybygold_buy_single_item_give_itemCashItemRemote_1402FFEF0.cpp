/*
 * Function: ?_buybygold_buy_single_item_give_item@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@PEAVCPlayer@@PEAU__item@_request_csi_buy_clzo@@AEAU_db_con@_STORAGE_LIST@@@Z
 * Address: 0x1402FFEF0
 */

signed __int64 __fastcall CashItemRemoteStore::_buybygold_buy_single_item_give_item(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo::__item *pSrc, _STORAGE_LIST::_db_con *GiveItem)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  signed __int64 result; // rax@11
  __int64 v7; // [sp+0h] [bp-68h]@1
  int v8; // [sp+30h] [bp-38h]@4
  _TimeItem_fld *v9; // [sp+38h] [bp-30h]@7
  __time32_t Time; // [sp+44h] [bp-24h]@9
  _STORAGE_LIST::_db_con *v11; // [sp+58h] [bp-10h]@10
  CPlayer *v12; // [sp+78h] [bp+10h]@1
  _request_csi_buy_clzo::__item *v13; // [sp+80h] [bp+18h]@1
  _STORAGE_LIST::_db_con *pCon; // [sp+88h] [bp+20h]@1

  pCon = GiveItem;
  v13 = pSrc;
  v12 = pOne;
  v4 = &v7;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v8 = 0;
  GiveItem->m_byTableCode = pSrc->byTblCode;
  GiveItem->m_wItemIndex = pSrc->wItemIdx;
  if ( IsOverLapItem(GiveItem->m_byTableCode) )
    pCon->m_dwDur = v13->byOverlapNum;
  else
    pCon->m_dwDur = GetItemDurPoint(v13->byTblCode, v13->wItemIdx);
  pCon->m_dwLv = (unsigned __int8)GetDefItemUpgSocketNum(v13->byTblCode, v13->wItemIdx);
  pCon->m_dwLv = GetBitAfterSetLimSocket(pCon->m_dwLv);
  pCon->m_wSerial = CPlayerDB::GetNewItemSerial(&v12->m_Param);
  pCon->m_wSerial = pCon->m_wSerial;
  v9 = TimeItem::FindTimeRec(v13->byTblCode, v13->wItemIdx);
  if ( v9 && v9->m_nCheckType )
  {
    pCon->m_byCsMethod = v9->m_nCheckType;
    _time32(&Time);
    pCon->m_dwT = v9->m_nUseTime + Time;
    pCon->m_dwLendRegdTime = Time;
  }
  v11 = CPlayer::Emb_AddStorage(v12, 0, (_STORAGE_LIST::_storage_con *)&pCon->m_bLoad, 0, 1);
  if ( v11 )
    result = 0i64;
  else
    result = 15i64;
  return result;
}
