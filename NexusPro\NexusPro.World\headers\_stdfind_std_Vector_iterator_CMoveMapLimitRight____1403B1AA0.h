#pragma once
#ifndef _STDFIND_STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT____1403B1AA0_H
#define _STDFIND_STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT____1403B1AA0_H

// Auto-generated header for _stdfind_std_Vector_iterator_CMoveMapLimitRight____1403B1AA0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_1(__int64 a1, __int64 a2)
;

#endif // _STDFIND_STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT____1403B1AA0_H
