/*
 * Function: ?pc_PlayAttack_Unit@CPlayer@@QEAAXPEAVCCharacter@@E@Z
 * Address: 0x1400830D0
 */

void __fastcall CPlayer::pc_PlayAttack_Unit(CPlayer *this, CCharacter *pDst, char byWeaponPart)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@28
  __int64 v6; // [sp+0h] [bp-538h]@1
  _UnitBullet_fld **ppBulletFld; // [sp+20h] [bp-518h]@28
  _unit_bullet_param **ppBulletParam; // [sp+28h] [bp-510h]@28
  int v9; // [sp+30h] [bp-508h]@28
  char v10; // [sp+38h] [bp-500h]@28
  _unit_bullet_param *v11; // [sp+48h] [bp-4F0h]@4
  _UnitBullet_fld *v12; // [sp+68h] [bp-4D0h]@4
  _UnitPart_fld *ppWeaponFld; // [sp+88h] [bp-4B0h]@4
  char v14; // [sp+94h] [bp-4A4h]@4
  float v15; // [sp+98h] [bp-4A0h]@8
  unsigned __int16 v16; // [sp+9Ch] [bp-49Ch]@8
  int v17; // [sp+A0h] [bp-498h]@10
  CPlayerAttack v18; // [sp+C0h] [bp-478h]@10
  _attack_param pAP; // [sp+3E0h] [bp-158h]@10
  CPartyModeKillMonsterExpNotify kPartyExpNotify; // [sp+480h] [bp-B8h]@19
  int j; // [sp+514h] [bp-24h]@26
  __int64 v22; // [sp+518h] [bp-20h]@4
  CCharacter *v23; // [sp+520h] [bp-18h]@28
  CGameObjectVtbl *v24; // [sp+528h] [bp-10h]@28
  CPlayer *v25; // [sp+540h] [bp+8h]@1
  CCharacter *pDsta; // [sp+548h] [bp+10h]@1
  char v27; // [sp+550h] [bp+18h]@1

  v27 = byWeaponPart;
  pDsta = pDst;
  v25 = this;
  v3 = &v6;
  for ( i = 332i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v22 = -2i64;
  v11 = 0i64;
  v12 = 0i64;
  ppWeaponFld = 0i64;
  v14 = CPlayer::_pre_check_unit_attack(v25, pDst, byWeaponPart, &ppWeaponFld, &v12, &v11);
  if ( v14 )
  {
    CPlayer::SendMsg_AttackResult_Error(v25, v14);
    if ( v25->m_bMove )
    {
      CCharacter::Stop((CCharacter *)&v25->vfptr);
      CGameObject::SendMsg_BreakStop((CGameObject *)&v25->vfptr);
    }
    return;
  }
  v15 = FLOAT_1_0;
  v16 = -1;
  if ( v12 )
  {
    v15 = v12->m_fGAAF;
    v16 = v12->m_dwIndex;
  }
  v17 = 0;
  CPlayerAttack::CPlayerAttack(&v18, (CCharacter *)&v25->vfptr);
  _attack_param::_attack_param(&pAP);
  CPlayer::make_unit_attack_param(v25, pDsta, ppWeaponFld, v15, &pAP);
  v25->m_byUsingWeaponPart = v27;
  CPlayerAttack::AttackUnit(&v18, &pAP);
  _ATTACK_DELAY_CHECKER::SetDelay(&v25->m_AttDelayChker, ppWeaponFld->m_nAttackDel);
  if ( _effect_parameter::GetEff_State(&v25->m_EP, 14) )
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v25->vfptr, 2, 14);
  if ( _effect_parameter::GetEff_State(&v25->m_EP, 21) )
  {
    if ( pAP.nAttactType != 4 && pAP.nAttactType != 5 && pAP.nAttactType != 6 && pAP.nAttactType != 7 )
      return;
    CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v25->vfptr, 2, 21);
  }
  CPartyModeKillMonsterExpNotify::CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
  if ( !v18.m_bFailure )
    CPlayer::_check_exp_after_attack(v25, v18.m_nDamagedObjNum, v18.m_DamList, &kPartyExpNotify);
  if ( v11->wLeftNum >= ppWeaponFld->m_nNeedBt )
    v11->wLeftNum -= LOWORD(ppWeaponFld->m_nNeedBt);
  else
    v11->wLeftNum = 0;
  v17 = v11->wLeftNum;
  CPlayer::SendMsg_AlterUnitBulletInform(v25, v27, v17);
  if ( !v11->wLeftNum )
    *v11 = (_unit_bullet_param)-1;
  CPlayer::SendMsg_AttackResult_Unit(v25, (CAttack *)&v18.m_pp, v27, v16);
  CPartyModeKillMonsterExpNotify::Notify(&kPartyExpNotify);
  for ( j = 0; j < v18.m_nDamagedObjNum; ++j )
  {
    v5 = CPlayerDB::GetLevel(&v25->m_Param);
    v23 = v18.m_DamList[j].m_pChar;
    v24 = v23->vfptr;
    v10 = 1;
    v9 = 0;
    LODWORD(ppBulletParam) = -1;
    LOBYTE(ppBulletFld) = v18.m_bIsCrtAtt;
    ((void (__fastcall *)(CCharacter *, _QWORD, CPlayer *, _QWORD))v24->SetDamage)(
      v23,
      v18.m_DamList[j].m_nDamage,
      v25,
      (unsigned int)v5);
  }
  CPlayer::SetBattleMode(v25, 1);
  CPartyModeKillMonsterExpNotify::~CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
}
