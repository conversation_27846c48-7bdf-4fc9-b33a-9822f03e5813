#pragma once
#ifndef SIZE_ADD_CHAR_RESULT_ZONEQEAAHXZ_14011F870_H
#define SIZE_ADD_CHAR_RESULT_ZONEQEAAHXZ_14011F870_H

// Auto-generated header for size_add_char_result_zoneQEAAHXZ_14011F870.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_add_char_result_zone::size(_add_char_result_zone *this)
;

#endif // SIZE_ADD_CHAR_RESULT_ZONEQEAAHXZ_14011F870_H
