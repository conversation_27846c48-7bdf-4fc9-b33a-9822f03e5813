#pragma once
#ifndef _CMOVEMAPLIMITINFOPORTALCMOVEMAPLIMITINFOPORTAL__1_1403A3FA0_H
#define _CMOVEMAPLIMITINFOPORTALCMOVEMAPLIMITINFOPORTAL__1_1403A3FA0_H

// Auto-generated header for _CMoveMapLimitInfoPortalCMoveMapLimitInfoPortal__1_1403A3FA0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _CMOVEMAPLIMITINFOPORTALCMOVEMAPLIMITINFOPORTAL__1_1403A3FA0_H
