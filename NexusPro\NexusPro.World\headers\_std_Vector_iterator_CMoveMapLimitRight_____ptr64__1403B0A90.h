#pragma once
#ifndef _STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT_____PTR64__1403B0A90_H
#define _STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT_____PTR64__1403B0A90_H

// Auto-generated header for _std_Vector_iterator_CMoveMapLimitRight_____ptr64__1403B0A90.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _STD_VECTOR_ITERATOR_CMOVEMAPLIMITRIGHT_____PTR64__1403B0A90_H
