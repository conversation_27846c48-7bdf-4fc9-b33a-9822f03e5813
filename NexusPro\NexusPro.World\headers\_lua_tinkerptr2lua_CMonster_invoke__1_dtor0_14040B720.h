#pragma once
#ifndef _LUA_TINKERPTR2LUA_CMONSTER_INVOKE__1_DTOR0_14040B720_H
#define _LUA_TINKERPTR2LUA_CMONSTER_INVOKE__1_DTOR0_14040B720_H

// Auto-generated header for _lua_tinkerptr2lua_CMonster_invoke__1_dtor0_14040B720.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _LUA_TINKERPTR2LUA_CMONSTER_INVOKE__1_DTOR0_14040B720_H
