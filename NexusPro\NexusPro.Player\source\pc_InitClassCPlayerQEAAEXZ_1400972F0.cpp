/*
 * Function: ?pc_InitClass@CPlayer@@QEAAEXZ
 * Address: 0x1400972F0
 */

char __fastcall CPlayer::pc_InitClass(CPlayer *this)
{
  __int64 *v1; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@5
  char *v4; // rdx@22
  CUserDB *v5; // rdi@22
  CUserDB *v6; // r8@22
  __int64 v7; // [sp+0h] [bp-B8h]@1
  char *szOldClass; // [sp+20h] [bp-98h]@22
  char *szCurClass; // [sp+28h] [bp-90h]@22
  int *piOldMaxPoint; // [sp+30h] [bp-88h]@22
  int *piAlterMaxPoint; // [sp+38h] [bp-80h]@22
  char *pszFileName; // [sp+40h] [bp-78h]@22
  char Dst; // [sp+54h] [bp-64h]@15
  int v14; // [sp+78h] [bp-40h]@15
  int v15; // [sp+7Ch] [bp-3Ch]@15
  int v16; // [sp+80h] [bp-38h]@15
  char v17; // [sp+84h] [bp-34h]@15
  int j; // [sp+94h] [bp-24h]@15
  _TRAP_PARAM::_param *v19; // [sp+98h] [bp-20h]@18
  unsigned __int64 v20; // [sp+A8h] [bp-10h]@4
  CPlayer *v21; // [sp+C0h] [bp+8h]@1

  v21 = this;
  v1 = &v7;
  for ( i = 44i64; i; --i )
  {
    *(_DWORD *)v1 = -858993460;
    v1 = (__int64 *)((char *)v1 + 4);
  }
  v20 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( v21->m_pUserDB )
  {
    if ( v21->m_Param.m_pClassHistory[0] )
    {
      if ( v21->m_pUsingUnit || v21->m_pParkingUnit )
      {
        result = 4;
      }
      else if ( _STORAGE_LIST::GetNumUseCon((_STORAGE_LIST *)&v21->m_Param.m_dbAnimus.m_nListNum) <= 0 )
      {
        if ( v21->m_bAfterEffect )
        {
          result = 11;
        }
        else
        {
          memcpy_0(&Dst, v21->m_pUserDB->m_AvatorData.dbAvator.m_szClassCode, 5ui64);
          v14 = v21->m_nMaxPoint[0];
          v15 = v21->m_nMaxPoint[1];
          v16 = v21->m_nMaxPoint[2];
          memset(&v17, 0, 4ui64);
          CPlayerDB::InitClass(&v21->m_Param);
          CPlayer::CalcAddPointByClass(v21);
          v21->m_nAddDfnMstByClass = 0;
          CPlayer::RewardChangeClassMastery(v21, v21->m_Param.m_pClassData);
          CUserDB::InitClass(v21->m_pUserDB, v21->m_Param.m_pClassData->m_strCode);
          CPlayer::ReCalcMaxHFSP(v21, 0, 0);
          CPlayer::_TowerAllReturn(v21, 3, 1);
          for ( j = 0; j < 20; ++j )
          {
            v19 = &v21->m_pmTrp.m_Item[j];
            if ( _TRAP_PARAM::_param::isLoad(v19) && v19->pItem->m_dwObjSerial == v19->dwSerial )
              CTrap::Destroy(v19->pItem, 4);
          }
          v21->m_fUnitPv_AttFc = FLOAT_1_0;
          v21->m_fUnitPv_DefFc = FLOAT_1_0;
          v21->m_fUnitPv_RepPr = FLOAT_1_0;
          CCharacter::RemoveAllContinousEffect((CCharacter *)&v21->vfptr);
          CPlayer::SendMsg_AlterMoneyInform(v21, 0);
          v4 = v21->m_pUserDB->m_AvatorData.dbAvator.m_szClassCode;
          v5 = v21->m_pUserDB;
          v6 = v21->m_pUserDB;
          pszFileName = v21->m_szItemHistoryFileName;
          piAlterMaxPoint = v21->m_nMaxPoint;
          piOldMaxPoint = &v14;
          szCurClass = v4;
          szOldClass = &Dst;
          CMgrAvatorItemHistory::InitClass(
            &CPlayer::s_MgrItemHistory,
            0,
            v6->m_AvatorData.dbAvator.m_dwClassInitCnt,
            v5->m_AvatorData.dbAvator.m_byLastClassGrade,
            &Dst,
            v4,
            &v14,
            v21->m_nMaxPoint,
            v21->m_szItemHistoryFileName);
          CUserDB::WriteLog_ChangeClassAfterInitClass(v21->m_pUserDB, 0, &Dst);
          v21->m_bDownCheckEquipEffect = 1;
          result = 0;
        }
      }
      else
      {
        result = 5;
      }
    }
    else
    {
      result = 2;
    }
  }
  else
  {
    result = 1;
  }
  return result;
}
