/*
 * Function: _dynamic_atexit_destructor_for__CPlayer::s_tblLimMasteryCumContinue__
 * Address: 0x1406E8200
 */

void __cdecl dynamic_atexit_destructor_for__CPlayer::s_tblLimMasteryCumContinue__()
{
  __int64 *v0; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v2; // [sp+0h] [bp-28h]@1

  v0 = &v2;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v0 = -858993460;
    v0 = (__int64 *)((char *)v0 + 4);
  }
  `eh vector destructor iterator'(
    &CPlayer::s_tblLimMasteryCumContinue,
    0xB0ui64,
    12,
    (void (__cdecl *)(void *))CRecordData::~CRecordData);
}
