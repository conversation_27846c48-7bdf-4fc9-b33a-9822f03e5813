/*
 * Function: ?player_create@CMgrAccountLobbyHistory@@QEAAX_NPEAU_AVATOR_DATA@@PEAD@Z
 * Address: 0x1402346B0
 */

void __fastcall CMgrAccountLobbyHistory::player_create(CMgrAccountLobbyHistory *this, bool bFirstStart, _AVATOR_DATA *pAvator, char *pszFileName)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-48h]@1
  char *v7; // [sp+20h] [bp-28h]@7
  const char *v8; // [sp+30h] [bp-18h]@5
  CMgrAccountLobbyHistory *v9; // [sp+50h] [bp+8h]@1
  bool v10; // [sp+58h] [bp+10h]@1
  _AVATOR_DATA *v11; // [sp+60h] [bp+18h]@1
  char *pszFileNamea; // [sp+68h] [bp+20h]@1

  pszFileNamea = pszFileName;
  v11 = pAvator;
  v10 = bFirstStart;
  v9 = this;
  v4 = &v6;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v4 = -*********;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  sLData[0] = 0;
  if ( bFirstStart )
    v8 = "First Connect Character";
  else
    v8 = "Old Character";
  v7 = v9->m_szCurTime;
  sprintf_s<10240>((char (*)[10240])sLBuf, "Player Create: %s [%s %s]\r\n", v8, v9->m_szCurDate);
  strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  if ( v10 && (v11->dbAvator.m_dwDalant || v11->dbAvator.m_dwGold) )
  {
    sprintf_s<10240>(
      (char (*)[10240])sLBuf,
      "WARNNING : First Connect Char Money Wrong : $D(%d), $G(%d)",
      v11->dbAvator.m_dwDalant,
      v11->dbAvator.m_dwGold);
    strcat_s<20000>((char (*)[20000])sLData, sLBuf);
  }
  strcat_s<20000>((char (*)[20000])sLData, "\r\n\t============\r\n\r\n");
  CMgrAccountLobbyHistory::WriteFile(v9, pszFileNamea, sLData);
}
