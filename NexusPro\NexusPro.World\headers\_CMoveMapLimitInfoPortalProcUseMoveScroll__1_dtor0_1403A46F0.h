#pragma once
#ifndef _CMOVEMAPLIMITINFOPORTALPROCUSEMOVESCROLL__1_DTOR0_1403A46F0_H
#define _CMOVEMAPLIMITINFOPORTALPROCUSEMOVESCROLL__1_DTOR0_1403A46F0_H

// Auto-generated header for _CMoveMapLimitInfoPortalProcUseMoveScroll__1_dtor0_1403A46F0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _CMOVEMAPLIMITINFOPORTALPROCUSEMOVESCROLL__1_DTOR0_1403A46F0_H
