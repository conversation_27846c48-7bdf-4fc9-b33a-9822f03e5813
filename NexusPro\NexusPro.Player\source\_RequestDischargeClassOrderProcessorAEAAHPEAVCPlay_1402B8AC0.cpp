/*
 * Function: ?_RequestDischarge@ClassOrderProcessor@@AEAAHPEAVCPlayer@@PEAD@Z
 * Address: 0x1402B8AC0
 */

signed __int64 __fastcall ClassOrderProcessor::_RequestDischarge(ClassOrderProcessor *this, CPlayer *pOne, char *pData)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  CPvpUserAndGuildRankingSystem *v5; // rax@4
  unsigned int v6; // eax@4
  signed __int64 result; // rax@5
  CandidateMgr *v8; // rax@8
  CandidateMgr *v9; // rax@12
  CPvpUserAndGuildRankingSystem *v10; // rax@14
  CPvpUserAndGuildRankingSystem *v11; // rax@14
  int v12; // eax@18
  char v13; // al@19
  int v14; // eax@20
  int v15; // eax@20
  PatriarchElectProcessor *v16; // rax@20
  unsigned int v17; // eax@20
  __int64 v18; // [sp+0h] [bp-88h]@1
  unsigned __int16 nLen[2]; // [sp+20h] [bp-68h]@20
  char *v20; // [sp+28h] [bp-60h]@20
  char *v21; // [sp+30h] [bp-58h]@6
  _candidate_info::ClassType eType; // [sp+38h] [bp-50h]@6
  _candidate_info *v23; // [sp+40h] [bp-48h]@8
  int j; // [sp+48h] [bp-40h]@14
  CPlayer *v25; // [sp+50h] [bp-38h]@17
  unsigned int v26; // [sp+58h] [bp-30h]@4
  int v27; // [sp+5Ch] [bp-2Ch]@4
  int v28; // [sp+60h] [bp-28h]@8
  int v29; // [sp+64h] [bp-24h]@12
  unsigned __int16 v30[2]; // [sp+68h] [bp-20h]@20
  char *v31; // [sp+70h] [bp-18h]@20
  int v32; // [sp+78h] [bp-10h]@20
  unsigned int v33; // [sp+7Ch] [bp-Ch]@20
  ClassOrderProcessor *v34; // [sp+90h] [bp+8h]@1
  CPlayer *v35; // [sp+98h] [bp+10h]@1
  char *v36; // [sp+A0h] [bp+18h]@1

  v36 = pData;
  v35 = pOne;
  v34 = this;
  v3 = &v18;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v26 = CPlayerDB::GetCharSerial(&pOne->m_Param);
  v27 = CPlayerDB::GetRaceCode(&v35->m_Param);
  v5 = CPvpUserAndGuildRankingSystem::Instance();
  v6 = CPvpUserAndGuildRankingSystem::GetCurrentRaceBossSerial(v5, v27, 0);
  if ( v26 == v6 )
  {
    v21 = v36;
    eType = (unsigned __int8)*v36 + 5;
    if ( (signed int)eType >= 5 )
    {
      v28 = CPlayerDB::GetRaceCode(&v35->m_Param);
      v8 = CandidateMgr::Instance();
      v23 = CandidateMgr::GetPatriarchGroup(v8, v28, eType);
      if ( v23 )
      {
        if ( !strcmp_0(v23->wszName, v21 + 1) )
        {
          v29 = CPlayerDB::GetRaceCode(&v35->m_Param);
          v9 = CandidateMgr::Instance();
          if ( CandidateMgr::DischargePatriarchGroup(v9, v29, eType) )
          {
            v10 = CPvpUserAndGuildRankingSystem::Instance();
            CPvpUserAndGuildRankingSystem::SetUpdateRaceBossSerial(v10, v23->byRace, v23->eClassType, 0);
            CNotifyNotifyRaceLeaderSownerUTaxrate::UpdateRaceLeader(
              &stru_1799C9AF8,
              v23->byRace,
              v23->eClassType,
              &wszLeaderName);
            v11 = CPvpUserAndGuildRankingSystem::Instance();
            CPvpUserAndGuildRankingSystem::ApplyUpdatedBossInfo(v11);
            for ( j = 0; j < 2532; ++j )
            {
              v25 = &g_Player + j;
              if ( v25->m_bOper )
              {
                v12 = CPlayerDB::GetRaceCode(&v25->m_Param);
                if ( v12 == v23->byRace )
                {
                  v13 = CPlayerDB::GetRaceCode(&v25->m_Param);
                  CNotifyNotifyRaceLeaderSownerUTaxrate::Notify(&stru_1799C9AF8, v13, v25->m_ObjID.m_wIndex);
                }
              }
            }
            v14 = CPlayerDB::GetRaceCode(&v35->m_Param);
            *(_DWORD *)v30 = _pt_appoint_inform_request_zocl::size(&v34->_kSend[v14]);
            v15 = CPlayerDB::GetRaceCode(&v35->m_Param);
            CNetProcess::LoadSendMsg(
              unk_1414F2088,
              v35->m_ObjID.m_wIndex,
              v34->_byPtType,
              (char *)&v34->_kSend[v15],
              v30[0]);
            v31 = v21 + 1;
            v32 = (unsigned __int8)*v21 + 5;
            v33 = CPlayerDB::GetCharSerial(&v35->m_Param);
            v16 = PatriarchElectProcessor::Instance();
            v17 = PatriarchElectProcessor::GetElectSerial(v16);
            v20 = v31;
            *(_DWORD *)nLen = v32;
            CLogFile::Write(&v34->_kSysLog, "Patriarch Discharge (ES:%d, PS:%d) >> Class(%d) Avator(%s)", v17, v33);
            result = 0i64;
          }
          else
          {
            result = 23i64;
          }
        }
        else
        {
          result = 24i64;
        }
      }
      else
      {
        result = 23i64;
      }
    }
    else
    {
      result = 22i64;
    }
  }
  else
  {
    result = 20i64;
  }
  return result;
}
