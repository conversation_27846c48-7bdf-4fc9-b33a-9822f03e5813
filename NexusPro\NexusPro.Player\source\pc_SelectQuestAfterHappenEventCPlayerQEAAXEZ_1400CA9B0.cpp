/*
 * Function: ?pc_SelectQuestAfterHappenEvent@CPlayer@@QEAAXE@Z
 * Address: 0x1400CA9B0
 */

void __fastcall CPlayer::pc_SelectQuestAfterHappenEvent(CPlayer *this, char bySelectIndex)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-68h]@1
  void *Src; // [sp+20h] [bp-48h]@4
  _happen_event_cont Dst; // [sp+38h] [bp-30h]@5
  CPlayer *v7; // [sp+70h] [bp+8h]@1
  char v8; // [sp+78h] [bp+10h]@1

  v8 = bySelectIndex;
  v7 = this;
  v2 = &v4;
  for ( i = 24i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  Src = CQuestMgr::GetLastHappenEvent(&v7->m_QuestMgr);
  if ( Src )
  {
    _happen_event_cont::_happen_event_cont(&Dst);
    memcpy_0(&Dst, Src, 0x18ui64);
    if ( !CPlayer::Emb_StartQuest(v7, v8, &Dst) )
      CPlayer::SendMsg_InsertQuestFailure(v7, Dst.m_QtHpType, Dst.m_nIndexInType, Dst.m_nRaceCode);
  }
}
