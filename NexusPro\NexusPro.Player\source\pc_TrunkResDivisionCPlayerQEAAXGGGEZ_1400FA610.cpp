/*
 * Function: ?pc_TrunkResDivision@CPlayer@@QEAAXGGGE@Z
 * Address: 0x1400FA610
 */

void __fastcall CPlayer::pc_TrunkResDivision(CPlayer *this, unsigned __int16 wStartSerial, unsigned __int16 wTarSerial, unsigned __int16 wMoveAmount, char byStorageIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-58h]@1
  bool bUpdate; // [sp+20h] [bp-38h]@40
  bool bSend; // [sp+28h] [bp-30h]@40
  char v10; // [sp+30h] [bp-28h]@4
  _STORAGE_LIST::_db_con *pStartOre; // [sp+38h] [bp-20h]@4
  _STORAGE_LIST::_db_con *pTargetOre; // [sp+40h] [bp-18h]@4
  CPlayer *p; // [sp+60h] [bp+8h]@1
  unsigned __int16 v14; // [sp+68h] [bp+10h]@1
  unsigned __int16 v15; // [sp+70h] [bp+18h]@1
  unsigned __int16 v16; // [sp+78h] [bp+20h]@1

  v16 = wMoveAmount;
  v15 = wTarSerial;
  v14 = wStartSerial;
  p = this;
  v5 = &v7;
  for ( i = 20i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v10 = 0;
  pStartOre = 0i64;
  pTargetOre = 0i64;
  if ( IsBeNearStore(p, 10) )
  {
    if ( p->m_Param.m_bTrunkOpen )
    {
      if ( byStorageIndex == 5 )
      {
        pStartOre = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&p->m_Param.m_dbTrunk.m_nListNum, v14);
      }
      else if ( byStorageIndex == 7 )
      {
        pStartOre = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&p->m_Param.m_dbExtTrunk.m_nListNum, v14);
      }
      if ( pStartOre )
      {
        if ( pStartOre->m_bLock )
        {
          v10 = 11;
        }
        else
        {
          if ( byStorageIndex == 5 )
          {
            pTargetOre = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&p->m_Param.m_dbTrunk.m_nListNum, v15);
          }
          else if ( byStorageIndex == 7 )
          {
            pTargetOre = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&p->m_Param.m_dbExtTrunk.m_nListNum, v15);
          }
          if ( pTargetOre )
          {
            if ( pTargetOre->m_bLock )
            {
              v10 = 11;
            }
            else if ( IsOverLapItem(pStartOre->m_byTableCode) && IsOverLapItem(pTargetOre->m_byTableCode) )
            {
              if ( pStartOre->m_byCsMethod && pTargetOre->m_byCsMethod && pStartOre->m_dwT != pTargetOre->m_dwT )
              {
                v10 = 23;
              }
              else if ( pStartOre->m_byTableCode == pTargetOre->m_byTableCode )
              {
                if ( pStartOre->m_wItemIndex == pTargetOre->m_wItemIndex )
                {
                  if ( pStartOre->m_dwDur >= v16 )
                  {
                    if ( pTargetOre->m_dwDur + v16 > 0x63 )
                      v10 = 9;
                  }
                  else
                  {
                    v10 = 8;
                  }
                }
                else
                {
                  v10 = 6;
                }
              }
              else
              {
                v10 = 6;
              }
            }
            else
            {
              v10 = 3;
            }
          }
          else
          {
            v10 = 5;
          }
        }
      }
      else
      {
        v10 = 5;
      }
    }
    else
    {
      v10 = 14;
    }
  }
  else
  {
    v10 = 13;
  }
  if ( !v10 )
  {
    bSend = 0;
    bUpdate = 0;
    CPlayer::Emb_AlterDurPoint(p, byStorageIndex, pStartOre->m_byStorageIndex, -v16, 0, 0);
    bSend = 0;
    bUpdate = 0;
    CPlayer::Emb_AlterDurPoint(p, byStorageIndex, pTargetOre->m_byStorageIndex, v16, 0, 0);
  }
  CPlayer::SendMsg_TrunkResDivision(p, v10, pStartOre, pTargetOre);
}
