#pragma once
#ifndef SIZE_MOVE_TO_OWN_STONEMAP_RESULT_ZOCLQEAAHXZ_1400F03C0_H
#define SIZE_MOVE_TO_OWN_STONEMAP_RESULT_ZOCLQEAAHXZ_1400F03C0_H

// Auto-generated header for size_move_to_own_stonemap_result_zoclQEAAHXZ_1400F03C0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_move_to_own_stonemap_result_zocl::size(_move_to_own_stonemap_result_zocl *this)
;

#endif // SIZE_MOVE_TO_OWN_STONEMAP_RESULT_ZOCLQEAAHXZ_1400F03C0_H
