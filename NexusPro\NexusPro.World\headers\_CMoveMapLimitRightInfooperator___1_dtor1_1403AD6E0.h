#pragma once
#ifndef _CMOVEMAPLIMITRIGHTINFOOPERATOR___1_DTOR1_1403AD6E0_H
#define _CMOVEMAPLIMITRIGHTINFOOPERATOR___1_DTOR1_1403AD6E0_H

// Auto-generated header for _CMoveMapLimitRightInfooperator___1_dtor1_1403AD6E0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_1(__int64 a1, __int64 a2)
;

#endif // _CMOVEMAPLIMITRIGHTINFOOPERATOR___1_DTOR1_1403AD6E0_H
