/*
 * Function: ?pc_TrunkIoMoneyRequest@CPlayer@@QEAAXEKK@Z
 * Address: 0x1400FAB80
 */

void __fastcall CPlayer::pc_TrunkIoMoneyRequest(CPlayer *this, char byCase, unsigned int dwDalant, unsigned int dwGold)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v6; // eax@27
  unsigned int v7; // eax@30
  unsigned int v8; // eax@36
  unsigned int v9; // eax@37
  __int64 v10; // [sp+0h] [bp-A8h]@1
  unsigned int dwIOGold; // [sp+20h] [bp-88h]@37
  unsigned int dwPayDalant; // [sp+28h] [bp-80h]@37
  unsigned int dwInvenDalant; // [sp+30h] [bp-78h]@37
  char v14; // [sp+60h] [bp-48h]@4
  unsigned int dwFeeDalant; // [sp+64h] [bp-44h]@4
  double v16; // [sp+68h] [bp-40h]@18
  double v17; // [sp+70h] [bp-38h]@18
  bool v18; // [sp+78h] [bp-30h]@36
  bool v19; // [sp+79h] [bp-2Fh]@36
  char *v20; // [sp+80h] [bp-28h]@36
  unsigned int v21; // [sp+88h] [bp-20h]@36
  unsigned int v22; // [sp+8Ch] [bp-1Ch]@36
  unsigned int v23; // [sp+90h] [bp-18h]@36
  unsigned int dwGolda; // [sp+94h] [bp-14h]@37
  CPlayer *p; // [sp+B0h] [bp+8h]@1
  char v26; // [sp+B8h] [bp+10h]@1
  signed int ui64AddMoney; // [sp+C0h] [bp+18h]@1
  signed int ui64AddGold; // [sp+C8h] [bp+20h]@1

  ui64AddGold = dwGold;
  ui64AddMoney = dwDalant;
  v26 = byCase;
  p = this;
  v4 = &v10;
  for ( i = 40i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v14 = 0;
  dwFeeDalant = 0;
  if ( IsBeNearStore(p, 10) )
  {
    if ( p->m_Param.m_bTrunkOpen )
    {
      if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) > 0 )
      {
        if ( v26 )
        {
          if ( (double)ui64AddMoney <= p->m_Param.m_dTrunkDalant )
          {
            if ( (double)ui64AddGold <= p->m_Param.m_dTrunkGold )
            {
              if ( !ui64AddMoney
                || (v6 = CPlayerDB::GetDalant(&p->m_Param), CanAddMoneyForMaxLimMoney((unsigned int)ui64AddMoney, v6)) )
              {
                if ( ui64AddGold )
                {
                  v7 = CPlayerDB::GetGold(&p->m_Param);
                  if ( !CanAddMoneyForMaxLimGold((unsigned int)ui64AddGold, v7) )
                    v14 = 19;
                }
              }
              else
              {
                v14 = 19;
              }
            }
            else
            {
              v14 = 6;
            }
          }
          else
          {
            v14 = 6;
          }
        }
        else if ( CPlayerDB::GetDalant(&p->m_Param) >= ui64AddMoney )
        {
          if ( CPlayerDB::GetGold(&p->m_Param) >= ui64AddGold )
          {
            if ( (unsigned int)ui64AddMoney <= 0x77359400 && (unsigned int)ui64AddGold <= 0x7A120 )
            {
              v16 = p->m_Param.m_dTrunkGold + (double)ui64AddGold;
              v17 = p->m_Param.m_dTrunkDalant + (double)ui64AddMoney;
              if ( v16 > 500000.0 || v17 > 1000000000.0 )
                v14 = 20;
            }
            else
            {
              v14 = 21;
            }
          }
          else
          {
            v14 = 6;
          }
        }
        else
        {
          v14 = 6;
        }
      }
      else
      {
        v14 = 2;
      }
    }
    else
    {
      v14 = 14;
    }
  }
  else
  {
    v14 = 13;
  }
  if ( !v14 )
  {
    if ( v26 )
    {
      CPlayerDB::SubTrunkDalant(&p->m_Param, ui64AddMoney);
      CPlayerDB::SubTrunkGold(&p->m_Param, ui64AddGold);
      CPlayer::AddDalant(p, ui64AddMoney, 0);
      CPlayer::AddGold(p, ui64AddGold, 0);
    }
    else
    {
      CPlayer::SubDalant(p, ui64AddMoney);
      CPlayer::SubGold(p, ui64AddGold);
      CPlayerDB::AddTrunkDalant(&p->m_Param, ui64AddMoney);
      CPlayerDB::AddTrunkGold(&p->m_Param, ui64AddGold);
    }
    CUserDB::Update_TrunkMoney(p->m_pUserDB, p->m_Param.m_dTrunkGold, p->m_Param.m_dTrunkDalant);
    v19 = v26 == 0;
    v18 = v26 == 0;
    v20 = p->m_szItemHistoryFileName;
    v21 = (signed int)floor(p->m_Param.m_dTrunkGold);
    v22 = (signed int)floor(p->m_Param.m_dTrunkDalant);
    v23 = CPlayerDB::GetGold(&p->m_Param);
    v8 = CPlayerDB::GetDalant(&p->m_Param);
    CMgrAvatorItemHistory::trunk_io_money(
      &CPlayer::s_MgrItemHistory,
      p->m_ObjID.m_wIndex,
      v18,
      ui64AddMoney,
      ui64AddGold,
      dwFeeDalant,
      v8,
      v23,
      v22,
      v21,
      v20);
  }
  dwGolda = CPlayerDB::GetGold(&p->m_Param);
  v9 = CPlayerDB::GetDalant(&p->m_Param);
  dwInvenDalant = dwFeeDalant;
  dwPayDalant = dwGolda;
  dwIOGold = v9;
  CPlayer::SendMsg_TrunkIoMoneyResult(
    p,
    v14,
    p->m_Param.m_dTrunkDalant,
    p->m_Param.m_dTrunkGold,
    v9,
    dwGolda,
    dwFeeDalant);
}
