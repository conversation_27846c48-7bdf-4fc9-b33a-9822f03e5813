/*
 * Function: ?pc_UnitPackFillRequest@CPlayer@@QEAAXEEPEAU__list@_unit_pack_fill_request_clzo@@H@Z
 * Address: 0x140104F00
 */

void __usercall CPlayer::pc_UnitPackFillRequest(CPlayer *this@<rcx>, char by<PERSON>lotIndex@<dl>, char by<PERSON><PERSON><PERSON><PERSON>@<r8b>, _unit_pack_fill_request_clzo::__list *pList@<r9>, float a5@<xmm0>, int bUseNPCLinkIntem)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  unsigned int v8; // eax@32
  unsigned int v9; // eax@43
  CMoneySupplyMgr *v10; // rax@51
  CMoneySupplyMgr *v11; // rax@53
  __int64 v12; // [sp+0h] [bp-148h]@1
  char v13; // [sp+40h] [bp-108h]@4
  _UNIT_DB_BASE::_LIST *pData; // [sp+48h] [bp-100h]@4
  unsigned __int8 v15; // [sp+50h] [bp-F8h]@4
  _base_fld *v16; // [sp+58h] [bp-F0h]@4
  __int64 v17; // [sp+70h] [bp-D8h]@4
  char v18; // [sp+78h] [bp-D0h]@4
  unsigned int dwSub; // [sp+C8h] [bp-80h]@4
  unsigned int v20; // [sp+CCh] [bp-7Ch]@4
  unsigned int v21; // [sp+F4h] [bp-54h]@4
  float v22; // [sp+F8h] [bp-50h]@4
  int j; // [sp+FCh] [bp-4Ch]@16
  unsigned __int64 v24; // [sp+100h] [bp-48h]@32
  unsigned int *v25; // [sp+108h] [bp-40h]@39
  int v26; // [sp+110h] [bp-38h]@46
  __int64 v27; // [sp+118h] [bp-30h]@32
  char *v28; // [sp+120h] [bp-28h]@43
  unsigned int v29; // [sp+128h] [bp-20h]@43
  unsigned int nAmount; // [sp+12Ch] [bp-1Ch]@51
  int nLv; // [sp+130h] [bp-18h]@51
  int v32; // [sp+134h] [bp-14h]@53
  CPlayer *p; // [sp+150h] [bp+8h]@1
  char v34; // [sp+158h] [bp+10h]@1
  char v35; // [sp+160h] [bp+18h]@1
  _unit_pack_fill_request_clzo::__list *pLista; // [sp+168h] [bp+20h]@1

  pLista = pList;
  v35 = byFillNum;
  v34 = bySlotIndex;
  p = this;
  v6 = &v12;
  for ( i = 80i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v13 = 0;
  pData = &p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex];
  v15 = p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].byFrame;
  v16 = CRecordData::GetRecord(&stru_1799C86D0 + 5, p->m_Param.m_UnitDB.m_List[(unsigned __int8)bySlotIndex].byPart[5]);
  v17 = 0i64;
  memset(&v18, 0, 0x38ui64);
  dwSub = 0;
  memset(&v20, 0, 0x18ui64);
  v21 = eGetTexRate(0) + 10000;
  eGetTex(0);
  v22 = a5 + 1.0;
  if ( p->m_pUserDB )
  {
    if ( TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, p->m_id.wIndex) == 99 )
    {
      v13 = 34;
    }
    else if ( bUseNPCLinkIntem || IsBeNearStore(p, 4) )
    {
      if ( CPlayerDB::GetRaceCode(&p->m_Param) )
      {
        v13 = 1;
      }
      else if ( v15 == 255 )
      {
        v13 = 5;
      }
      else if ( v16 )
      {
        for ( j = 0; j < (unsigned __int8)v35; ++j )
        {
          if ( pLista[j].bySpareIndex >= (signed int)v16[6].m_dwIndex )
          {
            v13 = 18;
            goto LABEL_35;
          }
          *(&v17 + pLista[j].bySpareIndex) = (__int64)CRecordData::GetRecord(&stru_1799C8AF0, pLista[j].wBulletIndex);
          if ( !*(&v17 + pLista[j].bySpareIndex) )
          {
            v13 = 18;
            goto LABEL_35;
          }
        }
        if ( p->m_pUsingUnit )
        {
          v13 = 2;
        }
        else
        {
          for ( j = 0; j < 8; ++j )
          {
            if ( *(&v17 + j) )
              *(&dwSub + *(_DWORD *)(*(&v17 + j) + 212)) += *(_DWORD *)(*(&v17 + j) + 216);
          }
          for ( j = 0; j < 7; ++j )
          {
            v24 = *(&dwSub + j);
            v24 *= v21;
            *(&dwSub + j) = v24 / 0x2710;
            v27 = j;
            v8 = CPlayer::GetMoney(p, j);
            if ( *(&dwSub + v27) > v8 )
            {
              v13 = 7;
              break;
            }
          }
        }
      }
      else
      {
        v13 = 17;
      }
    }
    else
    {
      v13 = 21;
    }
LABEL_35:
    if ( !v13 )
    {
      for ( j = 0; j < 8; ++j )
      {
        if ( *(&v17 + j) )
        {
          v25 = &pData->dwSpare[j];
          *(_WORD *)v25 = *(_WORD *)*(&v17 + j);
          *((_WORD *)v25 + 1) = *(_WORD *)(*(&v17 + j) + 208);
        }
      }
      CPlayer::SubDalant(p, dwSub);
      CPlayer::SubGold(p, v20);
      CUserDB::Update_UnitData(p->m_pUserDB, v34, pData);
      if ( v20 || dwSub )
      {
        v28 = p->m_szItemHistoryFileName;
        v29 = CPlayerDB::GetDalant(&p->m_Param);
        v9 = CPlayerDB::GetGold(&p->m_Param);
        CMgrAvatorItemHistory::pay_money(
          &CPlayer::s_MgrItemHistory,
          p->m_ObjID.m_wIndex,
          "Unit_Spare_Charge",
          dwSub,
          v20,
          v9,
          v29,
          v28);
      }
      if ( !p->m_byUserDgr )
      {
        eAddGold(0, v20);
        eAddDalant(0, dwSub);
      }
      v26 = CPlayerDB::GetLevel(&p->m_Param);
      if ( v26 == 30 || v26 == 40 || v26 == 50 || v26 == 60 )
      {
        if ( v20 )
        {
          nAmount = 2000 * v20;
          nLv = CPlayerDB::GetLevel(&p->m_Param);
          v10 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateBuyUnitData(v10, nLv, nAmount);
        }
        if ( dwSub )
        {
          v32 = CPlayerDB::GetLevel(&p->m_Param);
          v11 = CMoneySupplyMgr::Instance();
          CMoneySupplyMgr::UpdateBuyUnitData(v11, v32, dwSub);
        }
      }
    }
    CPlayer::SendMsg_UnitPackFillResult(p, v13, v34, v35, pLista, &dwSub);
  }
}
