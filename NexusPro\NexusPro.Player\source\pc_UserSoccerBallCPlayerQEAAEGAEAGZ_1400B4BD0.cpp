/*
 * Function: ?pc_UserSoccerBall@CPlayer@@QEAAEGAEAG@Z
 * Address: 0x1400B4BD0
 */

char __fastcall CPlayer::pc_UserSoccerBall(CPlayer *this, unsigned __int16 wItemSerial, unsigned __int16 *wItemIndex)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@6
  CPlayer::CashChangeStateFlag *v6; // rax@30
  __int64 v7; // [sp+0h] [bp-48h]@1
  _STORAGE_LIST::_db_con *v8; // [sp+20h] [bp-28h]@19
  _base_fld *v9; // [sp+28h] [bp-20h]@23
  CPlayer::CashChangeStateFlag v10; // [sp+30h] [bp-18h]@30
  int v11; // [sp+34h] [bp-14h]@27
  _STORAGE_LIST::_db_con *v12; // [sp+38h] [bp-10h]@28
  CPlayer *v13; // [sp+50h] [bp+8h]@1
  unsigned __int16 v14; // [sp+58h] [bp+10h]@1
  unsigned __int16 *v15; // [sp+60h] [bp+18h]@1

  v15 = wItemIndex;
  v14 = wItemSerial;
  v13 = this;
  v3 = &v7;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  if ( CGameObject::GetCurSecNum((CGameObject *)&v13->vfptr) == -1 || v13->m_bMapLoading )
  {
    result = 1;
  }
  else if ( v13->m_bCorpse )
  {
    result = 2;
  }
  else if ( v13->m_byStandType == 1 )
  {
    result = 3;
  }
  else if ( CPlayer::IsSiegeMode(v13) )
  {
    result = 4;
  }
  else if ( CPlayer::IsRidingUnit(v13) )
  {
    result = 5;
  }
  else if ( _effect_parameter::GetEff_State(&v13->m_EP, 20) )
  {
    result = 6;
  }
  else if ( _effect_parameter::GetEff_State(&v13->m_EP, 28) )
  {
    result = 6;
  }
  else
  {
    v8 = _STORAGE_LIST::GetPtrFromSerial((_STORAGE_LIST *)&v13->m_Param.m_dbInven.m_nListNum, v14);
    if ( v8 )
    {
      if ( v8->m_byTableCode == 27 )
      {
        v9 = CRecordData::GetRecord((CRecordData *)&unk_1799C6AA0 + 27, v8->m_wItemIndex);
        if ( v9 )
        {
          if ( v8->m_bLock )
          {
            result = 8;
          }
          else
          {
            v11 = v13->m_bTakeSoccerBall == 0;
            v13->m_bTakeSoccerBall = v11;
            *v15 = v8->m_wItemIndex;
            if ( v13->m_bTakeSoccerBall )
              v12 = v8;
            else
              v12 = 0i64;
            v13->m_pSoccerItem = v12;
            CPlayer::CashChangeStateFlag::CashChangeStateFlag(&v10, 0);
            CPlayer::UpdateVisualVer(v13, (CPlayer::CashChangeStateFlag)v6->0);
            result = 0;
          }
        }
        else
        {
          result = 7;
        }
      }
      else
      {
        result = 7;
      }
    }
    else
    {
      CPlayer::SendMsg_AdjustAmountInform(v13, 0, v14, 0);
      result = 7;
    }
  }
  return result;
}
