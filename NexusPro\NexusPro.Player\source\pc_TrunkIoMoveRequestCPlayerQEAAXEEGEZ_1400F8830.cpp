/*
 * Function: ?pc_TrunkIoMoveRequest@CPlayer@@QEAAXEEGE@Z
 * Address: 0x1400F8830
 */

void __fastcall CPlayer::pc_TrunkIoMoveRequest(CPlayer *this, char byStartStorageIndex, char byTarStorageIndex, unsigned __int16 wItemSerial, char byClientSlotIndex)
{
  __int64 *v5; // rdi@1
  signed __int64 i; // rcx@1
  char v7; // al@11
  char v8; // al@14
  int v9; // eax@50
  int v10; // ecx@50
  int v11; // eax@52
  unsigned int v12; // eax@53
  int v13; // eax@56
  int v14; // eax@59
  CMoneySupplyMgr *v15; // rax@74
  unsigned int v16; // eax@78
  __int64 v17; // [sp+0h] [bp-118h]@1
  bool bDelete[4]; // [sp+20h] [bp-F8h]@62
  char *strErrorCodePos; // [sp+28h] [bp-F0h]@62
  char v20; // [sp+40h] [bp-D8h]@4
  unsigned int dwConsumDanlant; // [sp+44h] [bp-D4h]@4
  _STORAGE_LIST::_db_con *pFixingItem; // [sp+48h] [bp-D0h]@4
  char *v23; // [sp+50h] [bp-C8h]@29
  int v24; // [sp+58h] [bp-C0h]@38
  int j; // [sp+5Ch] [bp-BCh]@38
  unsigned int v26; // [sp+60h] [bp-B8h]@52
  unsigned __int64 v27; // [sp+68h] [bp-B0h]@52
  char v28; // [sp+70h] [bp-A8h]@56
  char v29; // [sp+71h] [bp-A7h]@59
  _STORAGE_LIST::_db_con Dst; // [sp+88h] [bp-90h]@62
  unsigned int dwLeftDalant; // [sp+C4h] [bp-54h]@63
  _STORAGE_LIST::_db_con *v32; // [sp+C8h] [bp-50h]@64
  unsigned int v33; // [sp+D0h] [bp-48h]@65
  int v34; // [sp+D4h] [bp-44h]@70
  bool v35; // [sp+D8h] [bp-40h]@75
  unsigned int v36; // [sp+DCh] [bp-3Ch]@79
  int v37; // [sp+E0h] [bp-38h]@11
  int v38; // [sp+E4h] [bp-34h]@14
  int nTableCode; // [sp+E8h] [bp-30h]@50
  int v40; // [sp+ECh] [bp-2Ch]@56
  int v41; // [sp+F0h] [bp-28h]@59
  int nLv; // [sp+F4h] [bp-24h]@74
  int v43; // [sp+F8h] [bp-20h]@74
  char *v44; // [sp+100h] [bp-18h]@78
  CPlayer *p; // [sp+120h] [bp+8h]@1
  char v46; // [sp+128h] [bp+10h]@1
  char v47; // [sp+130h] [bp+18h]@1
  unsigned __int16 v48; // [sp+138h] [bp+20h]@1

  v48 = wItemSerial;
  v47 = byTarStorageIndex;
  v46 = byStartStorageIndex;
  p = this;
  v5 = &v17;
  for ( i = 68i64; i; --i )
  {
    *(_DWORD *)v5 = -858993460;
    v5 = (__int64 *)((char *)v5 + 4);
  }
  v20 = 0;
  dwConsumDanlant = 0;
  pFixingItem = 0i64;
  if ( !IsBeNearStore(p, 10) )
  {
    v20 = 13;
    goto $RESULT_85;
  }
  if ( !p->m_Param.m_bTrunkOpen )
  {
    v20 = 14;
    goto $RESULT_85;
  }
  if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&p->m_Param) <= 0 )
  {
    v20 = 2;
    goto $RESULT_85;
  }
  if ( v47 == 5 )
  {
    v37 = (unsigned __int8)byClientSlotIndex;
    v7 = CPlayerDB::GetTrunkSlotNum(&p->m_Param);
    if ( v37 >= (unsigned __int8)v7 )
    {
      v20 = 8;
      goto $RESULT_85;
    }
  }
  if ( v47 == 7 )
  {
    v38 = (unsigned __int8)byClientSlotIndex;
    v8 = CPlayerDB::GetExtTrunkSlotNum(&p->m_Param);
    if ( v38 >= (unsigned __int8)v8 )
    {
      v20 = 8;
      goto $RESULT_85;
    }
  }
  if ( _STORAGE_LIST::GetNumEmptyCon(p->m_Param.m_pStoragePtr[(unsigned __int8)v47]) <= 0 )
  {
    v20 = 8;
    goto $RESULT_85;
  }
  pFixingItem = _STORAGE_LIST::GetPtrFromSerial(p->m_Param.m_pStoragePtr[(unsigned __int8)v46], v48);
  if ( !pFixingItem )
  {
    v20 = 9;
    goto $RESULT_85;
  }
  if ( pFixingItem->m_bLock )
  {
    v20 = 10;
    goto $RESULT_85;
  }
  if ( (v47 == 5 || v47 == 7) && !IsTrunkIOAble(pFixingItem->m_byTableCode, pFixingItem->m_wItemIndex) )
  {
    v20 = 18;
    goto $RESULT_85;
  }
  if ( v47 == 1 )
  {
    if ( pFixingItem->m_byTableCode >= 8 )
    {
      v20 = 44;
      goto $RESULT_85;
    }
    v23 = &p->m_Param.m_dbEquip.m_pStorageList[pFixingItem->m_byTableCode].m_bLoad;
    if ( pFixingItem->m_bLoad )
    {
      v20 = 44;
      goto $RESULT_85;
    }
    if ( !CPlayer::_check_equip_part(p, pFixingItem) )
    {
      v20 = 44;
      goto $RESULT_85;
    }
  }
  if ( v47 != 2 )
    goto LABEL_82;
  if ( pFixingItem->m_byTableCode != 8 && pFixingItem->m_byTableCode != 9 && pFixingItem->m_byTableCode != 10 )
  {
    v20 = 44;
    goto $RESULT_85;
  }
  v24 = 0;
  for ( j = 0; j < 7; ++j )
  {
    if ( p->m_Param.m_dbEmbellish.m_pStorageList[j].m_bLoad
      && p->m_Param.m_dbEmbellish.m_pStorageList[j].m_byTableCode == pFixingItem->m_byTableCode )
    {
      ++v24;
    }
  }
  if ( v24 > 1 )
  {
    v20 = 44;
    goto $RESULT_85;
  }
  if ( CPlayer::_check_embel_part(p, pFixingItem) )
  {
LABEL_82:
    if ( v46 == 5 || v46 == 7 )
    {
      v9 = CPlayerDB::GetRaceCode(&p->m_Param);
      v10 = pFixingItem->m_wItemIndex;
      nTableCode = pFixingItem->m_byTableCode;
      dwConsumDanlant = GetItemStoragePrice(nTableCode, v10, v9);
      if ( IsOverLapItem(pFixingItem->m_byTableCode) )
        dwConsumDanlant *= LODWORD(pFixingItem->m_dwDur);
      v11 = CPlayerDB::GetRaceCode(&p->m_Param);
      v26 = eGetTexRate(v11) + 10000;
      v27 = v26 * (unsigned __int64)dwConsumDanlant;
      dwConsumDanlant = v27 / 0x2710;
    }
    v12 = CPlayerDB::GetDalant(&p->m_Param);
    if ( v12 >= dwConsumDanlant )
    {
      if ( v46 != 5
        || (v28 = CPlayerDB::GetTrunkSlotRace(&p->m_Param, v48),
            v40 = (unsigned __int8)v28,
            v13 = CPlayerDB::GetRaceCode(&p->m_Param),
            v40 == v13) )
      {
        if ( v46 == 7 )
        {
          v29 = CPlayerDB::GetExtTrunkSlotRace(&p->m_Param, v48);
          v41 = (unsigned __int8)v29;
          v14 = CPlayerDB::GetRaceCode(&p->m_Param);
          if ( v41 != v14 )
            v20 = 17;
        }
      }
      else
      {
        v20 = 17;
      }
    }
    else
    {
      v20 = 6;
    }
  }
  else
  {
    v20 = 44;
  }
$RESULT_85:
  if ( !v20 )
  {
    _STORAGE_LIST::_db_con::_db_con(&Dst);
    memcpy_0(&Dst, pFixingItem, 0x32ui64);
    strErrorCodePos = "CPlayer::pc_TrunkIoMoveRequest()";
    bDelete[0] = 0;
    if ( !CPlayer::Emb_DelStorage(p, v46, pFixingItem->m_byStorageIndex, 0, 0, "CPlayer::pc_TrunkIoMoveRequest()") )
    {
      dwLeftDalant = CPlayerDB::GetDalant(&p->m_Param);
      CPlayer::SendMsg_TrunkIoResult(p, 0, 18, dwLeftDalant, dwConsumDanlant);
      return;
    }
    v32 = CPlayer::Emb_AddStorage(p, v47, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 0);
    if ( !v32 )
    {
      CPlayer::Emb_AddStorage(p, v46, (_STORAGE_LIST::_storage_con *)&Dst.m_bLoad, 0, 0);
      v33 = CPlayerDB::GetDalant(&p->m_Param);
      CPlayer::SendMsg_TrunkIoResult(p, 0, 18, v33, dwConsumDanlant);
      return;
    }
    v32->m_byClientIndex = byClientSlotIndex;
    if ( v47 == 5 || v47 == 7 )
      CUserDB::Update_ItemSlot(p->m_pUserDB, v47, v32->m_byStorageIndex, byClientSlotIndex);
    if ( dwConsumDanlant )
    {
      CPlayer::SubDalant(p, dwConsumDanlant);
      v34 = CPlayerDB::GetLevel(&p->m_Param);
      if ( v34 == 30 || v34 == 40 || v34 == 50 || v34 == 60 )
      {
        nLv = CPlayerDB::GetLevel(&p->m_Param);
        v43 = CPlayerDB::GetRaceCode(&p->m_Param);
        v15 = CMoneySupplyMgr::Instance();
        CMoneySupplyMgr::UpdateFeeMoneyData(v15, v43, nLv, dwConsumDanlant);
      }
    }
    v35 = 1;
    if ( v46 == 5 || v46 == 7 )
      v35 = 0;
    v44 = p->m_szItemHistoryFileName;
    v16 = CPlayerDB::GetDalant(&p->m_Param);
    CMgrAvatorItemHistory::trunk_io_item(
      &CPlayer::s_MgrItemHistory,
      p->m_ObjID.m_wIndex,
      pFixingItem,
      v35,
      dwConsumDanlant,
      v16,
      v44);
  }
  v36 = CPlayerDB::GetDalant(&p->m_Param);
  CPlayer::SendMsg_TrunkIoResult(p, 0, v20, v36, dwConsumDanlant);
}
