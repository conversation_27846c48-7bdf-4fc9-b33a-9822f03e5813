/*
 * Function: ?pc_SelectClassRequest@CPlayer@@QEAAXGE@Z
 * Address: 0x1400951C0
 */

void __fastcall CPlayer::pc_SelectClassRequest(CPlayer *this, unsigned __int16 wSelClassIndex, char bySelectRewardItem)
{
  __int64 *v3; // rdi@1
  signed __int64 i; // rcx@1
  int v5; // eax@28
  CNationSettingManager *v6; // rax@41
  char *v7; // rdx@51
  CUserDB *v8; // rdi@51
  __int64 v9; // [sp+0h] [bp-F8h]@1
  char *szCurClass; // [sp+20h] [bp-D8h]@51
  int *piOldMaxPoint; // [sp+28h] [bp-D0h]@51
  int *piAlterMaxPoint; // [sp+30h] [bp-C8h]@51
  char *pszFileName; // [sp+38h] [bp-C0h]@51
  char v14; // [sp+40h] [bp-B8h]@4
  _class_fld *v15; // [sp+48h] [bp-B0h]@4
  _class_fld *pClassFld; // [sp+50h] [bp-A8h]@4
  char v17; // [sp+58h] [bp-A0h]@4
  unsigned __int8 v18; // [sp+59h] [bp-9Fh]@4
  int j; // [sp+5Ch] [bp-9Ch]@9
  char v20; // [sp+60h] [bp-98h]@4
  int v21; // [sp+64h] [bp-94h]@28
  char *Str1; // [sp+68h] [bp-90h]@33
  char Dst; // [sp+74h] [bp-84h]@38
  int v24; // [sp+98h] [bp-60h]@38
  int v25; // [sp+9Ch] [bp-5Ch]@38
  int v26; // [sp+A0h] [bp-58h]@38
  char v27; // [sp+A4h] [bp-54h]@38
  __int64 v28; // [sp+B8h] [bp-40h]@44
  int v29; // [sp+C8h] [bp-30h]@24
  float *fUnitPv_DefFc; // [sp+D0h] [bp-28h]@41
  int v31; // [sp+D8h] [bp-20h]@51
  unsigned __int64 v32; // [sp+E0h] [bp-18h]@4
  CPlayer *v33; // [sp+100h] [bp+8h]@1
  unsigned __int16 v34; // [sp+108h] [bp+10h]@1
  char v35; // [sp+110h] [bp+18h]@1

  v35 = bySelectRewardItem;
  v34 = wSelClassIndex;
  v33 = this;
  v3 = &v9;
  for ( i = 60i64; i; --i )
  {
    *(_DWORD *)v3 = -858993460;
    v3 = (__int64 *)((char *)v3 + 4);
  }
  v32 = (unsigned __int64)&v9 ^ _security_cookie;
  v14 = 0;
  v15 = v33->m_Param.m_pClassData;
  pClassFld = (_class_fld *)CRecordData::GetRecord(&stru_1799C6420, wSelClassIndex);
  v17 = -1;
  v18 = -1;
  v20 = 0;
  if ( v33->m_pUserDB )
  {
    if ( CPlayerDB::IsClassChangeableLv(&v33->m_Param) )
    {
      if ( pClassFld )
      {
        for ( j = 0; j < 8 && strncmp(v15->m_strCh_Class[(signed __int64)j], "-1", 2ui64); ++j )
        {
          if ( !strcmp_0(v15->m_strCh_Class[(signed __int64)j], pClassFld->m_strCode) )
          {
            v18 = j;
            break;
          }
        }
        if ( v18 == 255 )
        {
          v14 = 2;
        }
        else
        {
          for ( j = 0; j < 3; ++j )
          {
            if ( !v33->m_Param.m_pClassHistory[j] )
            {
              v17 = j;
              break;
            }
          }
          if ( (unsigned __int8)v17 == 255 )
          {
            v14 = 3;
          }
          else
          {
            v29 = (unsigned __int8)v17 + 1 > v33->m_pUserDB->m_AvatorData.dbAvator.m_byLastClassGrade;
            v20 = v29;
            if ( (_BYTE)v29 )
            {
              if ( (unsigned __int8)v35 == 255 )
              {
                if ( pClassFld->m_bSelectRewardItem )
                {
                  v14 = 4;
                }
                else
                {
                  v21 = GetRewardItemNumChangeClass(pClassFld);
                  v5 = _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&v33->m_Param.m_dbInven.m_nListNum);
                  if ( v5 < v21 )
                    v14 = 5;
                }
              }
              else if ( pClassFld->m_bSelectRewardItem )
              {
                Str1 = pClassFld->m_DefaultItem[(unsigned __int8)v35].strDefaultItem;
                if ( !strncmp(Str1, "0", 1ui64) )
                {
                  v14 = 4;
                }
                else if ( _STORAGE_LIST::GetNumEmptyCon((_STORAGE_LIST *)&v33->m_Param.m_dbInven.m_nListNum) < 1 )
                {
                  v14 = 5;
                }
              }
              else
              {
                v14 = 4;
              }
            }
          }
        }
      }
      else
      {
        v14 = 2;
      }
    }
    else
    {
      v14 = 1;
    }
    if ( !v14 )
    {
      memcpy_0(&Dst, v33->m_pUserDB->m_AvatorData.dbAvator.m_szClassCode, 5ui64);
      v24 = v33->m_nMaxPoint[0];
      v25 = v33->m_nMaxPoint[1];
      v26 = v33->m_nMaxPoint[2];
      memset(&v27, 0, 4ui64);
      CPlayerDB::SelectClass(&v33->m_Param, v17, pClassFld);
      if ( v33->m_Param.m_pClassData->m_dwIndex == 49
        && v33->m_Param.m_pClassHistory[0]
        && v33->m_Param.m_pClassHistory[0]->m_dwIndex == 45 )
      {
        v33->m_fUnitPv_AttFc = FLOAT_1_1;
        fUnitPv_DefFc = &v33->m_fUnitPv_DefFc;
        v6 = CTSingleton<CNationSettingManager>::Instance();
        CNationSettingManager::SetUnitPassiveValue(v6, fUnitPv_DefFc);
        v33->m_fUnitPv_RepPr = FLOAT_0_60000002;
      }
      CPlayer::CalcAddPointByClass(v33);
      v33->m_nAddDfnMstByClass = 0;
      for ( j = 0; j < 4; ++j )
      {
        v28 = (__int64)*v33->m_Param.m_ppHistoryEffect[j];
        if ( !v28 )
          break;
        v33->m_nAddDfnMstByClass += *(_DWORD *)(v28 + 1476);
      }
      if ( v20 && TimeLimitMgr::GetPlayerStatus(qword_1799CA2D0, v33->m_id.wIndex) != 99 )
      {
        CPlayer::RewardChangeClassMastery(v33, pClassFld);
        CPlayer::RewardChangeClassRewardItem(v33, pClassFld, v35);
      }
      CPlayer::ReCalcMaxHFSP(v33, 0, 0);
      CUserDB::Update_Class(
        v33->m_pUserDB,
        pClassFld->m_strCode,
        v17,
        v33->m_Param.m_pClassHistory[(unsigned __int8)v17]->m_dwIndex);
      if ( v20 )
        CPlayer::Emb_CreateQuestEvent(v33, quest_happen_type_class, pClassFld->m_strCode);
      v7 = v33->m_pUserDB->m_AvatorData.dbAvator.m_szClassCode;
      v8 = v33->m_pUserDB;
      v31 = (unsigned __int8)v17 + 1;
      pszFileName = v33->m_szItemHistoryFileName;
      piAlterMaxPoint = v33->m_nMaxPoint;
      piOldMaxPoint = &v24;
      szCurClass = v7;
      CMgrAvatorItemHistory::ClassUP(
        &CPlayer::s_MgrItemHistory,
        v17 + 1,
        v8->m_AvatorData.dbAvator.m_byLastClassGrade,
        &Dst,
        v7,
        &v24,
        v33->m_nMaxPoint,
        v33->m_szItemHistoryFileName);
      CUserDB::WriteLog_ChangeClassAfterInitClass(v33->m_pUserDB, 1, &Dst);
      v33->m_bUpCheckEquipEffect = 1;
    }
    CPlayer::SendMsg_SelectClassResult(v33, v14, v34);
  }
}
