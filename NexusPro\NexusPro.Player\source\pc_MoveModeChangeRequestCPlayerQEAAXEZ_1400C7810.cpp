/*
 * Function: ?pc_MoveModeChangeRequest@CPlayer@@QEAAXE@Z
 * Address: 0x1400C7810
 */

void __fastcall CPlayer::pc_MoveModeChangeRequest(CPlayer *this, char byMoveType)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v4; // [sp+0h] [bp-28h]@1
  CPlayer *v5; // [sp+30h] [bp+8h]@1
  char v6; // [sp+38h] [bp+10h]@1

  v6 = byMoveType;
  v5 = this;
  v2 = &v4;
  for ( i = 8i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( byMoveType != 2 )
    CPlayer::BreakCloakBooster(v5);
  v5->m_byMoveType = v6;
  CPlayer::SenseState(v5);
}
