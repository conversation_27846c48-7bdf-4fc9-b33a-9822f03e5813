/*
 * Function: ?_<PERSON><PERSON><PERSON><PERSON>@CPlayer@@QEAAXE@Z
 * Address: 0x140106DE0
 */

void __fastcall CPlayer::_UnitDestroy(CPlayer *this, char byUnitSlot)
{
  __int64 *v2; // rdi@1
  signed __int64 i; // rcx@1
  int v4; // ecx@9
  __int64 v5; // [sp+0h] [bp-48h]@1
  char *pszFileName; // [sp+20h] [bp-28h]@9
  _UNIT_DB_BASE::_LIST *pData; // [sp+30h] [bp-18h]@5
  _base_fld *v8; // [sp+38h] [bp-10h]@6
  CPlayer *v9; // [sp+50h] [bp+8h]@1
  char v10; // [sp+58h] [bp+10h]@1

  v10 = byUnitSlot;
  v9 = this;
  v2 = &v5;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v2 = -858993460;
    v2 = (__int64 *)((char *)v2 + 4);
  }
  if ( (signed int)(unsigned __int8)byUnitSlot < 4 )
  {
    pData = &v9->m_Param.m_UnitDB.m_List[(unsigned __int8)byUnitSlot];
    if ( v9->m_Param.m_UnitDB.m_List[(unsigned __int8)byUnitSlot].byFrame != 255 )
    {
      v8 = CRecordData::GetRecord(&stru_1799C8BA0, pData->byFrame);
      if ( v8 && (!*(_DWORD *)&v8[1].m_strCode[60] || *(_DWORD *)&v8[1].m_strCode[56]) )
      {
        v4 = v9->m_ObjID.m_wIndex;
        pszFileName = v9->m_szItemHistoryFileName;
        CMgrAvatorItemHistory::destroy_unit(
          &CPlayer::s_MgrItemHistory,
          v4,
          v10,
          pData->byFrame,
          v9->m_szItemHistoryFileName);
        _UNIT_DB_BASE::_LIST::Init(pData, -1);
        CPlayer::_DeleteUnitKey(v9, v10);
        if ( v9->m_pUserDB )
          CUserDB::Update_UnitDelete(v9->m_pUserDB, v10);
      }
      else
      {
        pData->dwGauge = 0;
        CUserDB::Update_UnitData(v9->m_pUserDB, v10, pData);
        CPlayer::_LockUnitKey(v9, v9->m_pUsingUnit->bySlotIndex, 0);
      }
      if ( v9->m_pUsingUnit == pData )
      {
        v9->m_pUsingUnit = 0i64;
        v9->m_pParkingUnit = 0i64;
      }
      CPlayer::SendMsg_UnitDestroy(v9, v10);
    }
  }
}
