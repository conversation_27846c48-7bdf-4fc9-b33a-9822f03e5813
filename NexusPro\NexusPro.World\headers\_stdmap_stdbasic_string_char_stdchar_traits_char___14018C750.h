#pragma once
#ifndef _STDMAP_STDBASIC_STRING_CHAR_STDCHAR_TRAITS_CHAR___14018C750_H
#define _STDMAP_STDBASIC_STRING_CHAR_STDCHAR_TRAITS_CHAR___14018C750_H

// Auto-generated header for _stdmap_stdbasic_string_char_stdchar_traits_char___14018C750.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_5(__int64 a1, __int64 a2)
;

#endif // _STDMAP_STDBASIC_STRING_CHAR_STDCHAR_TRAITS_CHAR___14018C750_H
