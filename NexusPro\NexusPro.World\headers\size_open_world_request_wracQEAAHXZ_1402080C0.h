#pragma once
#ifndef SIZE_OPEN_WORLD_REQUEST_WRACQEAAHXZ_1402080C0_H
#define SIZE_OPEN_WORLD_REQUEST_WRACQEAAHXZ_1402080C0_H

// Auto-generated header for size_open_world_request_wracQEAAHXZ_1402080C0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_open_world_request_wrac::size(_open_world_request_wrac *this)
;

#endif // SIZE_OPEN_WORLD_REQUEST_WRACQEAAHXZ_1402080C0_H
