/*
 * Function: ?_buybygold_check_coupon@CashItemRemoteStore@@AEAA?AW4CS_RCODE@@PEAVCPlayer@@PEAU_request_csi_buy_clzo@@PEAU_param_cashitem_dblog@@@Z
 * Address: 0x1402FF190
 */

signed __int64 __fastcall CashItemRemoteStore::_buybygold_check_coupon(CashItemRemoteStore *this, CPlayer *pOne, _request_csi_buy_clzo *pRecv, _param_cashitem_dblog *pSheet)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v7; // [sp+0h] [bp-88h]@1
  bool *bCheck; // [sp+20h] [bp-68h]@11
  int v9; // [sp+30h] [bp-58h]@7
  bool v10; // [sp+48h] [bp-40h]@9
  char v11; // [sp+49h] [bp-3Fh]@9
  int j; // [sp+64h] [bp-24h]@9
  unsigned __int64 v13; // [sp+70h] [bp-18h]@4
  CashItemRemoteStore *v14; // [sp+90h] [bp+8h]@1
  CPlayer *pOnea; // [sp+98h] [bp+10h]@1
  _request_csi_buy_clzo *pBuyList; // [sp+A0h] [bp+18h]@1
  _param_cashitem_dblog *v17; // [sp+A8h] [bp+20h]@1

  v17 = pSheet;
  pBuyList = pRecv;
  pOnea = pOne;
  v14 = this;
  v4 = &v7;
  for ( i = 32i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v13 = (unsigned __int64)&v7 ^ _security_cookie;
  if ( pRecv->byCouponNum > 0 )
  {
    if ( __PAIR__(pSheet->in_bOneN_One, pSheet->in_bAdjustDiscount) )
      return 20i64;
    v9 = CashItemRemoteStore::CheckCouponType(v14, pRecv->CouponItem, pOne, pRecv->byCouponNum);
    if ( v9 <= 0 )
      return 19i64;
    v10 = 0;
    memset(&v11, 0, 0x13ui64);
    for ( j = 0; j < pBuyList->byCouponNum; ++j )
    {
      bCheck = &v10;
      if ( !CashItemRemoteStore::IsUsableCoupon(v14, pBuyList, pBuyList->CouponItem[j], pOnea, &v10) )
        return 19i64;
      *(_DWORD *)&v17->in_CouponItem[j].byStorageCode = *(_DWORD *)&pBuyList->CouponItem[j].byStorageCode;
    }
  }
  return 0i64;
}
