#pragma once
#ifndef SIZE_STOP_WORLD_REQUEST_WRACQEAAHXZ_1402080E0_H
#define SIZE_STOP_WORLD_REQUEST_WRACQEAAHXZ_1402080E0_H

// Auto-generated header for size_stop_world_request_wracQEAAHXZ_1402080E0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_stop_world_request_wrac::size(_stop_world_request_wrac *this)
;

#endif // SIZE_STOP_WORLD_REQUEST_WRACQEAAHXZ_1402080E0_H
