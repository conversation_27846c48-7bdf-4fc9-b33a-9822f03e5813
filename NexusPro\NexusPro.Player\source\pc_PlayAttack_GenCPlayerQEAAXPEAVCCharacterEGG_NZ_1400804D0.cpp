/*
 * Function: ?pc_PlayAttack_Gen@CPlayer@@QEAAXPEAVCCharacter@@EGG_N@Z
 * Address: 0x1400804D0
 */

void __fastcall CPlayer::pc_PlayAttack_Gen(CPlayer *this, CCharacter *pDst, char by<PERSON><PERSON><PERSON><PERSON>, unsigned __int16 wBulletSerial, unsigned __int16 wEffBtSerial, bool bCount)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  float v8; // xmm0_4@15
  int v9; // eax@26
  unsigned int v10; // eax@26
  unsigned __int64 v11; // rdx@46
  int v12; // eax@49
  CCharacter *v13; // rdx@50
  int v14; // eax@50
  CCharacter *v15; // rdx@51
  int v16; // eax@51
  int v17; // eax@55
  signed int v18; // eax@56
  int v19; // eax@57
  int v20; // eax@57
  __int64 v21; // [sp+0h] [bp-5B8h]@1
  _STORAGE_LIST::_db_con **ppBulletProp; // [sp+20h] [bp-598h]@49
  _BulletItem_fld **ppfldBullet; // [sp+28h] [bp-590h]@49
  _BulletItem_fld *pEffBtFld; // [sp+30h] [bp-588h]@49
  _STORAGE_LIST::_db_con **ppEffBtProp; // [sp+38h] [bp-580h]@49
  _STORAGE_LIST::_db_con *pItem; // [sp+58h] [bp-560h]@4
  _BulletItem_fld *pBulletFld; // [sp+78h] [bp-540h]@4
  _STORAGE_LIST::_db_con *v28; // [sp+98h] [bp-520h]@4
  _BulletItem_fld *v29; // [sp+B8h] [bp-500h]@4
  float v30; // [sp+C4h] [bp-4F4h]@4
  char v31; // [sp+C8h] [bp-4F0h]@6
  float v32; // [sp+CCh] [bp-4ECh]@10
  __int16 v33; // [sp+D0h] [bp-4E8h]@10
  CAttack pAt; // [sp+F0h] [bp-4C8h]@12
  _attack_param pAP; // [sp+400h] [bp-1B8h]@12
  bool v36; // [sp+484h] [bp-134h]@19
  int v37; // [sp+488h] [bp-130h]@24
  unsigned int delay; // [sp+48Ch] [bp-12Ch]@26
  CPartyModeKillMonsterExpNotify kPartyExpNotify; // [sp+4A0h] [bp-118h]@40
  int nTotalDam; // [sp+534h] [bp-84h]@41
  int v41; // [sp+538h] [bp-80h]@46
  int v42; // [sp+53Ch] [bp-7Ch]@46
  int j; // [sp+540h] [bp-78h]@46
  int v44; // [sp+544h] [bp-74h]@49
  CPlayer *v45; // [sp+548h] [bp-70h]@53
  unsigned int dwAlter; // [sp+550h] [bp-68h]@61
  unsigned __int16 v47; // [sp+554h] [bp-64h]@73
  unsigned __int16 v48; // [sp+558h] [bp-60h]@78
  __int64 v49; // [sp+560h] [bp-58h]@4
  int nAddDelay; // [sp+568h] [bp-50h]@26
  CGameObjectVtbl *v51; // [sp+570h] [bp-48h]@26
  CCharacter *v52; // [sp+578h] [bp-40h]@49
  CGameObjectVtbl *v53; // [sp+580h] [bp-38h]@49
  int v54; // [sp+588h] [bp-30h]@50
  CGameObjectVtbl *v55; // [sp+590h] [bp-28h]@50
  int v56; // [sp+598h] [bp-20h]@51
  CGameObjectVtbl *v57; // [sp+5A0h] [bp-18h]@51
  float v58; // [sp+5A8h] [bp-10h]@56
  CPlayer *v59; // [sp+5C0h] [bp+8h]@1
  CCharacter *pDsta; // [sp+5C8h] [bp+10h]@1
  char v61; // [sp+5D0h] [bp+18h]@1
  unsigned __int16 v62; // [sp+5D8h] [bp+20h]@1
  unsigned __int16 wEffBtSeriala; // [sp+5E0h] [bp+28h]@14

  v62 = wBulletSerial;
  v61 = byAttPart;
  pDsta = pDst;
  v59 = this;
  v6 = &v21;
  for ( i = 364i64; i; --i )
  {
    *(_DWORD *)v6 = -858993460;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  v49 = -2i64;
  pItem = 0i64;
  pBulletFld = 0i64;
  v28 = 0i64;
  v29 = 0i64;
  v30 = FLOAT_1_0;
  if ( !bCount || !v59->m_pmWpn.byWpClass )
  {
    v31 = CPlayer::_pre_check_normal_attack(
            v59,
            pDst,
            wBulletSerial,
            bCount,
            &pItem,
            &pBulletFld,
            wEffBtSerial,
            &v28,
            &v29);
    if ( v31 )
    {
      CPlayer::SendMsg_AttackResult_Error(v59, v31);
      if ( v59->m_bMove )
      {
        CCharacter::Stop((CCharacter *)&v59->vfptr);
        CGameObject::SendMsg_BreakStop((CGameObject *)&v59->vfptr);
      }
      return;
    }
    v32 = FLOAT_1_0;
    v33 = -1;
    if ( pItem )
    {
      v32 = pBulletFld->m_fGAAF;
      v33 = pItem->m_wItemIndex;
    }
    CAttack::CAttack(&pAt, (CCharacter *)&v59->vfptr);
    _attack_param::_attack_param(&pAP);
    if ( v28 && v29 )
    {
      v30 = v29->m_fGAAF;
      wEffBtSeriala = v28->m_wItemIndex;
    }
    v8 = v32;
    CPlayer::make_gen_attack_param(v59, pDsta, v61, pBulletFld, v32, &pAP, v29, v30);
    if ( v28 && v29 )
      CAttack::AttackGen(&pAt, &pAP, 0, 1);
    else
      CAttack::AttackGen(&pAt, &pAP, 0, 0);
    v36 = 0;
    if ( pAt.m_DamList[0].m_nDamage > 0 && v59->m_pmWpn.nActiveType > -1 && rand() % 100 < v59->m_pmWpn.nActiveProb )
      v36 = CPlayer::WeaponSFActive(v59, pAt.m_DamList, &pAt.m_nDamagedObjNum, &pAP.nShotNum, v62);
    CAttack::SetActiveSucc(&pAt, v36);
    if ( !bCount )
    {
      v37 = 0;
      if ( v59->m_pmWpn.byWpType != 7 )
      {
        _effect_parameter::GetEff_Plus(&v59->m_EP, v59->m_pmWpn.byWpClass + 9);
        v37 = (signed int)ffloor(v8);
      }
      nAddDelay = CPlayer::CalcEquipAttackDelay(v59);
      v51 = v59->vfptr;
      v9 = ((int (__fastcall *)(CPlayer *))v51->GetLevel)(v59);
      v10 = _WEAPON_PARAM::GetAttackDelay(&v59->m_pmWpn, v9, nAddDelay);
      delay = v37 + v10;
      if ( v59->m_pmWpn.byWpType == 7 )
      {
        _effect_parameter::GetEff_Plus(&v59->m_EP, 11);
        delay = (signed int)ffloor((float)(signed int)delay + v8);
      }
      _ATTACK_DELAY_CHECKER::SetDelay(&v59->m_AttDelayChker, delay);
    }
    if ( _effect_parameter::GetEff_State(&v59->m_EP, 14) )
      CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v59->vfptr, 2, 14);
    if ( _effect_parameter::GetEff_State(&v59->m_EP, 21) )
    {
      if ( pAP.nAttactType != 4 && pAP.nAttactType != 5 && pAP.nAttactType != 6 && pAP.nAttactType != 7 )
        return;
      CCharacter::RemoveSFContHelpByEffect((CCharacter *)&v59->vfptr, 2, 21);
    }
    if ( v59->m_bFreeSFByClass )
      pAt.m_bIsCrtAtt = 1;
    CPartyModeKillMonsterExpNotify::CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
    if ( !pAt.m_bFailure )
    {
      nTotalDam = CPlayer::_check_exp_after_attack(v59, pAt.m_nDamagedObjNum, pAt.m_DamList, &kPartyExpNotify);
      if ( nTotalDam > 0 )
        CPlayer::_check_dst_param_after_attack(v59, nTotalDam, pDsta);
    }
    if ( bCount )
      CPlayer::SendMsg_AttackResult_Count(v59, &pAt);
    else
      CPlayer::SendMsg_AttackResult_Gen(v59, &pAt, v33);
    CPartyModeKillMonsterExpNotify::Notify(&kPartyExpNotify);
    v41 = 0;
    v42 = 0;
    for ( j = 0; j < pAt.m_nDamagedObjNum; ++j )
    {
      v44 = pAt.m_DamList[j].m_nActiveDamage + pAt.m_DamList[j].m_nDamage;
      v12 = CPlayerDB::GetLevel(&v59->m_Param);
      v52 = pAt.m_DamList[j].m_pChar;
      v53 = v52->vfptr;
      LOBYTE(ppEffBtProp) = 1;
      LODWORD(pEffBtFld) = 0;
      LODWORD(ppfldBullet) = -1;
      LOBYTE(ppBulletProp) = pAt.m_bIsCrtAtt;
      ((void (__fastcall *)(CCharacter *, _QWORD, CPlayer *, _QWORD))v53->SetDamage)(
        v52,
        (unsigned int)v44,
        v59,
        (unsigned int)v12);
      if ( CPlayer::IsChaosMode(v59) )
      {
        v54 = ((int (__fastcall *)(CPlayer *))v59->vfptr->GetObjRace)(v59);
        v13 = pAt.m_DamList[j].m_pChar;
        v55 = pAt.m_DamList[j].m_pChar->vfptr;
        v14 = ((int (__fastcall *)(CCharacter *))v55->GetObjRace)(v13);
        if ( v54 == v14 )
          continue;
      }
      v56 = ((int (__fastcall *)(CPlayer *))v59->vfptr->GetObjRace)(v59);
      v15 = pAt.m_DamList[j].m_pChar;
      v57 = pAt.m_DamList[j].m_pChar->vfptr;
      v16 = ((int (__fastcall *)(CCharacter *))v57->GetObjRace)(v15);
      if ( v56 == v16 && !pAt.m_DamList[j].m_pChar->m_ObjID.m_byID )
      {
        v45 = (CPlayer *)pAt.m_DamList[j].m_pChar;
        if ( CPlayer::IsPunished(v45, 1, 0) )
          continue;
      }
      if ( !pAt.m_bFailure )
      {
        v17 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
        if ( CPlayer::IsPassMasteryLimitLvDiff(v59, v17) )
        {
          v58 = (float)pAt.m_DamList[j].m_nDamage;
          v18 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetMaxHP)(pAt.m_DamList[j].m_pChar);
          if ( (float)(v58 / (float)v18) >= 0.0099999998 )
          {
            v19 = ((int (__fastcall *)(CCharacter *))pAt.m_DamList[j].m_pChar->vfptr->GetLevel)(pAt.m_DamList[j].m_pChar);
            v20 = CPlayer::GetMasteryCumAfterAttack(v59, v19);
            v41 += v20;
            ++v42;
          }
        }
      }
    }
    if ( v42 > 0 && !(unsigned __int8)((int (__fastcall *)(CPlayer *))v59->vfptr->IsInTown)(v59) )
    {
      v11 = (unsigned __int64)v41 >> 32;
      LODWORD(v11) = v41 % v42;
      dwAlter = v41 / v42;
      if ( v41 / v42 > 0 )
      {
        if ( v59->m_pmWpn.byWpType == 7 )
        {
          CPlayer::Emb_AlterStat(v59, 6, 0, dwAlter, 0, "CPlayer::pc_PlayAttack_Gen()---0", 1);
        }
        else
        {
          LOBYTE(pEffBtFld) = 1;
          ppfldBullet = (_BulletItem_fld **)"CPlayer::pc_PlayAttack_Gen()---1";
          LOBYTE(ppBulletProp) = 0;
          CPlayer::Emb_AlterStat(v59, 0, v59->m_pmWpn.byWpClass, dwAlter, 0, "CPlayer::pc_PlayAttack_Gen()---1", 1);
        }
      }
    }
    if ( pDsta
      && v59->m_pRecalledAnimusChar
      && ((int (__fastcall *)(CCharacter *, unsigned __int64))pDsta->vfptr->GetHP)(pDsta, v11) > 0 )
    {
      CAnimus::MasterAttack_MasterInform(v59->m_pRecalledAnimusChar, pDsta);
    }
    if ( pDsta && ((int (__fastcall *)(CCharacter *, unsigned __int64))pDsta->vfptr->GetHP)(pDsta, v11) > 0 )
      _TOWER_PARAM::NotifyOwnerAttackInform(&v59->m_pmTwr, pDsta);
    if ( pItem )
    {
      LOBYTE(ppfldBullet) = 1;
      LOBYTE(ppBulletProp) = 0;
      v47 = CPlayer::Emb_AlterDurPoint(v59, 2, pItem->m_byStorageIndex, -pAP.nShotNum, 0, 1);
      if ( v47 )
        CPlayer::SendMsg_AlterWeaponBulletInform(v59, pItem->m_wSerial, v47);
      else
        CMgrAvatorItemHistory::consume_del_item(
          &CPlayer::s_MgrItemHistory,
          v59->m_ObjID.m_wIndex,
          pItem,
          v59->m_szItemHistoryFileName);
    }
    CPlayer::SetBattleMode(v59, 1);
    if ( v28 && pAP.nEffShotNum > 0 )
    {
      LOBYTE(ppfldBullet) = 1;
      LOBYTE(ppBulletProp) = 0;
      v48 = CPlayer::Emb_AlterDurPoint(v59, 2, v28->m_byStorageIndex, -pAP.nEffShotNum, 0, 1);
      if ( v48 )
        CPlayer::SendMsg_AlterWeaponBulletInform(v59, v28->m_wSerial, v48);
      else
        CMgrAvatorItemHistory::consume_del_item(
          &CPlayer::s_MgrItemHistory,
          v59->m_ObjID.m_wIndex,
          v28,
          v59->m_szItemHistoryFileName);
    }
    CPartyModeKillMonsterExpNotify::~CPartyModeKillMonsterExpNotify(&kPartyExpNotify);
  }
}
