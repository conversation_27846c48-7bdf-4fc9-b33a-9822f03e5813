#pragma once
#ifndef _CMOVEMAPLIMITRIGHTINFOREGIST__1_DTOR2_1403ACB40_H
#define _CMOVEMAPLIMITRIGHTINFOREGIST__1_DTOR2_1403ACB40_H

// Auto-generated header for _CMoveMapLimitRightInfoRegist__1_dtor2_1403ACB40.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_2(__int64 a1, __int64 a2)
;

#endif // _CMOVEMAPLIMITRIGHTINFOREGIST__1_DTOR2_1403ACB40_H
