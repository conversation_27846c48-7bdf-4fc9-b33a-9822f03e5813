/*
 * Function: ?pc_RequestUILockInit@CPlayer@@QEAAXPEAVCUserDB@@PEAD1E1@Z
 * Address: 0x140100BA0
 */

void __fastcall CPlayer::pc_RequestUILockInit(CPlayer *this, CUserDB *pUserDB, char *szUILockPW, char *szUILockPW_Confirm, char byUILock_HintIndex, char *uszUILock_HintAnswer)
{
  __int64 *v6; // rdi@1
  signed __int64 i; // rcx@1
  char *v8; // rax@21
  __int64 v9; // [sp+0h] [bp-48h]@1
  char byHintIndex[8]; // [sp+20h] [bp-28h]@29
  char *uszHintAnswer; // [sp+28h] [bp-20h]@33
  char v12; // [sp+30h] [bp-18h]@5
  CPlayer *v13; // [sp+50h] [bp+8h]@1
  CUserDB *v14; // [sp+58h] [bp+10h]@1
  char *Str; // [sp+60h] [bp+18h]@1
  const char *Str2; // [sp+68h] [bp+20h]@1

  Str2 = szUILockPW_Confirm;
  Str = szUILockPW;
  v14 = pUserDB;
  v13 = this;
  v6 = &v9;
  for ( i = 16i64; i; --i )
  {
    *(_DWORD *)v6 = -*********;
    v6 = (__int64 *)((char *)v6 + 4);
  }
  if ( !pUserDB->m_byUserDgr )
  {
    v12 = 0;
    if ( szUILockPW
      && szUILockPW_Confirm
      && uszUILock_HintAnswer
      && strlen_0(szUILockPW)
      && strlen_0(Str2)
      && strlen_0(uszUILock_HintAnswer) )
    {
      if ( strlen_0(Str) <= 0xC && strlen_0(Str2) <= 0xC && strlen_0(uszUILock_HintAnswer) <= 0x10 )
      {
        if ( !strcmp_0(Str, Str2) )
        {
          if ( !strcmp_0(Str, v14->m_szAccount_PW) )
          {
            v12 = 2;
          }
          else if ( (signed int)(unsigned __int8)CPlayerDB::GetTrunkSlotNum(&v13->m_Param) > 0
                 && (v8 = CPlayerDB::GetTrunkPasswdW(&v13->m_Param), !strcmp_0(Str, v8)) )
          {
            v12 = 2;
          }
          else if ( isalphastr(Str) || isdigitstr(Str) )
          {
            v12 = 5;
          }
          else if ( isalnumstr(Str) )
          {
            if ( IsSQLValidString(uszUILock_HintAnswer) )
            {
              if ( v14->m_byUILock )
                v12 = 11;
            }
            else
            {
              *(_QWORD *)byHintIndex = uszUILock_HintAnswer;
              CLogFile::Write(
                &stru_1799C8E78,
                "CPlayer::pc_CharacterRenameCheck() : Account %u(%s) !::IsSQLValidString( uszUILock_HintAnswer(%s) ) Invalid!",
                v14->m_dwAccountSerial,
                v14->m_szAccountID);
              v12 = 6;
            }
          }
          else
          {
            v12 = 5;
          }
        }
        else
        {
          v12 = 1;
        }
      }
      else
      {
        v12 = 6;
      }
    }
    else
    {
      v12 = 6;
    }
    if ( v12 )
    {
      CPlayer::SendMsg_UILock_Init_Result(v13, v12);
    }
    else
    {
      uszHintAnswer = uszUILock_HintAnswer;
      byHintIndex[0] = byUILock_HintIndex;
      CPlayer::SendMsg_UILock_Init_Request_ToAccount(
        v13,
        v14->m_dwAccountSerial,
        Str,
        v14->m_idWorld.wIndex,
        byUILock_HintIndex,
        uszUILock_HintAnswer);
    }
  }
}
