/*
 * Function: ?pc_SetTargetObjectRequest@CPlayer@@QEAAXPEAVCGameObject@@K_N@Z
 * Address: 0x1400FDA40
 */

void __fastcall CPlayer::pc_SetTargetObjectRequest(CPlayer *this, CGameObject *pTar, unsigned int dwSerial, bool bForce)
{
  __int64 *v4; // rdi@1
  signed __int64 i; // rcx@1
  __int64 v6; // [sp+0h] [bp-38h]@1
  char v7; // [sp+20h] [bp-18h]@4
  CPlayer *v8; // [sp+40h] [bp+8h]@1
  CGameObject *pTara; // [sp+48h] [bp+10h]@1
  bool v10; // [sp+58h] [bp+20h]@1

  v10 = bForce;
  pTara = pTar;
  v8 = this;
  v4 = &v6;
  for ( i = 12i64; i; --i )
  {
    *(_DWORD *)v4 = -858993460;
    v4 = (__int64 *)((char *)v4 + 4);
  }
  v7 = 0;
  if ( pTar )
  {
    if ( pTar->m_bLive )
    {
      if ( pTar->m_dwObjSerial == dwSerial )
      {
        if ( !IsTargeting(pTar) )
          v7 = 4;
      }
      else
      {
        v7 = 3;
      }
    }
    else
    {
      v7 = 2;
    }
  }
  else
  {
    v7 = 1;
  }
  if ( !v7 )
  {
    CPlayer::__target::init(&v8->m_TargetObject);
    v8->m_TargetObject.pObject = pTara;
    v8->m_TargetObject.byKind = pTara->m_ObjID.m_byKind;
    v8->m_TargetObject.byID = pTara->m_ObjID.m_byID;
    v8->m_TargetObject.dwSerial = pTara->m_dwObjSerial;
    v8->m_TargetObject.wHPRate = (*(int (__fastcall **)(CGameObject *))&pTara->vfptr->gap8[8])(pTara);
    ((void (__fastcall *)(CGameObject *, CPlayer *))pTara->vfptr->BeTargeted)(pTara, v8);
  }
  CPlayer::SendMsg_SetTargetObjectResult(v8, v7, v10);
  CPlayer::SendTargetMonsterSFContInfo(v8);
  CPlayer::SendTargetPlayerDamageContInfo(v8);
}
