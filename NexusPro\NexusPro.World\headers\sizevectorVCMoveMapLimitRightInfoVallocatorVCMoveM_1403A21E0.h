#pragma once
#ifndef SIZEVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVCMOVEM_1403A21E0_H
#define SIZEVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVCMOVEM_1403A21E0_H

// Auto-generated header for sizevectorVCMoveMapLimitRightInfoVallocatorVCMoveM_1403A21E0.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

#endif // SIZEVECTORVCMOVEMAPLIMITRIGHTINFOVALLOCATORVCMOVEM_1403A21E0_H
