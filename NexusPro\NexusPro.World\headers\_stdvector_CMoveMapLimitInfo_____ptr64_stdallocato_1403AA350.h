#pragma once
#ifndef _STDVECTOR_CMOVEMAPLIMITINFO_____PTR64_STDALLOCATO_1403AA350_H
#define _STDVECTOR_CMOVEMAPLIMITINFO_____PTR64_STDALLOCATO_1403AA350_H

// Auto-generated header for _stdvector_CMoveMapLimitInfo_____ptr64_stdallocato_1403AA350.cpp
// Generated by NexusPro Header Generator

// Standard includes
#include <windows.h>
#include <string>
#include <vector>
#include <memory>
#include <iostream>

// Custom includes
// No custom includes found

// Forward declarations

// Function declarations
_1_::dtor_0(__int64 a1, __int64 a2)
;

#endif // _STDVECTOR_CMOVEMAPLIMITINFO_____PTR64_STDALLOCATO_1403AA350_H
