/*
 * Function: ?pc_WPActiveAttack_Skill@CPlayer@@QEAA_NPEAU_be_damaged_char@@PEAH1PEAU_skill_fld@@EG@Z
 * Address: 0x14008AA30
 */

char __fastcall CPlayer::pc_WPActiveAttack_Skill(CPlayer *this, _be_damaged_char *pDamList, int *nDamagedObjNum, int *nShotNum, _skill_fld *pSkillFld, char byEffectCode, unsigned __int16 wBulletSerial)
{
  __int64 *v7; // rdi@1
  signed __int64 i; // rcx@1
  char result; // al@8
  __int64 v10; // [sp+0h] [bp-488h]@1
  _STORAGE_LIST::_db_con **ppBulletProp; // [sp+20h] [bp-468h]@11
  _BulletItem_fld **ppfldBullet; // [sp+28h] [bp-460h]@11
  _STORAGE_LIST::_db_con *pBulletItem; // [sp+30h] [bp-458h]@11
  float v14; // [sp+38h] [bp-450h]@11
  _attack_param *pAP; // [sp+40h] [bp-448h]@11
  int *nShotNuma; // [sp+48h] [bp-440h]@11
  _STORAGE_LIST::_db_con *v17; // [sp+58h] [bp-430h]@4
  _BulletItem_fld *v18; // [sp+78h] [bp-410h]@4
  int nAttType; // [sp+84h] [bp-404h]@4
  char Dst; // [sp+98h] [bp-3F0h]@4
  float v21; // [sp+B4h] [bp-3D4h]@9
  _attack_param pParam; // [sp+D0h] [bp-3B8h]@11
  CPlayerAttack v23; // [sp+170h] [bp-318h]@11
  char v24; // [sp+474h] [bp-14h]@15
  int j; // [sp+478h] [bp-10h]@12
  int k; // [sp+47Ch] [bp-Ch]@15
  CPlayer *v27; // [sp+490h] [bp+8h]@1
  _be_damaged_char *v28; // [sp+498h] [bp+10h]@1
  int *v29; // [sp+4A0h] [bp+18h]@1
  int *v30; // [sp+4A8h] [bp+20h]@1

  v30 = nShotNum;
  v29 = nDamagedObjNum;
  v28 = pDamList;
  v27 = this;
  v7 = &v10;
  for ( i = 288i64; i; --i )
  {
    *(_DWORD *)v7 = -858993460;
    v7 = (__int64 *)((char *)v7 + 4);
  }
  v17 = 0i64;
  v18 = 0i64;
  nAttType = 0;
  memcpy_0(&Dst, pDamList->m_pChar->m_fCurPos, 0xCui64);
  if ( byEffectCode )
    nAttType = pSkillFld->m_nAttType[0];
  else
    nAttType = pSkillFld->m_nAttType[v27->m_pmWpn.nActiveEffLvl - 1];
  if ( CPlayer::_pre_check_wpactive_skill_attack(v27, byEffectCode, pSkillFld, wBulletSerial, &v17, &v18) )
  {
    v21 = FLOAT_1_0;
    if ( v17 )
      v21 = v18->m_fGAAF;
    _attack_param::_attack_param(&pParam);
    nShotNuma = v30;
    pAP = &pParam;
    v14 = v21;
    pBulletItem = v17;
    LODWORD(ppfldBullet) = nAttType;
    LOBYTE(ppBulletProp) = byEffectCode;
    CPlayer::make_wpactive_skill_attack_param(
      v27,
      v28->m_pChar,
      pSkillFld,
      (float *)&Dst,
      byEffectCode,
      nAttType,
      v17,
      v21,
      &pParam,
      v30);
    CPlayerAttack::CPlayerAttack(&v23, (CCharacter *)&v27->vfptr);
    CPlayerAttack::WPActiveAttackSkill(&v23, &pParam);
    if ( v23.m_nDamagedObjNum > 0 )
    {
      for ( j = 0; j < v23.m_nDamagedObjNum; ++j )
      {
        v24 = 0;
        for ( k = 0; k < *v29; ++k )
        {
          if ( v28[k].m_pChar == v23.m_DamList[j].m_pChar )
          {
            v24 = 1;
            v28[k].m_bActiveSucc = v23.m_DamList[j].m_nDamage > 0;
            v28[k].m_nActiveDamage = v23.m_DamList[j].m_nDamage;
            break;
          }
        }
        if ( !v24 && *v29 < 30 )
        {
          v28[*v29].m_pChar = v23.m_DamList[j].m_pChar;
          v28[*v29].m_nDamage = 0;
          v28[*v29].m_bActiveSucc = v23.m_DamList[j].m_nDamage > 0;
          v28[(*v29)++].m_nActiveDamage = v23.m_DamList[j].m_nDamage;
        }
      }
    }
    result = 1;
  }
  else
  {
    result = 0;
  }
  return result;
}
